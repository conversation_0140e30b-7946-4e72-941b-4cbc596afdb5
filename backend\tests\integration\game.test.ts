import { createTestApp, TestAssertions } from '../utils/testApp';

describe('游戏API测试', () => {
  const testApp = createTestApp();
  const request = testApp.getRequest();

  beforeAll(async () => {
    await testApp.start();
  });

  afterAll(async () => {
    await testApp.stop();
  });

  describe('GET /api/v1/game/status', () => {
    it('应该返回游戏状态', async () => {
      const response = await request
        .get('/api/v1/game/status')
        .expect(200);

      TestAssertions.expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('isOnline');
      expect(response.body.data).toHaveProperty('serverTime');
      expect(response.body.data).toHaveProperty('playerCount');
      expect(response.body.data).toHaveProperty('serverStatus');
      expect(response.body.data).toHaveProperty('maintenanceMode');
      expect(response.body.data).toHaveProperty('version');
    });
  });

  describe('POST /api/v1/game/offline-rewards', () => {
    it('应该计算离线收益', async () => {
      const offlineData = {
        offlineTime: 7200, // 2小时
      };

      const response = await request
        .post('/api/v1/game/offline-rewards')
        .send(offlineData)
        .expect(200);

      TestAssertions.expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('offlineTime', offlineData.offlineTime);
      expect(response.body.data).toHaveProperty('rewards');
      expect(response.body.data.rewards).toHaveProperty('coins');
      expect(response.body.data.rewards).toHaveProperty('experience');
      expect(response.body.data.rewards).toHaveProperty('items');
    });

    it('应该使用默认离线时间', async () => {
      const response = await request
        .post('/api/v1/game/offline-rewards')
        .send({})
        .expect(200);

      TestAssertions.expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('offlineTime', 3600); // 默认1小时
    });
  });

  describe('GET /api/v1/game/config', () => {
    it('应该返回游戏配置', async () => {
      const response = await request
        .get('/api/v1/game/config')
        .expect(200);

      TestAssertions.expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('maxLevel');
      expect(response.body.data).toHaveProperty('experienceTable');
      expect(response.body.data).toHaveProperty('currencies');
      expect(response.body.data).toHaveProperty('features');
      expect(Array.isArray(response.body.data.experienceTable)).toBe(true);
    });
  });

  describe('GET /api/v1/game/achievements', () => {
    it('应该返回成就列表', async () => {
      const response = await request
        .get('/api/v1/game/achievements')
        .expect(200);

      TestAssertions.expectSuccessResponse(response);
      expect(Array.isArray(response.body.data)).toBe(true);
      
      if (response.body.data.length > 0) {
        const achievement = response.body.data[0];
        expect(achievement).toHaveProperty('id');
        expect(achievement).toHaveProperty('name');
        expect(achievement).toHaveProperty('description');
        expect(achievement).toHaveProperty('icon');
        expect(achievement).toHaveProperty('unlocked');
        expect(achievement).toHaveProperty('rewards');
      }
    });
  });

  describe('GET /api/v1/game/quests', () => {
    it('应该返回任务列表', async () => {
      const response = await request
        .get('/api/v1/game/quests')
        .expect(200);

      TestAssertions.expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('daily');
      expect(response.body.data).toHaveProperty('weekly');
      expect(response.body.data).toHaveProperty('main');
      expect(Array.isArray(response.body.data.daily)).toBe(true);
      expect(Array.isArray(response.body.data.weekly)).toBe(true);
      expect(Array.isArray(response.body.data.main)).toBe(true);
    });
  });

  describe('POST /api/v1/game/save', () => {
    it('应该保存游戏数据', async () => {
      const saveData = {
        playerData: {
          level: 10,
          experience: 1500,
          coins: 5000,
        },
        characterData: {
          name: '测试角色',
          attributes: {
            strength: 15,
            agility: 12,
          },
        },
      };

      const response = await request
        .post('/api/v1/game/save')
        .send(saveData)
        .expect(200);

      TestAssertions.expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('savedAt');
      expect(response.body.data).toHaveProperty('checksum');
      expect(response.body.data.savedAt).toBeValidDate();
    });
  });
});
