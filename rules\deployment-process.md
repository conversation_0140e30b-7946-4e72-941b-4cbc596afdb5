# 部署发布规范

> 📖 **导航**: [返回主页](./README.md) | [测试规范](./testing-procedures.md) | [小程序优化](./miniprogram-optimization.md)

## 🚀 发布流程

### 发布前检查清单
```typescript
// 发布检查清单
export const DeploymentChecklist = {
    // 代码质量检查
    codeQuality: [
        '所有代码已通过ESLint检查',
        '所有TypeScript类型检查通过',
        '代码覆盖率达到80%以上',
        '所有单元测试通过',
        '集成测试通过',
        '性能测试通过'
    ],
    
    // 功能完整性检查
    functionality: [
        '核心游戏功能正常',
        '武侠系统功能完整',
        '放置系统运行正常',
        '社交功能可用',
        'UI界面显示正确',
        '音效和音乐正常'
    ],
    
    // 平台兼容性检查
    compatibility: [
        '微信小程序测试通过',
        '抖音小程序测试通过',
        '不同设备尺寸适配正常',
        '网络异常处理正确',
        '离线功能正常'
    ],
    
    // 性能优化检查
    performance: [
        '启动时间小于3秒',
        '内存使用控制在限制内',
        '包体大小符合要求',
        '帧率稳定在30fps以上',
        '网络请求响应及时'
    ],
    
    // 安全性检查
    security: [
        '用户数据加密存储',
        '网络通信使用HTTPS',
        '防作弊机制有效',
        '敏感信息不暴露',
        '权限申请合理'
    ]
};
```

### 版本管理
```typescript
// 版本信息管理
export class VersionManager {
    // 版本号格式: MAJOR.MINOR.PATCH
    static readonly VERSION_PATTERN = /^(\d+)\.(\d+)\.(\d+)$/;
    
    // 版本类型
    static readonly VERSION_TYPES = {
        MAJOR: 'major',     // 重大版本更新
        MINOR: 'minor',     // 功能版本更新
        PATCH: 'patch',     // 修复版本更新
        HOTFIX: 'hotfix'    // 热修复版本
    };
    
    // 获取当前版本
    static getCurrentVersion(): string {
        return GameConfig.VERSION;
    }
    
    // 生成新版本号
    static generateNewVersion(type: string, currentVersion: string): string {
        const match = currentVersion.match(this.VERSION_PATTERN);
        if (!match) {
            throw new Error('Invalid version format');
        }
        
        let [, major, minor, patch] = match.map(Number);
        
        switch (type) {
            case this.VERSION_TYPES.MAJOR:
                major++;
                minor = 0;
                patch = 0;
                break;
            case this.VERSION_TYPES.MINOR:
                minor++;
                patch = 0;
                break;
            case this.VERSION_TYPES.PATCH:
            case this.VERSION_TYPES.HOTFIX:
                patch++;
                break;
            default:
                throw new Error('Invalid version type');
        }
        
        return `${major}.${minor}.${patch}`;
    }
    
    // 版本比较
    static compareVersions(version1: string, version2: string): number {
        const v1Parts = version1.split('.').map(Number);
        const v2Parts = version2.split('.').map(Number);
        
        for (let i = 0; i < 3; i++) {
            if (v1Parts[i] > v2Parts[i]) return 1;
            if (v1Parts[i] < v2Parts[i]) return -1;
        }
        
        return 0;
    }
}
```

## 🏗️ 构建配置

### 微信小程序构建
```javascript
// build-scripts/build-wechat.js
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class WeChatBuilder {
    constructor() {
        this.buildConfig = {
            platform: 'wechatgame',
            debug: false,
            sourceMaps: false,
            optimize: true,
            minify: true,
            subpackages: true
        };
    }
    
    async build() {
        console.log('开始构建微信小程序版本...');
        
        // 1. 清理构建目录
        this.cleanBuildDirectory();
        
        // 2. 设置微信小程序特定配置
        this.setupWeChatConfig();
        
        // 3. 执行构建
        await this.executeBuild();
        
        // 4. 优化构建结果
        this.optimizeBuildOutput();
        
        // 5. 生成分包配置
        this.generateSubpackageConfig();
        
        // 6. 验证构建结果
        this.validateBuild();
        
        console.log('微信小程序构建完成!');
    }
    
    cleanBuildDirectory() {
        const buildDir = path.join(__dirname, '../build/wechatgame');
        if (fs.existsSync(buildDir)) {
            fs.rmSync(buildDir, { recursive: true });
        }
    }
    
    setupWeChatConfig() {
        // 设置微信小程序特定的配置
        const wechatConfig = {
            appid: process.env.WECHAT_APP_ID,
            setting: {
                urlCheck: false,
                es6: true,
                enhance: true,
                postcss: true,
                minified: true,
                newFeature: true
            },
            compileType: 'game'
        };
        
        fs.writeFileSync(
            path.join(__dirname, '../project.config.json'),
            JSON.stringify(wechatConfig, null, 2)
        );
    }
    
    async executeBuild() {
        const buildCommand = `cocos build --platform wechatgame --debug false --optimize true`;
        execSync(buildCommand, { stdio: 'inherit' });
    }
    
    optimizeBuildOutput() {
        // 压缩资源文件
        this.compressAssets();
        
        // 移除调试代码
        this.removeDebugCode();
        
        // 优化代码结构
        this.optimizeCode();
    }
    
    generateSubpackageConfig() {
        const subpackageConfig = {
            subpackages: [
                {
                    name: 'battle',
                    root: 'subpackages/battle/'
                },
                {
                    name: 'wuxia',
                    root: 'subpackages/wuxia/'
                },
                {
                    name: 'social',
                    root: 'subpackages/social/'
                }
            ]
        };
        
        fs.writeFileSync(
            path.join(__dirname, '../build/wechatgame/game.json'),
            JSON.stringify(subpackageConfig, null, 2)
        );
    }
    
    validateBuild() {
        const buildDir = path.join(__dirname, '../build/wechatgame');
        
        // 检查包体大小
        const packageSize = this.calculatePackageSize(buildDir);
        if (packageSize > 4 * 1024 * 1024) { // 4MB
            throw new Error(`Package size too large: ${packageSize} bytes`);
        }
        
        // 检查必要文件
        const requiredFiles = ['game.js', 'game.json', 'project.config.json'];
        for (const file of requiredFiles) {
            if (!fs.existsSync(path.join(buildDir, file))) {
                throw new Error(`Required file missing: ${file}`);
            }
        }
        
        console.log(`构建验证通过，包体大小: ${(packageSize / 1024 / 1024).toFixed(2)}MB`);
    }
    
    calculatePackageSize(dir) {
        let totalSize = 0;
        const files = fs.readdirSync(dir);
        
        for (const file of files) {
            const filePath = path.join(dir, file);
            const stats = fs.statSync(filePath);
            
            if (stats.isDirectory()) {
                totalSize += this.calculatePackageSize(filePath);
            } else {
                totalSize += stats.size;
            }
        }
        
        return totalSize;
    }
}

// 执行构建
if (require.main === module) {
    const builder = new WeChatBuilder();
    builder.build().catch(console.error);
}

module.exports = WeChatBuilder;
```

### 抖音小程序构建
```javascript
// build-scripts/build-douyin.js
const WeChatBuilder = require('./build-wechat');

class DouyinBuilder extends WeChatBuilder {
    constructor() {
        super();
        this.buildConfig.platform = 'bytedance-mini-game';
    }
    
    setupDouyinConfig() {
        const douyinConfig = {
            appid: process.env.DOUYIN_APP_ID,
            setting: {
                urlCheck: false,
                es6: true,
                enhance: true,
                postcss: true,
                minified: true
            },
            compileType: 'game'
        };
        
        fs.writeFileSync(
            path.join(__dirname, '../project.config.json'),
            JSON.stringify(douyinConfig, null, 2)
        );
    }
    
    async executeBuild() {
        const buildCommand = `cocos build --platform bytedance-mini-game --debug false --optimize true`;
        execSync(buildCommand, { stdio: 'inherit' });
    }
}

module.exports = DouyinBuilder;
```

## 🔄 自动化部署

### 部署脚本
```javascript
// scripts/deploy.js
const WeChatBuilder = require('./build-scripts/build-wechat');
const DouyinBuilder = require('./build-scripts/build-douyin');

class DeploymentManager {
    constructor() {
        this.platforms = ['wechat', 'douyin'];
        this.environments = ['staging', 'production'];
    }
    
    async deploy(platform, environment) {
        console.log(`开始部署到 ${platform} ${environment} 环境...`);
        
        try {
            // 1. 运行部署前检查
            await this.runPreDeploymentChecks();
            
            // 2. 构建项目
            await this.buildProject(platform);
            
            // 3. 运行测试
            await this.runTests();
            
            // 4. 部署到目标环境
            await this.deployToEnvironment(platform, environment);
            
            // 5. 运行部署后验证
            await this.runPostDeploymentValidation(platform, environment);
            
            console.log(`✅ ${platform} ${environment} 部署成功!`);
            
        } catch (error) {
            console.error(`❌ ${platform} ${environment} 部署失败:`, error);
            throw error;
        }
    }
    
    async runPreDeploymentChecks() {
        console.log('运行部署前检查...');
        
        // 检查代码质量
        execSync('npm run lint', { stdio: 'inherit' });
        
        // 检查类型
        execSync('npx tsc --noEmit', { stdio: 'inherit' });
        
        // 检查版本号
        this.validateVersion();
        
        console.log('✅ 部署前检查通过');
    }
    
    async buildProject(platform) {
        console.log(`构建 ${platform} 平台...`);
        
        const Builder = platform === 'wechat' ? WeChatBuilder : DouyinBuilder;
        const builder = new Builder();
        await builder.build();
        
        console.log(`✅ ${platform} 构建完成`);
    }
    
    async runTests() {
        console.log('运行测试套件...');
        
        // 运行单元测试
        execSync('npm run test', { stdio: 'inherit' });
        
        // 运行集成测试
        execSync('npm run test:integration', { stdio: 'inherit' });
        
        console.log('✅ 所有测试通过');
    }
    
    async deployToEnvironment(platform, environment) {
        console.log(`部署到 ${platform} ${environment} 环境...`);
        
        if (platform === 'wechat') {
            await this.deployToWeChat(environment);
        } else if (platform === 'douyin') {
            await this.deployToDouyin(environment);
        }
        
        console.log(`✅ ${platform} ${environment} 部署完成`);
    }
    
    async deployToWeChat(environment) {
        const uploadCommand = environment === 'production' 
            ? 'npm run upload:wechat:prod'
            : 'npm run upload:wechat:staging';
            
        execSync(uploadCommand, { stdio: 'inherit' });
    }
    
    async deployToDouyin(environment) {
        const uploadCommand = environment === 'production'
            ? 'npm run upload:douyin:prod'
            : 'npm run upload:douyin:staging';
            
        execSync(uploadCommand, { stdio: 'inherit' });
    }
    
    async runPostDeploymentValidation(platform, environment) {
        console.log('运行部署后验证...');
        
        // 验证部署是否成功
        await this.validateDeployment(platform, environment);
        
        // 运行冒烟测试
        await this.runSmokeTests(platform, environment);
        
        console.log('✅ 部署后验证通过');
    }
    
    validateVersion() {
        const currentVersion = VersionManager.getCurrentVersion();
        if (!VersionManager.VERSION_PATTERN.test(currentVersion)) {
            throw new Error(`Invalid version format: ${currentVersion}`);
        }
    }
}

// 命令行接口
if (require.main === module) {
    const [platform, environment] = process.argv.slice(2);
    
    if (!platform || !environment) {
        console.error('Usage: node deploy.js <platform> <environment>');
        console.error('Platforms: wechat, douyin');
        console.error('Environments: staging, production');
        process.exit(1);
    }
    
    const deployer = new DeploymentManager();
    deployer.deploy(platform, environment).catch(error => {
        console.error('Deployment failed:', error);
        process.exit(1);
    });
}

module.exports = DeploymentManager;
```

## 📊 发布监控

### 发布后监控
```typescript
// 发布监控系统
export class DeploymentMonitor {
    private _metrics: Map<string, any> = new Map();
    private _alerts: IAlert[] = [];
    
    // 监控关键指标
    public startMonitoring(): void {
        this.monitorPerformance();
        this.monitorErrors();
        this.monitorUserActivity();
        this.monitorBusinessMetrics();
    }
    
    private monitorPerformance(): void {
        setInterval(() => {
            const metrics = {
                fps: this.getCurrentFPS(),
                memory: this.getMemoryUsage(),
                loadTime: this.getAverageLoadTime(),
                networkLatency: this.getNetworkLatency()
            };
            
            this._metrics.set('performance', metrics);
            this.checkPerformanceAlerts(metrics);
        }, 30000); // 每30秒检查一次
    }
    
    private monitorErrors(): void {
        // 监控JavaScript错误
        window.addEventListener('error', (event) => {
            this.reportError({
                type: 'javascript_error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                stack: event.error?.stack
            });
        });
        
        // 监控Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            this.reportError({
                type: 'unhandled_promise_rejection',
                reason: event.reason
            });
        });
    }
    
    private checkPerformanceAlerts(metrics: any): void {
        // FPS过低警报
        if (metrics.fps < 20) {
            this.createAlert('low_fps', `FPS过低: ${metrics.fps}`);
        }
        
        // 内存使用过高警报
        if (metrics.memory > 0.9) {
            this.createAlert('high_memory', `内存使用过高: ${(metrics.memory * 100).toFixed(1)}%`);
        }
        
        // 加载时间过长警报
        if (metrics.loadTime > 5000) {
            this.createAlert('slow_loading', `加载时间过长: ${metrics.loadTime}ms`);
        }
    }
    
    private createAlert(type: string, message: string): void {
        const alert: IAlert = {
            id: this.generateAlertId(),
            type,
            message,
            timestamp: Date.now(),
            severity: this.getAlertSeverity(type)
        };
        
        this._alerts.push(alert);
        this.sendAlert(alert);
    }
    
    private sendAlert(alert: IAlert): void {
        // 发送警报到监控系统
        console.warn(`[ALERT] ${alert.type}: ${alert.message}`);
        
        // 发送到远程监控服务
        this.sendToMonitoringService(alert);
    }
}

interface IAlert {
    id: string;
    type: string;
    message: string;
    timestamp: number;
    severity: 'low' | 'medium' | 'high' | 'critical';
}
```

---

> 📖 **相关文档**: [测试规范](./testing-procedures.md) | [开发工作流](./development-workflow.md) | [小程序优化](./miniprogram-optimization.md)
