import { _decorator, Component } from 'cc';
import { GameManager } from '../../managers/GameManager';
import { GameState } from '../../data/GameTypes';

const { ccclass } = _decorator;

/**
 * 测试运行器
 * 用于验证核心系统功能
 */
@ccclass('TestRunner')
export class TestRunner extends Component {
    
    protected start(): void {
        this.runTests();
    }

    /**
     * 运行所有测试
     */
    private async runTests(): Promise<void> {
        console.log('🧪 开始运行测试...');
        
        try {
            await this.testGameManager();
            console.log('✅ 所有测试通过');
        } catch (error) {
            console.error('❌ 测试失败:', error);
        }
    }

    /**
     * 测试游戏管理器
     */
    private async testGameManager(): Promise<void> {
        console.log('🎮 测试游戏管理器...');
        
        const gameManager = GameManager.getInstance();
        
        // 测试单例模式
        const gameManager2 = GameManager.getInstance();
        if (gameManager !== gameManager2) {
            throw new Error('单例模式测试失败');
        }
        console.log('✅ 单例模式测试通过');
        
        // 测试初始化
        await gameManager.initialize();
        if (!gameManager.isInitialized) {
            throw new Error('初始化测试失败');
        }
        console.log('✅ 初始化测试通过');
        
        // 测试状态管理
        gameManager.setGameState(GameState.PLAYING);
        if (gameManager.getGameState() !== GameState.PLAYING) {
            throw new Error('状态管理测试失败');
        }
        console.log('✅ 状态管理测试通过');
        
        // 测试游戏控制
        gameManager.startGame();
        if (!gameManager.isGameRunning()) {
            throw new Error('游戏控制测试失败');
        }
        console.log('✅ 游戏控制测试通过');
        
        // 测试暂停/恢复
        gameManager.pauseGame();
        if (!gameManager.isGamePaused()) {
            throw new Error('暂停测试失败');
        }
        
        gameManager.resumeGame();
        if (gameManager.isGamePaused()) {
            throw new Error('恢复测试失败');
        }
        console.log('✅ 暂停/恢复测试通过');
        
        // 测试配置获取
        const config = gameManager.getGameConfig();
        if (!config || !config.version) {
            throw new Error('配置获取测试失败');
        }
        console.log('✅ 配置获取测试通过');
        
        console.log('🎮 游戏管理器测试完成');
    }
}
