{"name": "ai-testing-framework", "version": "1.0.0", "description": "AI驱动的测试框架 - 武侠放置游戏迁移项目", "main": "dist/index.js", "bin": {"ai-test": "dist/index.js"}, "scripts": {"build": "tsc", "dev": "ts-node index.ts", "start": "node dist/index.js", "test": "jest", "lint": "eslint . --ext .ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepare": "npm run build", "ai-test:setup": "ts-node index.ts setup", "ai-test:discover": "ts-node index.ts discover", "ai-test:discover-and-test": "ts-node index.ts discover", "ai-test:validate-algorithms": "ts-node index.ts validate", "ai-test:report": "ts-node index.ts report", "ai-test:stats": "ts-node index.ts stats", "ai-test:clean": "ts-node index.ts clean", "demo:setup": "npm run ai-test:setup -- --project ../../", "demo:discover": "npm run ai-test:discover -- --project ../../", "demo:validate": "npm run ai-test:validate-algorithms", "demo:full": "npm run demo:setup && npm run demo:discover && npm run demo:validate"}, "keywords": ["ai", "testing", "automation", "game-development", "migration", "cocos-creator", "godot", "wuxia-game"], "author": "AI Testing Team", "license": "MIT", "dependencies": {"commander": "^11.0.0", "chalk": "^4.1.2", "inquirer": "^8.2.5", "ora": "^5.4.1", "fs-extra": "^11.1.1", "glob": "^10.3.3", "lodash": "^4.17.21", "axios": "^1.4.0"}, "devDependencies": {"@types/node": "^20.4.5", "@types/jest": "^29.5.3", "@types/lodash": "^4.14.195", "@types/fs-extra": "^11.0.1", "@types/inquirer": "^8.2.6", "typescript": "^5.1.6", "ts-node": "^10.9.1", "jest": "^29.6.1", "ts-jest": "^29.1.1", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "rimraf": "^5.0.1", "nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-org/wuxia-game-migration.git"}, "bugs": {"url": "https://github.com/your-org/wuxia-game-migration/issues"}, "homepage": "https://github.com/your-org/wuxia-game-migration#readme"}