"use strict";

module.exports = {
    "extension_name": "Cocos MCP Server",
    "description": "AI MCP Server for Cocos Creator 3.8",
    "panel_title": "MCP Server",
    "open_panel": "Open MCP Panel",
    "start_server": "Start Server",
    "stop_server": "Stop Server",
    "server_status": "Server Status",
    "port": "Port",
    "settings": "Settings",
    "connected": "Connected",
    "disconnected": "Disconnected",
    "server_running": "Server is running on port {0}",
    "server_stopped": "Server has stopped",
    "auto_start": "Auto Start",
    "debug_log": "Debug Logging",
    "max_connections": "Max Connections",
    "connection_info": "Connection Info",
    "http_url": "HTTP URL",
    "copy": "Copy",
    "save_settings": "Save Settings",
    "settings_saved": "Settings saved successfully",
    "server_started": "MCP Server Started",
    "server_stopped_msg": "MCP Server Stopped",
    "failed_to_start": "Failed to start server",
    "failed_to_stop": "Failed to stop server",
    "failed_to_save": "Failed to save settings",
    "url_copied": "HTTP URL copied to clipboard"
};