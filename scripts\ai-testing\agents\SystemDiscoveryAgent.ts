/**
 * 系统发现代理 - 自动发现和分析项目中的系统
 * 使用AI技术智能识别系统边界和特征
 */

import * as fs from 'fs';
import * as path from 'path';
import { SystemInfo } from '../types';

export interface ProjectStructure {
    rootPath: string;
    directories: DirectoryInfo[];
    files: FileInfo[];
    packageInfo?: PackageInfo;
}

export interface DirectoryInfo {
    name: string;
    path: string;
    files: FileInfo[];
    subdirectories: DirectoryInfo[];
}

export interface FileInfo {
    name: string;
    path: string;
    extension: string;
    size: number;
    content?: string;
}

export interface SystemBoundary {
    name: string;
    path: string;
    files: string[];
    entryPoints: string[];
    exports: string[];
    imports: string[];
}

export class SystemDiscoveryAgent {
    private projectStructureCache: Map<string, ProjectStructure> = new Map();
    private systemInfoCache: Map<string, SystemInfo> = new Map();

    /**
     * 自动发现项目中的所有系统
     */
    public async discoverSystems(projectPath: string): Promise<SystemInfo[]> {
        console.log(`🔍 SystemDiscoveryAgent: Discovering systems in ${projectPath}`);

        try {
            // 1. 扫描项目结构
            const projectStructure = await this.scanProjectStructure(projectPath);
            
            // 2. 识别系统边界
            const systemBoundaries = await this.identifySystemBoundaries(projectStructure);
            
            // 3. 分类和分析每个系统
            const systems: SystemInfo[] = [];
            for (const boundary of systemBoundaries) {
                const systemInfo = await this.classifySystem(boundary);
                systems.push(systemInfo);
            }

            console.log(`✅ Discovered ${systems.length} systems`);
            return systems;

        } catch (error) {
            console.error(`❌ Failed to discover systems:`, error);
            throw error;
        }
    }

    /**
     * 分析指定路径的系统
     */
    public async analyzeSystemAtPath(systemPath: string): Promise<SystemInfo> {
        console.log(`🔍 Analyzing system at ${systemPath}`);

        // 检查缓存
        if (this.systemInfoCache.has(systemPath)) {
            return this.systemInfoCache.get(systemPath)!;
        }

        try {
            // 1. 扫描系统目录
            const systemStructure = await this.scanSystemDirectory(systemPath);
            
            // 2. 分析系统特征
            const systemFeatures = await this.analyzeSystemFeatures(systemStructure);
            
            // 3. 确定系统类型
            const systemType = await this.determineSystemType(systemFeatures);
            
            // 4. 评估复杂度
            const complexity = await this.assessSystemComplexity(systemFeatures);
            
            // 5. 分析依赖关系
            const dependencies = await this.analyzeDependencies(systemStructure);
            
            // 6. 确定测试优先级
            const testingPriority = this.determineTestingPriority(systemType, complexity);
            
            // 7. 推荐测试策略
            const recommendedStrategies = this.recommendTestingStrategies(systemType, complexity);

            const systemInfo: SystemInfo = {
                name: path.basename(systemPath),
                path: systemPath,
                type: systemType,
                complexity,
                features: systemFeatures,
                dependencies,
                testingPriority,
                recommendedStrategies
            };

            // 缓存结果
            this.systemInfoCache.set(systemPath, systemInfo);
            
            console.log(`✅ System analysis complete:`, systemInfo);
            return systemInfo;

        } catch (error) {
            console.error(`❌ Failed to analyze system at ${systemPath}:`, error);
            throw error;
        }
    }

    /**
     * 智能系统分类
     * 使用模式匹配和启发式规则识别系统类型
     */
    public async classifySystem(systemBoundary: SystemBoundary): Promise<SystemInfo> {
        console.log(`🏷️ Classifying system: ${systemBoundary.name}`);

        // 分析文件内容和结构模式
        const patterns = await this.analyzeSystemPatterns(systemBoundary);
        
        // 基于模式确定系统类型
        const systemType = this.inferSystemType(patterns);
        
        // 评估系统复杂度
        const complexity = this.evaluateComplexity(patterns);
        
        // 提取系统特征
        const features = this.extractFeatures(patterns);
        
        // 分析依赖关系
        const dependencies = this.extractDependencies(systemBoundary);
        
        const systemInfo: SystemInfo = {
            name: systemBoundary.name,
            path: systemBoundary.path,
            type: systemType,
            complexity,
            features,
            dependencies,
            testingPriority: this.determineTestingPriority(systemType, complexity),
            recommendedStrategies: this.recommendTestingStrategies(systemType, complexity)
        };

        console.log(`✅ System classified as ${systemType} with ${complexity} complexity`);
        return systemInfo;
    }

    // 私有辅助方法

    /**
     * 扫描项目结构
     */
    private async scanProjectStructure(projectPath: string): Promise<ProjectStructure> {
        if (this.projectStructureCache.has(projectPath)) {
            return this.projectStructureCache.get(projectPath)!;
        }

        const structure: ProjectStructure = {
            rootPath: projectPath,
            directories: [],
            files: []
        };

        try {
            const items = await fs.promises.readdir(projectPath, { withFileTypes: true });
            
            for (const item of items) {
                const itemPath = path.join(projectPath, item.name);
                
                if (item.isDirectory()) {
                    // 跳过常见的忽略目录
                    if (this.shouldIgnoreDirectory(item.name)) {
                        continue;
                    }
                    
                    const dirInfo = await this.scanDirectory(itemPath);
                    structure.directories.push(dirInfo);
                } else if (item.isFile()) {
                    const fileInfo = await this.scanFile(itemPath);
                    structure.files.push(fileInfo);
                }
            }

            // 读取package.json信息（如果存在）
            const packageJsonPath = path.join(projectPath, 'package.json');
            if (fs.existsSync(packageJsonPath)) {
                structure.packageInfo = await this.readPackageInfo(packageJsonPath);
            }

            this.projectStructureCache.set(projectPath, structure);
            return structure;

        } catch (error) {
            console.error(`Failed to scan project structure at ${projectPath}:`, error);
            throw error;
        }
    }

    /**
     * 识别系统边界
     */
    private async identifySystemBoundaries(structure: ProjectStructure): Promise<SystemBoundary[]> {
        const boundaries: SystemBoundary[] = [];

        // 基于目录结构识别
        const directoryBoundaries = this.identifyByDirectory(structure);
        boundaries.push(...directoryBoundaries);

        // 基于文件模式识别
        const patternBoundaries = await this.identifyByPatterns(structure);
        boundaries.push(...patternBoundaries);

        // 合并和去重
        return this.mergeBoundaries(boundaries);
    }

    /**
     * 基于目录结构识别系统边界
     */
    private identifyByDirectory(structure: ProjectStructure): SystemBoundary[] {
        const boundaries: SystemBoundary[] = [];

        // 常见的系统目录模式
        const systemDirectoryPatterns = [
            /^(src|source)$/i,
            /^(components?|comp)$/i,
            /^(systems?|sys)$/i,
            /^(modules?|mod)$/i,
            /^(services?|svc)$/i,
            /^(controllers?|ctrl)$/i,
            /^(models?|model)$/i,
            /^(views?|view)$/i,
            /^(utils?|utilities)$/i,
            /^(helpers?|help)$/i,
            /^(managers?|mgr)$/i
        ];

        for (const directory of structure.directories) {
            // 检查是否匹配系统目录模式
            const isSystemDirectory = systemDirectoryPatterns.some(pattern => 
                pattern.test(directory.name)
            );

            if (isSystemDirectory) {
                boundaries.push({
                    name: directory.name,
                    path: directory.path,
                    files: this.collectFilePaths(directory),
                    entryPoints: this.identifyEntryPoints(directory),
                    exports: [],
                    imports: []
                });
            }
        }

        return boundaries;
    }

    /**
     * 基于文件模式识别系统边界
     */
    private async identifyByPatterns(structure: ProjectStructure): Promise<SystemBoundary[]> {
        const boundaries: SystemBoundary[] = [];

        // 游戏系统特定的文件模式
        const gameSystemPatterns = [
            { pattern: /wuxia|martial|sect|cultivation/i, type: 'wuxia-system' },
            { pattern: /battle|combat|fight|war/i, type: 'battle-system' },
            { pattern: /user|player|character|avatar/i, type: 'user-system' },
            { pattern: /social|friend|guild|chat/i, type: 'social-system' },
            { pattern: /ui|interface|view|screen/i, type: 'ui-system' },
            { pattern: /api|service|server|network/i, type: 'api-system' },
            { pattern: /data|database|storage|cache/i, type: 'data-system' }
        ];

        for (const { pattern, type } of gameSystemPatterns) {
            const matchingFiles = this.findFilesByPattern(structure, pattern);
            
            if (matchingFiles.length > 0) {
                boundaries.push({
                    name: type,
                    path: this.findCommonPath(matchingFiles),
                    files: matchingFiles,
                    entryPoints: this.identifyEntryPointsFromFiles(matchingFiles),
                    exports: [],
                    imports: []
                });
            }
        }

        return boundaries;
    }

    /**
     * 分析系统模式
     */
    private async analyzeSystemPatterns(boundary: SystemBoundary): Promise<SystemPatterns> {
        const patterns: SystemPatterns = {
            fileTypes: new Map(),
            namingPatterns: [],
            structuralPatterns: [],
            contentPatterns: []
        };

        // 分析文件类型分布
        for (const filePath of boundary.files) {
            const ext = path.extname(filePath);
            const count = patterns.fileTypes.get(ext) || 0;
            patterns.fileTypes.set(ext, count + 1);
        }

        // 分析命名模式
        patterns.namingPatterns = this.extractNamingPatterns(boundary.files);

        // 分析结构模式
        patterns.structuralPatterns = this.extractStructuralPatterns(boundary);

        // 分析内容模式（简化实现）
        patterns.contentPatterns = await this.extractContentPatterns(boundary.files.slice(0, 5)); // 只分析前5个文件

        return patterns;
    }

    /**
     * 推断系统类型
     */
    private inferSystemType(patterns: SystemPatterns): string {
        // 基于文件类型和命名模式推断系统类型
        const typeScores = new Map<string, number>();

        // 基于文件扩展名评分
        for (const [ext, count] of patterns.fileTypes) {
            if (ext === '.ts' || ext === '.js') {
                typeScores.set('business-logic', (typeScores.get('business-logic') || 0) + count);
            } else if (ext === '.vue' || ext === '.jsx' || ext === '.tsx') {
                typeScores.set('ui-system', (typeScores.get('ui-system') || 0) + count * 2);
            } else if (ext === '.json') {
                typeScores.set('data-management', (typeScores.get('data-management') || 0) + count);
            }
        }

        // 基于命名模式评分
        for (const pattern of patterns.namingPatterns) {
            if (pattern.includes('wuxia') || pattern.includes('martial')) {
                typeScores.set('wuxia-system', (typeScores.get('wuxia-system') || 0) + 3);
            } else if (pattern.includes('battle') || pattern.includes('combat')) {
                typeScores.set('battle-system', (typeScores.get('battle-system') || 0) + 3);
            } else if (pattern.includes('user') || pattern.includes('player')) {
                typeScores.set('user-management', (typeScores.get('user-management') || 0) + 2);
            } else if (pattern.includes('api') || pattern.includes('service')) {
                typeScores.set('api-service', (typeScores.get('api-service') || 0) + 2);
            }
        }

        // 返回得分最高的系统类型
        let maxScore = 0;
        let inferredType = 'game-logic'; // 默认类型

        for (const [type, score] of typeScores) {
            if (score > maxScore) {
                maxScore = score;
                inferredType = type;
            }
        }

        return inferredType;
    }

    /**
     * 评估系统复杂度
     */
    private evaluateComplexity(patterns: SystemPatterns): 'simple' | 'medium' | 'complex' {
        let complexityScore = 0;

        // 基于文件数量
        const totalFiles = Array.from(patterns.fileTypes.values()).reduce((sum, count) => sum + count, 0);
        if (totalFiles > 20) complexityScore += 2;
        else if (totalFiles > 10) complexityScore += 1;

        // 基于文件类型多样性
        if (patterns.fileTypes.size > 5) complexityScore += 2;
        else if (patterns.fileTypes.size > 3) complexityScore += 1;

        // 基于结构模式复杂度
        complexityScore += patterns.structuralPatterns.length;

        // 基于内容模式复杂度
        complexityScore += patterns.contentPatterns.length;

        if (complexityScore >= 6) return 'complex';
        if (complexityScore >= 3) return 'medium';
        return 'simple';
    }

    // 更多私有辅助方法的简化实现
    private shouldIgnoreDirectory(name: string): boolean {
        const ignorePatterns = ['node_modules', '.git', 'dist', 'build', '.vscode', '.idea'];
        return ignorePatterns.includes(name) || name.startsWith('.');
    }

    private async scanDirectory(dirPath: string): Promise<DirectoryInfo> {
        // 简化实现
        return {
            name: path.basename(dirPath),
            path: dirPath,
            files: [],
            subdirectories: []
        };
    }

    private async scanFile(filePath: string): Promise<FileInfo> {
        const stats = await fs.promises.stat(filePath);
        return {
            name: path.basename(filePath),
            path: filePath,
            extension: path.extname(filePath),
            size: stats.size
        };
    }

    private async readPackageInfo(packagePath: string): Promise<PackageInfo> {
        const content = await fs.promises.readFile(packagePath, 'utf-8');
        return JSON.parse(content);
    }

    private collectFilePaths(directory: DirectoryInfo): string[] {
        return directory.files.map(f => f.path);
    }

    private identifyEntryPoints(directory: DirectoryInfo): string[] {
        return directory.files
            .filter(f => f.name === 'index.ts' || f.name === 'index.js' || f.name === 'main.ts')
            .map(f => f.path);
    }

    private findFilesByPattern(structure: ProjectStructure, pattern: RegExp): string[] {
        const matchingFiles: string[] = [];
        
        const searchInDirectory = (dir: DirectoryInfo) => {
            for (const file of dir.files) {
                if (pattern.test(file.name) || pattern.test(file.path)) {
                    matchingFiles.push(file.path);
                }
            }
            for (const subdir of dir.subdirectories) {
                searchInDirectory(subdir);
            }
        };

        for (const dir of structure.directories) {
            searchInDirectory(dir);
        }

        return matchingFiles;
    }

    private findCommonPath(files: string[]): string {
        if (files.length === 0) return '';
        if (files.length === 1) return path.dirname(files[0]);
        
        // 简化实现：返回第一个文件的目录
        return path.dirname(files[0]);
    }

    private identifyEntryPointsFromFiles(files: string[]): string[] {
        return files.filter(f => 
            path.basename(f).startsWith('index.') || 
            path.basename(f).startsWith('main.')
        );
    }

    private mergeBoundaries(boundaries: SystemBoundary[]): SystemBoundary[] {
        // 简化实现：去重
        const uniqueBoundaries = new Map<string, SystemBoundary>();
        for (const boundary of boundaries) {
            uniqueBoundaries.set(boundary.name, boundary);
        }
        return Array.from(uniqueBoundaries.values());
    }

    private extractFeatures(patterns: SystemPatterns): string[] {
        return patterns.namingPatterns.concat(patterns.contentPatterns);
    }

    private extractDependencies(boundary: SystemBoundary): string[] {
        return boundary.imports;
    }

    private determineTestingPriority(systemType: string, complexity: string): 'low' | 'medium' | 'high' {
        if (systemType.includes('battle') || systemType.includes('wuxia')) return 'high';
        if (complexity === 'complex') return 'high';
        if (complexity === 'medium') return 'medium';
        return 'low';
    }

    private recommendTestingStrategies(systemType: string, complexity: string): string[] {
        const strategies = ['unit-testing'];
        
        if (systemType.includes('api')) strategies.push('api-testing');
        if (systemType.includes('ui')) strategies.push('ui-testing');
        if (systemType.includes('battle') || systemType.includes('wuxia')) {
            strategies.push('algorithm-consistency-testing', 'performance-testing');
        }
        if (complexity === 'complex') strategies.push('integration-testing');
        
        return strategies;
    }

    private extractNamingPatterns(files: string[]): string[] {
        return files.map(f => path.basename(f, path.extname(f)).toLowerCase());
    }

    private extractStructuralPatterns(boundary: SystemBoundary): string[] {
        return ['hierarchical']; // 简化实现
    }

    private async extractContentPatterns(files: string[]): Promise<string[]> {
        return ['typescript', 'class-based']; // 简化实现
    }

    private async scanSystemDirectory(systemPath: string): Promise<any> {
        return {}; // 简化实现
    }

    private async analyzeSystemFeatures(structure: any): Promise<string[]> {
        return ['typescript', 'modular']; // 简化实现
    }

    private async determineSystemType(features: string[]): Promise<string> {
        return 'game-logic'; // 简化实现
    }

    private async assessSystemComplexity(features: string[]): Promise<'simple' | 'medium' | 'complex'> {
        return 'medium'; // 简化实现
    }

    private async analyzeDependencies(structure: any): Promise<string[]> {
        return []; // 简化实现
    }
}

// 类型定义
interface SystemPatterns {
    fileTypes: Map<string, number>;
    namingPatterns: string[];
    structuralPatterns: string[];
    contentPatterns: string[];
}

interface PackageInfo {
    name: string;
    version: string;
    dependencies?: Record<string, string>;
    devDependencies?: Record<string, string>;
}
