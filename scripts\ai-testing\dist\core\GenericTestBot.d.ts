/**
 * 通用测试机器人 - 可适配任何系统的智能测试机器人
 * 根据系统特征动态生成和执行测试用例
 */
import { SystemContext, SystemType, TestCase, TestTask, TestTemplate } from '../types';
export interface BotCapability {
    name: string;
    description: string;
    applicableSystemTypes: SystemType[];
    implementation: any;
}
export declare class GenericTestBot {
    private systemContext;
    private testTemplates;
    private capabilities;
    private codeAnalysisAgent;
    private testGenerationAgent;
    private generatedTestCases;
    constructor(systemContext: SystemContext);
    /**
     * 核心能力：根据系统上下文动态生成测试
     */
    generateTestsForSystem(): Promise<TestCase[]>;
    /**
     * 动态能力适配
     * 根据系统类型加载相应的测试能力
     */
    adaptToSystemType(systemType: SystemType): Promise<void>;
    /**
     * 生成测试任务
     */
    generateTestTasks(module: any): Promise<TestTask[]>;
    /**
     * 算法一致性验证
     * 专门用于验证Godot和Cocos Creator版本的算法一致性
     */
    validateAlgorithmConsistency(godotCode: string, cocosCode: string): Promise<AlgorithmValidationResult>;
    /**
     * 加载测试模板
     */
    loadTestTemplates(templates: TestTemplate[]): Promise<void>;
    /**
     * 刷新测试用例
     */
    refreshTestCases(): Promise<void>;
    /**
     * 执行测试用例
     */
    executeTestCases(testCases?: TestCase[]): Promise<TestExecutionResult[]>;
    /**
     * 分析系统特征
     */
    private analyzeSystemFeatures;
    /**
     * 选择适用的测试模板
     */
    private selectTestTemplates;
    /**
     * 从模板生成测试用例
     */
    private generateFromTemplate;
    /**
     * 优化测试套件
     */
    private optimizeTestSuite;
    /**
     * 确定所需能力
     */
    private determineRequiredCapabilities;
    /**
     * 检查是否具有能力
     */
    private hasCapability;
    /**
     * 加载能力
     */
    private loadCapability;
    /**
     * 创建能力实现
     */
    private createCapabilityImplementation;
    /**
     * 将测试用例组织成任务
     */
    private organizeTestCasesIntoTasks;
    /**
     * 执行单个测试用例
     */
    private executeTestCase;
    private analyzeSystemCode;
    private analyzeSystemAPIs;
    private analyzeSystemData;
    private classifySystemType;
    private assessComplexity;
    private extractDependencies;
    private identifyCriticalPaths;
    private isTemplateApplicable;
    private removeDuplicateTests;
    private prioritizeTests;
    private optimizeCoverage;
    private groupTestCasesByType;
    private calculateTaskPriority;
    private extractTaskDependencies;
}
export interface SystemAnalysis {
    systemType: SystemType;
    complexity: string;
    dependencies: string[];
    criticalPaths: string[];
    dataStructures: any[];
    algorithms: any[];
    apis: any[];
}
export interface AlgorithmValidationResult {
    isConsistent: boolean;
    deviationPercentage: number;
    riskPoints?: string[];
    recommendations?: string[];
}
export interface TestExecutionResult {
    testCaseId: string;
    status: 'passed' | 'failed' | 'skipped';
    duration: number;
    error: string | null;
    details: any;
}
//# sourceMappingURL=GenericTestBot.d.ts.map