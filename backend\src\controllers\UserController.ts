import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { User, IUser } from '../models/User';
import { AppError, ErrorCodes } from '../utils/errors';
import { Logger } from '../utils/logger';
import { CacheManager } from '../utils/cache';
import { sendResponse } from '../utils/response';

/**
 * 用户控制器
 */
export class UserController {
  private cacheManager: CacheManager;

  constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  /**
   * 用户注册
   */
  public register = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { username, email, password, confirmPassword } = req.body;

      // 验证密码确认
      if (password !== confirmPassword) {
        throw new AppError('密码确认不匹配', 400, ErrorCodes.VALIDATION_ERROR);
      }

      // 检查用户名和邮箱是否已存在
      const existingUser = await User.findOne({
        $or: [{ username }, { email: email.toLowerCase() }]
      });

      if (existingUser) {
        if (existingUser.username === username) {
          throw new AppError('用户名已存在', 409, ErrorCodes.USERNAME_TAKEN);
        }
        if (existingUser.email === email.toLowerCase()) {
          throw new AppError('邮箱已被注册', 409, ErrorCodes.EMAIL_TAKEN);
        }
      }

      // 创建新用户
      const user = new User({
        username,
        email: email.toLowerCase(),
        password,
        profile: {
          nickname: username,
          preferences: {
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
            notifications: {
              email: true,
              push: true,
              inGame: true,
            },
            privacy: {
              showOnlineStatus: true,
              allowFriendRequests: true,
              showProfile: true,
            },
          },
        },
      });

      // 生成邮箱验证令牌
      const verificationToken = user.generateEmailVerificationToken();
      await user.save();

      // 生成JWT令牌
      const token = this.generateToken(user._id.toString());

      // 缓存用户信息
      await this.cacheManager.set(
        `user:${user._id}`,
        JSON.stringify(user.toPublicJSON()),
        3600 // 1小时
      );

      Logger.info('用户注册成功', {
        userId: user._id,
        username: user.username,
        email: user.email,
      });

      sendResponse(res, 201, '注册成功', {
        user: user.toPublicJSON(),
        token,
        verificationToken, // 在实际应用中，这应该通过邮件发送
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 用户登录
   */
  public login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { username, password } = req.body;

      // 查找用户（支持用户名或邮箱登录）
      const user = await User.findOne({
        $or: [
          { username },
          { email: username.toLowerCase() }
        ]
      }).select('+password');

      if (!user) {
        throw new AppError('用户名或密码错误', 401, ErrorCodes.INVALID_CREDENTIALS);
      }

      // 检查账户是否被锁定
      if (user.isLocked()) {
        throw new AppError('账户已被锁定，请稍后再试', 423, ErrorCodes.ACCOUNT_LOCKED);
      }

      // 验证密码
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        await user.incrementLoginAttempts();
        throw new AppError('用户名或密码错误', 401, ErrorCodes.INVALID_CREDENTIALS);
      }

      // 检查账户状态
      if (!user.isActive) {
        throw new AppError('账户已被禁用', 403, ErrorCodes.ACCOUNT_DISABLED);
      }

      // 重置登录尝试次数
      await user.resetLoginAttempts();

      // 更新登录信息
      user.profile.lastLoginAt = new Date();
      user.profile.loginCount += 1;
      await user.save();

      // 生成JWT令牌
      const token = this.generateToken(user._id.toString());

      // 缓存用户信息
      await this.cacheManager.set(
        `user:${user._id}`,
        JSON.stringify(user.toPublicJSON()),
        3600 // 1小时
      );

      Logger.info('用户登录成功', {
        userId: user._id,
        username: user.username,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });

      sendResponse(res, 200, '登录成功', {
        user: user.toPublicJSON(),
        token,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取用户资料
   */
  public getProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;

      // 先从缓存获取
      const cachedUser = await this.cacheManager.get(`user:${userId}`);
      if (cachedUser) {
        const user = JSON.parse(cachedUser);
        return sendResponse(res, 200, '获取用户资料成功', { user });
      }

      // 从数据库获取
      const user = await User.findById(userId);
      if (!user) {
        throw new AppError('用户不存在', 404, ErrorCodes.USER_NOT_FOUND);
      }

      // 更新缓存
      await this.cacheManager.set(
        `user:${userId}`,
        JSON.stringify(user.toPublicJSON()),
        3600
      );

      sendResponse(res, 200, '获取用户资料成功', {
        user: user.toPublicJSON(),
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 更新用户资料
   */
  public updateProfile = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const updates = req.body;

      // 过滤不允许更新的字段
      const allowedUpdates = [
        'profile.nickname',
        'profile.bio',
        'profile.preferences.language',
        'profile.preferences.timezone',
        'profile.preferences.notifications',
        'profile.preferences.privacy',
      ];

      const filteredUpdates: any = {};
      Object.keys(updates).forEach(key => {
        if (allowedUpdates.includes(key)) {
          filteredUpdates[key] = updates[key];
        }
      });

      const user = await User.findByIdAndUpdate(
        userId,
        { $set: filteredUpdates },
        { new: true, runValidators: true }
      );

      if (!user) {
        throw new AppError('用户不存在', 404, ErrorCodes.USER_NOT_FOUND);
      }

      // 更新缓存
      await this.cacheManager.set(
        `user:${userId}`,
        JSON.stringify(user.toPublicJSON()),
        3600
      );

      Logger.info('用户资料更新成功', {
        userId,
        updates: Object.keys(filteredUpdates),
      });

      sendResponse(res, 200, '用户资料更新成功', {
        user: user.toPublicJSON(),
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 修改密码
   */
  public changePassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { currentPassword, newPassword, confirmPassword } = req.body;

      // 验证新密码确认
      if (newPassword !== confirmPassword) {
        throw new AppError('新密码确认不匹配', 400, ErrorCodes.VALIDATION_ERROR);
      }

      // 获取用户（包含密码）
      const user = await User.findById(userId).select('+password');
      if (!user) {
        throw new AppError('用户不存在', 404, ErrorCodes.USER_NOT_FOUND);
      }

      // 验证当前密码
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        throw new AppError('当前密码错误', 401, ErrorCodes.INVALID_CREDENTIALS);
      }

      // 更新密码
      user.password = newPassword;
      await user.save();

      Logger.info('用户密码修改成功', {
        userId,
        username: user.username,
      });

      sendResponse(res, 200, '密码修改成功');
    } catch (error) {
      next(error);
    }
  };

  /**
   * 请求密码重置
   */
  public requestPasswordReset = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { email } = req.body;

      const user = await User.findOne({ email: email.toLowerCase() });
      if (!user) {
        // 为了安全，即使用户不存在也返回成功消息
        return sendResponse(res, 200, '如果邮箱存在，重置链接已发送');
      }

      // 生成重置令牌
      const resetToken = user.generatePasswordResetToken();
      await user.save();

      Logger.info('密码重置请求', {
        userId: user._id,
        email: user.email,
      });

      // 在实际应用中，这里应该发送邮件
      sendResponse(res, 200, '如果邮箱存在，重置链接已发送', {
        resetToken, // 仅用于测试，实际应用中不应返回
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 重置密码
   */
  public resetPassword = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { token, newPassword, confirmPassword } = req.body;

      // 验证密码确认
      if (newPassword !== confirmPassword) {
        throw new AppError('密码确认不匹配', 400, ErrorCodes.VALIDATION_ERROR);
      }

      // 哈希令牌
      const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

      // 查找用户
      const user = await User.findOne({
        'security.passwordResetToken': hashedToken,
        'security.passwordResetExpires': { $gt: new Date() },
      }).select('+security.passwordResetToken +security.passwordResetExpires');

      if (!user) {
        throw new AppError('重置令牌无效或已过期', 400, ErrorCodes.INVALID_TOKEN);
      }

      // 重置密码
      user.password = newPassword;
      user.security.passwordResetToken = undefined;
      user.security.passwordResetExpires = undefined;
      await user.save();

      Logger.info('密码重置成功', {
        userId: user._id,
        username: user.username,
      });

      sendResponse(res, 200, '密码重置成功');
    } catch (error) {
      next(error);
    }
  };

  /**
   * 邮箱验证
   */
  public verifyEmail = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { token } = req.body;

      // 哈希令牌
      const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

      // 查找用户
      const user = await User.findOne({
        'security.emailVerificationToken': hashedToken,
        'security.emailVerificationExpires': { $gt: new Date() },
      }).select('+security.emailVerificationToken +security.emailVerificationExpires');

      if (!user) {
        throw new AppError('验证令牌无效或已过期', 400, ErrorCodes.INVALID_TOKEN);
      }

      // 验证邮箱
      user.isEmailVerified = true;
      user.security.emailVerificationToken = undefined;
      user.security.emailVerificationExpires = undefined;
      await user.save();

      Logger.info('邮箱验证成功', {
        userId: user._id,
        email: user.email,
      });

      sendResponse(res, 200, '邮箱验证成功');
    } catch (error) {
      next(error);
    }
  };

  /**
   * 生成JWT令牌
   */
  private generateToken(userId: string): string {
    return jwt.sign(
      { id: userId },
      process.env['JWT_SECRET'] || 'default-secret',
      { expiresIn: process.env['JWT_EXPIRES_IN'] || '7d' }
    );
  }
}

export const userController = new UserController();
