#!/usr/bin/env ts-node

/**
 * AI测试框架 - 快速检查工具
 * 快速验证项目状态和基本功能
 */

import { Command } from 'commander';
import chalk from 'chalk';
import * as fs from 'fs';
import * as path from 'path';

const program = new Command();

interface QuickCheckOptions {
    projectPath: string;
    verbose?: boolean;
    checkTypes?: string[];
    outputFormat?: 'console' | 'json' | 'markdown';
}

interface CheckResult {
    name: string;
    status: 'pass' | 'fail' | 'warning';
    message: string;
    details?: any;
    suggestions?: string[];
}

interface QuickCheckReport {
    timestamp: string;
    projectPath: string;
    summary: {
        total: number;
        passed: number;
        failed: number;
        warnings: number;
        score: number;
    };
    checks: CheckResult[];
}

program
    .name('ai-test-quick-check')
    .description('Quick health check for game development projects')
    .version('1.0.0')
    .option('-p, --project-path <path>', 'Project path to check', process.cwd())
    .option('-v, --verbose', 'Enable verbose logging', false)
    .option('-t, --check-types <types>', 'Comma-separated list of check types to run')
    .option('-f, --output-format <format>', 'Output format: console, json, markdown', 'console')
    .action(async (options: QuickCheckOptions) => {
        await runQuickCheck(options);
    });

async function runQuickCheck(options: QuickCheckOptions): Promise<void> {
    console.log(chalk.blue('⚡ AI Testing Framework - Quick Check'));
    console.log(chalk.gray(`Project: ${options.projectPath}`));
    
    try {
        // 1. 确定要运行的检查类型
        const checkTypes = options.checkTypes 
            ? options.checkTypes.split(',').map(t => t.trim())
            : ['structure', 'dependencies', 'syntax', 'performance', 'security', 'best-practices'];

        console.log(chalk.blue(`\n🔍 Running ${checkTypes.length} check categories...`));

        // 2. 执行所有检查
        const allChecks: CheckResult[] = [];
        
        for (const checkType of checkTypes) {
            console.log(chalk.blue(`\n📋 ${checkType.toUpperCase()} Checks:`));
            const checks = await runCheckCategory(checkType, options);
            allChecks.push(...checks);
            
            // 显示即时结果
            checks.forEach(check => {
                const icon = getStatusIcon(check.status);
                const color = getStatusColor(check.status);
                console.log(color(`  ${icon} ${check.name}: ${check.message}`));
            });
        }

        // 3. 生成报告
        const report = generateReport(allChecks, options);
        
        // 4. 输出结果
        await outputReport(report, options);
        
        // 5. 显示总结
        displaySummary(report);

    } catch (error) {
        console.error(chalk.red('\n❌ Quick check failed:'), error);
        process.exit(1);
    }
}

async function runCheckCategory(category: string, options: QuickCheckOptions): Promise<CheckResult[]> {
    const checks: CheckResult[] = [];
    
    switch (category) {
        case 'structure':
            checks.push(...await runStructureChecks(options));
            break;
        case 'dependencies':
            checks.push(...await runDependencyChecks(options));
            break;
        case 'syntax':
            checks.push(...await runSyntaxChecks(options));
            break;
        case 'performance':
            checks.push(...await runPerformanceChecks(options));
            break;
        case 'security':
            checks.push(...await runSecurityChecks(options));
            break;
        case 'best-practices':
            checks.push(...await runBestPracticesChecks(options));
            break;
        default:
            console.log(chalk.yellow(`⚠️ Unknown check category: ${category}`));
    }
    
    return checks;
}

async function runStructureChecks(options: QuickCheckOptions): Promise<CheckResult[]> {
    const checks: CheckResult[] = [];
    
    // 检查项目根目录结构
    const requiredDirs = ['assets', 'scripts', 'scenes'];
    for (const dir of requiredDirs) {
        const dirPath = path.join(options.projectPath, dir);
        if (fs.existsSync(dirPath)) {
            checks.push({
                name: `Directory: ${dir}`,
                status: 'pass',
                message: 'Directory exists'
            });
        } else {
            checks.push({
                name: `Directory: ${dir}`,
                status: 'fail',
                message: 'Required directory missing',
                suggestions: [`Create ${dir} directory`]
            });
        }
    }
    
    // 检查配置文件
    const configFiles = ['package.json', 'tsconfig.json', 'project.godot'];
    for (const file of configFiles) {
        const filePath = path.join(options.projectPath, file);
        if (fs.existsSync(filePath)) {
            checks.push({
                name: `Config: ${file}`,
                status: 'pass',
                message: 'Configuration file found'
            });
        } else {
            checks.push({
                name: `Config: ${file}`,
                status: 'warning',
                message: 'Configuration file not found',
                suggestions: [`Consider adding ${file} for better project management`]
            });
        }
    }
    
    return checks;
}

async function runDependencyChecks(options: QuickCheckOptions): Promise<CheckResult[]> {
    const checks: CheckResult[] = [];
    
    // 检查package.json依赖
    const packageJsonPath = path.join(options.projectPath, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
        try {
            const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
            
            if (packageJson.dependencies) {
                checks.push({
                    name: 'Dependencies',
                    status: 'pass',
                    message: `Found ${Object.keys(packageJson.dependencies).length} dependencies`,
                    details: packageJson.dependencies
                });
            }
            
            if (packageJson.devDependencies) {
                checks.push({
                    name: 'Dev Dependencies',
                    status: 'pass',
                    message: `Found ${Object.keys(packageJson.devDependencies).length} dev dependencies`,
                    details: packageJson.devDependencies
                });
            }
            
        } catch (error) {
            checks.push({
                name: 'Package.json',
                status: 'fail',
                message: 'Invalid package.json format',
                suggestions: ['Fix package.json syntax errors']
            });
        }
    }
    
    // 检查node_modules
    const nodeModulesPath = path.join(options.projectPath, 'node_modules');
    if (fs.existsSync(nodeModulesPath)) {
        checks.push({
            name: 'Node Modules',
            status: 'pass',
            message: 'Dependencies installed'
        });
    } else {
        checks.push({
            name: 'Node Modules',
            status: 'warning',
            message: 'Dependencies not installed',
            suggestions: ['Run npm install or yarn install']
        });
    }
    
    return checks;
}

async function runSyntaxChecks(options: QuickCheckOptions): Promise<CheckResult[]> {
    const checks: CheckResult[] = [];
    
    // 检查TypeScript文件语法
    const tsFiles = await findFiles(options.projectPath, '.ts');
    if (tsFiles.length > 0) {
        checks.push({
            name: 'TypeScript Files',
            status: 'pass',
            message: `Found ${tsFiles.length} TypeScript files`,
            details: { count: tsFiles.length }
        });
        
        // 简单的语法检查（检查是否有明显的语法错误）
        let syntaxErrors = 0;
        for (const file of tsFiles.slice(0, 10)) { // 只检查前10个文件
            try {
                const content = fs.readFileSync(file, 'utf8');
                if (content.includes('SyntaxError') || content.includes('unexpected token')) {
                    syntaxErrors++;
                }
            } catch (error) {
                syntaxErrors++;
            }
        }
        
        if (syntaxErrors === 0) {
            checks.push({
                name: 'Syntax Check',
                status: 'pass',
                message: 'No obvious syntax errors detected'
            });
        } else {
            checks.push({
                name: 'Syntax Check',
                status: 'warning',
                message: `Potential syntax issues in ${syntaxErrors} files`,
                suggestions: ['Run TypeScript compiler to check for syntax errors']
            });
        }
    }
    
    return checks;
}

async function runPerformanceChecks(options: QuickCheckOptions): Promise<CheckResult[]> {
    const checks: CheckResult[] = [];
    
    // 检查项目大小
    const projectSize = await getDirectorySize(options.projectPath);
    if (projectSize < 100 * 1024 * 1024) { // 100MB
        checks.push({
            name: 'Project Size',
            status: 'pass',
            message: `Project size: ${formatBytes(projectSize)}`
        });
    } else if (projectSize < 500 * 1024 * 1024) { // 500MB
        checks.push({
            name: 'Project Size',
            status: 'warning',
            message: `Large project size: ${formatBytes(projectSize)}`,
            suggestions: ['Consider optimizing assets and removing unused files']
        });
    } else {
        checks.push({
            name: 'Project Size',
            status: 'fail',
            message: `Very large project size: ${formatBytes(projectSize)}`,
            suggestions: ['Optimize assets', 'Remove unused dependencies', 'Use asset compression']
        });
    }
    
    return checks;
}

async function runSecurityChecks(options: QuickCheckOptions): Promise<CheckResult[]> {
    const checks: CheckResult[] = [];
    
    // 检查敏感文件
    const sensitiveFiles = ['.env', 'config.json', 'secrets.json'];
    for (const file of sensitiveFiles) {
        const filePath = path.join(options.projectPath, file);
        if (fs.existsSync(filePath)) {
            checks.push({
                name: `Sensitive File: ${file}`,
                status: 'warning',
                message: 'Sensitive file detected',
                suggestions: ['Ensure sensitive files are not committed to version control']
            });
        }
    }
    
    if (checks.length === 0) {
        checks.push({
            name: 'Sensitive Files',
            status: 'pass',
            message: 'No obvious sensitive files detected'
        });
    }
    
    return checks;
}

async function runBestPracticesChecks(options: QuickCheckOptions): Promise<CheckResult[]> {
    const checks: CheckResult[] = [];
    
    // 检查README文件
    const readmeFiles = ['README.md', 'readme.md', 'README.txt'];
    const hasReadme = readmeFiles.some(file => fs.existsSync(path.join(options.projectPath, file)));
    
    if (hasReadme) {
        checks.push({
            name: 'Documentation',
            status: 'pass',
            message: 'README file found'
        });
    } else {
        checks.push({
            name: 'Documentation',
            status: 'warning',
            message: 'No README file found',
            suggestions: ['Add a README.md file to document your project']
        });
    }
    
    // 检查版本控制
    const gitPath = path.join(options.projectPath, '.git');
    if (fs.existsSync(gitPath)) {
        checks.push({
            name: 'Version Control',
            status: 'pass',
            message: 'Git repository detected'
        });
    } else {
        checks.push({
            name: 'Version Control',
            status: 'warning',
            message: 'No Git repository found',
            suggestions: ['Initialize Git repository for version control']
        });
    }
    
    return checks;
}

function generateReport(checks: CheckResult[], options: QuickCheckOptions): QuickCheckReport {
    const passed = checks.filter(c => c.status === 'pass').length;
    const failed = checks.filter(c => c.status === 'fail').length;
    const warnings = checks.filter(c => c.status === 'warning').length;
    const total = checks.length;
    const score = total > 0 ? Math.round((passed / total) * 100) : 0;
    
    return {
        timestamp: new Date().toISOString(),
        projectPath: options.projectPath,
        summary: {
            total,
            passed,
            failed,
            warnings,
            score
        },
        checks
    };
}

async function outputReport(report: QuickCheckReport, options: QuickCheckOptions): Promise<void> {
    if (options.outputFormat === 'json') {
        const outputPath = path.join(options.projectPath, `quick-check-${Date.now()}.json`);
        fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
        console.log(chalk.green(`\n📄 Report saved: ${outputPath}`));
    }
    // 其他格式的输出可以在这里添加
}

function displaySummary(report: QuickCheckReport): void {
    console.log(chalk.blue('\n📊 Quick Check Summary:'));
    console.log(chalk.gray(`  Total checks: ${report.summary.total}`));
    console.log(chalk.green(`  Passed: ${report.summary.passed}`));
    console.log(chalk.red(`  Failed: ${report.summary.failed}`));
    console.log(chalk.yellow(`  Warnings: ${report.summary.warnings}`));
    console.log(chalk.blue(`  Score: ${report.summary.score}%`));
    
    if (report.summary.score >= 90) {
        console.log(chalk.green('\n🎉 Excellent project health!'));
    } else if (report.summary.score >= 70) {
        console.log(chalk.yellow('\n👍 Good project health with room for improvement'));
    } else {
        console.log(chalk.red('\n⚠️ Project needs attention - review failed checks'));
    }
}

// 辅助函数
function getStatusIcon(status: string): string {
    switch (status) {
        case 'pass': return '✅';
        case 'fail': return '❌';
        case 'warning': return '⚠️';
        default: return '❓';
    }
}

function getStatusColor(status: string): any {
    switch (status) {
        case 'pass': return chalk.green;
        case 'fail': return chalk.red;
        case 'warning': return chalk.yellow;
        default: return chalk.gray;
    }
}

async function findFiles(dir: string, extension: string): Promise<string[]> {
    const files: string[] = [];
    
    function scanDir(currentDir: string) {
        try {
            const items = fs.readdirSync(currentDir);
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                    scanDir(fullPath);
                } else if (stat.isFile() && item.endsWith(extension)) {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            // 忽略权限错误等
        }
    }
    
    scanDir(dir);
    return files;
}

async function getDirectorySize(dir: string): Promise<number> {
    let size = 0;
    
    function scanDir(currentDir: string) {
        try {
            const items = fs.readdirSync(currentDir);
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                    scanDir(fullPath);
                } else if (stat.isFile()) {
                    size += stat.size;
                }
            }
        } catch (error) {
            // 忽略权限错误等
        }
    }
    
    scanDir(dir);
    return size;
}

function formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 如果直接运行此文件
if (require.main === module) {
    program.parse();
}
