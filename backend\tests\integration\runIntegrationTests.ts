import { apiDiscoveryTest, IApiDiscoveryResult } from './ApiDiscoveryTest';
import { businessLogicTest, IBusinessFlowResult } from './BusinessLogicTest';
import { Logger } from '../../src/utils/logger';
import fs from 'fs';
import path from 'path';

/**
 * 集成测试报告
 */
export interface IIntegrationTestReport {
    timestamp: string;
    summary: {
        totalTests: number;
        passedTests: number;
        failedTests: number;
        successRate: number;
        totalTime: number;
    };
    apiDiscovery: IApiDiscoveryResult;
    businessLogic: {
        totalFlows: number;
        successfulFlows: number;
        failedFlows: number;
        flows: IBusinessFlowResult[];
    };
    recommendations: string[];
    issues: string[];
}

/**
 * 集成测试报告生成器
 */
class IntegrationTestReporter {
    
    /**
     * 生成集成测试报告
     */
    public generateReport(
        apiResult: IApiDiscoveryResult,
        businessResults: IBusinessFlowResult[]
    ): IIntegrationTestReport {
        const timestamp = new Date().toISOString();
        
        // 计算总体统计
        const apiTests = apiResult.testedEndpoints;
        const apiPassed = apiResult.successfulTests;
        const apiFailed = apiResult.failedTests;
        
        const businessTests = businessResults.length;
        const businessPassed = businessResults.filter(r => r.success).length;
        const businessFailed = businessTests - businessPassed;
        
        const totalTests = apiTests + businessTests;
        const passedTests = apiPassed + businessPassed;
        const failedTests = apiFailed + businessFailed;
        const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
        
        const totalTime = businessResults.reduce((sum, r) => sum + r.totalTime, 0) + 
                         (apiResult.averageResponseTime * apiResult.testedEndpoints);

        // 生成建议和问题
        const recommendations = this.generateRecommendations(apiResult, businessResults);
        const issues = this.generateIssues(apiResult, businessResults);

        return {
            timestamp,
            summary: {
                totalTests,
                passedTests,
                failedTests,
                successRate,
                totalTime,
            },
            apiDiscovery: apiResult,
            businessLogic: {
                totalFlows: businessTests,
                successfulFlows: businessPassed,
                failedFlows: businessFailed,
                flows: businessResults,
            },
            recommendations,
            issues,
        };
    }

    /**
     * 生成建议
     */
    private generateRecommendations(
        apiResult: IApiDiscoveryResult,
        businessResults: IBusinessFlowResult[]
    ): string[] {
        const recommendations: string[] = [];

        // API相关建议
        if (apiResult.coverage < 100) {
            recommendations.push(`API测试覆盖率为${apiResult.coverage.toFixed(1)}%，建议增加测试覆盖`);
        }

        if (apiResult.averageResponseTime > 200) {
            recommendations.push(`API平均响应时间为${apiResult.averageResponseTime.toFixed(0)}ms，建议优化性能`);
        }

        if (apiResult.failedTests > 0) {
            recommendations.push(`有${apiResult.failedTests}个API测试失败，需要修复相关问题`);
        }

        // 业务逻辑相关建议
        const failedFlows = businessResults.filter(r => !r.success);
        if (failedFlows.length > 0) {
            recommendations.push(`有${failedFlows.length}个业务流程测试失败，需要检查业务逻辑`);
        }

        // 性能相关建议
        const slowFlows = businessResults.filter(r => r.totalTime > 5000);
        if (slowFlows.length > 0) {
            recommendations.push(`有${slowFlows.length}个业务流程执行时间超过5秒，建议优化性能`);
        }

        // 数据验证相关建议
        const validationIssues = this.countValidationIssues(businessResults);
        if (validationIssues > 0) {
            recommendations.push(`发现${validationIssues}个数据验证问题，建议加强数据验证`);
        }

        return recommendations;
    }

    /**
     * 生成问题列表
     */
    private generateIssues(
        apiResult: IApiDiscoveryResult,
        businessResults: IBusinessFlowResult[]
    ): string[] {
        const issues: string[] = [];

        // API问题
        apiResult.testResults.forEach(result => {
            if (!result.success) {
                issues.push(`API ${result.endpoint.method} ${result.endpoint.path}: ${result.error || '测试失败'}`);
            }
            if (result.validationErrors && result.validationErrors.length > 0) {
                result.validationErrors.forEach(error => {
                    issues.push(`API ${result.endpoint.method} ${result.endpoint.path}: ${error}`);
                });
            }
        });

        // 业务逻辑问题
        businessResults.forEach(flow => {
            if (!flow.success) {
                issues.push(`业务流程 ${flow.flowName}: ${flow.error || '测试失败'}`);
            }
            
            flow.steps.forEach(step => {
                if (!step.success) {
                    issues.push(`${flow.flowName} - ${step.stepName}: ${step.error || '步骤失败'}`);
                }
                
                if (step.validations) {
                    step.validations.forEach(validation => {
                        if (!validation.passed) {
                            issues.push(`${flow.flowName} - ${step.stepName}: 验证失败 ${validation.field} (期望: ${validation.expected}, 实际: ${validation.actual})`);
                        }
                    });
                }
            });
        });

        return issues;
    }

    /**
     * 统计验证问题数量
     */
    private countValidationIssues(businessResults: IBusinessFlowResult[]): number {
        let count = 0;
        
        businessResults.forEach(flow => {
            flow.steps.forEach(step => {
                if (step.validations) {
                    count += step.validations.filter(v => !v.passed).length;
                }
            });
        });

        return count;
    }

    /**
     * 生成Markdown报告
     */
    public generateMarkdownReport(report: IIntegrationTestReport): string {
        let markdown = `# Day12-13 API集成测试报告

> 📅 **测试日期**: ${report.timestamp}  
> 🧪 **测试类型**: AI驱动的API集成测试  
> ⏱️ **总测试时间**: ${report.summary.totalTime}ms  
> 📊 **成功率**: ${report.summary.successRate.toFixed(2)}%

## 📋 测试摘要

- **总测试数**: ${report.summary.totalTests}
- **通过测试**: ${report.summary.passedTests}
- **失败测试**: ${report.summary.failedTests}
- **成功率**: ${report.summary.successRate.toFixed(2)}%

## 🔍 API自动发现测试

### 测试统计
- **发现端点**: ${report.apiDiscovery.totalEndpoints}个
- **测试端点**: ${report.apiDiscovery.testedEndpoints}个
- **成功测试**: ${report.apiDiscovery.successfulTests}个
- **失败测试**: ${report.apiDiscovery.failedTests}个
- **测试覆盖率**: ${report.apiDiscovery.coverage.toFixed(2)}%
- **平均响应时间**: ${report.apiDiscovery.averageResponseTime.toFixed(0)}ms

### API端点测试结果

| 端点 | 方法 | 状态 | 响应时间 | 状态码 |
|------|------|------|----------|--------|
`;

        report.apiDiscovery.testResults.forEach(result => {
            const status = result.success ? '✅' : '❌';
            markdown += `| ${result.endpoint.path} | ${result.endpoint.method} | ${status} | ${result.responseTime}ms | ${result.statusCode} |\n`;
        });

        markdown += `
## 🔄 业务逻辑测试

### 测试统计
- **业务流程**: ${report.businessLogic.totalFlows}个
- **成功流程**: ${report.businessLogic.successfulFlows}个
- **失败流程**: ${report.businessLogic.failedFlows}个

### 业务流程测试结果

`;

        report.businessLogic.flows.forEach(flow => {
            const status = flow.success ? '✅' : '❌';
            markdown += `#### ${status} ${flow.flowName}\n\n`;
            markdown += `- **执行时间**: ${flow.totalTime}ms\n`;
            markdown += `- **测试步骤**: ${flow.steps.length}个\n`;
            
            if (flow.error) {
                markdown += `- **错误**: ${flow.error}\n`;
            }

            markdown += `\n**步骤详情**:\n\n`;
            flow.steps.forEach(step => {
                const stepStatus = step.success ? '✅' : '❌';
                markdown += `- ${stepStatus} ${step.stepName} (${step.responseTime}ms)\n`;
                
                if (step.validations && step.validations.some(v => !v.passed)) {
                    markdown += `  - 验证问题: ${step.validations.filter(v => !v.passed).length}个\n`;
                }
            });
            markdown += '\n';
        });

        // 问题和建议
        if (report.issues.length > 0) {
            markdown += `## ⚠️ 发现的问题

`;
            report.issues.forEach(issue => {
                markdown += `- ${issue}\n`;
            });
            markdown += '\n';
        }

        if (report.recommendations.length > 0) {
            markdown += `## 💡 改进建议

`;
            report.recommendations.forEach(rec => {
                markdown += `- ${rec}\n`;
            });
            markdown += '\n';
        }

        // 总结
        markdown += `## 🎯 测试总结

`;

        if (report.summary.successRate >= 90) {
            markdown += `🎉 **优秀**: 测试成功率达到${report.summary.successRate.toFixed(2)}%，API集成质量良好！\n\n`;
        } else if (report.summary.successRate >= 70) {
            markdown += `⚠️ **良好**: 测试成功率为${report.summary.successRate.toFixed(2)}%，存在一些需要改进的地方。\n\n`;
        } else {
            markdown += `🔴 **需要改进**: 测试成功率仅为${report.summary.successRate.toFixed(2)}%，需要重点关注失败的测试。\n\n`;
        }

        markdown += `### 关键指标
- **API响应性能**: ${report.apiDiscovery.averageResponseTime < 200 ? '✅ 良好' : '⚠️ 需优化'} (${report.apiDiscovery.averageResponseTime.toFixed(0)}ms)
- **业务流程完整性**: ${report.businessLogic.successfulFlows === report.businessLogic.totalFlows ? '✅ 完整' : '⚠️ 部分失败'}
- **数据一致性**: ${report.issues.filter(i => i.includes('验证失败')).length === 0 ? '✅ 一致' : '⚠️ 存在问题'}

---

**📋 AI驱动的API集成测试完成！**
`;

        return markdown;
    }

    /**
     * 保存报告到文件
     */
    public async saveReport(report: IIntegrationTestReport, filename: string): Promise<void> {
        const reportsDir = path.join(process.cwd(), '../../Reports');
        
        // 确保Reports目录存在
        if (!fs.existsSync(reportsDir)) {
            fs.mkdirSync(reportsDir, { recursive: true });
        }

        // 保存JSON报告
        const jsonPath = path.join(reportsDir, filename.replace('.md', '.json'));
        fs.writeFileSync(jsonPath, JSON.stringify(report, null, 2), 'utf8');

        // 保存Markdown报告
        const markdownReport = this.generateMarkdownReport(report);
        const markdownPath = path.join(reportsDir, filename);
        fs.writeFileSync(markdownPath, markdownReport, 'utf8');
        
        Logger.info('集成测试报告已保存', { jsonPath, markdownPath });
    }
}

/**
 * 主集成测试执行函数
 */
async function runIntegrationTests(): Promise<void> {
    const reporter = new IntegrationTestReporter();
    
    try {
        Logger.info('开始Day12-13 API集成测试');

        // 1. 运行API自动发现测试
        Logger.info('运行API自动发现测试...');
        const apiResult = await apiDiscoveryTest.runFullDiscovery();

        // 2. 运行业务逻辑测试
        Logger.info('运行业务逻辑测试...');
        const businessResults = await businessLogicTest.runAllBusinessTests();

        // 3. 生成测试报告
        const report = reporter.generateReport(apiResult, businessResults);
        
        // 4. 保存报告
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `Day12-13-Integration-Test-Report-${timestamp}.md`;
        await reporter.saveReport(report, filename);

        // 5. 输出测试摘要
        console.log('\n=== Day12-13 API集成测试完成 ===');
        console.log(`总测试数: ${report.summary.totalTests}`);
        console.log(`通过测试: ${report.summary.passedTests}`);
        console.log(`失败测试: ${report.summary.failedTests}`);
        console.log(`成功率: ${report.summary.successRate.toFixed(2)}%`);
        console.log(`API覆盖率: ${report.apiDiscovery.coverage.toFixed(2)}%`);
        console.log(`平均响应时间: ${report.apiDiscovery.averageResponseTime.toFixed(0)}ms`);
        console.log(`测试报告: ${filename}`);

        if (report.summary.failedTests > 0) {
            console.log('\n⚠️ 存在失败的测试，请查看详细报告');
            process.exit(1);
        } else {
            console.log('\n🎉 所有测试通过！API集成成功！');
            process.exit(0);
        }

    } catch (error) {
        Logger.error('集成测试执行失败', error);
        console.error('测试执行失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此文件，则执行集成测试
if (require.main === module) {
    runIntegrationTests();
}

export { runIntegrationTests, IntegrationTestReporter };
