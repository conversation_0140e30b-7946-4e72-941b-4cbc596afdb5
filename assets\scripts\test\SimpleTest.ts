import { _decorator, Component, director } from 'cc';
import { GameManager } from '../managers/GameManager';

const { ccclass } = _decorator;

/**
 * 简单测试脚本
 * 用于验证基础功能是否正常工作
 */
@ccclass('SimpleTest')
export class SimpleTest extends Component {

    protected start(): void {
        console.log('🧪 开始简单测试...');
        this.runBasicTests();
    }

    /**
     * 运行基础测试
     */
    private async runBasicTests(): Promise<void> {
        try {
            // 测试游戏管理器
            console.log('🎮 测试游戏管理器...');
            const gameManager = GameManager.getInstance();
            
            // 初始化测试
            await gameManager.initialize();
            console.log('✅ 游戏管理器初始化成功');
            
            // 状态测试
            console.log('📊 当前游戏状态:', gameManager.getGameState());
            console.log('⚙️ 游戏配置:', gameManager.getGameConfig());
            
            console.log('🎉 所有基础测试通过！');
            
        } catch (error) {
            console.error('❌ 测试失败:', error);
        }
    }
}
