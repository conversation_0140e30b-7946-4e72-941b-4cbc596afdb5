/**
 * 物品数据接口定义
 * 基于Godot项目中的items.xml结构
 */

/**
 * 物品数据接口
 */
export interface IItemData {
    /** 物品唯一标识符 */
    id: string;
    
    /** 物品名称 */
    name: string;
    
    /** 物品描述 */
    description: string;
    
    /** 物品类型 */
    type: ItemType;
    
    /** 稀有度 */
    rarity: ItemRarity;
    
    /** 物品价值 */
    value: number;
    
    /** 是否可堆叠 */
    stackable: boolean;
    
    /** 最大堆叠数量 */
    maxStack: number;
    
    /** 是否可使用 */
    usable: boolean;
    
    /** 是否可装备 */
    equipable: boolean;
    
    /** 物品属性加成 */
    stats: IItemStats;
    
    /** 使用要求 */
    requirements: IItemRequirements;
    
    /** 使用效果 */
    useEffects?: IItemUseEffect[];
    
    /** 装备位置 */
    equipSlot?: EquipSlot;
    
    /** 物品图标路径 */
    iconPath?: string;
    
    /** 物品模型路径 */
    modelPath?: string;

    /** 奖励表ID（用于宝箱类物品） */
    rewardTableId?: string;

    /** 冷却时间（秒） */
    cooldown?: number;

    /** 是否可交易 */
    tradeable?: boolean;

    /** 是否可丢弃 */
    droppable?: boolean;
}

/**
 * 物品类型枚举
 */
export enum ItemType {
    Weapon = 'weapon',
    Armor = 'armor',
    Accessory = 'accessory',
    Consumable = 'consumable',
    Material = 'material',
    Quest = 'quest',
    Misc = 'misc',
    Currency = 'currency'
}

/**
 * 物品稀有度枚举
 */
export enum ItemRarity {
    Common = 'common',
    Uncommon = 'uncommon',
    Rare = 'rare',
    Epic = 'epic',
    Legendary = 'legendary',
    Mythic = 'mythic'
}

/**
 * 装备位置枚举
 */
export enum EquipSlot {
    MainHand = 'main_hand',
    OffHand = 'off_hand',
    TwoHand = 'two_hand',
    Head = 'head',
    Chest = 'chest',
    Legs = 'legs',
    Feet = 'feet',
    Hands = 'hands',
    Ring = 'ring',
    Necklace = 'necklace',
    Earring = 'earring',
    Belt = 'belt'
}

/**
 * 物品属性接口（基于原Godot配置优化）
 */
export interface IItemStats {
    /** 基础伤害 */
    damage: number;

    /** 最大生命值 */
    maxHp: number;

    /** 最大法力值 */
    maxMp: number;

    /** 生命恢复速度 */
    recoverHp: number;

    /** 法力恢复速度 */
    recoverMp: number;

    /** 攻击间隔 */
    atkInterval: number;

    /** 攻击速度 */
    atkSpeed: number;

    /** 施法速度 */
    castSpeed: number;

    /** 物理防御 */
    def: number;

    /** 法术抗性 */
    spellResistance: number;

    /** 近战命中率 */
    meleeAccuracy: number;

    /** 远程命中率 */
    rangedAccuracy: number;

    /** 魔法命中率 */
    magicAccuracy: number;

    /** 近战闪避率 */
    meleeDodge: number;

    /** 远程闪避率 */
    rangedDodge: number;

    /** 魔法闪避率 */
    magicDodge: number;

    /** 穿透力 */
    penetrate: number;

    /** 暴击率 */
    criticalHit: number;

    /** 韧性 */
    toughness: number;

    /** 仇恨值 */
    enmity: number;

    /** 物理伤害加成 */
    damageIncreasePhysical: number;

    /** 魔法伤害加成 */
    damageIncreaseMagical: number;

    /** 火系伤害加成 */
    damageIncreaseFire: number;

    /** 冰系伤害加成 */
    damageIncreaseIce: number;

    /** 雷系伤害加成 */
    damageIncreaseLightning: number;

    /** 经验加成 */
    experienceBonus: number;

    /** 金币加成 */
    goldBonus: number;
}

/**
 * 物品使用要求接口
 */
export interface IItemRequirements {
    /** 所需等级 */
    level: number;
    
    /** 所需职业 */
    classes: string[];
    
    /** 所需属性 */
    attributes: IAttributeRequirement[];
    
    /** 前置物品 */
    items: string[];
    
    /** 前置任务 */
    quests: string[];
}

/**
 * 属性要求接口
 */
export interface IAttributeRequirement {
    /** 属性名称 */
    attribute: string;
    
    /** 所需数值 */
    value: number;
}

/**
 * 物品使用效果接口
 */
export interface IItemUseEffect {
    /** 效果类型 */
    type: ItemEffectType;

    /** 效果数值类型 */
    valueType: EffectValueType;

    /** 效果数值 */
    value: number;

    /** 持续时间（秒，0表示瞬时效果） */
    duration: number;

    /** 目标类型 */
    target: ItemEffectTarget;

    /** 效果描述 */
    description: string;

    /** 额外参数（用于特殊效果） */
    extraParams?: { [key: string]: any };
}

/**
 * 物品效果类型枚举
 */
export enum ItemEffectType {
    RestoreHealth = 'restore_health',
    RestoreMana = 'restore_mana',
    BuffAttribute = 'buff_attribute',
    DebuffAttribute = 'debuff_attribute',
    AddExperience = 'add_experience',
    AddGold = 'add_gold',
    LearnSkill = 'learn_skill',
    OpenRewardTable = 'open_reward_table',
    Teleport = 'teleport',
    Summon = 'summon',
    Transform = 'transform',
    RevivePlayer = 'revive_player',
    IncreaseLevel = 'increase_level'
}

/**
 * 效果数值类型枚举
 */
export enum EffectValueType {
    Fixed = 'fixed',           // 固定数值
    Percentage = 'percentage', // 百分比
    PerLevel = 'per_level',    // 每级数值
    Random = 'random'          // 随机数值
}

/**
 * 物品效果目标枚举
 */
export enum ItemEffectTarget {
    Self = 'self',
    Target = 'target',
    Party = 'party',
    Area = 'area'
}

/**
 * 背包物品接口
 */
export interface IInventoryItem {
    /** 物品ID */
    itemId: string;
    
    /** 数量 */
    quantity: number;
    
    /** 背包位置 */
    slot: number;
    
    /** 获得时间 */
    acquiredTime: number;
    
    /** 是否已装备 */
    equipped: boolean;
    
    /** 物品实例ID */
    instanceId: string;
    
    /** 强化等级 */
    enhanceLevel?: number;
    
    /** 附魔属性 */
    enchantments?: IEnchantment[];
    
    /** 耐久度 */
    durability?: number;
    
    /** 最大耐久度 */
    maxDurability?: number;
}

/**
 * 附魔接口
 */
export interface IEnchantment {
    /** 附魔ID */
    id: string;
    
    /** 附魔类型 */
    type: string;
    
    /** 附魔等级 */
    level: number;
    
    /** 附魔数值 */
    value: number;
    
    /** 附魔描述 */
    description: string;
}

/**
 * 装备集合接口
 */
export interface IEquipmentSet {
    /** 装备集合ID */
    id: string;
    
    /** 集合名称 */
    name: string;
    
    /** 集合描述 */
    description: string;
    
    /** 包含的物品ID列表 */
    items: string[];
    
    /** 集合效果 */
    setBonuses: ISetBonus[];
}

/**
 * 套装效果接口
 */
export interface ISetBonus {
    /** 所需装备数量 */
    requiredPieces: number;
    
    /** 效果描述 */
    description: string;
    
    /** 属性加成 */
    statBonuses: Partial<IItemStats>;
    
    /** 特殊效果 */
    specialEffects: IItemUseEffect[];
}

/**
 * 物品配置文件根接口
 */
export interface IItemConfig {
    /** 配置版本 */
    version: string;
    
    /** 最后更新时间 */
    lastUpdated: string;
    
    /** 物品列表 */
    items: IItemData[];
    
    /** 装备集合列表 */
    equipmentSets?: IEquipmentSet[];
}

/**
 * 物品获得事件接口
 */
export interface IItemAcquiredEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 物品ID */
    itemId: string;
    
    /** 获得数量 */
    quantity: number;
    
    /** 获得方式 */
    source: ItemAcquireSource;
    
    /** 来源详情 */
    sourceDetails?: string;
    
    /** 时间戳 */
    timestamp: number;
}

/**
 * 物品获得方式枚举
 */
export enum ItemAcquireSource {
    Drop = 'drop',
    Purchase = 'purchase',
    Quest = 'quest',
    Craft = 'craft',
    Trade = 'trade',
    Gift = 'gift',
    Admin = 'admin'
}

/**
 * 物品使用事件接口
 */
export interface IItemUseEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 物品ID */
    itemId: string;
    
    /** 使用数量 */
    quantity: number;
    
    /** 目标ID（如果有） */
    targetId?: string;
    
    /** 使用结果 */
    result: IItemUseResult;
    
    /** 时间戳 */
    timestamp: number;
}

/**
 * 物品使用结果接口
 */
export interface IItemUseResult {
    /** 是否成功 */
    success: boolean;
    
    /** 错误信息 */
    error?: string;
    
    /** 应用的效果 */
    effects: IItemUseEffect[];
    
    /** 消耗的数量 */
    consumed: number;
}

/**
 * 物品强化事件接口
 */
export interface IItemEnhanceEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 物品实例ID */
    itemInstanceId: string;
    
    /** 强化前等级 */
    oldLevel: number;
    
    /** 强化后等级 */
    newLevel: number;
    
    /** 是否成功 */
    success: boolean;
    
    /** 消耗的材料 */
    materials: IInventoryItem[];
    
    /** 时间戳 */
    timestamp: number;
}
