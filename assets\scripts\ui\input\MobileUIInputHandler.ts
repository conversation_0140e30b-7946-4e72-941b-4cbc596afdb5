/**
 * 移动端UI输入处理器 - 挂机手游专用
 * 专门为触摸操作和挂机游戏设计的输入处理系统
 */

import { _decorator, Component, input, Input, EventTouch, Node, Vec2 } from 'cc';
import { UIManager } from '../../managers/UIManager';
import { UIPanelType } from '../types/UITypes';
import { EventManager } from '../../managers/EventManager';

const { ccclass } = _decorator;

/**
 * 触摸手势类型
 */
export enum TouchGestureType {
    Tap = 'tap',                    // 单击
    DoubleTap = 'double_tap',       // 双击
    LongPress = 'long_press',       // 长按
    Swipe = 'swipe',               // 滑动
    Pinch = 'pinch',               // 捏合缩放
    Pan = 'pan'                    // 拖拽
}

/**
 * 滑动方向
 */
export enum SwipeDirection {
    Up = 'up',
    Down = 'down',
    Left = 'left',
    Right = 'right'
}

/**
 * 触摸事件数据接口
 */
export interface ITouchEventData {
    /** 事件类型 */
    type: TouchGestureType;
    
    /** 触摸位置 */
    position: Vec2;
    
    /** 开始位置 */
    startPosition?: Vec2;
    
    /** 结束位置 */
    endPosition?: Vec2;
    
    /** 滑动方向 */
    swipeDirection?: SwipeDirection;
    
    /** 滑动距离 */
    swipeDistance?: number;
    
    /** 持续时间 */
    duration?: number;
    
    /** 缩放比例 */
    scale?: number;
    
    /** 时间戳 */
    timestamp: number;
}

/**
 * 调试快捷键配置（仅开发模式）
 */
interface IDebugHotkey {
    /** 键码 */
    key: string;
    
    /** 动作 */
    action: string;
    
    /** 描述 */
    description: string;
    
    /** 是否启用 */
    enabled: boolean;
}

@ccclass('MobileUIInputHandler')
export class MobileUIInputHandler extends Component {
    
    // 触摸检测参数
    private _tapThreshold: number = 10;           // 单击阈值（像素）
    private _longPressThreshold: number = 500;    // 长按阈值（毫秒）
    private _doubleClickThreshold: number = 300;  // 双击时间阈值（毫秒）
    private _swipeThreshold: number = 50;         // 滑动阈值（像素）
    
    // 触摸状态
    private _touchStartTime: number = 0;
    private _touchStartPos: Vec2 = new Vec2();
    private _lastTouchTime: number = 0;
    private _lastTouchPos: Vec2 = new Vec2();
    private _isLongPressing: boolean = false;
    private _longPressTimer: number = 0;
    
    // 调试模式（仅开发时启用）
    private _debugMode: boolean = false;
    private _debugHotkeys: Map<string, IDebugHotkey> = new Map();

    protected onLoad(): void {
        // 绑定触摸事件
        this.bindTouchEvents();
        
        // 初始化调试模式
        this.initDebugMode();
        
        console.log('📱 移动端UI输入处理器初始化完成');
    }

    protected onDestroy(): void {
        this.unbindTouchEvents();
        this.clearLongPressTimer();
    }

    /**
     * 绑定触摸事件
     */
    private bindTouchEvents(): void {
        input.on(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.on(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.on(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        input.on(Input.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
    }

    /**
     * 解绑触摸事件
     */
    private unbindTouchEvents(): void {
        input.off(Input.EventType.TOUCH_START, this.onTouchStart, this);
        input.off(Input.EventType.TOUCH_MOVE, this.onTouchMove, this);
        input.off(Input.EventType.TOUCH_END, this.onTouchEnd, this);
        input.off(Input.EventType.TOUCH_CANCEL, this.onTouchCancel, this);
    }

    /**
     * 触摸开始
     */
    private onTouchStart(event: EventTouch): void {
        const touch = event.touch!;
        const location = touch.getLocation();
        
        this._touchStartTime = Date.now();
        this._touchStartPos.set(location.x, location.y);
        this._isLongPressing = false;
        
        // 启动长按检测
        this.startLongPressDetection();
        
        console.log(`📱 触摸开始: (${location.x}, ${location.y})`);
    }

    /**
     * 触摸移动
     */
    private onTouchMove(event: EventTouch): void {
        const touch = event.touch!;
        const location = touch.getLocation();
        const distance = Vec2.distance(this._touchStartPos, new Vec2(location.x, location.y));
        
        // 如果移动距离超过阈值，取消长按检测
        if (distance > this._tapThreshold) {
            this.clearLongPressTimer();
            this._isLongPressing = false;
        }
    }

    /**
     * 触摸结束
     */
    private onTouchEnd(event: EventTouch): void {
        const touch = event.touch!;
        const location = touch.getLocation();
        const currentTime = Date.now();
        const duration = currentTime - this._touchStartTime;
        const distance = Vec2.distance(this._touchStartPos, new Vec2(location.x, location.y));
        
        this.clearLongPressTimer();
        
        // 判断手势类型
        if (this._isLongPressing) {
            // 长按已触发，不处理其他手势
            return;
        }
        
        if (distance <= this._tapThreshold) {
            // 检查是否为双击
            if (currentTime - this._lastTouchTime < this._doubleClickThreshold &&
                Vec2.distance(this._lastTouchPos, new Vec2(location.x, location.y)) <= this._tapThreshold) {
                this.handleDoubleTap(new Vec2(location.x, location.y));
            } else {
                this.handleTap(new Vec2(location.x, location.y));
            }
            
            this._lastTouchTime = currentTime;
            this._lastTouchPos.set(location.x, location.y);
        } else if (distance > this._swipeThreshold) {
            // 滑动手势
            this.handleSwipe(
                this._touchStartPos,
                new Vec2(location.x, location.y),
                duration
            );
        }
        
        console.log(`📱 触摸结束: 距离=${distance.toFixed(2)}, 时长=${duration}ms`);
    }

    /**
     * 触摸取消
     */
    private onTouchCancel(event: EventTouch): void {
        this.clearLongPressTimer();
        this._isLongPressing = false;
        console.log('📱 触摸取消');
    }

    /**
     * 启动长按检测
     */
    private startLongPressDetection(): void {
        this.clearLongPressTimer();
        this._longPressTimer = setTimeout(() => {
            this._isLongPressing = true;
            this.handleLongPress(this._touchStartPos);
        }, this._longPressThreshold);
    }

    /**
     * 清除长按计时器
     */
    private clearLongPressTimer(): void {
        if (this._longPressTimer) {
            clearTimeout(this._longPressTimer);
            this._longPressTimer = 0;
        }
    }

    /**
     * 处理单击
     */
    private handleTap(position: Vec2): void {
        console.log(`👆 单击: (${position.x}, ${position.y})`);
        
        this.emitTouchEvent({
            type: TouchGestureType.Tap,
            position,
            timestamp: Date.now()
        });
    }

    /**
     * 处理双击
     */
    private handleDoubleTap(position: Vec2): void {
        console.log(`👆👆 双击: (${position.x}, ${position.y})`);
        
        this.emitTouchEvent({
            type: TouchGestureType.DoubleTap,
            position,
            timestamp: Date.now()
        });
    }

    /**
     * 处理长按
     */
    private handleLongPress(position: Vec2): void {
        console.log(`👆⏱️ 长按: (${position.x}, ${position.y})`);
        
        this.emitTouchEvent({
            type: TouchGestureType.LongPress,
            position,
            timestamp: Date.now()
        });
    }

    /**
     * 处理滑动
     */
    private handleSwipe(startPos: Vec2, endPos: Vec2, duration: number): void {
        const deltaX = endPos.x - startPos.x;
        const deltaY = endPos.y - startPos.y;
        const distance = Vec2.distance(startPos, endPos);
        
        // 判断滑动方向
        let direction: SwipeDirection;
        if (Math.abs(deltaX) > Math.abs(deltaY)) {
            direction = deltaX > 0 ? SwipeDirection.Right : SwipeDirection.Left;
        } else {
            direction = deltaY > 0 ? SwipeDirection.Up : SwipeDirection.Down;
        }
        
        console.log(`👆➡️ 滑动: ${direction}, 距离=${distance.toFixed(2)}, 时长=${duration}ms`);
        
        this.emitTouchEvent({
            type: TouchGestureType.Swipe,
            position: endPos,
            startPosition: startPos,
            endPosition: endPos,
            swipeDirection: direction,
            swipeDistance: distance,
            duration,
            timestamp: Date.now()
        });
        
        // 处理特定的滑动手势
        this.handleSwipeGestures(direction, distance);
    }

    /**
     * 处理滑动手势
     */
    private handleSwipeGestures(direction: SwipeDirection, distance: number): void {
        // 只有在距离足够大时才触发面板操作
        if (distance < 100) return;
        
        const uiManager = UIManager.getInstance();
        
        switch (direction) {
            case SwipeDirection.Up:
                // 向上滑动 - 可以打开主菜单或设置
                console.log('🔼 向上滑动手势 - 打开主菜单');
                break;
                
            case SwipeDirection.Down:
                // 向下滑动 - 可以关闭当前面板
                console.log('🔽 向下滑动手势 - 关闭面板');
                break;
                
            case SwipeDirection.Left:
                // 向左滑动 - 可以切换到下一个面板
                console.log('◀️ 向左滑动手势');
                break;
                
            case SwipeDirection.Right:
                // 向右滑动 - 可以切换到上一个面板
                console.log('▶️ 向右滑动手势');
                break;
        }
    }

    /**
     * 初始化调试模式
     */
    private initDebugMode(): void {
        // 检查是否为开发环境
        this._debugMode = true; // 在发布时设为false
        
        if (this._debugMode) {
            this.registerDebugHotkeys();
            input.on(Input.EventType.KEY_DOWN, this.onDebugKeyDown, this);
            console.log('🐛 调试模式已启用');
        }
    }

    /**
     * 注册调试快捷键
     */
    private registerDebugHotkeys(): void {
        const debugKeys: IDebugHotkey[] = [
            { key: 'KeyI', action: 'toggle_inventory', description: '切换背包', enabled: true },
            { key: 'KeyK', action: 'toggle_skills', description: '切换技能', enabled: true },
            { key: 'KeyM', action: 'toggle_menu', description: '切换菜单', enabled: true },
            { key: 'Escape', action: 'close_panels', description: '关闭面板', enabled: true },
            { key: 'F1', action: 'show_debug', description: '显示调试信息', enabled: true }
        ];
        
        for (const key of debugKeys) {
            this._debugHotkeys.set(key.key, key);
        }
        
        console.log(`🐛 注册了 ${debugKeys.length} 个调试快捷键`);
    }

    /**
     * 调试按键处理
     */
    private onDebugKeyDown(event: any): void {
        if (!this._debugMode) return;
        
        const keyCode = event.code;
        const hotkey = this._debugHotkeys.get(keyCode);
        
        if (hotkey && hotkey.enabled) {
            console.log(`🐛 调试快捷键: ${hotkey.description}`);
            this.executeDebugAction(hotkey.action);
        }
    }

    /**
     * 执行调试动作
     */
    private executeDebugAction(action: string): void {
        const uiManager = UIManager.getInstance();
        
        switch (action) {
            case 'toggle_inventory':
                uiManager.togglePanel(UIPanelType.Inventory);
                break;
            case 'toggle_skills':
                uiManager.togglePanel(UIPanelType.Skills);
                break;
            case 'toggle_menu':
                uiManager.togglePanel(UIPanelType.MainMenu);
                break;
            case 'close_panels':
                uiManager.hideAllPanels();
                break;
            case 'show_debug':
                console.log('🐛 调试信息:', uiManager.getUIStats());
                break;
        }
    }

    /**
     * 发送触摸事件
     */
    private emitTouchEvent(eventData: ITouchEventData): void {
        EventManager.getInstance().emit('mobile_touch_gesture', eventData);
    }

    // ==================== 公共API ====================

    /**
     * 设置触摸阈值
     */
    public setTouchThresholds(tap: number, longPress: number, swipe: number): void {
        this._tapThreshold = tap;
        this._longPressThreshold = longPress;
        this._swipeThreshold = swipe;
        console.log(`📱 触摸阈值已更新: 单击=${tap}, 长按=${longPress}ms, 滑动=${swipe}`);
    }

    /**
     * 启用/禁用调试模式
     */
    public setDebugMode(enabled: boolean): void {
        this._debugMode = enabled;
        
        if (enabled) {
            input.on(Input.EventType.KEY_DOWN, this.onDebugKeyDown, this);
            console.log('🐛 调试模式已启用');
        } else {
            input.off(Input.EventType.KEY_DOWN, this.onDebugKeyDown, this);
            console.log('📱 调试模式已禁用');
        }
    }

    /**
     * 获取触摸统计信息
     */
    public getTouchStats(): any {
        return {
            tapThreshold: this._tapThreshold,
            longPressThreshold: this._longPressThreshold,
            swipeThreshold: this._swipeThreshold,
            debugMode: this._debugMode,
            debugHotkeys: Array.from(this._debugHotkeys.values())
        };
    }
}
