"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.testSceneTools = testSceneTools;
exports.testAssetTools = testAssetTools;
exports.testProjectTools = testProjectTools;
exports.runAllTests = runAllTests;
/**
 * 手动测试脚本
 * 可以在 Cocos Creator 控制台中执行测试
 */
async function testSceneTools() {
    console.log('=== Testing Scene Tools ===');
    try {
        // 1. 获取场景信息
        console.log('1. Getting scene info...');
        const sceneInfo = await Editor.Message.request('scene', 'get-scene-info');
        console.log('Scene info:', sceneInfo);
        // 2. 创建节点
        console.log('\n2. Creating test node...');
        const createResult = await Editor.Message.request('scene', 'create-node', {
            name: 'TestNode_' + Date.now(),
            type: 'cc.Node'
        });
        console.log('Create result:', createResult);
        if (createResult && createResult.uuid) {
            const nodeUuid = createResult.uuid;
            // 3. 查询节点
            console.log('\n3. Querying node...');
            const nodeInfo = await Editor.Message.request('scene', 'query-node', {
                uuid: nodeUuid
            });
            console.log('Node info:', nodeInfo);
            // 4. 设置节点属性
            console.log('\n4. Setting node position...');
            await Editor.Message.request('scene', 'set-node-property', {
                uuid: nodeUuid,
                path: 'position',
                value: { x: 100, y: 200, z: 0 }
            });
            console.log('Position set successfully');
            // 5. 添加组件
            console.log('\n5. Adding Sprite component...');
            const addCompResult = await Editor.Message.request('scene', 'add-component', {
                uuid: nodeUuid,
                component: 'cc.Sprite'
            });
            console.log('Component added:', addCompResult);
            // 6. 查询组件
            console.log('\n6. Querying component...');
            const compInfo = await Editor.Message.request('scene', 'query-node-component', {
                uuid: nodeUuid,
                component: 'cc.Sprite'
            });
            console.log('Component info:', compInfo);
            // 7. 删除节点
            console.log('\n7. Removing test node...');
            await Editor.Message.request('scene', 'remove-node', {
                uuid: nodeUuid
            });
            console.log('Node removed successfully');
        }
    }
    catch (error) {
        console.error('Test failed:', error);
    }
}
async function testAssetTools() {
    console.log('\n=== Testing Asset Tools ===');
    try {
        // 1. 查询资源
        console.log('1. Querying image assets...');
        const assets = await Editor.Message.request('asset-db', 'query-assets', {
            pattern: '**/*.png',
            ccType: 'cc.ImageAsset'
        });
        console.log('Found assets:', (assets === null || assets === void 0 ? void 0 : assets.length) || 0);
        // 2. 获取资源信息
        console.log('\n2. Getting asset database info...');
        const assetInfo = await Editor.Message.request('asset-db', 'query-asset-info', {
            uuid: 'db://assets'
        });
        console.log('Asset info:', assetInfo);
    }
    catch (error) {
        console.error('Test failed:', error);
    }
}
async function testProjectTools() {
    console.log('\n=== Testing Project Tools ===');
    try {
        // 1. 获取项目信息
        console.log('1. Getting project info...');
        const projectInfo = await Editor.Message.request('project', 'query-info');
        console.log('Project info:', projectInfo);
        // 2. 检查构建能力
        console.log('\n2. Checking build capability...');
        const canBuild = await Editor.Message.request('project', 'can-build');
        console.log('Can build:', canBuild);
    }
    catch (error) {
        console.error('Test failed:', error);
    }
}
async function runAllTests() {
    console.log('Starting MCP Server Tools Test...\n');
    await testSceneTools();
    await testAssetTools();
    await testProjectTools();
    console.log('\n=== All tests completed ===');
}
// 导出到全局，方便在控制台调用
global.MCPTest = {
    testSceneTools,
    testAssetTools,
    testProjectTools,
    runAllTests
};
//# sourceMappingURL=data:application/json;base64,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