#!/bin/bash

HOOKS_DIR=".git/hooks"

case "$1" in
    "dev"|"development")
        echo "🔧 切换到开发模式Git hooks..."
        if [ -f "$HOOKS_DIR/pre-commit-dev" ]; then
            cp "$HOOKS_DIR/pre-commit-dev" "$HOOKS_DIR/pre-commit"
            chmod +x "$HOOKS_DIR/pre-commit"
            echo "✅ 已切换到开发模式"
        else
            echo "❌ 开发模式hook不存在"
        fi
        ;;
    "strict"|"production")
        echo "🔧 切换到严格模式Git hooks..."
        if [ -f "$HOOKS_DIR/pre-commit-strict" ]; then
            cp "$HOOKS_DIR/pre-commit-strict" "$HOOKS_DIR/pre-commit"
            chmod +x "$HOOKS_DIR/pre-commit"
            echo "✅ 已切换到严格模式"
        else
            echo "❌ 严格模式hook不存在"
        fi
        ;;
    "off"|"disable")
        echo "🔧 禁用Git hooks..."
        if [ -f "$HOOKS_DIR/pre-commit" ]; then
            mv "$HOOKS_DIR/pre-commit" "$HOOKS_DIR/pre-commit.disabled"
            echo "✅ Git hooks已禁用"
        else
            echo "ℹ️ Git hooks已经是禁用状态"
        fi
        ;;
    "on"|"enable")
        echo "🔧 启用Git hooks..."
        if [ -f "$HOOKS_DIR/pre-commit.disabled" ]; then
            mv "$HOOKS_DIR/pre-commit.disabled" "$HOOKS_DIR/pre-commit"
            chmod +x "$HOOKS_DIR/pre-commit"
            echo "✅ Git hooks已启用"
        else
            echo "ℹ️ Git hooks已经是启用状态"
        fi
        ;;
    *)
        echo "用法: $0 {dev|strict|off|on}"
        echo ""
        echo "模式说明:"
        echo "  dev      - 开发模式（宽松检查，允许用户选择）"
        echo "  strict   - 严格模式（严格检查，失败时阻止提交）"
        echo "  off      - 禁用所有Git hooks"
        echo "  on       - 启用Git hooks"
        exit 1
        ;;
esac
