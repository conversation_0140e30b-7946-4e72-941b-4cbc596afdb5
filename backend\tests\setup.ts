import dotenv = require('dotenv');

// 加载测试环境变量
dotenv.config({ path: '.env.test' });

// 设置测试环境
process.env['NODE_ENV'] = 'test';
process.env['LOG_LEVEL'] = 'error'; // 减少测试时的日志输出

// 全局测试配置
beforeAll(async () => {
  // 测试前的全局设置
  console.log('🧪 开始运行测试...');
});

afterAll(async () => {
  // 测试后的全局清理
  console.log('✅ 测试运行完成');
});

// 每个测试前的设置
beforeEach(() => {
  // 清除所有模拟
  jest.clearAllMocks();
});

// 全局错误处理
process.on('unhandledRejection', (reason, _promise) => {
  console.error('测试中出现未处理的Promise拒绝:', reason);
});

// 扩展Jest匹配器
expect.extend({
  toBeValidDate(received) {
    const pass = received instanceof Date && !isNaN(received.getTime());
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid date`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid date`,
        pass: false,
      };
    }
  },
  
  toHaveValidStructure(received, expectedStructure) {
    const hasValidStructure = (obj: any, structure: any): boolean => {
      for (const key in structure) {
        if (!(key in obj)) {
          return false;
        }
        if (typeof structure[key] === 'object' && structure[key] !== null) {
          if (!hasValidStructure(obj[key], structure[key])) {
            return false;
          }
        }
      }
      return true;
    };

    const pass = hasValidStructure(received, expectedStructure);
    if (pass) {
      return {
        message: () => `expected object not to have valid structure`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected object to have valid structure`,
        pass: false,
      };
    }
  },
});

// TypeScript声明扩展
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidDate(): R;
      toHaveValidStructure(expectedStructure: any): R;
    }
  }
}
