# Git 提交规范

## 提交消息格式

```
<type>(<scope>): <subject>

<body>

<footer>
```

### 格式说明

- **type**：提交类型（必需）
- **scope**：影响范围（可选）
- **subject**：简短描述（必需）
- **body**：详细描述（可选）
- **footer**：关联Issue或破坏性变更（可选）

## 提交类型 (type)

### 主要类型

- **feat**：新功能
- **fix**：修复bug
- **docs**：文档更新
- **style**：代码格式调整（不影响功能）
- **refactor**：重构代码（既不是新功能也不是修复）
- **test**：添加或修改测试
- **chore**：构建过程或辅助工具的变动

### 次要类型

- **perf**：性能优化
- **ci**：CI/CD配置文件和脚本的变动
- **build**：构建系统或外部依赖的变动
- **revert**：回滚之前的提交

## 影响范围 (scope)

### 后端模块范围

- **auth**：认证模块
- **user**：用户模块
- **logic**：游戏逻辑
- **data**：数据管理
- **battle**：战斗系统
- **character**：角色系统
- **social**：社交功能
- **item**：物品系统
- **config**：配置管理
- **database**：数据库相关
- **cache**：缓存相关
- **network**：网络通信
- **api**：API接口
- **middleware**：中间件
- **utils**：工具函数
- **types**：类型定义

## 提交示例

### 基本示例

```bash
# 新功能
git commit -m "feat(auth): 添加JWT认证功能"

# 修复bug
git commit -m "fix(user): 修复用户注册时邮箱验证问题"

# 文档更新
git commit -m "docs(api): 更新用户API文档"

# 代码重构
git commit -m "refactor(database): 优化数据库连接池配置"

# 测试
git commit -m "test(auth): 添加登录功能单元测试"

# 构建相关
git commit -m "chore(deps): 升级mongoose到最新版本"
```

### 详细示例

```bash
git commit -m "feat(battle): 实现实时战斗系统

- 添加WebSocket连接管理
- 实现战斗房间匹配逻辑
- 添加技能释放和伤害计算
- 支持多人战斗模式

Closes #123"
```

### 破坏性变更示例

```bash
git commit -m "feat(api): 重构用户API接口

BREAKING CHANGE: 用户API响应格式已更改
- 移除了deprecated字段 'oldField'
- 新增必需字段 'newField'
- 更新了错误响应格式

Migration guide: docs/migration-v2.md"
```

## 提交规范检查

### 安装commitizen

```bash
npm install -g commitizen
npm install -g cz-conventional-changelog
```

### 配置package.json

```json
{
  "config": {
    "commitizen": {
      "path": "cz-conventional-changelog"
    }
  }
}
```

### 使用方式

```bash
# 使用commitizen提交
git cz

# 或者使用npm script
npm run commit
```

## 提交最佳实践

### 1. 提交频率

- 小步快跑，频繁提交
- 每个提交只包含一个逻辑变更
- 避免大型提交

### 2. 提交内容

- 确保每次提交都是可工作的状态
- 提交前运行测试
- 不要提交调试代码

### 3. 提交消息

- 使用现在时态："添加功能" 而不是 "添加了功能"
- 首字母小写
- 不要在结尾添加句号
- 限制第一行在50个字符以内
- 如需详细说明，在第二行空行后添加

### 4. 代码审查

- 提交前自我审查代码
- 确保代码符合项目规范
- 添加必要的注释和文档

## 常见错误示例

### ❌ 错误示例

```bash
# 类型错误
git commit -m "add new feature"

# 描述不清晰
git commit -m "fix bug"

# 包含多个变更
git commit -m "feat: 添加用户功能和修复登录bug"

# 格式错误
git commit -m "Feat: Add user authentication"
```

### ✅ 正确示例

```bash
# 清晰的类型和描述
git commit -m "feat(auth): 添加用户认证功能"

# 具体的bug修复
git commit -m "fix(login): 修复密码验证逻辑错误"

# 单一职责
git commit -m "feat(user): 添加用户注册功能"
git commit -m "fix(auth): 修复登录验证问题"

# 正确的格式
git commit -m "feat(auth): 添加JWT令牌验证"
```
