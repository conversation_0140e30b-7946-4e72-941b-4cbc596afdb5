# 🧪 数据库权限测试指南

## 📋 测试方法概览

我已经为您的云函数添加了完整的数据库权限测试功能。您可以通过以下几种方式进行测试：

## 🔧 方法一：微信开发者工具测试（推荐）

### 步骤1：打开微信开发者工具
1. 启动微信开发者工具
2. 进入您的小程序项目
3. 点击"云开发"按钮

### 步骤2：进入云函数控制台
1. 在云开发控制台中，点击"云函数"
2. 找到 `idlegame-api` 函数
3. 点击函数名称进入详情页

### 步骤3：执行测试
1. 点击"测试"按钮
2. 在测试参数中输入以下JSON：

```json
{
  "path": "/api/test/database",
  "httpMethod": "GET",
  "headers": {},
  "body": {}
}
```

3. 点击"执行测试"
4. 查看返回结果和日志

### 预期测试结果
```json
{
  "success": true,
  "message": "数据库权限测试完成",
  "testResults": [
    {
      "test": "创建用户数据",
      "status": "success",
      "message": "创建成功，ID: xxx"
    },
    {
      "test": "读取自己的数据", 
      "status": "success",
      "message": "找到 1 条记录"
    },
    {
      "test": "读取他人数据",
      "status": "success", 
      "message": "正确阻止了访问他人数据（返回空结果）"
    },
    {
      "test": "读取公共数据",
      "status": "success",
      "message": "成功读取 1 个技能"
    },
    {
      "test": "游戏数据同步",
      "status": "success",
      "message": "同步成功，ID: xxx"
    }
  ],
  "userInfo": {
    "openid": "用户的openid",
    "timestamp": "2025-07-25T15:30:00.000Z"
  }
}
```

## 🔧 方法二：小程序端测试

### 创建测试页面
在您的小程序项目中创建测试页面：

```javascript
// pages/test/test.js
Page({
  data: {
    testResults: []
  },

  async onLoad() {
    await this.runDatabaseTest();
  },

  async runDatabaseTest() {
    try {
      console.log('🧪 开始数据库权限测试...');
      
      const result = await wx.cloud.callFunction({
        name: 'idlegame-api',
        data: {
          path: '/api/test/database',
          httpMethod: 'GET',
          headers: {},
          body: {}
        }
      });

      console.log('测试结果:', result);
      
      this.setData({
        testResults: result.result.testResults || []
      });

      if (result.result.success) {
        wx.showToast({
          title: '测试完成',
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: '测试失败',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('测试失败:', error);
      wx.showToast({
        title: '测试出错',
        icon: 'none'
      });
    }
  }
});
```

### 测试页面WXML
```xml
<!-- pages/test/test.wxml -->
<view class="container">
  <view class="title">数据库权限测试</view>
  
  <view class="test-results">
    <view wx:for="{{testResults}}" wx:key="test" class="test-item">
      <view class="test-name">{{item.test}}</view>
      <view class="test-status status-{{item.status}}">{{item.status}}</view>
      <view class="test-message">{{item.message}}</view>
    </view>
  </view>
  
  <button bindtap="runDatabaseTest" class="test-btn">重新测试</button>
</view>
```

## 🔧 方法三：命令行测试

### 使用tcb命令测试
```bash
# 调用云函数进行测试
tcb fn invoke idlegame-api --envId cloudbase-7gzvsi422b6fddd6 --params '{
  "path": "/api/test/database",
  "httpMethod": "GET",
  "headers": {},
  "body": {}
}'
```

## 📊 测试项目说明

### 1. 创建用户数据
- **目的**: 测试是否能成功创建用户记录
- **预期**: 成功创建，返回记录ID
- **失败原因**: 权限配置错误或集合不存在

### 2. 读取自己的数据
- **目的**: 测试用户是否能读取自己的数据
- **预期**: 成功读取到自己创建的记录
- **失败原因**: 权限配置过于严格

### 3. 读取他人数据
- **目的**: 测试权限隔离是否有效
- **预期**: 无法读取或返回空结果
- **失败原因**: 权限配置过于宽松，存在安全风险

### 4. 读取公共数据
- **目的**: 测试公共数据的访问权限
- **预期**: 能够正常读取公共配置数据
- **失败原因**: 公共数据权限配置错误

### 5. 游戏数据同步
- **目的**: 测试游戏数据的存储功能
- **预期**: 成功保存游戏进度数据
- **失败原因**: 游戏数据集合权限配置错误

## 🚨 常见问题排查

### 问题1: 权限被拒绝
```
错误信息: "Permission denied"
解决方案: 检查数据库集合的权限配置
```

### 问题2: 集合不存在
```
错误信息: "Collection does not exist"
解决方案: 在云开发控制台创建对应的数据库集合
```

### 问题3: 无法获取用户身份
```
错误信息: "OPENID is undefined"
解决方案: 确保在小程序环境中调用，或配置云函数权限
```

## 📈 测试结果分析

### ✅ 理想的测试结果
- 所有测试项目状态为 "success"
- 用户数据隔离正常
- 公共数据可正常访问
- 游戏数据同步正常

### ⚠️ 需要关注的结果
- 任何 "failed" 状态的测试项
- "读取他人数据" 返回了实际数据
- 权限过于宽松的警告

### 🔧 优化建议
1. 根据测试结果调整权限配置
2. 为敏感数据添加额外的安全规则
3. 定期运行测试确保权限正常

## 🎯 下一步操作

1. **运行测试**: 按照上述方法执行测试
2. **分析结果**: 查看测试结果，识别问题
3. **调整权限**: 根据测试结果优化数据库权限
4. **重复测试**: 确保所有权限配置正确

---

**💡 提示**: 建议在每次修改数据库权限配置后都运行一次测试，确保权限设置符合预期。
