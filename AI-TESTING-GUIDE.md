# 🤖 AI测试框架使用指南

## 概述
AI测试框架是为武侠放置游戏Cocos Creator项目开发的智能测试系统，提供全面的项目健康检查和代码质量分析。

## 快速开始

### 1. 运行完整测试
```bash
npm run test:ai
```

### 2. 运行简化测试
```bash
npm run test:ai:simple
```

### 3. 修复代码质量问题
```bash
npm run fix:quality
```

### 4. 启动测试服务器
```bash
npm run server:test
```

## 测试项目

### 基础检查
- ✅ 项目结构检查
- ✅ Cocos Creator配置验证
- ✅ 脚本文件分析
- ✅ 资源文件检查

### 高级检查
- ✅ 组件依赖分析
- ✅ 场景完整性验证
- ✅ 资源引用检查
- ✅ 武侠系统测试

### 质量检查
- ✅ 代码质量分析
- ✅ 性能基准测试
- ✅ 后端服务器状态

## CI/CD集成

项目已配置GitHub Actions自动化测试：
- 每次推送到main/develop分支时运行
- 每个Pull Request都会运行测试
- 每天凌晨2点定时运行
- 测试报告自动发布到GitHub Pages

## VSCode集成

使用Ctrl+Shift+P打开命令面板，搜索"Tasks: Run Task"：
- AI测试框架 - 完整测试
- AI测试框架 - 快速测试
- 代码质量修复
- 启动测试服务器

## Git Hooks

Pre-commit hook已配置，每次提交前自动运行AI测试。
如果测试失败，提交将被阻止。

## 报告文件

- `comprehensive-test-report.json` - 详细测试报告
- `code-quality-fix-report.json` - 代码质量修复报告

## 故障排除

### 测试服务器连接失败
```bash
npm run server:test
# 等待几秒钟后重新运行测试
npm run test:ai
```

### 代码质量问题过多
```bash
npm run fix:quality
npm run test:ai
```

### PowerShell执行策略问题
以管理员身份运行PowerShell：
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

## 贡献指南

1. 运行测试确保项目健康
2. 修复代码质量问题
3. 提交前确保所有测试通过
4. 定期运行完整测试套件

## 支持

如有问题，请查看测试报告中的详细信息和建议。
