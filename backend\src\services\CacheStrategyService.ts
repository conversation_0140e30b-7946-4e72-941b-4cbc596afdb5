import { CacheManager } from '../utils/cache';
import { Logger } from '../utils/logger';
import { IUser } from '../models/User';
import { ICharacter } from '../models/Character';
import { ISkill, IUserSkill } from '../models/Skill';
import { IItem, IUserItem } from '../models/Item';

/**
 * 缓存策略配置
 */
interface CacheStrategy {
  ttl: number;           // 生存时间(秒)
  prefix: string;        // 缓存键前缀
  serialize: boolean;    // 是否序列化
}

/**
 * 缓存策略服务
 * 统一管理各种数据的缓存策略
 */
export class CacheStrategyService {
  private static instance: CacheStrategyService;
  private cacheManager: CacheManager;

  // 缓存策略配置
  private strategies: Record<string, CacheStrategy> = {
    // 用户数据缓存 - 1小时
    user: {
      ttl: 3600,
      prefix: 'user',
      serialize: true,
    },
    
    // 角色数据缓存 - 30分钟
    character: {
      ttl: 1800,
      prefix: 'char',
      serialize: true,
    },
    
    // 技能配置缓存 - 2小时 (配置数据变化较少)
    skillConfig: {
      ttl: 7200,
      prefix: 'skill_cfg',
      serialize: true,
    },
    
    // 用户技能缓存 - 30分钟
    userSkill: {
      ttl: 1800,
      prefix: 'user_skill',
      serialize: true,
    },
    
    // 物品配置缓存 - 2小时
    itemConfig: {
      ttl: 7200,
      prefix: 'item_cfg',
      serialize: true,
    },
    
    // 用户物品缓存 - 15分钟
    userItem: {
      ttl: 900,
      prefix: 'user_item',
      serialize: true,
    },
    
    // 技能冷却缓存 - 5分钟
    skillCooldown: {
      ttl: 300,
      prefix: 'skill_cd',
      serialize: true,
    },
    
    // 战斗状态缓存 - 10分钟
    battleState: {
      ttl: 600,
      prefix: 'battle',
      serialize: true,
    },
    
    // 用户会话缓存 - 24小时
    userSession: {
      ttl: 86400,
      prefix: 'session',
      serialize: true,
    },
  };

  private constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  public static getInstance(): CacheStrategyService {
    if (!CacheStrategyService.instance) {
      CacheStrategyService.instance = new CacheStrategyService();
    }
    return CacheStrategyService.instance;
  }

  /**
   * 缓存用户数据
   */
  public async cacheUser(userId: string, userData: IUser): Promise<void> {
    const strategy = this.strategies.user;
    const key = `${userId}`;
    
    await this.cacheManager.set(key, userData, {
      ttl: strategy.ttl,
      prefix: strategy.prefix,
      serialize: strategy.serialize,
    });
    
    Logger.debug('用户数据已缓存', { userId, ttl: strategy.ttl });
  }

  /**
   * 获取缓存的用户数据
   */
  public async getCachedUser(userId: string): Promise<IUser | null> {
    const strategy = this.strategies.user;
    const key = `${userId}`;
    
    return await this.cacheManager.get<IUser>(key, {
      prefix: strategy.prefix,
      serialize: strategy.serialize,
    });
  }

  /**
   * 缓存角色数据
   */
  public async cacheCharacter(characterId: string, characterData: ICharacter): Promise<void> {
    const strategy = this.strategies.character;
    const key = `${characterId}`;
    
    await this.cacheManager.set(key, characterData, {
      ttl: strategy.ttl,
      prefix: strategy.prefix,
      serialize: strategy.serialize,
    });
    
    Logger.debug('角色数据已缓存', { characterId, ttl: strategy.ttl });
  }

  /**
   * 获取缓存的角色数据
   */
  public async getCachedCharacter(characterId: string): Promise<ICharacter | null> {
    const strategy = this.strategies.character;
    const key = `${characterId}`;
    
    return await this.cacheManager.get<ICharacter>(key, {
      prefix: strategy.prefix,
      serialize: strategy.serialize,
    });
  }

  /**
   * 批量缓存用户角色
   */
  public async cacheUserCharacters(userId: string, characters: ICharacter[]): Promise<void> {
    const strategy = this.strategies.character;
    
    const cacheItems = characters.map(char => ({
      key: char._id.toString(),
      value: char,
      ttl: strategy.ttl,
      prefix: strategy.prefix,
    }));
    
    // 同时缓存用户角色列表
    cacheItems.push({
      key: `list_${userId}`,
      value: characters.map(c => c._id.toString()),
      ttl: strategy.ttl,
      prefix: strategy.prefix,
    });
    
    await this.cacheManager.mset(cacheItems);
    
    Logger.debug('用户角色批量缓存完成', { 
      userId, 
      characterCount: characters.length 
    });
  }

  /**
   * 缓存技能配置
   */
  public async cacheSkillConfig(skillId: string, skillData: ISkill): Promise<void> {
    const strategy = this.strategies.skillConfig;
    const key = `${skillId}`;
    
    await this.cacheManager.set(key, skillData, {
      ttl: strategy.ttl,
      prefix: strategy.prefix,
      serialize: strategy.serialize,
    });
    
    Logger.debug('技能配置已缓存', { skillId, ttl: strategy.ttl });
  }

  /**
   * 批量缓存技能配置
   */
  public async cacheSkillConfigs(skills: ISkill[]): Promise<void> {
    const strategy = this.strategies.skillConfig;
    
    const cacheItems = skills.map(skill => ({
      key: skill.id,
      value: skill,
      ttl: strategy.ttl,
      prefix: strategy.prefix,
    }));
    
    await this.cacheManager.mset(cacheItems);
    
    Logger.debug('技能配置批量缓存完成', { skillCount: skills.length });
  }

  /**
   * 获取缓存的技能配置
   */
  public async getCachedSkillConfig(skillId: string): Promise<ISkill | null> {
    const strategy = this.strategies.skillConfig;
    const key = `${skillId}`;
    
    return await this.cacheManager.get<ISkill>(key, {
      prefix: strategy.prefix,
      serialize: strategy.serialize,
    });
  }

  /**
   * 缓存用户技能数据
   */
  public async cacheUserSkills(userId: string, userSkills: IUserSkill[]): Promise<void> {
    const strategy = this.strategies.userSkill;
    
    const cacheItems = userSkills.map(userSkill => ({
      key: `${userId}_${userSkill.skillId}`,
      value: userSkill,
      ttl: strategy.ttl,
      prefix: strategy.prefix,
    }));
    
    // 缓存用户技能列表
    cacheItems.push({
      key: `list_${userId}`,
      value: userSkills,
      ttl: strategy.ttl,
      prefix: strategy.prefix,
    });
    
    await this.cacheManager.mset(cacheItems);
    
    Logger.debug('用户技能批量缓存完成', { 
      userId, 
      skillCount: userSkills.length 
    });
  }

  /**
   * 获取缓存的用户技能列表
   */
  public async getCachedUserSkills(userId: string): Promise<IUserSkill[] | null> {
    const strategy = this.strategies.userSkill;
    const key = `list_${userId}`;
    
    return await this.cacheManager.get<IUserSkill[]>(key, {
      prefix: strategy.prefix,
      serialize: strategy.serialize,
    });
  }

  /**
   * 缓存技能冷却状态
   */
  public async cacheSkillCooldown(userId: string, skillId: string, cooldownData: any): Promise<void> {
    const strategy = this.strategies.skillCooldown;
    const key = `${userId}_${skillId}`;
    
    await this.cacheManager.set(key, cooldownData, {
      ttl: strategy.ttl,
      prefix: strategy.prefix,
      serialize: strategy.serialize,
    });
    
    Logger.debug('技能冷却已缓存', { userId, skillId, ttl: strategy.ttl });
  }

  /**
   * 获取缓存的技能冷却状态
   */
  public async getCachedSkillCooldown(userId: string, skillId: string): Promise<any | null> {
    const strategy = this.strategies.skillCooldown;
    const key = `${userId}_${skillId}`;
    
    return await this.cacheManager.get(key, {
      prefix: strategy.prefix,
      serialize: strategy.serialize,
    });
  }

  /**
   * 缓存物品配置
   */
  public async cacheItemConfig(itemId: string, itemData: IItem): Promise<void> {
    const strategy = this.strategies.itemConfig;
    const key = `${itemId}`;
    
    await this.cacheManager.set(key, itemData, {
      ttl: strategy.ttl,
      prefix: strategy.prefix,
      serialize: strategy.serialize,
    });
    
    Logger.debug('物品配置已缓存', { itemId, ttl: strategy.ttl });
  }

  /**
   * 批量缓存物品配置
   */
  public async cacheItemConfigs(items: IItem[]): Promise<void> {
    const strategy = this.strategies.itemConfig;
    
    const cacheItems = items.map(item => ({
      key: item.id,
      value: item,
      ttl: strategy.ttl,
      prefix: strategy.prefix,
    }));
    
    await this.cacheManager.mset(cacheItems);
    
    Logger.debug('物品配置批量缓存完成', { itemCount: items.length });
  }

  /**
   * 清除用户相关的所有缓存
   */
  public async clearUserCache(userId: string): Promise<void> {
    const patterns = [
      `${this.strategies.user.prefix}:${userId}`,
      `${this.strategies.character.prefix}:list_${userId}`,
      `${this.strategies.userSkill.prefix}:list_${userId}`,
      `${this.strategies.userItem.prefix}:list_${userId}`,
      `${this.strategies.skillCooldown.prefix}:${userId}_*`,
      `${this.strategies.userSession.prefix}:${userId}`,
    ];
    
    for (const pattern of patterns) {
      await this.cacheManager.del(pattern);
    }
    
    Logger.debug('用户缓存已清除', { userId });
  }

  /**
   * 预热缓存 - 预加载常用数据
   */
  public async warmupCache(): Promise<void> {
    try {
      Logger.info('开始缓存预热...');
      
      // 这里可以预加载一些常用的配置数据
      // 比如技能配置、物品配置等
      
      Logger.info('缓存预热完成');
    } catch (error) {
      Logger.error('缓存预热失败', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  public async getCacheStats(): Promise<any> {
    return await this.cacheManager.getStats();
  }
}

export const cacheStrategyService = CacheStrategyService.getInstance();
