import { _decorator, Component } from 'cc';
import { 
    HttpMethod, 
    IHttpRequestConfig, 
    IHttpResponse, 
    INetworkError, 
    IRequestInterceptor, 
    IResponseInterceptor,
    IHttpClientOptions,
    NetworkEventType
} from './types/NetworkTypes';
import { EventManager } from '../managers/EventManager';

const { ccclass } = _decorator;

/**
 * HTTP客户端类
 * 提供HTTP请求功能，支持拦截器、重试、超时等特性
 */
@ccclass('HttpClient')
export class HttpClient {
    private _baseURL: string = '';
    private _timeout: number = 10000;
    private _defaultHeaders: Record<string, string> = {
        'Content-Type': 'application/json'
    };
    private _retries: number = 3;
    private _retryDelay: number = 1000;
    private _requestInterceptor?: IRequestInterceptor;
    private _responseInterceptor?: IResponseInterceptor;

    constructor(options?: IHttpClientOptions) {
        if (options) {
            this.configure(options);
        }
    }

    /**
     * 配置HTTP客户端
     */
    public configure(options: IHttpClientOptions): void {
        if (options.baseURL) this._baseURL = options.baseURL;
        if (options.timeout) this._timeout = options.timeout;
        if (options.headers) this._defaultHeaders = { ...this._defaultHeaders, ...options.headers };
        if (options.retries !== undefined) this._retries = options.retries;
        if (options.retryDelay) this._retryDelay = options.retryDelay;
        if (options.requestInterceptor) this._requestInterceptor = options.requestInterceptor;
        if (options.responseInterceptor) this._responseInterceptor = options.responseInterceptor;
    }

    /**
     * GET请求
     */
    public async get<T = any>(url: string, params?: any): Promise<T> {
        const config: IHttpRequestConfig = {
            url: this._buildURL(url),
            method: HttpMethod.GET,
            params,
            headers: this._defaultHeaders,
            timeout: this._timeout,
            retries: this._retries,
            retryDelay: this._retryDelay
        };
        
        const response = await this._request<T>(config);
        return response.data;
    }

    /**
     * POST请求
     */
    public async post<T = any>(url: string, data?: any): Promise<T> {
        const config: IHttpRequestConfig = {
            url: this._buildURL(url),
            method: HttpMethod.POST,
            data,
            headers: this._defaultHeaders,
            timeout: this._timeout,
            retries: this._retries,
            retryDelay: this._retryDelay
        };
        
        const response = await this._request<T>(config);
        return response.data;
    }

    /**
     * PUT请求
     */
    public async put<T = any>(url: string, data?: any): Promise<T> {
        const config: IHttpRequestConfig = {
            url: this._buildURL(url),
            method: HttpMethod.PUT,
            data,
            headers: this._defaultHeaders,
            timeout: this._timeout,
            retries: this._retries,
            retryDelay: this._retryDelay
        };
        
        const response = await this._request<T>(config);
        return response.data;
    }

    /**
     * DELETE请求
     */
    public async delete<T = any>(url: string): Promise<T> {
        const config: IHttpRequestConfig = {
            url: this._buildURL(url),
            method: HttpMethod.DELETE,
            headers: this._defaultHeaders,
            timeout: this._timeout,
            retries: this._retries,
            retryDelay: this._retryDelay
        };
        
        const response = await this._request<T>(config);
        return response.data;
    }

    /**
     * 设置请求拦截器
     */
    public setRequestInterceptor(interceptor: IRequestInterceptor): void {
        this._requestInterceptor = interceptor;
    }

    /**
     * 设置响应拦截器
     */
    public setResponseInterceptor(interceptor: IResponseInterceptor): void {
        this._responseInterceptor = interceptor;
    }

    /**
     * 执行HTTP请求
     */
    private async _request<T>(config: IHttpRequestConfig): Promise<IHttpResponse<T>> {
        try {
            // 应用请求拦截器
            if (this._requestInterceptor?.onRequest) {
                config = await this._requestInterceptor.onRequest(config);
            }

            // 发送网络事件
            EventManager.getInstance().emit(NetworkEventType.REQUEST_START, { config });

            // 执行请求
            const response = await this._executeRequest<T>(config);

            // 应用响应拦截器
            if (this._responseInterceptor?.onResponse) {
                return await this._responseInterceptor.onResponse(response);
            }

            // 发送成功事件
            EventManager.getInstance().emit(NetworkEventType.REQUEST_SUCCESS, { response });

            return response;
        } catch (error) {
            const networkError = this._createNetworkError(error, config);
            
            // 应用错误拦截器
            if (this._responseInterceptor?.onResponseError) {
                await this._responseInterceptor.onResponseError(networkError);
            }

            // 发送错误事件
            EventManager.getInstance().emit(NetworkEventType.REQUEST_ERROR, { error: networkError });

            throw networkError;
        }
    }

    /**
     * 执行实际的HTTP请求
     */
    private async _executeRequest<T>(config: IHttpRequestConfig): Promise<IHttpResponse<T>> {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            // 设置超时
            xhr.timeout = config.timeout || this._timeout;
            
            // 构建URL
            let url = config.url;
            if (config.params && config.method === HttpMethod.GET) {
                const params = new URLSearchParams(config.params);
                url += (url.includes('?') ? '&' : '?') + params.toString();
            }

            xhr.open(config.method, url, true);

            // 设置请求头
            if (config.headers) {
                Object.entries(config.headers).forEach(([key, value]) => {
                    xhr.setRequestHeader(key, value);
                });
            }

            // 设置响应处理
            xhr.onload = () => {
                const response: IHttpResponse<T> = {
                    data: this._parseResponse(xhr.responseText),
                    status: xhr.status,
                    statusText: xhr.statusText,
                    headers: this._parseHeaders(xhr.getAllResponseHeaders()),
                    config
                };

                if (xhr.status >= 200 && xhr.status < 300) {
                    resolve(response);
                } else {
                    reject(this._createNetworkError(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`), config, response));
                }
            };

            xhr.onerror = () => {
                reject(this._createNetworkError(new Error('Network Error'), config));
            };

            xhr.ontimeout = () => {
                reject(this._createNetworkError(new Error('Request Timeout'), config));
            };

            // 发送请求
            const requestData = config.data ? JSON.stringify(config.data) : null;
            xhr.send(requestData);
        });
    }

    /**
     * 构建完整URL
     */
    private _buildURL(url: string): string {
        if (url.startsWith('http://') || url.startsWith('https://')) {
            return url;
        }
        return this._baseURL + (url.startsWith('/') ? url : '/' + url);
    }

    /**
     * 解析响应数据
     */
    private _parseResponse(responseText: string): any {
        try {
            return JSON.parse(responseText);
        } catch {
            return responseText;
        }
    }

    /**
     * 解析响应头
     */
    private _parseHeaders(headerString: string): Record<string, string> {
        const headers: Record<string, string> = {};
        if (headerString) {
            headerString.split('\r\n').forEach(line => {
                const [key, value] = line.split(': ');
                if (key && value) {
                    headers[key.toLowerCase()] = value;
                }
            });
        }
        return headers;
    }

    /**
     * 创建网络错误对象
     */
    private _createNetworkError(error: any, config: IHttpRequestConfig, response?: IHttpResponse): INetworkError {
        return {
            code: error.code || 'NETWORK_ERROR',
            message: error.message || 'Unknown network error',
            status: response?.status,
            config,
            response,
            timestamp: Date.now()
        };
    }
}
