{"extends": "./temp/tsconfig.cocos.json", "compilerOptions": {"strict": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "target": "ES2020", "module": "ES2020", "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["./assets/scripts/*"], "@core/*": ["./assets/scripts/core/*"], "@systems/*": ["./assets/scripts/systems/*"], "@ui/*": ["./assets/scripts/ui/*"], "@data/*": ["./assets/scripts/data/*"], "@scenes/*": ["./assets/scripts/scenes/*"]}}, "include": ["assets/scripts/**/*.ts"], "exclude": ["node_modules", "temp", "library", "build"]}