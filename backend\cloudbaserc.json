{"envId": "cloudbase-7gzvsi422b6fddd6", "functions": [{"name": "idlegame-api-v18", "timeout": 30, "envVariables": {"NODE_ENV": "production", "WECHAT_CLOUD": "true", "FREE_TIER": "true", "TCB_ENV": "cloudbase-7gzvsi422b6fddd6"}, "runtime": "Nodejs18.15", "memorySize": 512, "handler": "index.main"}], "databases": [{"collections": [{"collectionName": "users", "description": "用户信息表"}, {"collectionName": "game_sessions", "description": "游戏会话数据表"}, {"collectionName": "skills", "description": "技能数据表"}, {"collectionName": "items", "description": "物品数据表"}, {"collectionName": "characters", "description": "角色数据表"}]}]}