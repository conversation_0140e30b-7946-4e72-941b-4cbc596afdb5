import { LogAnalyzer, LogQueryCondition, LogStatistics } from './logAnalyzer';
import { Logger } from './logger';

/**
 * 图表数据接口
 */
export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string;
    borderWidth?: number;
  }>;
}

/**
 * 仪表板数据接口
 */
export interface DashboardData {
  summary: {
    totalLogs: number;
    errorRate: number;
    averageResponseTime: number;
    activeUsers: number;
  };
  charts: {
    logsByLevel: ChartData;
    logsByHour: ChartData;
    responseTimeDistribution: ChartData;
    errorTrends: ChartData;
    topOperations: ChartData;
  };
  alerts: Array<{
    level: 'info' | 'warning' | 'error';
    message: string;
    timestamp: Date;
  }>;
  recentErrors: Array<{
    timestamp: string;
    message: string;
    count: number;
  }>;
}

/**
 * 日志可视化工具类
 */
export class LogVisualizer {
  private static instance: LogVisualizer;
  private logAnalyzer: LogAnalyzer;

  private constructor() {
    this.logAnalyzer = LogAnalyzer.getInstance();
  }

  public static getInstance(): LogVisualizer {
    if (!LogVisualizer.instance) {
      LogVisualizer.instance = new LogVisualizer();
    }
    return LogVisualizer.instance;
  }

  /**
   * 生成仪表板数据
   */
  public async generateDashboard(timeRange: { start: Date; end: Date }): Promise<DashboardData> {
    const condition: LogQueryCondition = {
      startTime: timeRange.start,
      endTime: timeRange.end,
      limit: 10000,
    };

    const stats = await this.logAnalyzer.generateStatistics(condition);
    const logs = await this.logAnalyzer.queryLogs(condition);

    // 计算活跃用户数
    const activeUsers = new Set(
      logs
        .filter(log => log.metadata?.userId)
        .map(log => log.metadata.userId)
    ).size;

    // 生成图表数据
    const charts = {
      logsByLevel: this.generateLogsByLevelChart(stats),
      logsByHour: this.generateLogsByHourChart(stats),
      responseTimeDistribution: await this.generateResponseTimeChart(condition),
      errorTrends: await this.generateErrorTrendsChart(condition),
      topOperations: this.generateTopOperationsChart(stats),
    };

    // 生成告警
    const alerts = this.generateAlerts(stats);

    // 获取最近错误
    const recentErrors = stats.topErrors.slice(0, 5).map(error => ({
      timestamp: new Date().toISOString(),
      message: error.message,
      count: error.count,
    }));

    return {
      summary: {
        totalLogs: stats.totalLogs,
        errorRate: stats.errorRate,
        averageResponseTime: stats.averageResponseTime,
        activeUsers,
      },
      charts,
      alerts,
      recentErrors,
    };
  }

  /**
   * 生成按级别分组的日志图表
   */
  private generateLogsByLevelChart(stats: LogStatistics): ChartData {
    const levels = ['error', 'warn', 'info', 'debug'];
    const data = levels.map(level => stats.byLevel[level] || 0);
    const colors = ['#ff6b6b', '#ffa726', '#42a5f5', '#66bb6a'];

    return {
      labels: levels.map(level => level.toUpperCase()),
      datasets: [{
        label: '日志数量',
        data,
        backgroundColor: colors,
        borderWidth: 1,
      }],
    };
  }

  /**
   * 生成按小时分组的日志图表
   */
  private generateLogsByHourChart(stats: LogStatistics): ChartData {
    const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
    const data = hours.map(hour => stats.byHour[parseInt(hour)] || 0);

    return {
      labels: hours.map(hour => `${hour}:00`),
      datasets: [{
        label: '日志数量',
        data,
        borderColor: '#42a5f5',
        backgroundColor: 'rgba(66, 165, 245, 0.1)',
        borderWidth: 2,
      }],
    };
  }

  /**
   * 生成响应时间分布图表
   */
  private async generateResponseTimeChart(condition: LogQueryCondition): Promise<ChartData> {
    const logs = await this.logAnalyzer.queryLogs({
      ...condition,
      limit: 5000,
    });

    const responseTimes = logs
      .filter(log => log.metadata?.duration)
      .map(log => log.metadata.duration);

    // 分组响应时间
    const ranges = [
      { label: '0-100ms', min: 0, max: 100 },
      { label: '100-500ms', min: 100, max: 500 },
      { label: '500ms-1s', min: 500, max: 1000 },
      { label: '1s-5s', min: 1000, max: 5000 },
      { label: '5s+', min: 5000, max: Infinity },
    ];

    const data = ranges.map(range => 
      responseTimes.filter(time => time >= range.min && time < range.max).length
    );

    return {
      labels: ranges.map(range => range.label),
      datasets: [{
        label: '请求数量',
        data,
        backgroundColor: ['#4caf50', '#8bc34a', '#ffc107', '#ff9800', '#f44336'],
        borderWidth: 1,
      }],
    };
  }

  /**
   * 生成错误趋势图表
   */
  private async generateErrorTrendsChart(condition: LogQueryCondition): Promise<ChartData> {
    const timeRange = condition.endTime!.getTime() - condition.startTime!.getTime();
    const intervals = 24; // 24个时间点
    const intervalSize = timeRange / intervals;

    const labels: string[] = [];
    const errorCounts: number[] = [];

    for (let i = 0; i < intervals; i++) {
      const start = new Date(condition.startTime!.getTime() + i * intervalSize);
      const end = new Date(condition.startTime!.getTime() + (i + 1) * intervalSize);

      const intervalLogs = await this.logAnalyzer.queryLogs({
        level: 'error',
        startTime: start,
        endTime: end,
        limit: 1000,
      });

      labels.push(start.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' }));
      errorCounts.push(intervalLogs.length);
    }

    return {
      labels,
      datasets: [{
        label: '错误数量',
        data: errorCounts,
        borderColor: '#f44336',
        backgroundColor: 'rgba(244, 67, 54, 0.1)',
        borderWidth: 2,
      }],
    };
  }

  /**
   * 生成热门操作图表
   */
  private generateTopOperationsChart(stats: LogStatistics): ChartData {
    const topOperations = stats.topOperations.slice(0, 10);

    return {
      labels: topOperations.map(op => op.operation),
      datasets: [{
        label: '操作次数',
        data: topOperations.map(op => op.count),
        backgroundColor: '#42a5f5',
        borderWidth: 1,
      }],
    };
  }

  /**
   * 生成告警信息
   */
  private generateAlerts(stats: LogStatistics): Array<{
    level: 'info' | 'warning' | 'error';
    message: string;
    timestamp: Date;
  }> {
    const alerts: Array<{
      level: 'info' | 'warning' | 'error';
      message: string;
      timestamp: Date;
    }> = [];

    // 错误率告警
    if (stats.errorRate > 10) {
      alerts.push({
        level: 'error',
        message: `错误率过高: ${stats.errorRate.toFixed(2)}%`,
        timestamp: new Date(),
      });
    } else if (stats.errorRate > 5) {
      alerts.push({
        level: 'warning',
        message: `错误率偏高: ${stats.errorRate.toFixed(2)}%`,
        timestamp: new Date(),
      });
    }

    // 响应时间告警
    if (stats.averageResponseTime > 2000) {
      alerts.push({
        level: 'error',
        message: `平均响应时间过长: ${stats.averageResponseTime.toFixed(0)}ms`,
        timestamp: new Date(),
      });
    } else if (stats.averageResponseTime > 1000) {
      alerts.push({
        level: 'warning',
        message: `平均响应时间偏长: ${stats.averageResponseTime.toFixed(0)}ms`,
        timestamp: new Date(),
      });
    }

    // 日志量告警
    if (stats.totalLogs > 10000) {
      alerts.push({
        level: 'warning',
        message: `日志量较大: ${stats.totalLogs} 条`,
        timestamp: new Date(),
      });
    }

    return alerts;
  }

  /**
   * 生成实时监控数据
   */
  public async generateRealTimeData(): Promise<any> {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    const condition: LogQueryCondition = {
      startTime: oneHourAgo,
      endTime: now,
      limit: 1000,
    };

    const [logs, stats] = await Promise.all([
      this.logAnalyzer.queryLogs(condition),
      this.logAnalyzer.generateStatistics(condition),
    ]);

    // 最近5分钟的日志
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
    const recentLogs = logs.filter(log => new Date(log.timestamp) > fiveMinutesAgo);

    return {
      timestamp: now.toISOString(),
      metrics: {
        logsPerMinute: recentLogs.length,
        errorRate: stats.errorRate,
        averageResponseTime: stats.averageResponseTime,
        activeRequests: recentLogs.filter(log => log.metadata?.type === 'api_request').length,
      },
      recentActivity: recentLogs.slice(0, 10).map(log => ({
        timestamp: log.timestamp,
        level: log.level,
        message: log.message,
        type: log.metadata?.type,
      })),
      systemHealth: {
        status: stats.errorRate < 5 ? 'healthy' : stats.errorRate < 10 ? 'warning' : 'critical',
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
      },
    };
  }

  /**
   * 生成性能报告
   */
  public async generatePerformanceReport(timeRange: { start: Date; end: Date }): Promise<any> {
    const condition: LogQueryCondition = {
      startTime: timeRange.start,
      endTime: timeRange.end,
      limit: 10000,
    };

    const logs = await this.logAnalyzer.queryLogs(condition);
    const apiLogs = logs.filter(log => log.metadata?.type === 'api_request');

    // 计算性能指标
    const responseTimes = apiLogs
      .filter(log => log.metadata?.duration)
      .map(log => log.metadata.duration);

    const performanceMetrics = {
      totalRequests: apiLogs.length,
      averageResponseTime: responseTimes.length > 0 ? 
        responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0,
      p95ResponseTime: this.calculatePercentile(responseTimes, 95),
      p99ResponseTime: this.calculatePercentile(responseTimes, 99),
      slowestRequests: apiLogs
        .filter(log => log.metadata?.duration > 1000)
        .sort((a, b) => b.metadata.duration - a.metadata.duration)
        .slice(0, 10)
        .map(log => ({
          url: log.metadata?.url,
          method: log.metadata?.method,
          duration: log.metadata?.duration,
          timestamp: log.timestamp,
        })),
    };

    return {
      period: {
        start: timeRange.start.toISOString(),
        end: timeRange.end.toISOString(),
      },
      performance: performanceMetrics,
      recommendations: this.generatePerformanceRecommendations(performanceMetrics),
    };
  }

  /**
   * 计算百分位数
   */
  private calculatePercentile(values: number[], percentile: number): number {
    if (values.length === 0) return 0;
    
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[Math.max(0, index)];
  }

  /**
   * 生成性能优化建议
   */
  private generatePerformanceRecommendations(metrics: any): string[] {
    const recommendations: string[] = [];

    if (metrics.averageResponseTime > 1000) {
      recommendations.push('平均响应时间过长，建议优化数据库查询和API逻辑');
    }

    if (metrics.p95ResponseTime > 2000) {
      recommendations.push('95%响应时间过长，建议检查慢查询和性能瓶颈');
    }

    if (metrics.slowestRequests.length > 0) {
      recommendations.push(`发现${metrics.slowestRequests.length}个慢请求，建议优化相关接口`);
    }

    return recommendations;
  }

  /**
   * 导出可视化数据
   */
  public async exportVisualizationData(timeRange: { start: Date; end: Date }, format: 'json' | 'csv' = 'json'): Promise<string> {
    const dashboardData = await this.generateDashboard(timeRange);
    
    if (format === 'json') {
      return JSON.stringify(dashboardData, null, 2);
    } else {
      // 简化的CSV导出
      const csvData = [
        ['Metric', 'Value'],
        ['Total Logs', dashboardData.summary.totalLogs.toString()],
        ['Error Rate', `${dashboardData.summary.errorRate.toFixed(2)}%`],
        ['Average Response Time', `${dashboardData.summary.averageResponseTime.toFixed(0)}ms`],
        ['Active Users', dashboardData.summary.activeUsers.toString()],
      ];

      return csvData.map(row => row.join(',')).join('\n');
    }
  }
}
