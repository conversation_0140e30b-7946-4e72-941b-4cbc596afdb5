import mongoose = require('mongoose');
import { DatabaseConfig } from '../../src/config/database';
import { DatabaseUtils } from '../../src/utils/database';
import { createBaseSchema } from '../../src/models/BaseSchema';
import { BaseModel, IBaseDocument } from '../../src/models/BaseModel';

// 测试模型定义
interface ITestDocument extends IBaseDocument {
  name: string;
  value: number;
  category: string;
  isActive: boolean;
  tags: string[];
  metadata: any;
}

const testSchema = createBaseSchema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
  },
  value: {
    type: Number,
    required: true,
    min: 0,
    max: 1000,
  },
  category: {
    type: String,
    enum: ['A', 'B', 'C'],
    default: 'A',
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  tags: [{
    type: String,
    trim: true,
  }],
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    default: {},
  },
});

const TestModel = mongoose.model<ITestDocument>('Test', testSchema);

class TestRepository extends BaseModel<ITestDocument> {
  constructor() {
    super(TestModel);
  }
}

describe('数据库连接和ORM测试', () => {
  let dbConfig: DatabaseConfig;
  let dbUtils: DatabaseUtils;
  let testRepo: TestRepository;

  beforeAll(async () => {
    // 设置测试环境
    process.env['NODE_ENV'] = 'test';
    process.env['MONGODB_URI'] = 'mongodb://localhost:27017/idlegame_test';

    dbConfig = DatabaseConfig.getInstance();
    dbUtils = DatabaseUtils.getInstance();
    testRepo = new TestRepository();

    try {
      await dbConfig.connect();
    } catch (error) {
      console.warn('数据库连接失败，跳过数据库测试');
    }
  });

  afterAll(async () => {
    try {
      // 清理测试数据
      await TestModel.deleteMany({});
      await dbConfig.disconnect();
    } catch (error) {
      console.warn('清理测试数据失败');
    }
  });

  beforeEach(async () => {
    // 每个测试前清理数据
    try {
      await TestModel.deleteMany({});
    } catch (error) {
      // 忽略清理错误
    }
  });

  describe('数据库连接测试', () => {
    it('应该成功连接到数据库', async () => {
      const isConnected = dbConfig.getConnectionStatus();
      expect(isConnected).toBe(true);
    });

    it('应该通过健康检查', async () => {
      const isHealthy = await dbConfig.healthCheck();
      expect(isHealthy).toBe(true);
    });

    it('应该获取数据库统计信息', async () => {
      const stats = dbConfig.getStats();
      expect(stats).toHaveProperty('connectionState');
      expect(stats).toHaveProperty('activeConnections');
    });

    it('应该获取数据库信息', async () => {
      const info = await dbConfig.getDatabaseInfo();
      expect(info).toHaveProperty('serverStatus');
      expect(info).toHaveProperty('dbStats');
      expect(info).toHaveProperty('connectionInfo');
    });
  });

  describe('基础模型CRUD操作测试', () => {
    it('应该创建文档', async () => {
      const testData = {
        name: '测试文档',
        value: 100,
        category: 'A' as const,
        tags: ['test', 'document'],
        metadata: { source: 'test' },
      };

      const created = await testRepo.create(testData);
      
      expect(created).toBeDefined();
      expect(created.name).toBe(testData.name);
      expect(created.value).toBe(testData.value);
      expect(created.category).toBe(testData.category);
      expect(created.tags).toEqual(testData.tags);
      expect(created.isDeleted).toBe(false);
      expect(created.version).toBe(0);
      expect(created.createdAt).toBeInstanceOf(Date);
      expect(created.updatedAt).toBeInstanceOf(Date);
    });

    it('应该批量创建文档', async () => {
      const testData = [
        { name: '文档1', value: 10, category: 'A' as const },
        { name: '文档2', value: 20, category: 'B' as const },
        { name: '文档3', value: 30, category: 'C' as const },
      ];

      const created = await testRepo.createMany(testData);
      
      expect(created).toHaveLength(3);
      expect(created[0]?.name).toBe('文档1');
      expect(created[1]?.name).toBe('文档2');
      expect(created[2]?.name).toBe('文档3');
    });

    it('应该根据ID查找文档', async () => {
      const testData = {
        name: '查找测试',
        value: 50,
        category: 'A' as const,
      };

      const created = await testRepo.create(testData);
      const found = await testRepo.findById(created._id);
      
      expect(found).toBeDefined();
      expect(found!.name).toBe(testData.name);
      expect(found!._id.toString()).toBe(created._id.toString());
    });

    it('应该查找单个文档', async () => {
      const testData = {
        name: '唯一文档',
        value: 75,
        category: 'B' as const,
      };

      await testRepo.create(testData);
      const found = await testRepo.findOne({ name: '唯一文档' });
      
      expect(found).toBeDefined();
      expect(found!.name).toBe(testData.name);
      expect(found!.value).toBe(testData.value);
    });

    it('应该查找多个文档', async () => {
      const testData = [
        { name: '文档A', value: 10, category: 'A' as const },
        { name: '文档B', value: 20, category: 'A' as const },
        { name: '文档C', value: 30, category: 'B' as const },
      ];

      await testRepo.createMany(testData);
      const found = await testRepo.find({ category: 'A' });
      
      expect(found).toHaveLength(2);
      expect(found[0]?.category).toBe('A');
      expect(found[1]?.category).toBe('A');
    });

    it('应该分页查询文档', async () => {
      const testData = Array.from({ length: 25 }, (_, i) => ({
        name: `文档${i + 1}`,
        value: i + 1,
        category: 'A' as const,
      }));

      await testRepo.createMany(testData);
      
      const page1 = await testRepo.paginate({}, { page: 1, limit: 10 });
      expect(page1.items).toHaveLength(10);
      expect(page1.pagination.page).toBe(1);
      expect(page1.pagination.total).toBe(25);
      expect(page1.pagination.pages).toBe(3);
      expect(page1.pagination.hasNext).toBe(true);
      expect(page1.pagination.hasPrev).toBe(false);

      const page2 = await testRepo.paginate({}, { page: 2, limit: 10 });
      expect(page2.items).toHaveLength(10);
      expect(page2.pagination.page).toBe(2);
      expect(page2.pagination.hasNext).toBe(true);
      expect(page2.pagination.hasPrev).toBe(true);

      const page3 = await testRepo.paginate({}, { page: 3, limit: 10 });
      expect(page3.items).toHaveLength(5);
      expect(page3.pagination.page).toBe(3);
      expect(page3.pagination.hasNext).toBe(false);
      expect(page3.pagination.hasPrev).toBe(true);
    });

    it('应该更新文档', async () => {
      const testData = {
        name: '原始名称',
        value: 100,
        category: 'A' as const,
      };

      const created = await testRepo.create(testData);
      const originalVersion = created.version;
      
      const updated = await testRepo.updateById(created._id, {
        name: '更新名称',
        value: 200,
      });
      
      expect(updated).toBeDefined();
      expect(updated!.name).toBe('更新名称');
      expect(updated!.value).toBe(200);
      expect(updated!.version).toBe(originalVersion + 1);
      expect(updated!.updatedAt.getTime()).toBeGreaterThan(created.updatedAt.getTime());
    });

    it('应该软删除文档', async () => {
      const testData = {
        name: '待删除文档',
        value: 100,
        category: 'A' as const,
      };

      const created = await testRepo.create(testData);
      const deleted = await testRepo.softDeleteById(created._id);
      
      expect(deleted).toBeDefined();
      expect(deleted!.isDeleted).toBe(true);
      expect(deleted!.deletedAt).toBeInstanceOf(Date);

      // 验证软删除的文档不会被普通查询找到
      const found = await testRepo.findById(created._id);
      expect(found).toBeNull();

      // 但可以通过包含已删除的选项找到
      const foundWithDeleted = await testRepo.findById(created._id, { includeDeleted: true });
      expect(foundWithDeleted).toBeDefined();
      expect(foundWithDeleted!.isDeleted).toBe(true);
    });

    it('应该计数文档', async () => {
      const testData = [
        { name: '文档1', value: 10, category: 'A' as const },
        { name: '文档2', value: 20, category: 'A' as const },
        { name: '文档3', value: 30, category: 'B' as const },
      ];

      await testRepo.createMany(testData);
      
      const totalCount = await testRepo.count();
      expect(totalCount).toBe(3);

      const categoryACount = await testRepo.count({ category: 'A' });
      expect(categoryACount).toBe(2);

      const categoryBCount = await testRepo.count({ category: 'B' });
      expect(categoryBCount).toBe(1);
    });

    it('应该检查文档是否存在', async () => {
      const testData = {
        name: '存在检查',
        value: 100,
        category: 'A' as const,
      };

      await testRepo.create(testData);
      
      const exists = await testRepo.exists({ name: '存在检查' });
      expect(exists).toBe(true);

      const notExists = await testRepo.exists({ name: '不存在' });
      expect(notExists).toBe(false);
    });
  });

  describe('数据库工具类测试', () => {
    it('应该执行批量操作', async () => {
      const operations = [
        {
          insertOne: {
            document: {
              name: '批量插入1',
              value: 10,
              category: 'A',
            },
          },
        },
        {
          insertOne: {
            document: {
              name: '批量插入2',
              value: 20,
              category: 'B',
            },
          },
        },
      ];

      const result = await dbUtils.bulkWrite(TestModel, operations);

      expect(result.insertedCount).toBe(2);
      expect(result.modifiedCount).toBe(0);
      expect(result.deletedCount).toBe(0);

      const count = await testRepo.count();
      expect(count).toBe(2);
    });

    it('应该获取数据库连接信息', async () => {
      const connectionInfo = dbUtils.getConnectionInfo();

      expect(connectionInfo).toHaveProperty('connectionState');
      expect(connectionInfo).toHaveProperty('activeConnections');
    });
  });

  describe('Schema功能测试', () => {
    it('应该验证必需字段', async () => {
      try {
        await testRepo.create({
          // 缺少必需的name和value字段
          category: 'A' as const,
        } as any);
        fail('应该抛出验证错误');
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    it('应该自动设置默认值', async () => {
      const doc = await testRepo.create({
        name: '默认值测试',
        value: 100,
        // 不设置category，应该使用默认值'A'
      });

      expect(doc.category).toBe('A');
      expect(doc.isActive).toBe(true);
      expect(doc.isDeleted).toBe(false);
      expect(doc.version).toBe(0);
    });
  });
});
