# 资源管理规范

> 📖 **导航**: [返回主页](./README.md) | [项目结构](./project-structure.md) | [小程序优化](./miniprogram-optimization.md)

## 📦 资源管理架构

### 资源分类体系
```typescript
// 资源管理架构
export class ResourceArchitecture {
    // 资源类型分类
    static readonly ResourceTypes = {
        // 纹理资源
        TEXTURES: {
            ui: '用户界面纹理',
            characters: '角色纹理',
            items: '物品纹理',
            skills: '技能纹理',
            world: '世界环境纹理',
            effects: '特效纹理'
        },
        
        // 音频资源
        AUDIO: {
            bgm: '背景音乐',
            sfx: '音效',
            voice: '语音'
        },
        
        // 数据资源
        DATA: {
            config: '配置数据',
            localization: '本地化数据',
            balance: '平衡性数据'
        },
        
        // 预制体资源
        PREFABS: {
            ui: 'UI预制体',
            characters: '角色预制体',
            items: '物品预制体',
            effects: '特效预制体'
        }
    };
    
    // 资源优先级
    static readonly ResourcePriority = {
        CRITICAL: 0,    // 关键资源，立即加载
        HIGH: 1,        // 高优先级，优先加载
        MEDIUM: 2,      // 中等优先级，按需加载
        LOW: 3,         // 低优先级，后台加载
        LAZY: 4         // 懒加载，使用时加载
    };
    
    // 资源生命周期
    static readonly ResourceLifecycle = {
        PRELOAD: '预加载',      // 游戏启动时加载
        ON_DEMAND: '按需加载',   // 使用时加载
        CACHE: '缓存管理',      // 内存缓存管理
        RELEASE: '资源释放'     // 不用时释放
    };
}
```

### 资源管理器实现
```typescript
// 资源管理器
@ccclass('ResourceManager')
export class ResourceManager extends BaseManager {
    private static _instance: ResourceManager = null;
    
    // 资源缓存
    private _textureCache: Map<string, Texture2D> = new Map();
    private _audioCache: Map<string, AudioClip> = new Map();
    private _dataCache: Map<string, any> = new Map();
    private _prefabCache: Map<string, Prefab> = new Map();
    
    // 加载状态
    private _loadingTasks: Map<string, Promise<any>> = new Map();
    private _loadedResources: Set<string> = new Set();
    
    // 内存管理
    private _memoryUsage: number = 0;
    private _memoryLimit: number = 100 * 1024 * 1024; // 100MB
    
    public static getInstance(): ResourceManager {
        if (!this._instance) {
            this._instance = new ResourceManager();
        }
        return this._instance;
    }
    
    protected async initializeManager(): Promise<void> {
        this.setupMemoryMonitoring();
        await this.preloadCriticalResources();
    }
    
    // 纹理资源管理
    public async loadTexture(path: string, priority: number = ResourceArchitecture.ResourcePriority.MEDIUM): Promise<Texture2D> {
        // 检查缓存
        if (this._textureCache.has(path)) {
            return this._textureCache.get(path);
        }
        
        // 检查是否正在加载
        if (this._loadingTasks.has(path)) {
            return this._loadingTasks.get(path);
        }
        
        // 开始加载
        const loadPromise = this.performTextureLoad(path, priority);
        this._loadingTasks.set(path, loadPromise);
        
        try {
            const texture = await loadPromise;
            this._textureCache.set(path, texture);
            this._loadedResources.add(path);
            this.updateMemoryUsage();
            return texture;
        } finally {
            this._loadingTasks.delete(path);
        }
    }
    
    private async performTextureLoad(path: string, priority: number): Promise<Texture2D> {
        return new Promise((resolve, reject) => {
            resources.load(path, Texture2D, (err, texture) => {
                if (err) {
                    console.error(`Failed to load texture: ${path}`, err);
                    reject(err);
                } else {
                    resolve(texture);
                }
            });
        });
    }
    
    // 音频资源管理
    public async loadAudio(path: string, priority: number = ResourceArchitecture.ResourcePriority.MEDIUM): Promise<AudioClip> {
        if (this._audioCache.has(path)) {
            return this._audioCache.get(path);
        }
        
        if (this._loadingTasks.has(path)) {
            return this._loadingTasks.get(path);
        }
        
        const loadPromise = this.performAudioLoad(path, priority);
        this._loadingTasks.set(path, loadPromise);
        
        try {
            const audioClip = await loadPromise;
            this._audioCache.set(path, audioClip);
            this._loadedResources.add(path);
            this.updateMemoryUsage();
            return audioClip;
        } finally {
            this._loadingTasks.delete(path);
        }
    }
    
    private async performAudioLoad(path: string, priority: number): Promise<AudioClip> {
        return new Promise((resolve, reject) => {
            resources.load(path, AudioClip, (err, audioClip) => {
                if (err) {
                    console.error(`Failed to load audio: ${path}`, err);
                    reject(err);
                } else {
                    resolve(audioClip);
                }
            });
        });
    }
    
    // 数据资源管理
    public async loadJSON(path: string): Promise<any> {
        if (this._dataCache.has(path)) {
            return this._dataCache.get(path);
        }
        
        if (this._loadingTasks.has(path)) {
            return this._loadingTasks.get(path);
        }
        
        const loadPromise = this.performJSONLoad(path);
        this._loadingTasks.set(path, loadPromise);
        
        try {
            const data = await loadPromise;
            this._dataCache.set(path, data);
            this._loadedResources.add(path);
            return data;
        } finally {
            this._loadingTasks.delete(path);
        }
    }
    
    private async performJSONLoad(path: string): Promise<any> {
        return new Promise((resolve, reject) => {
            resources.load(path, JsonAsset, (err, jsonAsset) => {
                if (err) {
                    console.error(`Failed to load JSON: ${path}`, err);
                    reject(err);
                } else {
                    resolve(jsonAsset.json);
                }
            });
        });
    }
    
    // 预制体资源管理
    public async loadPrefab(path: string): Promise<Prefab> {
        if (this._prefabCache.has(path)) {
            return this._prefabCache.get(path);
        }
        
        if (this._loadingTasks.has(path)) {
            return this._loadingTasks.get(path);
        }
        
        const loadPromise = this.performPrefabLoad(path);
        this._loadingTasks.set(path, loadPromise);
        
        try {
            const prefab = await loadPromise;
            this._prefabCache.set(path, prefab);
            this._loadedResources.add(path);
            this.updateMemoryUsage();
            return prefab;
        } finally {
            this._loadingTasks.delete(path);
        }
    }
    
    private async performPrefabLoad(path: string): Promise<Prefab> {
        return new Promise((resolve, reject) => {
            resources.load(path, Prefab, (err, prefab) => {
                if (err) {
                    console.error(`Failed to load prefab: ${path}`, err);
                    reject(err);
                } else {
                    resolve(prefab);
                }
            });
        });
    }
    
    // 批量预加载
    public async preloadResources(paths: string[]): Promise<void> {
        const loadPromises = paths.map(path => this.preloadSingleResource(path));
        await Promise.allSettled(loadPromises);
    }
    
    private async preloadSingleResource(path: string): Promise<void> {
        try {
            // 根据文件扩展名判断资源类型
            const extension = path.split('.').pop()?.toLowerCase();
            
            switch (extension) {
                case 'png':
                case 'jpg':
                case 'jpeg':
                    await this.loadTexture(path, ResourceArchitecture.ResourcePriority.HIGH);
                    break;
                case 'mp3':
                case 'wav':
                case 'ogg':
                    await this.loadAudio(path, ResourceArchitecture.ResourcePriority.HIGH);
                    break;
                case 'json':
                    await this.loadJSON(path);
                    break;
                case 'prefab':
                    await this.loadPrefab(path);
                    break;
                default:
                    console.warn(`Unknown resource type: ${path}`);
            }
        } catch (error) {
            console.error(`Failed to preload resource: ${path}`, error);
        }
    }
    
    // 关键资源预加载
    private async preloadCriticalResources(): Promise<void> {
        const criticalResources = [
            // UI关键纹理
            'textures/ui/common/button_normal',
            'textures/ui/common/button_pressed',
            'textures/ui/common/panel_background',
            
            // 音效
            'audio/sfx/ui_click',
            'audio/sfx/ui_open',
            'audio/sfx/ui_close',
            
            // 核心配置
            'data/game/core_config.json',
            'data/game/ui_config.json'
        ];
        
        await this.preloadResources(criticalResources);
    }
    
    // 内存管理
    private setupMemoryMonitoring(): void {
        setInterval(() => {
            this.checkMemoryUsage();
        }, 10000); // 每10秒检查一次内存使用
    }
    
    private checkMemoryUsage(): void {
        this.updateMemoryUsage();
        
        if (this._memoryUsage > this._memoryLimit * 0.8) {
            console.warn(`Memory usage high: ${(this._memoryUsage / 1024 / 1024).toFixed(2)}MB`);
            this.performMemoryCleanup();
        }
    }
    
    private updateMemoryUsage(): void {
        let totalSize = 0;
        
        // 计算纹理内存使用
        for (const texture of this._textureCache.values()) {
            totalSize += this.calculateTextureSize(texture);
        }
        
        // 计算音频内存使用
        for (const audio of this._audioCache.values()) {
            totalSize += this.calculateAudioSize(audio);
        }
        
        this._memoryUsage = totalSize;
    }
    
    private calculateTextureSize(texture: Texture2D): number {
        // 估算纹理内存使用 (宽 * 高 * 4字节)
        return texture.width * texture.height * 4;
    }
    
    private calculateAudioSize(audio: AudioClip): number {
        // 估算音频内存使用
        return audio.getDuration() * 44100 * 2 * 2; // 44.1kHz, 16bit, stereo
    }
    
    // 内存清理
    public performMemoryCleanup(): void {
        console.log('Performing memory cleanup...');
        
        // 清理未使用的纹理
        this.clearUnusedTextures();
        
        // 清理音频缓存
        this.clearAudioCache();
        
        // 清理数据缓存
        this.clearDataCache();
        
        this.updateMemoryUsage();
        console.log(`Memory usage after cleanup: ${(this._memoryUsage / 1024 / 1024).toFixed(2)}MB`);
    }
    
    public clearUnusedTextures(): void {
        const unusedTextures: string[] = [];
        
        for (const [path, texture] of this._textureCache) {
            if (texture.refCount <= 1) {
                unusedTextures.push(path);
            }
        }
        
        for (const path of unusedTextures) {
            this._textureCache.delete(path);
            this._loadedResources.delete(path);
        }
        
        console.log(`Cleared ${unusedTextures.length} unused textures`);
    }
    
    public clearAudioCache(): void {
        // 清理所有音频缓存（音频通常可以重新加载）
        const audioCount = this._audioCache.size;
        this._audioCache.clear();
        
        console.log(`Cleared ${audioCount} audio clips from cache`);
    }
    
    private clearDataCache(): void {
        // 保留关键配置数据，清理其他数据
        const criticalData = ['core_config', 'ui_config'];
        const keysToDelete: string[] = [];
        
        for (const key of this._dataCache.keys()) {
            if (!criticalData.some(critical => key.includes(critical))) {
                keysToDelete.push(key);
            }
        }
        
        for (const key of keysToDelete) {
            this._dataCache.delete(key);
        }
        
        console.log(`Cleared ${keysToDelete.length} data entries from cache`);
    }
    
    // 资源释放
    public releaseResource(path: string): void {
        this._textureCache.delete(path);
        this._audioCache.delete(path);
        this._dataCache.delete(path);
        this._prefabCache.delete(path);
        this._loadedResources.delete(path);
        
        // 释放Cocos Creator资源
        resources.release(path);
    }
    
    // 获取资源信息
    public getResourceInfo(): IResourceInfo {
        return {
            textureCount: this._textureCache.size,
            audioCount: this._audioCache.size,
            dataCount: this._dataCache.size,
            prefabCount: this._prefabCache.size,
            memoryUsage: this._memoryUsage,
            memoryLimit: this._memoryLimit,
            loadedResources: Array.from(this._loadedResources)
        };
    }
}

// 资源信息接口
interface IResourceInfo {
    textureCount: number;
    audioCount: number;
    dataCount: number;
    prefabCount: number;
    memoryUsage: number;
    memoryLimit: number;
    loadedResources: string[];
}
```

---

> 📖 **相关文档**: [小程序优化](./miniprogram-optimization.md) | [数据管理](./data-management.md) | [项目结构](./project-structure.md)
