import express = require('express');
import Joi = require('joi');
import { Logger } from '../utils/logger';

/**
 * 参数验证中间件
 */

export interface ValidationSchema {
  body?: Joi.ObjectSchema;
  query?: Joi.ObjectSchema;
  params?: Joi.ObjectSchema;
  headers?: Joi.ObjectSchema;
  options?: Joi.ValidationOptions;
}

/**
 * 验证错误接口
 */
export interface ValidationError {
  field: string;
  message: string;
  value?: any;
  type: string;
}

/**
 * 默认验证选项
 */
const defaultValidationOptions: Joi.ValidationOptions = {
  abortEarly: false, // 返回所有错误
  allowUnknown: false, // 不允许未知字段
  stripUnknown: true, // 移除未知字段
  convert: true, // 自动类型转换
};

/**
 * 创建验证中间件
 * @param schema 验证模式
 * @returns Express中间件函数
 */
export function validate(schema: ValidationSchema) {
  return (req: express.Request, res: express.Response, next: express.NextFunction) => {
    const errors: ValidationError[] = [];
    const options = { ...defaultValidationOptions, ...schema.options };

    try {
      // 验证请求体
      if (schema.body && req.body) {
        const { error, value } = schema.body.validate(req.body, options);
        if (error) {
          errors.push(...formatJoiErrors(error, 'body'));
        } else {
          req.body = value; // 使用验证后的值
        }
      }

      // 验证查询参数
      if (schema.query && req.query) {
        const { error, value } = schema.query.validate(req.query, options);
        if (error) {
          errors.push(...formatJoiErrors(error, 'query'));
        } else {
          req.query = value;
        }
      }

      // 验证路径参数
      if (schema.params && req.params) {
        const { error, value } = schema.params.validate(req.params, options);
        if (error) {
          errors.push(...formatJoiErrors(error, 'params'));
        } else {
          req.params = value;
        }
      }

      // 验证请求头
      if (schema.headers && req.headers) {
        const { error } = schema.headers.validate(req.headers, options);
        if (error) {
          errors.push(...formatJoiErrors(error, 'headers'));
        }
      }

      if (errors.length > 0) {
        Logger.warn('参数验证失败', {
          requestId: (req as any).requestId,
          url: req.originalUrl,
          method: req.method,
          errors,
          ip: req.ip,
        });

        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors,
          timestamp: new Date().toISOString(),
        });
      }

      Logger.debug('参数验证通过', {
        requestId: (req as any).requestId,
        url: req.originalUrl,
        method: req.method,
      });

      next();
    } catch (error) {
      Logger.error('参数验证中间件错误', {
        requestId: (req as any).requestId,
        error,
      });

      res.status(500).json({
        success: false,
        message: '参数验证失败',
        error: '服务器内部错误',
        timestamp: new Date().toISOString(),
      });
    }
  };
}

/**
 * 格式化Joi验证错误
 */
function formatJoiErrors(joiError: Joi.ValidationError, source: string): ValidationError[] {
  return joiError.details.map(detail => ({
    field: `${source}.${detail.path.join('.')}`,
    message: detail.message,
    value: detail.context?.value,
    type: detail.type,
  }));
}

/**
 * 常用验证模式
 */
export const ValidationSchemas = {
  // 用户注册
  userRegister: {
    body: Joi.object({
      username: Joi.string().pattern(/^[a-zA-Z0-9_]+$/).min(3).max(20).required()
        .messages({
          'string.pattern.base': '用户名只能包含字母、数字和下划线',
          'string.min': '用户名至少3个字符',
          'string.max': '用户名最多20个字符',
          'any.required': '用户名是必需的',
        }),
      email: Joi.string().email().required()
        .messages({
          'string.email': '请输入有效的邮箱地址',
          'any.required': '邮箱是必需的',
        }),
      password: Joi.string().min(6).max(50).required()
        .messages({
          'string.min': '密码至少6个字符',
          'string.max': '密码最多50个字符',
          'any.required': '密码是必需的',
        }),
      confirmPassword: Joi.string().valid(Joi.ref('password')).required()
        .messages({
          'any.only': '确认密码必须与密码一致',
          'any.required': '确认密码是必需的',
        }),
    }),
  },

  // 用户登录
  userLogin: {
    body: Joi.object({
      username: Joi.string().required()
        .messages({
          'any.required': '用户名或邮箱是必需的',
        }),
      password: Joi.string().required()
        .messages({
          'any.required': '密码是必需的',
        }),
    }),
  },

  // 用户资料更新
  userProfileUpdate: {
    body: Joi.object({
      'profile.nickname': Joi.string().max(30).allow('').optional(),
      'profile.bio': Joi.string().max(200).allow('').optional(),
      'profile.preferences.language': Joi.string().valid('zh-CN', 'en-US').optional(),
      'profile.preferences.timezone': Joi.string().optional(),
      'profile.preferences.notifications': Joi.object({
        email: Joi.boolean().optional(),
        push: Joi.boolean().optional(),
        inGame: Joi.boolean().optional(),
      }).optional(),
      'profile.preferences.privacy': Joi.object({
        showOnlineStatus: Joi.boolean().optional(),
        allowFriendRequests: Joi.boolean().optional(),
        showProfile: Joi.boolean().optional(),
      }).optional(),
    }).min(1),
  },

  // 修改密码
  changePassword: {
    body: Joi.object({
      currentPassword: Joi.string().required()
        .messages({
          'any.required': '当前密码是必需的',
        }),
      newPassword: Joi.string().min(6).max(50).required()
        .messages({
          'string.min': '新密码至少6个字符',
          'string.max': '新密码最多50个字符',
          'any.required': '新密码是必需的',
        }),
      confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required()
        .messages({
          'any.only': '确认密码必须与新密码一致',
          'any.required': '确认密码是必需的',
        }),
    }),
  },

  // 密码重置请求
  passwordReset: {
    body: Joi.object({
      email: Joi.string().email().required()
        .messages({
          'string.email': '请输入有效的邮箱地址',
          'any.required': '邮箱是必需的',
        }),
    }),
  },

  // 重置密码
  resetPassword: {
    body: Joi.object({
      token: Joi.string().required()
        .messages({
          'any.required': '重置令牌是必需的',
        }),
      newPassword: Joi.string().min(6).max(50).required()
        .messages({
          'string.min': '新密码至少6个字符',
          'string.max': '新密码最多50个字符',
          'any.required': '新密码是必需的',
        }),
      confirmPassword: Joi.string().valid(Joi.ref('newPassword')).required()
        .messages({
          'any.only': '确认密码必须与新密码一致',
          'any.required': '确认密码是必需的',
        }),
    }),
  },

  // 邮箱验证
  emailVerification: {
    body: Joi.object({
      token: Joi.string().required()
        .messages({
          'any.required': '验证令牌是必需的',
        }),
    }),
  },

  // 角色创建
  characterCreate: {
    body: Joi.object({
      name: Joi.string().pattern(/^[\u4e00-\u9fa5a-zA-Z0-9_\s]+$/).min(2).max(20).required()
        .messages({
          'string.pattern.base': '角色名只能包含中文、字母、数字、下划线和空格',
          'string.min': '角色名至少2个字符',
          'string.max': '角色名最多20个字符',
          'any.required': '角色名是必需的',
        }),
      characterClass: Joi.string().valid('warrior', 'mage', 'archer', 'assassin').required()
        .messages({
          'any.only': '请选择有效的职业：warrior, mage, archer, assassin',
          'any.required': '角色职业是必需的',
        }),
      gender: Joi.string().valid('male', 'female').optional()
        .messages({
          'any.only': '性别只能是male或female',
        }),
    }),
  },

  // 角色更新
  characterUpdate: {
    body: Joi.object({
      name: Joi.string().pattern(/^[\u4e00-\u9fa5a-zA-Z0-9_\s]+$/).min(2).max(20).optional()
        .messages({
          'string.pattern.base': '角色名只能包含中文、字母、数字、下划线和空格',
          'string.min': '角色名至少2个字符',
          'string.max': '角色名最多20个字符',
        }),
    }).min(1),
  },

  // MongoDB ObjectId验证
  mongoId: {
    params: Joi.object({
      characterId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).optional(),
      itemId: Joi.string().optional(),
      skillId: Joi.string().optional(),
      instanceId: Joi.string().optional(),
      equipSlot: Joi.string().valid('weapon', 'armor', 'helmet', 'boots', 'gloves', 'accessory1', 'accessory2').optional(),
    }).or('characterId', 'itemId', 'skillId', 'instanceId', 'equipSlot')
      .messages({
        'string.pattern.base': '无效的ID格式',
        'object.missing': '至少需要提供一个有效的ID',
      }),
  },

  // 添加物品到背包
  addItem: {
    body: Joi.object({
      itemId: Joi.string().required()
        .messages({
          'any.required': '物品ID是必需的',
        }),
      quantity: Joi.number().integer().min(1).optional().default(1)
        .messages({
          'number.min': '数量不能小于1',
          'number.integer': '数量必须是整数',
        }),
    }),
  },

  // 学习技能
  learnSkill: {
    body: Joi.object({
      skillId: Joi.string().required()
        .messages({
          'any.required': '技能ID是必需的',
        }),
    }),
  },

  // 属性分配
  attributeAllocation: {
    body: Joi.object({
      attributes: Joi.object({
        strength: Joi.number().integer().min(0).max(100).required(),
        agility: Joi.number().integer().min(0).max(100).required(),
        intelligence: Joi.number().integer().min(0).max(100).required(),
        vitality: Joi.number().integer().min(0).max(100).required(),
        spirit: Joi.number().integer().min(0).max(100).required(),
      }).required(),
      remainingPoints: Joi.number().integer().min(0).required(),
    }),
  },

  // 战斗动作
  battleAction: {
    body: Joi.object({
      battleId: Joi.string().required()
        .messages({
          'any.required': '战斗ID是必需的',
        }),
      action: Joi.string().valid('attack', 'skill', 'item', 'defend').required()
        .messages({
          'any.only': '请选择有效的战斗动作',
          'any.required': '战斗动作是必需的',
        }),
      skillId: Joi.string().when('action', {
        is: 'skill',
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
      itemId: Joi.string().when('action', {
        is: 'item',
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
    }),
  },

  // 聊天消息
  chatMessage: {
    body: Joi.object({
      content: Joi.string().min(1).max(500).required()
        .messages({
          'string.min': '消息内容不能为空',
          'string.max': '消息内容最多500个字符',
          'any.required': '消息内容是必需的',
        }),
      chatType: Joi.string().valid('world', 'guild', 'private').default('world'),
      targetUserId: Joi.string().when('chatType', {
        is: 'private',
        then: Joi.required(),
        otherwise: Joi.optional(),
      }),
    }),
  },

  // 分页参数
  pagination: {
    query: Joi.object({
      page: Joi.number().integer().min(1).default(1),
      limit: Joi.number().integer().min(1).max(100).default(20),
      sort: Joi.string().optional(),
      order: Joi.string().valid('asc', 'desc').default('desc'),
    }),
  },

  // ID参数
  idParam: {
    params: Joi.object({
      id: Joi.string().required()
        .messages({
          'any.required': 'ID参数是必需的',
        }),
    }),
  },
};

/**
 * 自定义验证规则
 */
export const customValidators = {
  // 验证ObjectId格式
  objectId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/)
    .messages({
      'string.pattern.base': '无效的ID格式',
    }),

  // 验证密码强度
  strongPassword: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .messages({
      'string.min': '密码至少8个字符',
      'string.pattern.base': '密码必须包含大小写字母、数字和特殊字符',
    }),

  // 验证中文字符
  chineseName: Joi.string()
    .pattern(/^[\u4e00-\u9fa5a-zA-Z0-9_-]+$/)
    .messages({
      'string.pattern.base': '名称只能包含中文、英文、数字、下划线和连字符',
    }),
};
