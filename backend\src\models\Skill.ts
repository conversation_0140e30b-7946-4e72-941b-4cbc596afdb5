import mongoose, { Document, Schema } from 'mongoose';

/**
 * 技能伤害类型枚举（与前端ISkillData.ts保持一致）
 */
export enum SkillDamageType {
  PHYSICAL = 'physical',
  MAGICAL = 'magical',
  TRUE = 'true',
  HEALING = 'healing',
}

/**
 * 技能目标类型枚举
 */
export enum SkillTargetType {
  SELF = 'self',
  ALLY = 'ally',
  ENEMY = 'enemy',
  ALL_ALLIES = 'all_allies',
  ALL_ENEMIES = 'all_enemies',
  ALL = 'all',
  AREA = 'area',
}

/**
 * 技能效果类型枚举
 */
export enum SkillEffectType {
  DAMAGE = 'damage',
  HEAL = 'heal',
  BUFF = 'buff',
  DEBUFF = 'debuff',
  STUN = 'stun',
  SLOW = 'slow',
  POISON = 'poison',
  BURN = 'burn',
  SHIELD = 'shield',
}

/**
 * 技能要求接口
 */
export interface ISkillRequirements {
  level: number;
  skillPoints: number;
  prerequisiteSkills: string[];
  attributes?: {
    strength?: number;
    agility?: number;
    intelligence?: number;
    vitality?: number;
    spirit?: number;
  };
}

/**
 * 技能效果接口
 */
export interface ISkillEffect {
  type: SkillEffectType;
  value: number;
  duration: number;
  chance: number;
  target: SkillTargetType;
  description: string;
}

/**
 * 技能数据接口（基于前端ISkillData.ts）
 */
export interface ISkill extends Document {
  _id: mongoose.Types.ObjectId;
  id: string; // 技能唯一标识符
  name: string;
  description: string;
  manaCost: number;
  castTime: number;
  cooldown: number;
  damageType: SkillDamageType;
  targetType: SkillTargetType;
  baseDamageMultiplier: number;
  level: number;
  maxLevel: number;
  requirements: ISkillRequirements;
  effects: ISkillEffect[];
  
  // 额外的服务器端字段
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 用户技能接口（玩家学习的技能）
 */
export interface IUserSkill extends Document {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  skillId: string; // 引用技能配置的ID
  level: number;
  experience: number;
  experienceRequired: number;
  learned: boolean;
  lastUsed: Date;
  cooldownRemaining: number;
  
  createdAt: Date;
  updatedAt: Date;
  
  // 方法
  canUse(): boolean;
  use(): Promise<void>;
  addExperience(amount: number): Promise<boolean>;
  levelUp(): Promise<void>;
}

/**
 * 技能Schema（配置数据）
 */
const SkillSchema = new Schema<ISkill>({
  id: {
    type: String,
    required: [true, '技能ID是必填项'],
    unique: true,
    trim: true,
    index: true,
  },
  name: {
    type: String,
    required: [true, '技能名称是必填项'],
    trim: true,
    maxlength: [50, '技能名称最多50个字符'],
  },
  description: {
    type: String,
    required: [true, '技能描述是必填项'],
    maxlength: [500, '技能描述最多500个字符'],
  },
  manaCost: {
    type: Number,
    required: [true, '法力消耗是必填项'],
    min: [0, '法力消耗不能为负数'],
  },
  castTime: {
    type: Number,
    required: [true, '施法时间是必填项'],
    min: [0, '施法时间不能为负数'],
  },
  cooldown: {
    type: Number,
    required: [true, '冷却时间是必填项'],
    min: [0, '冷却时间不能为负数'],
  },
  damageType: {
    type: String,
    enum: Object.values(SkillDamageType),
    required: [true, '伤害类型是必填项'],
    index: true,
  },
  targetType: {
    type: String,
    enum: Object.values(SkillTargetType),
    required: [true, '目标类型是必填项'],
    index: true,
  },
  baseDamageMultiplier: {
    type: Number,
    required: [true, '基础伤害倍数是必填项'],
    min: [0, '基础伤害倍数不能为负数'],
  },
  level: {
    type: Number,
    default: 1,
    min: [1, '技能等级不能小于1'],
  },
  maxLevel: {
    type: Number,
    default: 10,
    min: [1, '最大等级不能小于1'],
  },
  requirements: {
    level: {
      type: Number,
      default: 1,
      min: [1, '所需等级不能小于1'],
    },
    skillPoints: {
      type: Number,
      default: 1,
      min: [0, '所需技能点不能为负数'],
    },
    prerequisiteSkills: [{
      type: String,
    }],
    attributes: {
      strength: { type: Number, min: 0 },
      agility: { type: Number, min: 0 },
      intelligence: { type: Number, min: 0 },
      vitality: { type: Number, min: 0 },
      spirit: { type: Number, min: 0 },
    },
  },
  effects: [{
    type: {
      type: String,
      enum: Object.values(SkillEffectType),
      required: true,
    },
    value: {
      type: Number,
      required: true,
    },
    duration: {
      type: Number,
      default: 0,
      min: 0,
    },
    chance: {
      type: Number,
      default: 1,
      min: 0,
      max: 1,
    },
    target: {
      type: String,
      enum: Object.values(SkillTargetType),
      required: true,
    },
    description: {
      type: String,
      required: true,
      maxlength: [200, '效果描述最多200个字符'],
    },
  }],
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

/**
 * 用户技能Schema
 */
const UserSkillSchema = new Schema<IUserSkill>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '用户ID是必填项'],
    index: true,
  },
  skillId: {
    type: String,
    required: [true, '技能ID是必填项'],
    index: true,
  },
  level: {
    type: Number,
    default: 1,
    min: [1, '技能等级不能小于1'],
    max: [10, '技能等级不能超过10'],
  },
  experience: {
    type: Number,
    default: 0,
    min: [0, '经验值不能为负数'],
  },
  experienceRequired: {
    type: Number,
    default: 100,
    min: [1, '所需经验不能小于1'],
  },
  learned: {
    type: Boolean,
    default: false,
    index: true,
  },
  lastUsed: {
    type: Date,
    default: null,
  },
  cooldownRemaining: {
    type: Number,
    default: 0,
    min: [0, '冷却时间不能为负数'],
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// 索引
SkillSchema.index({ id: 1 });
SkillSchema.index({ damageType: 1 });
SkillSchema.index({ targetType: 1 });
SkillSchema.index({ 'requirements.level': 1 });

UserSkillSchema.index({ userId: 1, skillId: 1 }, { unique: true });
UserSkillSchema.index({ userId: 1, learned: 1 });
UserSkillSchema.index({ lastUsed: 1 });

// 虚拟字段
UserSkillSchema.virtual('experiencePercentage').get(function() {
  return (this.experience / this.experienceRequired * 100).toFixed(1);
});

UserSkillSchema.virtual('isOnCooldown').get(function() {
  return this.cooldownRemaining > 0;
});

// 中间件：更新冷却时间
UserSkillSchema.pre('save', function(next) {
  if (this.lastUsed && this.cooldownRemaining > 0) {
    const timePassed = (Date.now() - this.lastUsed.getTime()) / 1000;
    this.cooldownRemaining = Math.max(0, this.cooldownRemaining - timePassed);
  }
  next();
});

// 实例方法：检查是否可以使用
UserSkillSchema.methods.canUse = function(): boolean {
  return this.learned && this.cooldownRemaining <= 0;
};

// 实例方法：使用技能
UserSkillSchema.methods.use = async function(): Promise<void> {
  if (!this.canUse()) {
    throw new Error('技能无法使用');
  }
  
  // 获取技能配置数据
  const skillConfig = await Skill.findOne({ id: this.skillId });
  if (!skillConfig) {
    throw new Error('技能配置不存在');
  }
  
  this.lastUsed = new Date();
  this.cooldownRemaining = skillConfig.cooldown;
  
  await this.save();
};

// 实例方法：添加经验值
UserSkillSchema.methods.addExperience = async function(amount: number): Promise<boolean> {
  this.experience += amount;
  
  let leveledUp = false;
  while (this.experience >= this.experienceRequired && this.level < 10) {
    await this.levelUp();
    leveledUp = true;
  }
  
  if (!leveledUp) {
    await this.save();
  }
  
  return leveledUp;
};

// 实例方法：升级
UserSkillSchema.methods.levelUp = async function(): Promise<void> {
  if (this.level >= 10) {
    throw new Error('技能已达到最大等级');
  }
  
  this.experience -= this.experienceRequired;
  this.level += 1;
  this.experienceRequired = this.level * 100; // 简单的经验计算公式
  
  await this.save();
};

export const Skill = mongoose.model<ISkill>('Skill', SkillSchema);
export const UserSkill = mongoose.model<IUserSkill>('UserSkill', UserSkillSchema);
