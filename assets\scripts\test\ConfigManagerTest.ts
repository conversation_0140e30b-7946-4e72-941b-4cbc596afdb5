/**
 * ConfigManager测试组件
 * 测试配置管理器的功能
 *
 * 使用方法：
 * 1. 在Cocos Creator中打开任意场景
 * 2. 选择Canvas或任意节点
 * 3. 在属性检查器中点击"添加组件"
 * 4. 搜索"ConfigManagerTest"并添加
 * 5. 运行场景，使用键盘快捷键测试功能
 */

import { _decorator, Component, input, Input, EventKeyboard, KeyCode } from 'cc';
import { ConfigManager } from '../managers/ConfigManager';
import { ISkillData } from '../config/interfaces/ISkillData';

const { ccclass } = _decorator;

@ccclass('ConfigManagerTest')
export class ConfigManagerTest extends Component {
    private _configManager: ConfigManager | null = null;

    protected onLoad(): void {
        console.log('🧪 ConfigManager测试组件加载');
        this._initializeKeyboardInput();
        this._initializeConfigManager();
    }

    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this._onKeyDown, this);
        console.log('🧪 ConfigManager测试组件销毁');
    }

    /**
     * 初始化键盘输入
     */
    private _initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this._onKeyDown, this);
        console.log('⌨️ ConfigManager测试键盘输入已初始化');
        this._showTestInstructions();
    }

    /**
     * 初始化配置管理器
     */
    private async _initializeConfigManager(): Promise<void> {
        try {
            this._configManager = ConfigManager.getInstance();
            await this._configManager.initialize();
            console.log('✅ ConfigManager初始化成功');
        } catch (error) {
            console.error('❌ ConfigManager初始化失败:', error);
        }
    }

    /**
     * 显示测试说明
     */
    private _showTestInstructions(): void {
        console.log('🧪 ========== ConfigManager测试 ==========');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 测试技能配置加载');
        console.log('   按 2 键 - 测试技能数据查询');
        console.log('   按 3 键 - 测试技能数据筛选');
        console.log('   按 4 键 - 测试配置统计信息');
        console.log('   按 5 键 - 测试配置重新加载');
        console.log('   按 6 键 - 测试错误处理');
        console.log('   按 7 键 - 测试性能基准');
        console.log('   按 8 键 - 显示所有技能');
        console.log('   按 9 键 - 测试奖励表配置');
        console.log('   按 0 键 - 测试物品使用效果');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🧪 =====================================');
    }

    /**
     * 键盘按键处理
     */
    private _onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this._testSkillConfigLoading();
                break;
            case KeyCode.DIGIT_2:
                this._testSkillDataQuery();
                break;
            case KeyCode.DIGIT_3:
                this._testSkillDataFiltering();
                break;
            case KeyCode.DIGIT_4:
                this._testConfigStats();
                break;
            case KeyCode.DIGIT_5:
                this._testConfigReload();
                break;
            case KeyCode.DIGIT_6:
                this._testErrorHandling();
                break;
            case KeyCode.DIGIT_7:
                this._testPerformanceBenchmark();
                break;
            case KeyCode.DIGIT_8:
                this._showAllSkills();
                break;
            case KeyCode.DIGIT_9:
                this._testRewardTableConfig();
                break;
            case KeyCode.DIGIT_0:
                this._testItemUseEffects();
                break;
            case KeyCode.KEY_H:
                this._showTestInstructions();
                break;
        }
    }

    /**
     * 测试技能配置加载
     */
    private async _testSkillConfigLoading(): Promise<void> {
        console.log('🔄 ========== 技能配置加载测试 ==========');
        
        if (!this._configManager) {
            console.log('❌ ConfigManager未初始化');
            return;
        }

        try {
            const startTime = Date.now();
            const result = await this._configManager.loadSkillConfig({ forceReload: true });
            const loadTime = Date.now() - startTime;
            
            if (result.success) {
                console.log('✅ 技能配置加载成功');
                console.log(`⏱️ 加载时间: ${loadTime}ms`);
                console.log(`📊 配置版本: ${result.version}`);
                console.log(`📦 技能数量: ${this._configManager.getAllSkillData().length}`);
            } else {
                console.log('❌ 技能配置加载失败:', result.error);
            }
        } catch (error) {
            console.log('❌ 技能配置加载异常:', error);
        }
        
        console.log('🔄 =====================================');
    }

    /**
     * 测试技能数据查询
     */
    private _testSkillDataQuery(): void {
        console.log('🔍 ========== 技能数据查询测试 ==========');
        
        if (!this._configManager) {
            console.log('❌ ConfigManager未初始化');
            return;
        }

        // 测试单个技能查询
        const testSkillIds = ['basic_attack', 'fireball', 'heal', 'nonexistent_skill'];
        
        for (const skillId of testSkillIds) {
            const skillData = this._configManager.getSkillData(skillId);
            
            if (skillData) {
                console.log(`✅ 找到技能: ${skillData.name} (${skillData.id})`);
                console.log(`   - 法力消耗: ${skillData.manaCost}`);
                console.log(`   - 冷却时间: ${skillData.cooldown}秒`);
                console.log(`   - 伤害类型: ${skillData.damageType}`);
            } else {
                console.log(`❌ 未找到技能: ${skillId}`);
            }
        }
        
        console.log('🔍 =====================================');
    }

    /**
     * 测试技能数据筛选
     */
    private _testSkillDataFiltering(): void {
        console.log('🎯 ========== 技能数据筛选测试 ==========');
        
        if (!this._configManager) {
            console.log('❌ ConfigManager未初始化');
            return;
        }

        // 测试不同的筛选条件
        const tests = [
            {
                name: '物理伤害技能',
                filter: (skill: ISkillData) => skill.damageType === 'physical'
            },
            {
                name: '魔法伤害技能',
                filter: (skill: ISkillData) => skill.damageType === 'magical'
            },
            {
                name: '无消耗技能',
                filter: (skill: ISkillData) => skill.manaCost === 0
            },
            {
                name: '高级技能(等级要求>=10)',
                filter: (skill: ISkillData) => skill.requirements.level >= 10
            },
            {
                name: '群体技能',
                filter: (skill: ISkillData) => skill.targetType === 'area'
            }
        ];

        for (const test of tests) {
            const filteredSkills = this._configManager.getSkillsByCondition(test.filter);
            console.log(`🎯 ${test.name}: ${filteredSkills.length} 个`);
            
            if (filteredSkills.length > 0) {
                filteredSkills.forEach(skill => {
                    console.log(`   - ${skill.name} (${skill.id})`);
                });
            }
        }
        
        console.log('🎯 =====================================');
    }

    /**
     * 测试配置统计信息
     */
    private _testConfigStats(): void {
        console.log('📊 ========== 配置统计信息测试 ==========');
        
        if (!this._configManager) {
            console.log('❌ ConfigManager未初始化');
            return;
        }

        const stats = this._configManager.getConfigStats();
        
        console.log('📊 配置统计信息:');
        console.log(`   - 技能数量: ${stats.skills}`);
        console.log(`   - 实体数量: ${stats.entities}`);
        console.log(`   - 物品数量: ${stats.items}`);
        console.log(`   - 任务数量: ${stats.quests}`);
        console.log(`   - 已加载配置: ${stats.loadedConfigs.join(', ')}`);
        console.log(`   - 初始化状态: ${stats.isInitialized ? '✅ 已初始化' : '❌ 未初始化'}`);
        
        console.log('📊 =====================================');
    }

    /**
     * 测试配置重新加载
     */
    private async _testConfigReload(): Promise<void> {
        console.log('🔄 ========== 配置重新加载测试 ==========');
        
        if (!this._configManager) {
            console.log('❌ ConfigManager未初始化');
            return;
        }

        try {
            const startTime = Date.now();
            await this._configManager.reloadAllConfigs();
            const reloadTime = Date.now() - startTime;
            
            console.log('✅ 所有配置重新加载成功');
            console.log(`⏱️ 重新加载时间: ${reloadTime}ms`);
            
            const stats = this._configManager.getConfigStats();
            console.log(`📊 重新加载后统计: ${stats.skills} 技能, ${stats.items} 物品`);
            
        } catch (error) {
            console.log('❌ 配置重新加载失败:', error);
        }
        
        console.log('🔄 =====================================');
    }

    /**
     * 测试错误处理
     */
    private async _testErrorHandling(): Promise<void> {
        console.log('⚠️ ========== 错误处理测试 ==========');
        
        if (!this._configManager) {
            console.log('❌ ConfigManager未初始化');
            return;
        }

        // 测试加载不存在的配置
        try {
            console.log('🔄 测试加载不存在的配置...');
            // 这里可以测试加载不存在的配置文件
            console.log('✅ 错误处理正常');
        } catch (error) {
            console.log('✅ 错误被正确捕获:', error.message);
        }

        // 测试查询不存在的数据
        const nonexistentSkill = this._configManager.getSkillData('nonexistent_skill_id');
        if (nonexistentSkill === null) {
            console.log('✅ 不存在的技能查询返回null');
        } else {
            console.log('❌ 不存在的技能查询应该返回null');
        }
        
        console.log('⚠️ =====================================');
    }

    /**
     * 测试性能基准
     */
    private _testPerformanceBenchmark(): void {
        console.log('⚡ ========== 性能基准测试 ==========');
        
        if (!this._configManager) {
            console.log('❌ ConfigManager未初始化');
            return;
        }

        const iterations = 10000;
        
        // 测试单个查询性能
        console.log(`🔄 执行 ${iterations} 次单个技能查询...`);
        const startTime1 = Date.now();
        for (let i = 0; i < iterations; i++) {
            this._configManager.getSkillData('fireball');
        }
        const queryTime = Date.now() - startTime1;
        console.log(`⏱️ 单个查询性能: ${queryTime}ms (${(queryTime / iterations).toFixed(3)}ms/次)`);

        // 测试批量查询性能
        console.log(`🔄 执行 ${iterations / 100} 次批量技能查询...`);
        const startTime2 = Date.now();
        for (let i = 0; i < iterations / 100; i++) {
            this._configManager.getAllSkillData();
        }
        const batchTime = Date.now() - startTime2;
        console.log(`⏱️ 批量查询性能: ${batchTime}ms (${(batchTime / (iterations / 100)).toFixed(3)}ms/次)`);

        // 测试筛选性能
        console.log(`🔄 执行 ${iterations / 100} 次技能筛选...`);
        const startTime3 = Date.now();
        for (let i = 0; i < iterations / 100; i++) {
            this._configManager.getSkillsByCondition(skill => skill.damageType === 'magical');
        }
        const filterTime = Date.now() - startTime3;
        console.log(`⏱️ 筛选性能: ${filterTime}ms (${(filterTime / (iterations / 100)).toFixed(3)}ms/次)`);
        
        console.log('⚡ =====================================');
    }

    /**
     * 显示所有技能
     */
    private _showAllSkills(): void {
        console.log('📋 ========== 所有技能列表 ==========');
        
        if (!this._configManager) {
            console.log('❌ ConfigManager未初始化');
            return;
        }

        const allSkills = this._configManager.getAllSkillData();
        
        if (allSkills.length === 0) {
            console.log('⚠️ 没有找到任何技能数据');
            return;
        }

        console.log(`📊 总共 ${allSkills.length} 个技能:`);
        
        allSkills.forEach((skill, index) => {
            console.log(`${index + 1}. ${skill.name} (${skill.id})`);
            console.log(`   - 类型: ${skill.damageType} | 目标: ${skill.targetType}`);
            console.log(`   - 消耗: ${skill.manaCost} MP | 冷却: ${skill.cooldown}s`);
            console.log(`   - 等级要求: ${skill.requirements.level} | 最大等级: ${skill.maxLevel}`);
            console.log(`   - 描述: ${skill.description}`);
            console.log('');
        });

        console.log('📋 =====================================');
    }

    /**
     * 测试奖励表配置
     */
    private _testRewardTableConfig(): void {
        console.log('🎁 ========== 奖励表配置测试 ==========');

        if (!this._configManager) {
            console.log('❌ ConfigManager未初始化');
            return;
        }

        // 测试奖励表查询
        const testRewardTableIds = ['bronze_chest_rewards', 'silver_chest_rewards', 'daily_login_rewards', 'nonexistent_table'];

        for (const tableId of testRewardTableIds) {
            const rewardTable = this._configManager.getRewardTableData(tableId);

            if (rewardTable) {
                console.log(`✅ 找到奖励表: ${rewardTable.name} (${rewardTable.id})`);
                console.log(`   - 类型: ${rewardTable.type}`);
                console.log(`   - 奖励数量: ${rewardTable.minRewards}-${rewardTable.maxRewards}`);
                console.log(`   - 奖励项: ${rewardTable.rewards.length} 个`);

                // 显示前3个奖励项
                rewardTable.rewards.slice(0, 3).forEach((reward, index) => {
                    console.log(`     ${index + 1}. ${reward.type}: ${reward.rewardId} (概率: ${(reward.probability * 100).toFixed(1)}%)`);
                });
            } else {
                console.log(`❌ 未找到奖励表: ${tableId}`);
            }
        }

        // 测试按类型筛选
        const randomTables = this._configManager.getRewardTablesByType('random');
        console.log(`🎲 随机类型奖励表: ${randomTables.length} 个`);

        const sequentialTables = this._configManager.getRewardTablesByType('sequential');
        console.log(`📋 顺序类型奖励表: ${sequentialTables.length} 个`);

        console.log('🎁 =====================================');
    }

    /**
     * 测试物品使用效果
     */
    private _testItemUseEffects(): void {
        console.log('💊 ========== 物品使用效果测试 ==========');

        if (!this._configManager) {
            console.log('❌ ConfigManager未初始化');
            return;
        }

        // 测试不同类型的物品
        const testItemIds = ['hp_potion_small', 'fireball_scroll', 'treasure_chest_bronze', 'iron_sword'];

        for (const itemId of testItemIds) {
            const itemData = this._configManager.getItemData(itemId);

            if (itemData) {
                console.log(`📦 物品: ${itemData.name} (${itemData.id})`);
                console.log(`   - 类型: ${itemData.type} | 稀有度: ${itemData.rarity}`);
                console.log(`   - 可使用: ${itemData.usable ? '是' : '否'} | 可装备: ${itemData.equipable ? '是' : '否'}`);

                if (itemData.useEffects && itemData.useEffects.length > 0) {
                    console.log(`   - 使用效果:`);
                    itemData.useEffects.forEach((effect, index) => {
                        console.log(`     ${index + 1}. ${effect.type}: ${effect.value} (${effect.valueType})`);
                        if (effect.extraParams) {
                            console.log(`        额外参数: ${JSON.stringify(effect.extraParams)}`);
                        }
                    });
                } else {
                    console.log(`   - 无使用效果`);
                }

                if (itemData.rewardTableId) {
                    console.log(`   - 奖励表: ${itemData.rewardTableId}`);
                }

                // 显示属性加成
                const hasStats = Object.values(itemData.stats).some(value => value !== 0);
                if (hasStats) {
                    console.log(`   - 属性加成:`);
                    Object.entries(itemData.stats).forEach(([key, value]) => {
                        if (value !== 0) {
                            console.log(`     ${key}: +${value}`);
                        }
                    });
                }

                console.log('');
            } else {
                console.log(`❌ 未找到物品: ${itemId}`);
            }
        }

        console.log('💊 =====================================');
    }
}
