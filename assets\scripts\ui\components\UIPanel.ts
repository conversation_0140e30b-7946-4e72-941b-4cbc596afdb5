/**
 * UI面板组件
 * 通用的UI面板实现，继承自BaseUIPanel
 */

import { _decorator, Node, Button, Label } from 'cc';
import { BaseUIPanel } from '../base/BaseUIPanel';
import { UIPanelType } from '../types/UITypes';
import { UIButton } from './UIButton';

const { ccclass, property } = _decorator;

@ccclass('UIPanel')
export class UIPanel extends BaseUIPanel {
    
    @property({ type: Node, tooltip: '面板内容节点' })
    public contentNode: Node | null = null;
    
    @property({ type: Node, tooltip: '标题栏节点' })
    public titleBar: Node | null = null;
    
    @property({ type: Label, tooltip: '标题文本' })
    public titleLabel: Label | null = null;
    
    @property({ type: UIButton, tooltip: '关闭按钮' })
    public closeButton: UIButton | null = null;
    
    @property({ type: Node, tooltip: '按钮容器' })
    public buttonContainer: Node | null = null;
    
    @property({ tooltip: '面板标题' })
    public panelTitle: string = '';
    
    @property({ tooltip: '是否显示关闭按钮' })
    public showCloseButton: boolean = true;
    
    @property({ tooltip: '是否可拖拽' })
    public draggable: boolean = false;
    
    // 私有属性
    private _buttons: Map<string, UIButton> = new Map();
    private _isDragging: boolean = false;
    private _dragOffset: { x: number; y: number } = { x: 0, y: 0 };

    // ==================== BaseUIPanel抽象方法实现 ====================

    protected onPanelLoad(): void {
        console.log(`📋 UIPanel加载: ${this._panelType}`);
        
        // 自动查找组件
        this.findComponents();
        
        // 设置标题
        if (this.panelTitle && this.titleLabel) {
            this.titleLabel.string = this.panelTitle;
        }
        
        // 设置关闭按钮
        if (this.closeButton) {
            this.closeButton.setVisible(this.showCloseButton);
        }
    }

    protected onPanelEnable(): void {
        console.log(`📋 UIPanel启用: ${this._panelType}`);
    }

    protected onPanelDisable(): void {
        console.log(`📋 UIPanel禁用: ${this._panelType}`);
    }

    protected onPanelDestroy(): void {
        console.log(`📋 UIPanel销毁: ${this._panelType}`);
        this._buttons.clear();
    }

    protected async initializeUI(): Promise<void> {
        console.log(`📋 初始化UIPanel UI: ${this._panelType}`);
        
        // 查找所有按钮
        this.findAllButtons();
        
        // 设置拖拽
        if (this.draggable && this.titleBar) {
            this.setupDragging();
        }
    }

    protected bindEvents(): void {
        // 绑定关闭按钮事件
        if (this.closeButton) {
            this.closeButton.setClickCallback(() => {
                this.onCloseButtonClick();
            });
        }
        
        // 绑定其他按钮事件
        this.bindButtonEvents();
    }

    protected unbindEvents(): void {
        // 解绑按钮事件
        this.unbindButtonEvents();
        
        // 解绑拖拽事件
        if (this.titleBar) {
            this.titleBar.off(Node.EventType.TOUCH_START);
            this.titleBar.off(Node.EventType.TOUCH_MOVE);
            this.titleBar.off(Node.EventType.TOUCH_END);
        }
    }

    protected async onInitialize(data?: any): Promise<void> {
        console.log(`📋 UIPanel初始化完成: ${this._panelType}`, data);
        
        // 子类可以重写此方法进行特定初始化
        await this.onPanelInitialize(data);
    }

    protected async onBeforeShow(data?: any): Promise<void> {
        console.log(`📋 UIPanel显示前: ${this._panelType}`, data);
        
        // 刷新面板数据
        if (data) {
            this.refreshPanelData(data);
        }
        
        // 子类可以重写此方法
        await this.onPanelBeforeShow(data);
    }

    protected async onAfterShow(data?: any): Promise<void> {
        console.log(`📋 UIPanel显示后: ${this._panelType}`, data);
        
        // 子类可以重写此方法
        await this.onPanelAfterShow(data);
    }

    protected async onBeforeHide(): Promise<void> {
        console.log(`📋 UIPanel隐藏前: ${this._panelType}`);
        
        // 子类可以重写此方法
        await this.onPanelBeforeHide();
    }

    protected async onAfterHide(): Promise<void> {
        console.log(`📋 UIPanel隐藏后: ${this._panelType}`);
        
        // 子类可以重写此方法
        await this.onPanelAfterHide();
    }

    protected onRefresh(data?: any): void {
        console.log(`📋 UIPanel刷新: ${this._panelType}`, data);
        
        if (data) {
            this.refreshPanelData(data);
        }
        
        // 子类可以重写此方法
        this.onPanelRefresh(data);
    }

    // ==================== 私有方法 ====================

    /**
     * 查找组件
     */
    private findComponents(): void {
        if (!this.contentNode) {
            this.contentNode = this.node.getChildByName('Content');
        }
        
        if (!this.titleBar) {
            this.titleBar = this.node.getChildByName('TitleBar');
        }
        
        if (!this.titleLabel && this.titleBar) {
            this.titleLabel = this.titleBar.getComponentInChildren(Label);
        }
        
        if (!this.closeButton) {
            const closeButtonNode = this.node.getChildByName('CloseButton') || 
                                   (this.titleBar ? this.titleBar.getChildByName('CloseButton') : null);
            if (closeButtonNode) {
                this.closeButton = closeButtonNode.getComponent(UIButton);
            }
        }
        
        if (!this.buttonContainer) {
            this.buttonContainer = this.node.getChildByName('ButtonContainer') ||
                                  this.node.getChildByName('Buttons');
        }
    }

    /**
     * 查找所有按钮
     */
    private findAllButtons(): void {
        const buttons = this.node.getComponentsInChildren(UIButton);
        
        for (const button of buttons) {
            const buttonId = button.node.name;
            this._buttons.set(buttonId, button);
        }
        
        console.log(`📋 找到 ${this._buttons.size} 个按钮`);
    }

    /**
     * 绑定按钮事件
     */
    private bindButtonEvents(): void {
        // 子类可以重写此方法绑定特定按钮事件
    }

    /**
     * 解绑按钮事件
     */
    private unbindButtonEvents(): void {
        // 子类可以重写此方法解绑特定按钮事件
    }

    /**
     * 设置拖拽
     */
    private setupDragging(): void {
        if (!this.titleBar) return;
        
        this.titleBar.on(Node.EventType.TOUCH_START, this.onDragStart, this);
        this.titleBar.on(Node.EventType.TOUCH_MOVE, this.onDragMove, this);
        this.titleBar.on(Node.EventType.TOUCH_END, this.onDragEnd, this);
    }

    /**
     * 开始拖拽
     */
    private onDragStart(event: any): void {
        this._isDragging = true;
        const touch = event.touch;
        const worldPos = touch.getLocation();
        const nodePos = this.node.position;
        
        this._dragOffset.x = worldPos.x - nodePos.x;
        this._dragOffset.y = worldPos.y - nodePos.y;
    }

    /**
     * 拖拽移动
     */
    private onDragMove(event: any): void {
        if (!this._isDragging) return;
        
        const touch = event.touch;
        const worldPos = touch.getLocation();
        
        this.node.setPosition(
            worldPos.x - this._dragOffset.x,
            worldPos.y - this._dragOffset.y,
            this.node.position.z
        );
    }

    /**
     * 结束拖拽
     */
    private onDragEnd(event: any): void {
        this._isDragging = false;
    }

    /**
     * 关闭按钮点击
     */
    private onCloseButtonClick(): void {
        console.log(`📋 关闭面板: ${this._panelType}`);
        this.hide();
    }

    /**
     * 刷新面板数据
     */
    private refreshPanelData(data: any): void {
        // 子类可以重写此方法处理特定数据
    }

    // ==================== 公共API ====================

    /**
     * 设置标题
     */
    public setTitle(title: string): void {
        this.panelTitle = title;
        if (this.titleLabel) {
            this.titleLabel.string = title;
        }
    }

    /**
     * 获取按钮
     */
    public getButton(buttonId: string): UIButton | null {
        return this._buttons.get(buttonId) || null;
    }

    /**
     * 添加按钮
     */
    public addButton(buttonId: string, button: UIButton): void {
        this._buttons.set(buttonId, button);
    }

    /**
     * 移除按钮
     */
    public removeButton(buttonId: string): void {
        this._buttons.delete(buttonId);
    }

    /**
     * 设置关闭按钮可见性
     */
    public setCloseButtonVisible(visible: boolean): void {
        this.showCloseButton = visible;
        if (this.closeButton) {
            this.closeButton.setVisible(visible);
        }
    }

    /**
     * 设置拖拽状态
     */
    public setDraggable(draggable: boolean): void {
        this.draggable = draggable;
        
        if (draggable && this.titleBar) {
            this.setupDragging();
        } else if (this.titleBar) {
            this.titleBar.off(Node.EventType.TOUCH_START);
            this.titleBar.off(Node.EventType.TOUCH_MOVE);
            this.titleBar.off(Node.EventType.TOUCH_END);
        }
    }

    // ==================== 子类可重写的方法 ====================

    /**
     * 面板特定初始化
     */
    protected async onPanelInitialize(data?: any): Promise<void> {
        // 子类重写
    }

    /**
     * 面板显示前处理
     */
    protected async onPanelBeforeShow(data?: any): Promise<void> {
        // 子类重写
    }

    /**
     * 面板显示后处理
     */
    protected async onPanelAfterShow(data?: any): Promise<void> {
        // 子类重写
    }

    /**
     * 面板隐藏前处理
     */
    protected async onPanelBeforeHide(): Promise<void> {
        // 子类重写
    }

    /**
     * 面板隐藏后处理
     */
    protected async onPanelAfterHide(): Promise<void> {
        // 子类重写
    }

    /**
     * 面板刷新处理
     */
    protected onPanelRefresh(data?: any): void {
        // 子类重写
    }
}
