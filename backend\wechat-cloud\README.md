# 🚀 IdleGame 微信云开发部署指南

> 将您的2D多人在线挂机放置游戏后端部署到微信云开发平台

## 📋 项目概述

本指南将帮助您将IdleGame的Node.js后端服务部署到微信云开发平台，支持小程序、H5等多端访问。

### 🎯 支持的部署方案

| 方案 | 成本 | 适用场景 | 配置 |
|------|------|----------|------|
| 🆓 **免费版** | 0元/月 | 日活<500人 | 0.25核+0.5GB |
| 💼 **标准版** | 15-200元/月 | 中型项目 | 1核+2GB起 |
| 🚀 **企业版** | 200+元/月 | 大型项目 | 自定义配置 |

## 🏗️ 技术架构

```
微信小程序/H5前端
        ↓
    微信云托管
   (Node.js后端)
        ↓
   云数据库/MongoDB
        ↓
     云存储/CDN
```

### 核心特性
- ✅ **完整Node.js支持**: 直接部署现有Express应用
- ✅ **自动扩缩容**: 根据负载自动调整实例数量
- ✅ **免费额度**: 小型项目完全免费
- ✅ **数据库迁移**: 支持MongoDB到云数据库的迁移
- ✅ **监控告警**: 完整的监控和日志系统
- ✅ **CI/CD支持**: 自动化部署流程

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装腾讯云CLI工具
npm install -g @cloudbase/cli

# 安装云开发SDK
cd backend
npm install @cloudbase/node-sdk

# 登录腾讯云
tcb login
```

### 2. 获取环境信息
1. 打开微信开发者工具
2. 创建小程序项目（如果没有）
3. 点击"云开发"，开通云开发服务
4. 记录环境ID（格式：your-env-xxxxxx）

### 3. 配置环境变量
```bash
# 复制环境变量模板
cp .env.wechat.free.example .env.wechat

# 编辑环境变量，设置您的环境ID
nano .env.wechat
```

### 4. 一键部署
```bash
# 设置执行权限
chmod +x wechat-cloud/deploy.sh

# 🆓 免费版部署（推荐）
./wechat-cloud/deploy.sh prod v1.0.0 free

# 💼 标准版部署
# ./wechat-cloud/deploy.sh prod v1.0.0 standard
```

## 📁 文件结构
```
backend/
├── wechat-cloud/
│   ├── README.md                    # 本文档
│   ├── QUICK_START.md              # 5分钟快速部署
│   ├── FREE_TIER_GUIDE.md          # 免费版详细指南
│   ├── DEPLOYMENT_CHECKLIST.md    # 部署检查清单
│   ├── container.config.json      # 标准版配置
│   ├── container.config.free.json # 免费版配置
│   ├── Dockerfile.wechat          # 云托管Dockerfile
│   ├── deploy.sh                   # 自动化部署脚本
│   └── database-migration/         # 数据库迁移工具
│       └── migrate-to-cloudbase.js
├── src/config/
│   └── cloudbase.ts               # 云开发数据库适配器
├── .env.wechat.example            # 标准版环境变量模板
└── .env.wechat.free.example       # 免费版环境变量模板
```

## 📋 详细部署步骤

### 第一步：开通微信云开发
1. **登录微信开发者工具**
   - 下载并安装微信开发者工具
   - 使用微信扫码登录

2. **创建小程序项目**
   ```
   项目名称：IdleGame
   目录：选择一个空目录
   AppID：使用您的小程序AppID
   开发模式：小程序
   ```

3. **开通云开发服务**
   - 点击工具栏的"云开发"按钮
   - 选择"开通云开发"
   - 选择"按量付费"模式
   - 记录环境ID（如：idlegame-xxx）

### 第二步：配置云托管
1. **进入云托管控制台**
   - 在微信开发者工具中点击"云开发" → "云托管"
   - 点击"新建服务"

2. **配置服务参数**
   ```
   服务名称：idlegame-backend
   部署方式：容器镜像
   端口：3000
   CPU：0.25核（免费版）或 1核（标准版）
   内存：0.5GB（免费版）或 2GB（标准版）
   ```

### 第三步：准备本地环境
1. **安装必要工具**
   ```bash
   # 安装腾讯云CLI
   npm install -g @cloudbase/cli

   # 验证安装
   tcb --version
   ```

2. **登录腾讯云**
   ```bash
   # 登录（会打开浏览器）
   tcb login

   # 验证登录状态
   tcb auth list
   ```

3. **安装项目依赖**
   ```bash
   cd backend
   npm install @cloudbase/node-sdk
   ```

### 第四步：配置项目
1. **选择配置模板**
   ```bash
   # 免费版配置（推荐新手）
   cp .env.wechat.free.example .env.wechat

   # 标准版配置
   # cp .env.wechat.example .env.wechat
   ```

2. **编辑环境变量**
   ```bash
   # 编辑配置文件
   nano .env.wechat

   # 必须修改的配置项：
   TCB_ENV=your-env-xxxxxx  # 替换为您的环境ID
   JWT_SECRET=your-super-secret-key  # 设置JWT密钥
   ```

### 第五步：执行部署
1. **设置脚本权限**
   ```bash
   chmod +x wechat-cloud/deploy.sh
   ```

2. **执行部署命令**
   ```bash
   # 免费版部署
   ./wechat-cloud/deploy.sh prod v1.0.0 free

   # 标准版部署
   # ./wechat-cloud/deploy.sh prod v1.0.0 standard
   ```

3. **等待部署完成**
   - 脚本会自动构建Docker镜像
   - 推送到云托管平台
   - 启动服务并进行健康检查

### 第六步：验证部署
1. **检查服务状态**
   ```bash
   # 查看服务信息
   tcb run describe --name idlegame-backend-free

   # 查看服务日志
   tcb run logs --name idlegame-backend-free --tail
   ```

2. **测试API接口**
   ```bash
   # 获取服务URL（从上面的describe命令输出）
   SERVICE_URL="https://your-service-url"

   # 测试健康检查
   curl $SERVICE_URL/api/health

   # 测试API文档
   curl $SERVICE_URL/api-docs
   ```

## 🔧 数据库迁移（可选）

如果您需要从现有MongoDB迁移数据：

### 1. 配置迁移环境
```bash
export MONGODB_URI="mongodb://localhost:27017/idlegame"
export TCB_ENV="your-env-xxxxxx"
```

### 2. 执行数据迁移
```bash
cd wechat-cloud/database-migration
node migrate-to-cloudbase.js
```

### 3. 验证迁移结果
- 检查云数据库控制台
- 验证数据完整性
- 测试应用功能

### 🎉 微信云开发免费额度
微信云开发为每个环境提供丰富的免费额度：

#### 云托管免费额度
| 资源类型 | 免费额度 | 说明 |
|----------|----------|------|
| CPU时间 | 1000 GHz·秒/月 | 约等于0.5核运行24小时 |
| 内存时间 | 2000 GB·秒/月 | 约等于1GB内存运行24小时 |
| 流量 | 1GB/月 | 外网出流量 |

#### 云数据库免费额度
| 资源类型 | 免费额度 | 说明 |
|----------|----------|------|
| 存储容量 | 2GB | 数据存储空间 |
| 读操作 | 5万次/天 | 查询、聚合等操作 |
| 写操作 | 3万次/天 | 增删改操作 |
| 集合数量 | 无限制 | 数据表数量 |

#### 云存储免费额度
| 资源类型 | 免费额度 | 说明 |
|----------|----------|------|
| 存储容量 | 5GB | 文件存储空间 |
| 下载流量 | 10GB/月 | CDN流量 |
| 上传流量 | 10GB/月 | 文件上传流量 |
| 请求次数 | 150万次/月 | API调用次数 |

#### 云函数免费额度
| 资源类型 | 免费额度 | 说明 |
|----------|----------|------|
| 调用次数 | 100万次/月 | 函数执行次数 |
| 资源使用量 | 40万GBs/月 | 内存×执行时间 |
| 外网出流量 | 1GB/月 | 函数外网访问 |

### 💡 免费额度优化策略

#### 方案A：纯免费方案（推荐小型项目）
```
配置建议：
- 云托管：0.25核 + 0.5GB内存
- 数据库：使用云数据库免费额度
- 存储：使用云存储免费额度
- 适用：日活<500的小型游戏

预计成本：完全免费！
```

#### 方案B：混合方案（推荐中型项目）
```
配置建议：
- 云托管：0.5核 + 1GB内存（部分免费）
- 数据库：云数据库 + 少量付费
- 存储：云存储免费额度
- 适用：日活500-2000的中型游戏

预计成本：10-30元/月
```

### 付费价格（超出免费额度后）
| 配置 | 价格 | 说明 |
|------|------|------|
| 1核2GB | 0.055元/小时 | 基础配置 |
| 2核4GB | 0.11元/小时 | 推荐配置 |
| 4核8GB | 0.22元/小时 | 高性能配置 |

### 云数据库付费价格
| 操作类型 | 价格 | 说明 |
|----------|------|------|
| 读操作 | 0.015元/万次 | 查询、聚合等 |
| 写操作 | 0.05元/万次 | 增删改操作 |
| 存储 | 0.07元/GB/天 | 数据存储费用 |

### 实际成本预估（考虑免费额度）
```
小型游戏（500日活）：
- 云托管：完全免费
- 云数据库：完全免费
- 云存储：完全免费
- 总计：0元/月 🎉

中型游戏（2000日活）：
- 云托管：免费额度 + 10元
- 云数据库：免费额度 + 5元
- 云存储：完全免费
- 总计：约15元/月

大型游戏（1万日活）：
- 云托管：免费额度 + 50元
- 云数据库：免费额度 + 80元
- 云存储：免费额度 + 10元
- 总计：约140元/月
```

## 🚨 常见问题解决

### 1. 部署失败
**问题**：Docker镜像构建失败
**解决**：
```bash
# 检查Dockerfile语法
docker build -f wechat-cloud/Dockerfile.wechat -t test .

# 查看构建日志
docker build --no-cache -f wechat-cloud/Dockerfile.wechat -t test .
```

### 2. 服务无法启动
**问题**：容器启动后立即退出
**解决**：
```bash
# 查看服务日志
tcb run logs --name idlegame-backend

# 检查健康检查
curl -f http://your-service-url/api/health
```

### 3. 数据库连接失败
**问题**：无法连接云数据库
**解决**：
```typescript
// 检查环境变量配置
console.log('TCB_ENV:', process.env.TCB_ENV);
console.log('WECHAT_CLOUD:', process.env.WECHAT_CLOUD);

// 测试数据库连接
const db = CloudBaseConfig.getInstance().getDatabase();
const result = await db.collection('test').limit(1).get();
console.log('数据库连接测试:', result);
```

### 4. 性能问题
**问题**：响应时间过长
**解决**：
- 增加实例数量
- 升级实例配置
- 优化数据库查询
- 添加缓存层

## 📊 监控和运维

### 监控指标
在云托管控制台可以查看：
- **CPU使用率**：实例CPU使用情况
- **内存使用率**：实例内存使用情况
- **请求数量**：API请求统计
- **响应时间**：API响应时间分布
- **错误率**：4xx/5xx错误统计

### 日志管理
```bash
# 查看实时日志
tcb run logs --name idlegame-backend --tail

# 查看历史日志
tcb run logs --name idlegame-backend --since 2h

# 下载日志文件
tcb run logs --name idlegame-backend --download
```

### 告警配置
建议配置以下告警：
- CPU使用率 > 80%
- 内存使用率 > 90%
- 错误率 > 5%
- 响应时间 > 2秒

## 🔄 CI/CD集成

### GitHub Actions示例
```yaml
name: Deploy to WeChat Cloud
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2

      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Deploy to WeChat Cloud
        run: |
          npm install -g @cloudbase/cli
          tcb login --key ${{ secrets.TCB_SECRET_KEY }}
          ./wechat-cloud/deploy.sh prod ${{ github.sha }}
```

## 📞 技术支持

### 官方资源
1. **微信云开发文档**：https://developers.weixin.qq.com/miniprogram/dev/wxcloud/
2. **云托管文档**：https://developers.weixin.qq.com/miniprogram/dev/wxcloud/guide/container/
3. **API参考**：https://developers.weixin.qq.com/miniprogram/dev/wxcloud/reference-server-api/

### 社区支持
1. **微信开发者社区**：https://developers.weixin.qq.com/community/
2. **GitHub示例项目**：搜索"wechat cloud base"
3. **技术交流群**：加入微信开发者技术群

### 紧急支持
如果遇到紧急问题：
1. 查看云托管控制台的服务状态
2. 检查实时日志和监控指标
3. 联系微信云开发技术支持
4. 如需要，可以快速回滚到上一个版本

---

**🎉 恭喜！您现在已经掌握了将IdleGame后端部署到微信云开发的完整流程！**

按照这个指南，您可以：
- ✅ 将现有Node.js后端无缝迁移到微信云托管
- ✅ 实现数据库从MongoDB到云数据库的迁移
- ✅ 建立完整的监控和运维体系
- ✅ 实现自动化部署和CI/CD集成

## 🎮 游戏功能适配


## 📚 相关文档

### 快速链接
- 📖 [5分钟快速部署](./QUICK_START.md)
- 🆓 [免费版详细指南](./FREE_TIER_GUIDE.md)
- ✅ [部署检查清单](./DEPLOYMENT_CHECKLIST.md)

### 官方文档
- [微信云开发官方文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/)
- [云托管使用指南](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/guide/container/)
- [云数据库API参考](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/reference-server-api/database/)

### 社区资源
- [微信开发者社区](https://developers.weixin.qq.com/community/)
- [云开发示例项目](https://github.com/TencentCloudBase)
- [技术交流群](https://developers.weixin.qq.com/community/develop/mixflow)

---

**🎉 恭喜！您现在拥有了完整的IdleGame微信云开发部署方案！**

**下一步建议**：
1. 📖 阅读 [快速开始指南](./QUICK_START.md) 进行5分钟部署
2. 🆓 使用免费版验证游戏概念
3. 📊 根据监控数据优化性能
4. 🚀 用户增长后升级到标准版
5. 🔄 建立完整的CI/CD流程

**技术支持**：如有问题，请查看常见问题解答或联系微信云开发技术支持。
