{"metadata": {"generatedAt": "2025-07-26T03:03:09.854Z", "projectPath": "D:\\COCOS\\Projects\\IdleGame\\COCOS_IdelGame", "totalExecutionTime": 263, "testFramework": "AI-Testing-Framework-v1.0"}, "summary": {"totalTests": 12, "passedTests": 10, "failedTests": 2, "successRate": 83.3, "executionTime": 263}, "results": [{"name": "项目结构检查", "status": "passed", "message": "项目结构完整 (发现 6 个必需项目)", "details": {"found": ["project.json", "assets", "settings", "assets/scripts", "assets/scenes", "assets/resources"], "missing": []}}, {"name": "Cocos Creator配置", "status": "passed", "message": "配置检查 4/4 项通过 (引擎: cocos-creator)", "details": {"config": {"hasVersion": true, "hasEngine": true, "hasName": true, "validEngine": true}, "raw": {"engine": "cocos-creator", "packages": "packages", "version": "3.8.6", "name": "<PERSON><PERSON><PERSON><PERSON>", "id": "d7d09da7-3476-4f62-91cb-61d527ea4910", "settings": {"assets": {"server": "", "bundleVer": "1.0.0"}, "rendering": {"renderMode": 0, "renderPipeline": "builtin-forward"}, "physics": {"enabled": true, "gravity": {"x": 0, "y": -10}}, "scripting": {"typescript": true, "allowDeclareKeyword": true}, "animation": {"enabled": true}, "audio": {"enabled": true}, "network": {"enabled": true}}, "platforms": {"wechatgame": {"enabled": true, "orientation": "portrait", "separateEngine": true, "startScene": "Launch"}, "bytedance-mini-game": {"enabled": true, "orientation": "portrait", "separateEngine": true, "startScene": "Launch"}}}}}, {"name": "脚本文件分析", "status": "passed", "message": "发现 72 个脚本文件，57 个组件，55 个系统", "details": {"totalFiles": 72, "components": 57, "systems": 55, "totalLines": 25701, "fileTypes": {".ts": 72}, "averageLinesPerFile": 357}}, {"name": "资源文件检查", "status": "passed", "message": "发现 6 个资源文件，总大小 55.98 KB", "details": {"assets": {"images": 0, "prefabs": 0, "scenes": 3, "data": 3, "audio": 0}, "totalSize": 57325, "totalAssets": 6}}, {"name": "后端服务器状态", "status": "passed", "message": "服务器运行正常 (状态码: 200)", "details": {"statusCode": 200, "response": "{\"status\":\"ok\",\"message\":\"Test server is running\",\"timestamp\":\"2025-07-26T03:03:09.681Z\",\"uptime\":3.2183385}"}}, {"name": "游戏逻辑验证", "status": "passed", "message": "游戏逻辑测试 5/5 项通过", "details": {"tests": [{"name": "角色创建", "result": true}, {"name": "战斗系统", "result": true}, {"name": "技能系统", "result": true}, {"name": "装备系统", "result": true}, {"name": "经验计算", "result": true}], "passed": 5, "total": 5}}, {"name": "性能基准测试", "status": "passed", "message": "基准测试完成，耗时 107ms", "details": {"duration": 107, "benchmark": "basic", "threshold": 200}}, {"name": "代码质量分析", "status": "failed", "message": "代码质量: needs-improvement，发现 65 个问题", "details": {"totalFiles": 72, "totalLines": 25701, "issuesFound": 65, "quality": "needs-improvement"}}, {"name": "组件依赖检查", "status": "passed", "message": "分析了 67 个依赖关系，发现 0 个问题", "details": {"dependencies": {"cc": ["TestRunner.ts", "AudioManager.ts", "BaseManager.ts", "ConfigManager.ts", "ConfigManager.ts", "EventManager.ts", "GameManager.ts", "InputManager.ts", "ResourceManager.ts", "SceneManager.ts", "UIManager.ts", "HttpClient.ts", "NetworkManager.ts", "WebSocketClient.ts", "BattleScene.ts", "BattleSceneClean.ts", "LaunchScene.ts", "MainScene.ts", "Character.ts", "BasicTest.ts", "ConfigManagerQuickTest.ts", "ConfigManagerTest.ts", "Day2IntegrationTest.ts", "EmergencyKeyboardTest.ts", "KeyboardInputTest.ts", "ManagerTest.ts", "MobileIdleUITest.ts", "NetworkTest.ts", "PerformanceBenchmarkTest.ts", "QuickConfigVerify.ts", "SimpleConfigTest.ts", "SimpleKeyboardTest.ts", "SimpleLaunchTest.ts", "SimpleTest.ts", "SimpleUIPanelRegistrar.ts", "StandaloneTest.ts", "SystemIntegrationTest.ts", "TestManager.ts", "UIFrameworkValidator.ts", "UIInteractionTest.ts", "UISystemTest.ts", "BaseUIComponent.ts", "BaseUIPanel.ts", "EnhancedBasePanel.ts", "SceneSwitchButtons.ts", "SkillBar.ts", "SkillBarUI.ts", "SkillDisplayBar.ts", "SkillSlot.ts", "UIButton.ts", "UIDialog.ts", "UIPanel.ts", "UIPanelConfigurator.ts", "MobileUIInputHandler.ts", "UIInputHandler.ts", "InventoryPanel.ts", "SkillPanel.ts", "SkillSelectionPanel.ts", "UITypes.ts"], "../../managers/GameManager": ["TestRunner.ts"], "../../data/GameTypes": ["TestRunner.ts", "Character.ts"], "./BaseManager": ["AudioManager.ts", "ConfigManager.ts", "EventManager.ts", "GameManager.ts", "index.ts", "InputManager.ts", "ResourceManager.ts", "SceneManager.ts", "UIManager.ts"], "../config/interfaces/ISkillData": ["ConfigManager.ts", "ConfigManagerTest.ts"], "../config/interfaces/IEntityData": ["ConfigManager.ts"], "../config/interfaces/IItemData": ["ConfigManager.ts"], "../config/interfaces/IQuestData": ["ConfigManager.ts"], "../config/interfaces/IRewardData": ["ConfigManager.ts"], "./types/ManagerTypes": ["EventManager.ts", "GameManager.ts", "ResourceManager.ts", "SceneManager.ts"], "./GameManager": ["index.ts"], "./SceneManager": ["index.ts"], "./EventManager": ["index.ts", "UIManager.ts"], "./ResourceManager": ["index.ts"], "./AudioManager": ["index.ts"], "./InputManager": ["index.ts"], "../network/NetworkManager": ["index.ts", "BattleScene.ts", "NetworkTest.ts"], "./ConfigManager": ["index.ts"], "./UIManager": ["index.ts"], "./NetworkManager": ["ApiClient.ts", "index.ts"], "../data/ISkillData": ["ApiClient.ts", "DataSyncManager.ts"], "../data/IEntityData": ["ApiClient.ts", "DataSyncManager.ts"], "../data/IItemData": ["ApiClient.ts", "DataSyncManager.ts"], "../utils/Logger": ["ApiClient.ts", "DataSyncManager.ts"], "../managers/BaseManager": ["DataSyncManager.ts", "NetworkManager.ts"], "./ApiClient": ["DataSyncManager.ts"], "../managers/EventManager": ["DataSyncManager.ts", "HttpClient.ts", "NetworkManager.ts", "WebSocketClient.ts", "BattleScene.ts", "BattleSceneClean.ts", "NetworkTest.ts", "SystemIntegrationTest.ts", "UIInteractionTest.ts", "UISystemTest.ts"], "./HttpClient": ["index.ts", "NetworkManager.ts"], "./WebSocketClient": ["index.ts", "NetworkManager.ts"], "../network/HttpClient": ["BattleScene.ts", "NetworkTest.ts"], "../network/WebSocketClient": ["BattleScene.ts", "NetworkTest.ts"], "../network/index": ["BattleScene.ts", "NetworkTest.ts"], "../network/types/NetworkTypes": ["BattleScene.ts", "NetworkTest.ts", "SystemIntegrationTest.ts"], "../managers/index": ["BattleScene.ts", "SystemIntegrationTest.ts"], "../managers/UIManager": ["BattleSceneClean.ts", "MobileIdleUITest.ts", "SimpleUIPanelRegistrar.ts", "UIFrameworkValidator.ts", "UIInteractionTest.ts", "UISystemTest.ts"], "../ui/input/MobileUIInputHandler": ["BattleSceneClean.ts", "MobileIdleUITest.ts"], "../test/SimpleUIPanelRegistrar": ["BattleSceneClean.ts"], "../managers": ["LaunchScene.ts", "EmergencyKeyboardTest.ts", "ManagerTest.ts", "StandaloneTest.ts"], "../managers/ConfigManager": ["ConfigManagerQuickTest.ts", "ConfigManagerTest.ts", "QuickConfigVerify.ts", "SimpleConfigTest.ts"], "../managers/GameManager": ["Day2IntegrationTest.ts", "SimpleTest.ts"], "../systems/characters/Character": ["Day2IntegrationTest.ts"], "../data/GameTypes": ["Day2IntegrationTest.ts"], "../ui/types/UITypes": ["MobileIdleUITest.ts", "SimpleUIPanelRegistrar.ts", "UIFrameworkValidator.ts", "UIInteractionTest.ts", "UISystemTest.ts"], "../ui/components/SkillDisplayBar": ["MobileIdleUITest.ts"], "./TestManager": ["PerformanceBenchmarkTest.ts"], "../ui/base/BaseUIComponent": ["UIFrameworkValidator.ts"], "../ui/base/EnhancedBasePanel": ["UIFrameworkValidator.ts"], "../ui/components/SkillBarUI": ["UIFrameworkValidator.ts"], "../ui/components/SkillSlot": ["UIFrameworkValidator.ts"], "../ui/panels/SkillSelectionPanel": ["UIFrameworkValidator.ts"], "../ui/input/UIInputHandler": ["UISystemTest.ts"], "../../managers/EventManager": ["BaseUIComponent.ts", "EnhancedBasePanel.ts", "SkillBar.ts", "SkillBarUI.ts", "SkillDisplayBar.ts", "MobileUIInputHandler.ts", "UIInputHandler.ts", "SkillSelectionPanel.ts"], "../types/UITypes": ["EnhancedBasePanel.ts", "UIButton.ts", "UIDialog.ts", "UIPanel.ts", "UIPanelConfigurator.ts", "MobileUIInputHandler.ts", "UIInputHandler.ts", "InventoryPanel.ts", "SkillPanel.ts", "SkillSelectionPanel.ts"], "./UIButton": ["SkillBar.ts", "SkillDisplayBar.ts", "UIDialog.ts", "UIPanel.ts"], "../panels/SkillPanel": ["SkillBar.ts", "SkillBarUI.ts", "SkillDisplayBar.ts", "SkillSlot.ts"], "../../managers/ConfigManager": ["SkillBar.ts", "InventoryPanel.ts", "SkillPanel.ts", "SkillSelectionPanel.ts"], "../base/BaseUIComponent": ["SkillBarUI.ts", "SkillSlot.ts"], "../../managers/AudioManager": ["UIButton.ts"], "./UIPanel": ["UIDialog.ts"], "../base/BaseUIPanel": ["UIPanel.ts"], "../../managers/UIManager": ["UIPanelConfigurator.ts", "MobileUIInputHandler.ts", "UIInputHandler.ts"], "../components/UIPanel": ["InventoryPanel.ts", "SkillPanel.ts"], "../components/UIButton": ["InventoryPanel.ts", "SkillPanel.ts"], "../../config/interfaces/IItemData": ["InventoryPanel.ts"], "../../config/interfaces/ISkillData": ["SkillPanel.ts"], "../base/EnhancedBasePanel": ["SkillSelectionPanel.ts"], "./SkillPanel": ["SkillSelectionPanel.ts"]}, "missingDeps": []}}, {"name": "场景完整性验证", "status": "passed", "message": "检查了 3 个场景，3 个有效", "details": {"scenes": [{"name": "Battle.scene", "hasNodes": false, "nodeCount": 0, "hasComponents": true}, {"name": "Launch.scene", "hasNodes": false, "nodeCount": 0, "hasComponents": true}, {"name": "Main.scene", "hasNodes": false, "nodeCount": 0, "hasComponents": true}], "totalScenes": 3, "validScenes": 3}}, {"name": "资源引用检查", "status": "failed", "message": "检查了 110 个资源，发现 1 个损坏引用", "details": {"totalAssets": 110, "brokenReferences": [{"file": "Battle.scene", "uuid": "461e160d-2e32-4c37-85a8-9c8627d2b677"}]}}, {"name": "武侠系统测试", "status": "passed", "message": "武侠系统实现度 5/5，发现 31 个相关文件", "details": {"systems": {"hasCharacterSystem": true, "hasSkillSystem": true, "hasBattleSystem": true, "hasCultivationSystem": true, "hasSectSystem": true}, "relatedFiles": 31, "implementedSystems": 5}}], "recommendations": ["⚠️ 发现 2 个问题需要解决：", "   • 代码质量分析: 代码质量: needs-improvement，发现 65 个问题", "   • 资源引用检查: 检查了 110 个资源，发现 1 个损坏引用"]}