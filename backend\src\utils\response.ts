import { Response } from 'express';

/**
 * 标准API响应接口
 */
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  timestamp: string;
  requestId?: string;
}

/**
 * 分页响应接口
 */
export interface PaginatedResponse<T = any> extends ApiResponse<{
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}> {}

/**
 * 分页信息接口
 */
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

/**
 * 发送标准API响应
 */
export function sendResponse<T = any>(
  res: Response,
  statusCode: number,
  message: string,
  data?: T,
  requestId?: string
): void {
  const response: ApiResponse<T> = {
    success: statusCode < 400,
    message,
    data,
    timestamp: new Date().toISOString(),
    requestId: requestId || (res.locals.requestId as string),
  };

  res.status(statusCode).json(response);
}

/**
 * 发送成功响应
 */
export function sendSuccess<T = any>(
  res: Response,
  message: string = '操作成功',
  data?: T,
  requestId?: string
): void {
  sendResponse(res, 200, message, data, requestId);
}

/**
 * 发送创建成功响应
 */
export function sendCreated<T = any>(
  res: Response,
  message: string = '创建成功',
  data?: T,
  requestId?: string
): void {
  sendResponse(res, 201, message, data, requestId);
}

/**
 * 发送分页响应
 */
export function sendPaginatedResponse<T = any>(
  res: Response,
  message: string,
  items: T[],
  pagination: PaginationInfo,
  requestId?: string
): void {
  const response: PaginatedResponse<T> = {
    success: true,
    message,
    data: {
      items,
      pagination,
    },
    timestamp: new Date().toISOString(),
    requestId: requestId || (res.locals.requestId as string),
  };

  res.status(200).json(response);
}

/**
 * 发送错误响应
 */
export function sendError(
  res: Response,
  statusCode: number,
  message: string,
  error?: any,
  requestId?: string
): void {
  const response: ApiResponse = {
    success: false,
    message,
    timestamp: new Date().toISOString(),
    requestId: requestId || (res.locals.requestId as string),
  };

  // 在开发环境中包含错误详情
  if (process.env['NODE_ENV'] === 'development' && error) {
    (response as any).error = error;
  }

  res.status(statusCode).json(response);
}

/**
 * 发送验证错误响应
 */
export function sendValidationError(
  res: Response,
  message: string = '参数验证失败',
  errors?: any[],
  requestId?: string
): void {
  const response: ApiResponse = {
    success: false,
    message,
    timestamp: new Date().toISOString(),
    requestId: requestId || (res.locals.requestId as string),
  };

  if (errors && errors.length > 0) {
    (response as any).errors = errors;
  }

  res.status(400).json(response);
}

/**
 * 发送未授权响应
 */
export function sendUnauthorized(
  res: Response,
  message: string = '未授权访问',
  requestId?: string
): void {
  sendError(res, 401, message, undefined, requestId);
}

/**
 * 发送禁止访问响应
 */
export function sendForbidden(
  res: Response,
  message: string = '权限不足',
  requestId?: string
): void {
  sendError(res, 403, message, undefined, requestId);
}

/**
 * 发送资源未找到响应
 */
export function sendNotFound(
  res: Response,
  message: string = '资源未找到',
  requestId?: string
): void {
  sendError(res, 404, message, undefined, requestId);
}

/**
 * 发送冲突响应
 */
export function sendConflict(
  res: Response,
  message: string = '资源冲突',
  requestId?: string
): void {
  sendError(res, 409, message, undefined, requestId);
}

/**
 * 发送服务器错误响应
 */
export function sendInternalError(
  res: Response,
  message: string = '服务器内部错误',
  error?: any,
  requestId?: string
): void {
  sendError(res, 500, message, error, requestId);
}

/**
 * 计算分页信息
 */
export function calculatePagination(
  page: number,
  limit: number,
  total: number
): PaginationInfo {
  const pages = Math.ceil(total / limit);
  
  return {
    page: Math.max(1, page),
    limit: Math.max(1, limit),
    total: Math.max(0, total),
    pages: Math.max(0, pages),
  };
}

/**
 * 解析分页参数
 */
export function parsePaginationParams(query: any): { page: number; limit: number } {
  const page = Math.max(1, parseInt(query.page) || 1);
  const limit = Math.min(100, Math.max(1, parseInt(query.limit) || 20));
  
  return { page, limit };
}

/**
 * 解析排序参数
 */
export function parseSortParams(query: any, allowedFields: string[] = []): any {
  const { sortBy, sortOrder } = query;
  
  if (!sortBy || !allowedFields.includes(sortBy)) {
    return {};
  }
  
  const order = sortOrder === 'asc' ? 1 : -1;
  return { [sortBy]: order };
}

/**
 * 构建查询过滤器
 */
export function buildQueryFilter(query: any, allowedFilters: string[] = []): any {
  const filter: any = {};
  
  allowedFilters.forEach(field => {
    if (query[field] !== undefined) {
      filter[field] = query[field];
    }
  });
  
  return filter;
}

/**
 * 响应工具类
 */
export class ResponseHelper {
  /**
   * 发送标准响应
   */
  static send<T = any>(
    res: Response,
    statusCode: number,
    message: string,
    data?: T,
    requestId?: string
  ): void {
    sendResponse(res, statusCode, message, data, requestId);
  }

  /**
   * 发送成功响应
   */
  static success<T = any>(
    res: Response,
    message: string = '操作成功',
    data?: T
  ): void {
    sendSuccess(res, message, data);
  }

  /**
   * 发送创建成功响应
   */
  static created<T = any>(
    res: Response,
    message: string = '创建成功',
    data?: T
  ): void {
    sendCreated(res, message, data);
  }

  /**
   * 发送分页响应
   */
  static paginated<T = any>(
    res: Response,
    message: string,
    items: T[],
    pagination: PaginationInfo
  ): void {
    sendPaginatedResponse(res, message, items, pagination);
  }

  /**
   * 发送错误响应
   */
  static error(
    res: Response,
    statusCode: number,
    message: string,
    error?: any
  ): void {
    sendError(res, statusCode, message, error);
  }

  /**
   * 发送验证错误响应
   */
  static validationError(
    res: Response,
    message: string = '参数验证失败',
    errors?: any[]
  ): void {
    sendValidationError(res, message, errors);
  }

  /**
   * 发送未授权响应
   */
  static unauthorized(
    res: Response,
    message: string = '未授权访问'
  ): void {
    sendUnauthorized(res, message);
  }

  /**
   * 发送禁止访问响应
   */
  static forbidden(
    res: Response,
    message: string = '权限不足'
  ): void {
    sendForbidden(res, message);
  }

  /**
   * 发送资源未找到响应
   */
  static notFound(
    res: Response,
    message: string = '资源未找到'
  ): void {
    sendNotFound(res, message);
  }

  /**
   * 发送冲突响应
   */
  static conflict(
    res: Response,
    message: string = '资源冲突'
  ): void {
    sendConflict(res, message);
  }

  /**
   * 发送服务器错误响应
   */
  static internalError(
    res: Response,
    message: string = '服务器内部错误',
    error?: any
  ): void {
    sendInternalError(res, message, error);
  }
}
