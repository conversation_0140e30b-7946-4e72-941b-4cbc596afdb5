#!/bin/bash

# IdleGame 部署脚本
# 用于自动化部署应用到生产环境

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="idlegame"
DOCKER_COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env"
BACKUP_DIR="./backups"
LOG_FILE="./deploy.log"

# 函数：打印彩色消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}" | tee -a $LOG_FILE
}

# 函数：检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        print_message $RED "错误: $1 命令未找到，请先安装"
        exit 1
    fi
}

# 函数：检查文件是否存在
check_file() {
    if [ ! -f "$1" ]; then
        print_message $RED "错误: 文件 $1 不存在"
        exit 1
    fi
}

# 函数：创建备份
create_backup() {
    print_message $BLUE "创建数据备份..."
    
    # 创建备份目录
    mkdir -p $BACKUP_DIR
    
    # 备份数据库
    if docker ps | grep -q "${PROJECT_NAME}-mongodb"; then
        BACKUP_FILE="${BACKUP_DIR}/mongodb-backup-$(date +%Y%m%d-%H%M%S).gz"
        docker exec ${PROJECT_NAME}-mongodb mongodump --archive --gzip > $BACKUP_FILE
        print_message $GREEN "数据库备份完成: $BACKUP_FILE"
    fi
    
    # 备份配置文件
    if [ -f "$ENV_FILE" ]; then
        cp $ENV_FILE "${BACKUP_DIR}/env-backup-$(date +%Y%m%d-%H%M%S)"
        print_message $GREEN "配置文件备份完成"
    fi
}

# 函数：检查环境
check_environment() {
    print_message $BLUE "检查部署环境..."
    
    # 检查必要的命令
    check_command "docker"
    check_command "docker-compose"
    check_command "git"
    
    # 检查必要的文件
    check_file $DOCKER_COMPOSE_FILE
    
    # 检查环境变量文件
    if [ ! -f "$ENV_FILE" ]; then
        print_message $YELLOW "警告: .env 文件不存在，将使用默认配置"
        if [ -f ".env.example" ]; then
            print_message $BLUE "复制 .env.example 到 .env"
            cp .env.example .env
        fi
    fi
    
    # 检查Docker服务
    if ! docker info &> /dev/null; then
        print_message $RED "错误: Docker 服务未运行"
        exit 1
    fi
    
    print_message $GREEN "环境检查通过"
}

# 函数：拉取最新代码
pull_latest_code() {
    print_message $BLUE "拉取最新代码..."
    
    # 检查是否有未提交的更改
    if [ -n "$(git status --porcelain)" ]; then
        print_message $YELLOW "警告: 存在未提交的更改"
        read -p "是否继续部署? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_message $RED "部署已取消"
            exit 1
        fi
    fi
    
    # 拉取最新代码
    git fetch origin
    git pull origin main
    
    print_message $GREEN "代码更新完成"
}

# 函数：构建镜像
build_images() {
    print_message $BLUE "构建Docker镜像..."
    
    # 构建镜像
    docker-compose build --no-cache
    
    print_message $GREEN "镜像构建完成"
}

# 函数：停止旧服务
stop_services() {
    print_message $BLUE "停止现有服务..."
    
    # 停止服务
    docker-compose down
    
    print_message $GREEN "服务已停止"
}

# 函数：启动服务
start_services() {
    print_message $BLUE "启动服务..."
    
    # 启动基础服务
    docker-compose up -d mongodb redis
    
    # 等待数据库启动
    print_message $BLUE "等待数据库启动..."
    sleep 30
    
    # 启动应用服务
    docker-compose up -d backend frontend
    
    # 如果启用监控，启动监控服务
    if [ "$ENABLE_MONITORING" = "true" ]; then
        print_message $BLUE "启动监控服务..."
        docker-compose --profile monitoring up -d
    fi
    
    print_message $GREEN "服务启动完成"
}

# 函数：健康检查
health_check() {
    print_message $BLUE "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        print_message $BLUE "健康检查尝试 $attempt/$max_attempts"
        
        # 检查后端健康状态
        if curl -f http://localhost:3000/api/health &> /dev/null; then
            print_message $GREEN "后端服务健康检查通过"
            break
        fi
        
        if [ $attempt -eq $max_attempts ]; then
            print_message $RED "健康检查失败，服务可能未正常启动"
            docker-compose logs backend
            exit 1
        fi
        
        sleep 10
        ((attempt++))
    done
    
    # 检查前端
    if curl -f http://localhost/health &> /dev/null; then
        print_message $GREEN "前端服务健康检查通过"
    else
        print_message $YELLOW "前端健康检查失败，但继续部署"
    fi
}

# 函数：清理旧镜像
cleanup() {
    print_message $BLUE "清理旧镜像和容器..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的容器
    docker container prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    print_message $GREEN "清理完成"
}

# 函数：显示部署信息
show_deployment_info() {
    print_message $GREEN "部署完成！"
    echo
    echo "=== 部署信息 ==="
    echo "前端地址: http://localhost"
    echo "后端API: http://localhost:3000/api"
    echo "健康检查: http://localhost:3000/api/health"
    
    if [ "$ENABLE_MONITORING" = "true" ]; then
        echo "Grafana监控: http://localhost:3001"
        echo "Prometheus: http://localhost:9090"
    fi
    
    echo
    echo "=== 服务状态 ==="
    docker-compose ps
    
    echo
    echo "=== 日志查看 ==="
    echo "查看所有日志: docker-compose logs"
    echo "查看后端日志: docker-compose logs backend"
    echo "查看前端日志: docker-compose logs frontend"
}

# 函数：显示帮助信息
show_help() {
    echo "IdleGame 部署脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -b, --backup            创建备份"
    echo "  -m, --monitoring        启用监控服务"
    echo "  -p, --production        生产环境部署"
    echo "  -d, --development       开发环境部署"
    echo "  --no-pull              跳过代码拉取"
    echo "  --no-build             跳过镜像构建"
    echo "  --no-cleanup           跳过清理"
    echo
    echo "示例:"
    echo "  $0                      # 标准部署"
    echo "  $0 -m                   # 启用监控的部署"
    echo "  $0 -p -b                # 生产环境部署并创建备份"
}

# 主函数
main() {
    # 解析命令行参数
    ENABLE_BACKUP=false
    ENABLE_MONITORING=false
    PRODUCTION_MODE=false
    SKIP_PULL=false
    SKIP_BUILD=false
    SKIP_CLEANUP=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--backup)
                ENABLE_BACKUP=true
                shift
                ;;
            -m|--monitoring)
                ENABLE_MONITORING=true
                shift
                ;;
            -p|--production)
                PRODUCTION_MODE=true
                shift
                ;;
            -d|--development)
                PRODUCTION_MODE=false
                shift
                ;;
            --no-pull)
                SKIP_PULL=true
                shift
                ;;
            --no-build)
                SKIP_BUILD=true
                shift
                ;;
            --no-cleanup)
                SKIP_CLEANUP=true
                shift
                ;;
            *)
                print_message $RED "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 开始部署
    print_message $GREEN "开始 IdleGame 部署..."
    echo "部署日志: $LOG_FILE"
    
    # 检查环境
    check_environment
    
    # 创建备份
    if [ "$ENABLE_BACKUP" = true ]; then
        create_backup
    fi
    
    # 拉取代码
    if [ "$SKIP_PULL" = false ]; then
        pull_latest_code
    fi
    
    # 构建镜像
    if [ "$SKIP_BUILD" = false ]; then
        build_images
    fi
    
    # 停止旧服务
    stop_services
    
    # 启动新服务
    start_services
    
    # 健康检查
    health_check
    
    # 清理
    if [ "$SKIP_CLEANUP" = false ]; then
        cleanup
    fi
    
    # 显示部署信息
    show_deployment_info
    
    print_message $GREEN "部署成功完成！"
}

# 执行主函数
main "$@"
