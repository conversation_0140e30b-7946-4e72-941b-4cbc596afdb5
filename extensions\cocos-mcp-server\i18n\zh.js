"use strict";

module.exports = {
    "extension_name": "Cocos MCP 服务器",
    "description": "适用于 Cocos Creator 3.8 的 AI MCP 服务器",
    "panel_title": "MCP 服务器",
    "open_panel": "打开 MCP 面板",
    "start_server": "启动服务器",
    "stop_server": "停止服务器",
    "server_status": "服务器状态",
    "port": "端口",
    "settings": "设置",
    "connected": "已连接",
    "disconnected": "未连接",
    "server_running": "服务器正在端口 {0} 上运行",
    "server_stopped": "服务器已停止",
    "auto_start": "自动启动",
    "debug_log": "调试日志",
    "max_connections": "最大连接数",
    "connection_info": "连接信息",
    "http_url": "HTTP 地址",
    "copy": "复制",
    "save_settings": "保存设置",
    "settings_saved": "设置保存成功",
    "server_started": "MCP 服务器已启动",
    "server_stopped_msg": "MCP 服务器已停止",
    "failed_to_start": "启动服务器失败",
    "failed_to_stop": "停止服务器失败",
    "failed_to_save": "保存设置失败",
    "url_copied": "HTTP 地址已复制到剪贴板"
};