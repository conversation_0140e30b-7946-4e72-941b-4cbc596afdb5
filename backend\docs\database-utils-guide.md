# 数据库工具类使用指南

> 📚 **文档**: 数据库工具类完整使用指南  
> 🔧 **工具**: DatabaseUtils  
> 📅 **更新**: 2025年7月23日

## 📋 概述

DatabaseUtils是一个强大的数据库工具类，提供了事务处理、批量操作、聚合查询、分页查询、数据迁移、一致性检查等高级功能。

## 🚀 快速开始

### 获取实例

```typescript
import { DatabaseUtils } from '../utils/database';

const dbUtils = DatabaseUtils.getInstance();
```

## 🔄 事务处理

### 基本事务

```typescript
const result = await dbUtils.withTransaction(async (session) => {
  const user = new UserModel({ name: '张三', email: '<EMAIL>' });
  await user.save({ session });

  const profile = new ProfileModel({ userId: user._id, bio: '用户简介' });
  await profile.save({ session });

  return { userId: user._id, profileId: profile._id };
});
```

### 带选项的事务

```typescript
const result = await dbUtils.withTransaction(
  async (session) => {
    // 事务操作
    return await performComplexOperation(session);
  },
  {
    readPreference: 'primary',
    writeConcern: { w: 'majority', j: true },
    maxTimeMS: 30000,
  }
);
```

## 📦 批量操作

### 批量插入

```typescript
const operations = [
  {
    insertOne: {
      document: { name: '用户1', email: '<EMAIL>' },
    },
  },
  {
    insertOne: {
      document: { name: '用户2', email: '<EMAIL>' },
    },
  },
];

const result = await dbUtils.bulkWrite(UserModel, operations);
console.log(`插入了 ${result.insertedCount} 个文档`);
```

### 混合批量操作

```typescript
const operations = [
  {
    insertOne: {
      document: { name: '新用户', email: '<EMAIL>' },
    },
  },
  {
    updateOne: {
      filter: { email: '<EMAIL>' },
      update: { $set: { name: '更新用户' } },
    },
  },
  {
    deleteOne: {
      filter: { email: '<EMAIL>' },
    },
  },
];

const result = await dbUtils.bulkWrite(UserModel, operations);
```

## 📊 聚合查询

### 基本聚合

```typescript
const pipeline = [
  { $match: { isActive: true } },
  {
    $group: {
      _id: '$department',
      totalSalary: { $sum: '$salary' },
      avgSalary: { $avg: '$salary' },
      count: { $sum: 1 },
    },
  },
  { $sort: { totalSalary: -1 } },
];

const results = await dbUtils.aggregate(EmployeeModel, pipeline);
```

### 带选项的聚合

```typescript
const results = await dbUtils.aggregate(
  LargeDataModel,
  pipeline,
  {
    allowDiskUse: true,
    maxTimeMS: 60000,
    batchSize: 1000,
  }
);
```

## 📄 分页查询

### 基本分页

```typescript
const result = await dbUtils.paginate(
  UserModel,
  { isActive: true },
  { page: 1, limit: 20 }
);

console.log(`总共 ${result.totalDocs} 个文档`);
console.log(`当前页 ${result.page}/${result.totalPages}`);
console.log(`文档:`, result.docs);
```

### 高级分页

```typescript
const result = await dbUtils.paginate(
  UserModel,
  { department: 'IT' },
  {
    page: 2,
    limit: 10,
    sort: { createdAt: -1 },
    select: 'name email department',
    populate: 'profile',
    lean: true,
  }
);
```

## 🔄 数据迁移

### 基本迁移

```typescript
const progress = await dbUtils.migrateData(
  OldUserModel,
  NewUserModel,
  (oldDoc) => {
    return {
      name: oldDoc.fullName,
      email: oldDoc.emailAddress,
      isActive: oldDoc.status === 'active',
      migratedAt: new Date(),
    };
  }
);

console.log(`迁移完成: ${progress.migrated}/${progress.total}`);
```

### 带进度回调的迁移

```typescript
const progress = await dbUtils.migrateData(
  SourceModel,
  TargetModel,
  transformFunction,
  {
    batchSize: 500,
    dryRun: false,
    skipErrors: true,
    progressCallback: (progress) => {
      console.log(`进度: ${progress.percentage}% (${progress.processed}/${progress.total})`);
    },
  }
);
```

## ✅ 数据一致性检查

### 定义检查规则

```typescript
const rules = [
  {
    name: '邮箱格式检查',
    description: '验证邮箱字段格式',
    check: (doc) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return !doc.email || emailRegex.test(doc.email);
    },
    fix: (doc) => {
      if (doc.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(doc.email)) {
        return { email: null };
      }
      return null;
    },
    severity: 'medium',
  },
  {
    name: '必需字段检查',
    check: (doc) => doc.name && doc.name.trim().length > 0,
    fix: (doc) => {
      if (!doc.name || doc.name.trim().length === 0) {
        return { name: '未知用户' };
      }
      return null;
    },
    severity: 'high',
  },
];
```

### 执行一致性检查

```typescript
const result = await dbUtils.checkDataConsistency(UserModel, rules);

console.log(`检查了 ${result.total} 个文档`);
console.log(`发现 ${result.issues} 个问题`);
console.log(`修复了 ${result.fixed} 个问题`);
console.log(`错误 ${result.errors} 个`);

// 查看每个规则的结果
result.ruleResults.forEach(ruleResult => {
  console.log(`规则 "${ruleResult.rule}": ${ruleResult.issues} 问题, ${ruleResult.fixed} 修复`);
});
```

## 📈 性能测试

```typescript
const perfResult = await dbUtils.performanceTest(UserModel, {
  insert: 1000,
  find: 500,
  update: 200,
  delete: 100,
});

console.log('性能测试结果:');
console.log(`插入: ${perfResult.insert.docsPerSecond} docs/sec`);
console.log(`查询: ${perfResult.find.queriesPerSecond} queries/sec`);
console.log(`更新: ${perfResult.update.updatesPerSecond} updates/sec`);
```

## 🗂️ 索引管理

### 创建索引

```typescript
const indexName = await dbUtils.createIndex(
  UserModel,
  { email: 1, department: 1 },
  { 
    name: 'email_department_idx',
    unique: true,
    background: true 
  }
);
```

### 获取索引信息

```typescript
const indexes = await dbUtils.getIndexes(UserModel);
indexes.forEach(index => {
  console.log(`索引: ${index.name}, 字段: ${JSON.stringify(index.key)}`);
});
```

### 删除索引

```typescript
await dbUtils.dropIndex(UserModel, 'email_department_idx');
```

## 📊 集合统计

```typescript
const stats = await dbUtils.getCollectionStats(UserModel);
console.log(`集合: ${stats.collection}`);
console.log(`文档数: ${stats.count}`);
console.log(`大小: ${stats.size} bytes`);
console.log(`平均文档大小: ${stats.avgObjSize} bytes`);
console.log(`索引数: ${stats.indexes}`);
```

## 🧹 数据清理

### 清理过期数据

```typescript
// 清理30天前的日志数据
const deletedCount = await dbUtils.cleanupExpiredData(
  LogModel,
  'createdAt',
  30
);
console.log(`清理了 ${deletedCount} 条过期日志`);
```

### 集合优化

```typescript
const result = await dbUtils.optimizeCollection(UserModel);
console.log('集合优化完成:', result.stats);
```

## 💾 数据导入导出

### 导出数据

```typescript
const data = await dbUtils.exportCollection(
  UserModel,
  { isActive: true },
  { limit: 1000, sort: { createdAt: -1 } }
);

// 保存到文件
fs.writeFileSync('users_backup.json', JSON.stringify(data, null, 2));
```

### 导入数据

```typescript
const data = JSON.parse(fs.readFileSync('users_backup.json', 'utf8'));

const result = await dbUtils.importCollection(
  UserModel,
  data,
  { upsert: true, batchSize: 500 }
);

console.log(`导入了 ${result.insertedCount} 个新文档`);
console.log(`更新了 ${result.modifiedCount} 个文档`);
```

## 🔍 连接信息

```typescript
const connectionInfo = dbUtils.getConnectionInfo();
console.log('数据库连接状态:', connectionInfo);
```

## ⚠️ 最佳实践

### 1. 事务使用
- 只在需要ACID特性时使用事务
- 保持事务操作简短
- 避免在事务中执行长时间运行的操作

### 2. 批量操作
- 使用批量操作提高性能
- 合理设置批次大小（通常100-1000）
- 考虑使用无序操作提高性能

### 3. 聚合查询
- 对大数据集启用allowDiskUse
- 设置合理的maxTimeMS避免长时间运行
- 在聚合管道早期使用$match减少数据量

### 4. 分页查询
- 限制每页最大数量（建议不超过100）
- 使用索引优化排序性能
- 考虑使用lean()提高查询性能

### 5. 数据迁移
- 先进行dryRun测试
- 设置合理的批次大小
- 启用skipErrors处理部分失败

### 6. 一致性检查
- 定期运行一致性检查
- 在修复前备份数据
- 按严重程度优先处理问题

---

**📞 技术支持**: 如有问题请联系开发团队  
**📖 更多文档**: 查看 `backend/docs/` 目录
