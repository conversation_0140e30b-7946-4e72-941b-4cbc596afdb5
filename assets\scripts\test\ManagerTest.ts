import { _decorator, Component, Node, input, Input, EventKeyboard, KeyCode } from 'cc';
import { ManagerInitializer, Managers, GameState } from '../managers';

const { ccclass, property } = _decorator;

/**
 * 管理器测试组件
 * 用于测试和验证管理器系统的功能
 */
@ccclass('ManagerTest')
export class ManagerTest extends Component {

    protected onLoad(): void {
        console.log('🧪 管理器测试组件加载');
        this.initializeKeyboardInput();
    }

    protected start(): void {
        console.log('🧪 管理器测试组件开始');
        this.showTestInstructions();
        this.initializeManagers();
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ 管理器测试键盘输入已初始化');
    }

    /**
     * 显示测试说明
     */
    private showTestInstructions(): void {
        console.log('🧪 ========== 管理器测试说明 ==========');
        console.log('📍 当前测试: 核心管理器系统');
        console.log('⌨️ 测试快捷键:');
        console.log('   按 M 键 - 显示管理器状态');
        console.log('   按 G 键 - 测试GameManager');
        console.log('   按 S 键 - 测试SceneManager');
        console.log('   按 E 键 - 测试EventManager');
        console.log('   按 R 键 - 测试ResourceManager');
        console.log('   按 I 键 - 重新初始化所有管理器');
        console.log('   按 D 键 - 销毁所有管理器');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🧪 =====================================');
    }

    /**
     * 初始化管理器
     */
    private async initializeManagers(): Promise<void> {
        try {
            console.log('🎯 开始初始化管理器系统...');
            await ManagerInitializer.initializeAllManagers();
            console.log('✅ 管理器系统初始化完成');
        } catch (error) {
            console.error('❌ 管理器系统初始化失败:', error);
        }
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.KEY_M:
                this.showManagerStatus();
                break;
            case KeyCode.KEY_G:
                this.testGameManager();
                break;
            case KeyCode.KEY_S:
                this.testSceneManager();
                break;
            case KeyCode.KEY_E:
                this.testEventManager();
                break;
            case KeyCode.KEY_R:
                this.testResourceManager();
                break;
            case KeyCode.KEY_I:
                this.reinitializeManagers();
                break;
            case KeyCode.KEY_D:
                this.destroyManagers();
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 显示管理器状态
     */
    private showManagerStatus(): void {
        console.log('📊 ========== 管理器状态 ==========');
        
        const status = ManagerInitializer.getManagersStatus();
        console.log(`📈 已初始化管理器: ${status.initialized.length}/${status.total}`);
        console.log(`📋 管理器列表: ${status.initialized.join(', ')}`);
        
        // 显示各个管理器的详细状态
        for (const [name, info] of Object.entries(status.status)) {
            if (info.initialized && info.instance) {
                console.log(`📊 ${name}:`, info.instance.getStatus());
            } else {
                console.log(`📊 ${name}: 未初始化`);
            }
        }
        
        console.log('📊 ================================');
    }

    /**
     * 测试GameManager
     */
    private testGameManager(): void {
        console.log('🎮 ========== 测试GameManager ==========');
        
        try {
            const gameManager = Managers.Game;
            const gameInfo = gameManager.getGameInfo();
            
            console.log('🎮 游戏信息:', gameInfo);
            console.log(`🎮 当前状态: ${gameInfo.state}`);
            console.log(`🎮 运行时间: ${gameInfo.runTime}ms`);
            console.log(`🎮 是否运行中: ${gameInfo.isRunning}`);
            
            // 测试状态切换
            if (gameInfo.state === GameState.PREPARING) {
                console.log('🚀 启动游戏...');
                gameManager.startGame();
            } else if (gameInfo.state === GameState.RUNNING) {
                console.log('⏸️ 暂停游戏...');
                gameManager.pauseGame();
            } else if (gameInfo.state === GameState.PAUSED) {
                console.log('▶️ 恢复游戏...');
                gameManager.resumeGame();
            }
            
        } catch (error) {
            console.error('❌ GameManager测试失败:', error);
        }
        
        console.log('🎮 ====================================');
    }

    /**
     * 测试SceneManager
     */
    private testSceneManager(): void {
        console.log('🎬 ========== 测试SceneManager ==========');
        
        try {
            const sceneManager = Managers.Scene;
            const currentScene = sceneManager.getCurrentScene();
            const history = sceneManager.getSceneHistory();
            
            console.log('🎬 当前场景:', currentScene);
            console.log('🎬 场景历史:', history);
            console.log(`🎬 是否正在切换: ${sceneManager.isTransitioning()}`);
            
            // 测试场景预加载状态
            const scenes = ['Launch', 'Main', 'Battle'];
            scenes.forEach(sceneName => {
                const isPreloaded = sceneManager.isScenePreloaded(sceneName);
                console.log(`🎬 ${sceneName}场景预加载状态: ${isPreloaded ? '已预加载' : '未预加载'}`);
            });
            
        } catch (error) {
            console.error('❌ SceneManager测试失败:', error);
        }
        
        console.log('🎬 ======================================');
    }

    /**
     * 测试EventManager
     */
    private testEventManager(): void {
        console.log('📡 ========== 测试EventManager ==========');
        
        try {
            const eventManager = Managers.Event;
            const status = eventManager.getStatus();
            
            console.log('📡 事件管理器状态:', status);
            console.log(`📡 事件总数: ${status.totalEvents}`);
            console.log(`📡 监听器总数: ${status.totalListeners}`);
            
            // 测试事件系统
            const testEventName = 'test-event';
            
            // 添加测试监听器
            eventManager.on(testEventName, (data) => {
                console.log('📡 收到测试事件:', data);
            });
            
            // 触发测试事件
            eventManager.emit(testEventName, { message: '这是一个测试事件', timestamp: Date.now() });
            
            // 显示事件统计
            const eventStats = eventManager.getEventStats();
            console.log('📡 事件统计:', Array.from(eventStats.entries()));
            
        } catch (error) {
            console.error('❌ EventManager测试失败:', error);
        }
        
        console.log('📡 ========================================');
    }

    /**
     * 测试ResourceManager
     */
    private testResourceManager(): void {
        console.log('📦 ========== 测试ResourceManager ==========');
        
        try {
            const resourceManager = Managers.Resource;
            const cacheStats = resourceManager.getCacheStats();
            
            console.log('📦 资源缓存统计:', cacheStats);
            console.log(`📦 缓存资源数: ${cacheStats.totalResources}`);
            console.log(`📦 缓存大小: ${cacheStats.formattedSize}`);
            console.log(`📦 使用率: ${cacheStats.usagePercentage.toFixed(1)}%`);
            
            // 显示已缓存的资源
            const cachedPaths = resourceManager.getCachedResourcePaths();
            console.log('📦 已缓存资源:', cachedPaths);
            
            // 测试资源加载(这里只是示例，实际项目中需要有效的资源路径)
            console.log('📦 资源管理器功能正常');
            
        } catch (error) {
            console.error('❌ ResourceManager测试失败:', error);
        }
        
        console.log('📦 ==========================================');
    }

    /**
     * 重新初始化管理器
     */
    private async reinitializeManagers(): Promise<void> {
        console.log('🔄 重新初始化所有管理器...');
        
        try {
            await ManagerInitializer.reinitializeAllManagers();
            console.log('✅ 管理器重新初始化完成');
        } catch (error) {
            console.error('❌ 管理器重新初始化失败:', error);
        }
    }

    /**
     * 销毁管理器
     */
    private destroyManagers(): void {
        console.log('🗑️ 销毁所有管理器...');
        
        try {
            ManagerInitializer.destroyAllManagers();
            console.log('✅ 管理器销毁完成');
        } catch (error) {
            console.error('❌ 管理器销毁失败:', error);
        }
    }

    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🧪 管理器测试组件销毁');
    }
}
