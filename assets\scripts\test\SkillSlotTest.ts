import { _decorator, Component, Node, instantiate, Prefab, resources, Vec3 } from 'cc';
import { SkillSlot } from '../ui/components/SkillSlot';
import { IPlayerSkill } from '../ui/panels/SkillPanel';
import { ISkillData } from '../data/ISkillData';

const { ccclass, property } = _decorator;

/**
 * SkillSlot预制体测试脚本
 * 用于验证SkillSlot的各项功能
 */
@ccclass('SkillSlotTest')
export class SkillSlotTest extends Component {
    
    @property(Node)
    testContainer: Node = null;
    
    private skillSlots: SkillSlot[] = [];
    private testSkillData: IPlayerSkill[] = [];
    
    start() {
        console.log('=== SkillSlot预制体测试开始 ===');
        this.initTestData();
        this.loadAndTestPrefab();
    }
    
    /**
     * 初始化测试数据
     */
    private initTestData() {
        // 创建测试技能数据
        const skillDataList: ISkillData[] = [
            {
                id: 'fireball',
                name: '火球术',
                description: '发射一个火球攻击敌人',
                iconPath: 'textures/skills/fireball',
                maxLevel: 5,
                cooldownTime: 3.0,
                manaCost: 20,
                damage: 50,
                range: 10,
                effects: [],
                requirements: { level: 1, skills: [] },
                category: 'combat'
            },
            {
                id: 'heal',
                name: '治疗术',
                description: '恢复生命值',
                iconPath: 'textures/skills/heal',
                maxLevel: 3,
                cooldownTime: 5.0,
                manaCost: 30,
                damage: 0,
                range: 0,
                effects: [],
                requirements: { level: 1, skills: [] },
                category: 'support'
            },
            {
                id: 'shield',
                name: '护盾',
                description: '提供魔法护盾保护',
                iconPath: 'textures/skills/shield',
                maxLevel: 4,
                cooldownTime: 8.0,
                manaCost: 40,
                damage: 0,
                range: 0,
                effects: [],
                requirements: { level: 1, skills: [] },
                category: 'defense'
            }
        ];

        // 转换为 IPlayerSkill 格式
        this.testSkillData = skillDataList.map((skillData, index) => ({
            skillData: skillData,
            level: index === 2 ? 0 : index + 1, // 第三个技能未学习
            experience: 0,
            requiredExperience: 100,
            unlocked: index !== 2, // 第三个技能未解锁
            learnTime: Date.now(),
            useCount: 0,
            lastUseTime: 0,
            cooldownEndTime: 0
        }));

        console.log('测试数据初始化完成:', this.testSkillData.length, '个技能');
    }
    
    /**
     * 加载并测试预制体
     */
    private loadAndTestPrefab() {
        resources.load('ui/skill/SkillSlot', Prefab, (err, prefab) => {
            if (err) {
                console.error('加载SkillSlot预制体失败:', err);
                return;
            }
            
            console.log('SkillSlot预制体加载成功');
            this.createTestSlots(prefab);
        });
    }
    
    /**
     * 创建测试技能槽
     */
    private createTestSlots(prefab: Prefab) {
        const startX = -200;
        const spacing = 100;
        
        for (let i = 0; i < this.testSkillData.length; i++) {
            // 实例化预制体
            const slotNode = instantiate(prefab);
            let skillSlot = slotNode.getComponent(SkillSlot);

            // 如果预制体没有SkillSlot组件，手动添加
            if (!skillSlot) {
                console.warn(`技能槽 ${i} 没有找到SkillSlot组件，正在添加...`);
                skillSlot = slotNode.addComponent(SkillSlot);
            }

            // 设置位置
            slotNode.setPosition(new Vec3(startX + i * spacing, 0, 0));

            // 添加到容器
            this.testContainer.addChild(slotNode);

            // 初始化技能槽
            skillSlot.setSlotIndex(i);
            skillSlot.updateSkill(this.testSkillData[i]);

            this.skillSlots.push(skillSlot);

            console.log(`技能槽 ${i} 创建成功:`, this.testSkillData[i].skillData.name);
        }
        
        // 开始功能测试
        this.scheduleOnce(() => {
            this.runFunctionTests();
        }, 1.0);
    }
    
    /**
     * 技能槽点击回调
     */
    private onSkillSlotClicked(playerSkill: IPlayerSkill, slotIndex: number) {
        console.log(`技能槽 ${slotIndex} 被点击:`, playerSkill.skillData.name);

        // 测试冷却功能
        if (playerSkill.level > 0) {
            this.testCooldown(slotIndex);
        } else {
            console.log('技能未学习，无法使用');
        }
    }
    
    /**
     * 测试冷却功能
     */
    private testCooldown(slotIndex: number) {
        const skillSlot = this.skillSlots[slotIndex];
        const skillData = this.testSkillData[slotIndex];
        
        console.log(`开始测试技能 ${skillData.name} 的冷却功能`);
        
        // 开始冷却
        skillSlot.startCooldown();
        
        // 模拟冷却完成
        this.scheduleOnce(() => {
            console.log(`技能 ${skillData.name} 冷却完成`);
        }, skillData.cooldownTime);
    }
    
    /**
     * 运行功能测试
     */
    private runFunctionTests() {
        console.log('=== 开始功能测试 ===');
        
        // 测试1: 验证UI显示
        this.testUIDisplay();
        
        // 测试2: 验证状态切换
        this.scheduleOnce(() => {
            this.testStateChanges();
        }, 2.0);
        
        // 测试3: 验证升级功能
        this.scheduleOnce(() => {
            this.testLevelUp();
        }, 4.0);
        
        // 测试4: 验证禁用/启用
        this.scheduleOnce(() => {
            this.testEnableDisable();
        }, 6.0);
    }
    
    /**
     * 测试UI显示
     */
    private testUIDisplay() {
        console.log('--- 测试UI显示 ---');
        
        this.skillSlots.forEach((slot, index) => {
            const skillData = this.testSkillData[index];
            
            // 检查各个UI元素是否正确显示
            console.log(`技能槽 ${index}:`);
            console.log(`  - 名称: ${skillData.name}`);
            console.log(`  - 等级: ${skillData.currentLevel}/${skillData.maxLevel}`);
            console.log(`  - 快捷键: ${skillData.hotkey}`);
            console.log(`  - 是否可用: ${skillData.currentLevel > 0}`);
        });
    }
    
    /**
     * 测试状态切换
     */
    private testStateChanges() {
        console.log('--- 测试状态切换 ---');

        // 检查技能槽是否存在
        if (!this.skillSlots[0]) {
            console.error('技能槽0不存在，跳过状态切换测试');
            return;
        }

        // 测试禁用状态
        this.skillSlots[0].setAvailable(false);
        console.log('技能槽0设为禁用状态');

        // 2秒后恢复
        this.scheduleOnce(() => {
            if (this.skillSlots[0]) {
                this.skillSlots[0].setAvailable(true);
                console.log('技能槽0恢复启用状态');
            }
        }, 2.0);
    }
    
    /**
     * 测试升级功能
     */
    private testLevelUp() {
        console.log('--- 测试升级功能 ---');
        
        // 升级第三个技能（从0级到1级）
        const skillData = this.testSkillData[2];
        if (skillData.currentLevel < skillData.maxLevel) {
            skillData.currentLevel++;
            this.skillSlots[2].updateSkillData(skillData);
            console.log(`${skillData.name} 升级到 ${skillData.currentLevel} 级`);
        }
    }
    
    /**
     * 测试启用/禁用功能
     */
    private testEnableDisable() {
        console.log('--- 测试启用/禁用功能 ---');
        
        // 禁用所有技能槽
        this.skillSlots.forEach((slot, index) => {
            slot.setEnabled(false);
            console.log(`技能槽 ${index} 已禁用`);
        });
        
        // 3秒后重新启用
        this.scheduleOnce(() => {
            this.skillSlots.forEach((slot, index) => {
                slot.setEnabled(true);
                console.log(`技能槽 ${index} 已启用`);
            });
            
            console.log('=== 所有测试完成 ===');
        }, 3.0);
    }
    
    /**
     * 手动触发测试（可在编辑器中调用）
     */
    public manualTest() {
        console.log('手动触发测试');
        this.runFunctionTests();
    }
}
