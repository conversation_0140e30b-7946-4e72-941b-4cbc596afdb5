import express = require('express');
import { AppError, ErrorCodes } from '../utils/errors';
import { Logger } from '../utils/logger';

/**
 * 错误响应接口
 */
interface ErrorResponse {
  success: false;
  message: string;
  error: string;
  errorCode: string;
  timestamp: string;
  requestId?: string;
  details?: any;
  stack?: string;
}

/**
 * 全局错误处理中间件
 */
export function errorHandler(
  error: Error,
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
): void {
  // 生成请求ID用于错误追踪
  const requestId = req.headers['x-request-id'] as string || generateRequestId();

  // 记录错误日志
  logError(error, req, requestId);

  // 处理不同类型的错误
  if (error instanceof AppError) {
    handleAppError(error, res, requestId);
  } else if (error.name === 'ValidationError') {
    handleValidationError(error, res, requestId);
  } else if (error.name === 'CastError') {
    handleCastError(error, res, requestId);
  } else if (error.name === 'MongoError' || error.name === 'MongoServerError') {
    handleMongoError(error, res, requestId);
  } else if (error.name === 'JsonWebTokenError') {
    handleJWTError(error, res, requestId);
  } else if (error.name === 'TokenExpiredError') {
    handleTokenExpiredError(error, res, requestId);
  } else {
    handleGenericError(error, res, requestId);
  }
}

/**
 * 处理应用自定义错误
 */
function handleAppError(error: AppError, res: express.Response, requestId: string): void {
  const response: ErrorResponse = {
    success: false,
    message: error.message,
    error: error.errorCode,
    errorCode: error.errorCode,
    timestamp: error.timestamp,
    requestId,
  };

  // 添加详细信息（如果有）
  if ('details' in error) {
    response.details = (error as any).details;
  }

  // 开发环境下添加堆栈信息
  if (process.env['NODE_ENV'] === 'development') {
    response.stack = error.stack;
  }

  res.status(error.statusCode).json(response);
}

/**
 * 处理Joi验证错误
 */
function handleValidationError(error: any, res: express.Response, requestId: string): void {
  const details = error.details?.map((detail: any) => detail.message) || [];
  
  const response: ErrorResponse = {
    success: false,
    message: '参数验证失败',
    error: ErrorCodes.VALIDATION_ERROR,
    errorCode: ErrorCodes.VALIDATION_ERROR,
    timestamp: new Date().toISOString(),
    requestId,
    details,
  };

  res.status(400).json(response);
}

/**
 * 处理MongoDB类型转换错误
 */
function handleCastError(error: any, res: express.Response, requestId: string): void {
  const response: ErrorResponse = {
    success: false,
    message: '无效的ID格式',
    error: ErrorCodes.VALIDATION_ERROR,
    errorCode: ErrorCodes.VALIDATION_ERROR,
    timestamp: new Date().toISOString(),
    requestId,
  };

  res.status(400).json(response);
}

/**
 * 处理MongoDB错误
 */
function handleMongoError(error: any, res: express.Response, requestId: string): void {
  let message = '数据库操作失败';
  let statusCode = 500;

  // 处理重复键错误
  if (error.code === 11000) {
    const field = Object.keys(error.keyValue)[0];
    message = `${field}已存在`;
    statusCode = 409;
  }

  const response: ErrorResponse = {
    success: false,
    message,
    error: ErrorCodes.DATABASE_ERROR,
    errorCode: ErrorCodes.DATABASE_ERROR,
    timestamp: new Date().toISOString(),
    requestId,
  };

  res.status(statusCode).json(response);
}

/**
 * 处理JWT错误
 */
function handleJWTError(error: any, res: express.Response, requestId: string): void {
  const response: ErrorResponse = {
    success: false,
    message: '无效的访问令牌',
    error: ErrorCodes.INVALID_TOKEN,
    errorCode: ErrorCodes.INVALID_TOKEN,
    timestamp: new Date().toISOString(),
    requestId,
  };

  res.status(401).json(response);
}

/**
 * 处理JWT过期错误
 */
function handleTokenExpiredError(error: any, res: express.Response, requestId: string): void {
  const response: ErrorResponse = {
    success: false,
    message: '访问令牌已过期',
    error: ErrorCodes.TOKEN_EXPIRED,
    errorCode: ErrorCodes.TOKEN_EXPIRED,
    timestamp: new Date().toISOString(),
    requestId,
  };

  res.status(401).json(response);
}

/**
 * 处理通用错误
 */
function handleGenericError(error: Error, res: express.Response, requestId: string): void {
  const response: ErrorResponse = {
    success: false,
    message: process.env['NODE_ENV'] === 'development' ? error.message : '服务器内部错误',
    error: ErrorCodes.INTERNAL_ERROR,
    errorCode: ErrorCodes.INTERNAL_ERROR,
    timestamp: new Date().toISOString(),
    requestId,
  };

  // 开发环境下添加堆栈信息
  if (process.env['NODE_ENV'] === 'development') {
    response.stack = error.stack;
  }

  res.status(500).json(response);
}

/**
 * 记录错误日志
 */
function logError(error: Error, req: express.Request, requestId: string): void {
  const errorInfo = {
    requestId,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    body: req.body,
    query: req.query,
    params: req.params,
    headers: req.headers,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack,
    },
  };

  if (error instanceof AppError) {
    if (error.statusCode >= 500) {
      Logger.error('应用错误', errorInfo);
    } else {
      Logger.warn('客户端错误', errorInfo);
    }
  } else {
    Logger.error('未处理的错误', errorInfo);
  }
}

/**
 * 生成请求ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * 404错误处理中间件
 */
export function notFoundHandler(
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
): void {
  const response: ErrorResponse = {
    success: false,
    message: `路由 ${req.originalUrl} 不存在`,
    error: ErrorCodes.NOT_FOUND,
    errorCode: ErrorCodes.NOT_FOUND,
    timestamp: new Date().toISOString(),
  };

  Logger.warn('404错误', {
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  res.status(404).json(response);
}

/**
 * 异步错误包装器
 */
export function asyncHandler(fn: Function) {
  return (req: express.Request, res: express.Response, next: express.NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}
