# Git 工作流程和分支策略

## 分支策略

### 主要分支

- **main** - 生产环境分支，始终保持稳定可发布状态
- **develop** - 开发主分支，集成所有功能开发
- **staging** - 预发布分支，用于测试环境部署

### 功能分支

- **feature/*** - 功能开发分支
  - 命名规范：`feature/功能名称` 或 `feature/JIRA-123-功能描述`
  - 从 `develop` 分支创建
  - 完成后合并回 `develop`

- **hotfix/*** - 紧急修复分支
  - 命名规范：`hotfix/修复描述` 或 `hotfix/JIRA-456-bug描述`
  - 从 `main` 分支创建
  - 完成后同时合并到 `main` 和 `develop`

- **release/*** - 发布准备分支
  - 命名规范：`release/v1.0.0`
  - 从 `develop` 分支创建
  - 完成后合并到 `main` 和 `develop`

## 工作流程

### 1. 功能开发流程

```bash
# 1. 切换到develop分支并更新
git checkout develop
git pull origin develop

# 2. 创建功能分支
git checkout -b feature/user-authentication

# 3. 开发功能并提交
git add .
git commit -m "feat: 添加用户认证功能"

# 4. 推送到远程仓库
git push origin feature/user-authentication

# 5. 创建Pull Request到develop分支
# 6. 代码审查通过后合并
# 7. 删除功能分支
git branch -d feature/user-authentication
```

### 2. 发布流程

```bash
# 1. 从develop创建release分支
git checkout develop
git pull origin develop
git checkout -b release/v1.0.0

# 2. 版本号更新和最后调整
npm version patch  # 或 minor/major

# 3. 合并到main
git checkout main
git merge release/v1.0.0
git tag v1.0.0

# 4. 合并回develop
git checkout develop
git merge release/v1.0.0

# 5. 推送所有更改
git push origin main
git push origin develop
git push origin v1.0.0
```

### 3. 紧急修复流程

```bash
# 1. 从main创建hotfix分支
git checkout main
git pull origin main
git checkout -b hotfix/critical-security-fix

# 2. 修复问题并测试
git add .
git commit -m "fix: 修复安全漏洞"

# 3. 合并到main
git checkout main
git merge hotfix/critical-security-fix
git tag v1.0.1

# 4. 合并到develop
git checkout develop
git merge hotfix/critical-security-fix

# 5. 推送更改
git push origin main
git push origin develop
git push origin v1.0.1
```

## 代码审查规范

### Pull Request 要求

1. **标题格式**：`[类型] 简短描述`
2. **描述内容**：
   - 功能描述
   - 变更内容
   - 测试说明
   - 相关Issue链接

3. **审查检查项**：
   - 代码质量和规范
   - 测试覆盖率
   - 文档更新
   - 安全性检查

### 合并要求

- 至少1人审查通过
- 所有CI检查通过
- 无冲突
- 测试覆盖率不低于80%

## 版本管理

### 版本号规范

采用语义化版本控制 (Semantic Versioning)：

- **MAJOR.MINOR.PATCH** (例如：1.2.3)
- **MAJOR**：不兼容的API修改
- **MINOR**：向后兼容的功能性新增
- **PATCH**：向后兼容的问题修正

### 标签规范

- 发布版本：`v1.0.0`
- 预发布版本：`v1.0.0-alpha.1`、`v1.0.0-beta.1`
- 开发版本：`v1.0.0-dev.20231122`
