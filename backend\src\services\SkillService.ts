import { Skill, UserSkill, ISkill, IUserSkill } from '../models/Skill';
import { Character, I<PERSON>haracter } from '../models/Character';
import { User } from '../models/User';
import { AppError, ErrorCodes } from '../utils/errors';
import { Logger } from '../utils/logger';
import { CacheManager } from '../utils/cache';
import { cacheStrategyService } from './CacheStrategyService';

/**
 * 技能使用结果接口
 */
export interface ISkillResult {
  success: boolean;
  message: string;
  damage?: number;
  healing?: number;
  effects?: ISkillEffect[];
  cooldownRemaining?: number;
  experienceGained?: number;
  levelUp?: boolean;
}

/**
 * 技能效果接口
 */
export interface ISkillEffect {
  type: string;
  value: number;
  duration: number;
  target: string;
  description: string;
}

/**
 * 技能冷却信息接口
 */
export interface ISkillCooldown {
  skillId: string;
  remainingTime: number;
  canUse: boolean;
}

/**
 * 技能服务类
 */
export class SkillService {
  private static instance: SkillService;
  private cacheManager: CacheManager;

  private constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  public static getInstance(): SkillService {
    if (!SkillService.instance) {
      SkillService.instance = new SkillService();
    }
    return SkillService.instance;
  }

  /**
   * 学习技能
   */
  public async learnSkill(userId: string, skillId: string): Promise<ISkillResult> {
    try {
      // 获取用户和角色信息
      const user = await User.findById(userId);
      if (!user) {
        throw new AppError('用户不存在', 404, ErrorCodes.USER_NOT_FOUND);
      }

      const character = await Character.findOne({
        _id: user.gameData.currentCharacterId,
        userId,
        isActive: true,
      });

      if (!character) {
        throw new AppError('当前角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 获取技能配置
      const skillConfig = await Skill.findOne({ id: skillId, isActive: true });
      if (!skillConfig) {
        throw new AppError('技能不存在', 404, ErrorCodes.NOT_FOUND);
      }

      // 检查是否已经学习过
      const existingUserSkill = await UserSkill.findOne({ userId, skillId });
      if (existingUserSkill && existingUserSkill.learned) {
        throw new AppError('技能已学习', 400, ErrorCodes.INVALID_ACTION);
      }

      // 检查学习要求
      const canLearn = await this.checkSkillRequirements(character, skillConfig);
      if (!canLearn.success) {
        throw new AppError(canLearn.message, 400, ErrorCodes.INVALID_ACTION);
      }

      // 检查技能点
      if (character.skillPoints < skillConfig.requirements.skillPoints) {
        throw new AppError('技能点不足', 400, ErrorCodes.INSUFFICIENT_RESOURCES);
      }

      // 学习技能
      let userSkill: IUserSkill;
      if (existingUserSkill) {
        existingUserSkill.learned = true;
        existingUserSkill.level = 1;
        existingUserSkill.experience = 0;
        userSkill = await existingUserSkill.save();
      } else {
        userSkill = new UserSkill({
          userId,
          skillId,
          level: 1,
          experience: 0,
          experienceRequired: 100,
          learned: true,
        });
        await userSkill.save();
      }

      // 扣除技能点并添加到角色技能列表
      character.skillPoints -= skillConfig.requirements.skillPoints;
      if (!character.skills.includes(skillConfig._id)) {
        character.skills.push(skillConfig._id);
      }
      await character.save();

      // 更新用户统计
      user.gameData.statistics.skillsLearned += 1;
      await user.save();

      // 清除相关缓存
      await cacheStrategyService.clearUserCache(userId);

      Logger.info('技能学习成功', {
        userId,
        skillId,
        skillName: skillConfig.name,
        skillPointsUsed: skillConfig.requirements.skillPoints,
        remainingSkillPoints: character.skillPoints,
      });

      return {
        success: true,
        message: `成功学习技能：${skillConfig.name}`,
        experienceGained: 0,
        levelUp: false,
      };
    } catch (error) {
      Logger.error('技能学习失败', { userId, skillId, error });
      throw error;
    }
  }

  /**
   * 使用技能
   */
  public async useSkill(
    userId: string,
    skillId: string,
    targetId?: string
  ): Promise<ISkillResult> {
    try {
      // 获取用户技能
      const userSkill = await UserSkill.findOne({ userId, skillId, learned: true });
      if (!userSkill) {
        throw new AppError('技能未学习', 400, ErrorCodes.INVALID_ACTION);
      }

      // 检查冷却时间
      if (!userSkill.canUse()) {
        throw new AppError('技能冷却中', 400, ErrorCodes.INVALID_ACTION);
      }

      // 获取技能配置
      const skillConfig = await Skill.findOne({ id: skillId, isActive: true });
      if (!skillConfig) {
        throw new AppError('技能配置不存在', 404, ErrorCodes.NOT_FOUND);
      }

      // 获取施法者角色
      const caster = await Character.findOne({
        userId,
        isActive: true,
      });

      if (!caster) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 检查法力值
      if (caster.attributes.currentMp < skillConfig.manaCost) {
        throw new AppError('法力值不足', 400, ErrorCodes.INSUFFICIENT_RESOURCES);
      }

      // 获取目标（如果有）
      let target: ICharacter | null = null;
      if (targetId) {
        target = await Character.findById(targetId);
        if (!target) {
          throw new AppError('目标不存在', 404, ErrorCodes.NOT_FOUND);
        }
      }

      // 计算技能效果
      const skillResult = await this.calculateSkillEffect(skillConfig, caster, target, userSkill);

      // 应用技能效果
      await this.applySkillEffect(skillResult, caster, target);

      // 消耗法力值
      caster.attributes.currentMp -= skillConfig.manaCost;
      await caster.save();

      // 设置技能冷却
      await userSkill.use();

      // 增加技能经验
      const expGained = Math.floor(skillConfig.requirements.level * 10);
      const leveledUp = await userSkill.addExperience(expGained);

      // 清除相关缓存
      await cacheStrategyService.clearUserCache(userId);

      Logger.info('技能使用成功', {
        userId,
        skillId,
        skillName: skillConfig.name,
        targetId,
        damage: skillResult.damage,
        healing: skillResult.healing,
        expGained,
        leveledUp,
      });

      return {
        ...skillResult,
        experienceGained: expGained,
        levelUp: leveledUp,
        cooldownRemaining: skillConfig.cooldown,
      };
    } catch (error) {
      Logger.error('技能使用失败', { userId, skillId, targetId, error });
      throw error;
    }
  }

  /**
   * 获取技能冷却时间
   */
  public async getSkillCooldown(userId: string, skillId: string): Promise<ISkillCooldown> {
    try {
      const userSkill = await UserSkill.findOne({ userId, skillId, learned: true });
      if (!userSkill) {
        throw new AppError('技能未学习', 400, ErrorCodes.INVALID_ACTION);
      }

      const remainingTime = Math.max(0, userSkill.cooldownRemaining);
      
      return {
        skillId,
        remainingTime,
        canUse: remainingTime <= 0,
      };
    } catch (error) {
      Logger.error('获取技能冷却失败', { userId, skillId, error });
      throw error;
    }
  }

  /**
   * 获取用户所有技能冷却状态
   */
  public async getAllSkillCooldowns(userId: string): Promise<ISkillCooldown[]> {
    try {
      const userSkills = await UserSkill.find({ userId, learned: true });
      
      const cooldowns: ISkillCooldown[] = [];
      for (const userSkill of userSkills) {
        const remainingTime = Math.max(0, userSkill.cooldownRemaining);
        cooldowns.push({
          skillId: userSkill.skillId,
          remainingTime,
          canUse: remainingTime <= 0,
        });
      }

      return cooldowns;
    } catch (error) {
      Logger.error('获取所有技能冷却失败', { userId, error });
      throw error;
    }
  }

  /**
   * 检查技能学习要求
   */
  private async checkSkillRequirements(
    character: ICharacter,
    skillConfig: ISkill
  ): Promise<{ success: boolean; message: string }> {
    const requirements = skillConfig.requirements;

    // 检查等级要求
    if (character.level < requirements.level) {
      return {
        success: false,
        message: `需要等级${requirements.level}，当前等级${character.level}`,
      };
    }

    // 检查前置技能
    if (requirements.prerequisiteSkills && requirements.prerequisiteSkills.length > 0) {
      for (const prereqSkillId of requirements.prerequisiteSkills) {
        const hasPrereq = await UserSkill.findOne({
          userId: character.userId,
          skillId: prereqSkillId,
          learned: true,
        });
        
        if (!hasPrereq) {
          const prereqSkill = await Skill.findOne({ id: prereqSkillId });
          return {
            success: false,
            message: `需要先学习技能：${prereqSkill?.name || prereqSkillId}`,
          };
        }
      }
    }

    // 检查属性要求
    if (requirements.attributes) {
      const attrs = character.attributes;
      const reqAttrs = requirements.attributes;
      
      if (reqAttrs.strength && attrs.strength < reqAttrs.strength) {
        return { success: false, message: `力量不足，需要${reqAttrs.strength}` };
      }
      if (reqAttrs.agility && attrs.agility < reqAttrs.agility) {
        return { success: false, message: `敏捷不足，需要${reqAttrs.agility}` };
      }
      if (reqAttrs.intelligence && attrs.intelligence < reqAttrs.intelligence) {
        return { success: false, message: `智力不足，需要${reqAttrs.intelligence}` };
      }
      if (reqAttrs.vitality && attrs.vitality < reqAttrs.vitality) {
        return { success: false, message: `体力不足，需要${reqAttrs.vitality}` };
      }
      if (reqAttrs.spirit && attrs.spirit < reqAttrs.spirit) {
        return { success: false, message: `精神不足，需要${reqAttrs.spirit}` };
      }
    }

    return { success: true, message: '满足学习要求' };
  }

  /**
   * 计算技能效果
   */
  private async calculateSkillEffect(
    skillConfig: ISkill,
    caster: ICharacter,
    target: ICharacter | null,
    userSkill: IUserSkill
  ): Promise<ISkillResult> {
    const result: ISkillResult = {
      success: true,
      message: `使用技能：${skillConfig.name}`,
      effects: [],
    };

    // 基础伤害计算
    if (skillConfig.damageType !== 'healing') {
      const baseDamage = this.calculateBaseDamage(skillConfig, caster, userSkill);
      const finalDamage = target ? this.applyDefense(baseDamage, target) : baseDamage;
      result.damage = Math.max(1, Math.floor(finalDamage));
    }

    // 治疗计算
    if (skillConfig.damageType === 'healing') {
      const baseHealing = this.calculateBaseHealing(skillConfig, caster, userSkill);
      result.healing = Math.floor(baseHealing);
    }

    // 应用技能效果
    if (skillConfig.effects && skillConfig.effects.length > 0) {
      result.effects = skillConfig.effects.map(effect => ({
        type: effect.type,
        value: effect.value * (1 + userSkill.level * 0.1), // 技能等级加成
        duration: effect.duration,
        target: effect.target,
        description: effect.description,
      }));
    }

    return result;
  }

  /**
   * 计算基础伤害
   */
  private calculateBaseDamage(
    skillConfig: ISkill,
    caster: ICharacter,
    userSkill: IUserSkill
  ): number {
    const casterAttrs = caster.attributes;
    let baseDamage = casterAttrs.damage * skillConfig.baseDamageMultiplier;

    // 根据伤害类型应用属性加成
    switch (skillConfig.damageType) {
      case 'physical':
        baseDamage += casterAttrs.strength * 0.5;
        break;
      case 'magical':
        baseDamage += casterAttrs.intelligence * 0.8;
        break;
      case 'true':
        // 真实伤害不受属性影响
        break;
    }

    // 技能等级加成
    baseDamage *= (1 + (userSkill.level - 1) * 0.15);

    // 随机波动 ±10%
    const randomFactor = 0.9 + Math.random() * 0.2;
    baseDamage *= randomFactor;

    return baseDamage;
  }

  /**
   * 计算基础治疗
   */
  private calculateBaseHealing(
    skillConfig: ISkill,
    caster: ICharacter,
    userSkill: IUserSkill
  ): number {
    const casterAttrs = caster.attributes;
    let baseHealing = casterAttrs.intelligence * skillConfig.baseDamageMultiplier;

    // 精神属性加成
    baseHealing += casterAttrs.spirit * 0.6;

    // 技能等级加成
    baseHealing *= (1 + (userSkill.level - 1) * 0.12);

    // 随机波动 ±5%
    const randomFactor = 0.95 + Math.random() * 0.1;
    baseHealing *= randomFactor;

    return baseHealing;
  }

  /**
   * 应用防御减免
   */
  private applyDefense(damage: number, target: ICharacter): number {
    const targetAttrs = target.attributes;
    
    // 物理防御减免
    const defenseReduction = targetAttrs.def / (targetAttrs.def + 100);
    const finalDamage = damage * (1 - defenseReduction);

    return finalDamage;
  }

  /**
   * 应用技能效果
   */
  private async applySkillEffect(
    skillResult: ISkillResult,
    caster: ICharacter,
    target: ICharacter | null
  ): Promise<void> {
    // 应用伤害
    if (skillResult.damage && target) {
      const isDead = target.takeDamage(skillResult.damage);
      if (isDead) {
        Logger.info('目标被击败', {
          targetId: target._id,
          damage: skillResult.damage,
        });
      }
      await target.save();
    }

    // 应用治疗
    if (skillResult.healing) {
      const healTarget = target || caster;
      healTarget.heal(skillResult.healing);
      await healTarget.save();
    }

    // 应用其他效果
    if (skillResult.effects && skillResult.effects.length > 0) {
      for (const effect of skillResult.effects) {
        await this.applyStatusEffect(effect, caster, target);
      }
    }
  }

  /**
   * 应用状态效果
   */
  private async applyStatusEffect(
    effect: ISkillEffect,
    caster: ICharacter,
    target: ICharacter | null
  ): Promise<void> {
    const effectTarget = target || caster;
    
    // 添加状态效果到角色
    effectTarget.battleStatus.statusEffects.push({
      effectId: effect.type,
      duration: effect.duration,
      startTime: new Date(),
    });

    await effectTarget.save();
  }


}

export const skillService = SkillService.getInstance();
