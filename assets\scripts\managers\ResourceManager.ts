import { _decorator, Component, Node, resources, Asset, assetManager } from 'cc';
import { BaseManager } from './BaseManager';
import { LoadProgress, ResourceCacheInfo, ResourceManagerConfig } from './types/ManagerTypes';

const { ccclass, property } = _decorator;

/**
 * 资源管理器
 * 负责资源的加载、缓存和管理
 */
@ccclass('ResourceManager')
export class ResourceManager extends BaseManager {
    
    /**
     * 资源缓存
     */
    private _resourceCache: Map<string, ResourceCacheInfo> = new Map();
    
    /**
     * 资源管理器配置
     */
    private _config: ResourceManagerConfig = {
        cacheLimit: 100, // MB
        preloadResources: [],
        enableCompression: true,
        debug: true,
        autoInit: true
    };
    
    /**
     * 当前缓存大小(字节)
     */
    private _currentCacheSize: number = 0;
    
    /**
     * 加载中的资源
     */
    private _loadingResources: Set<string> = new Set();

    /**
     * 获取ResourceManager单例实例
     */
    public static getInstance(): ResourceManager {
        return super.getInstance.call(this) as ResourceManager;
    }

    /**
     * 初始化资源管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('📦 初始化资源管理器...');
        
        try {
            // 清理现有缓存
            this.clearCache();
            
            // 预加载资源
            await this.preloadResources();
            
            console.log('✅ 资源管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 资源管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁资源管理器
     */
    public destroyManager(): void {
        console.log('🗑️ 销毁资源管理器...');
        
        // 清理所有缓存
        this.clearCache();
        
        // 清理数据
        this._loadingResources.clear();
        this._currentCacheSize = 0;
        
        console.log('✅ 资源管理器销毁完成');
    }

    /**
     * 预加载资源
     */
    private async preloadResources(): Promise<void> {
        console.log('📦 预加载资源...');
        
        const preloadList = this._config.preloadResources || [];
        if (preloadList.length === 0) {
            console.log('📦 没有需要预加载的资源');
            return;
        }
        
        try {
            const loadPromises = preloadList.map(path => this.loadResource(path));
            await Promise.all(loadPromises);
            console.log('✅ 资源预加载完成');
        } catch (error) {
            console.warn('⚠️ 部分资源预加载失败:', error);
        }
    }

    /**
     * 加载单个资源
     */
    public async loadResource<T extends Asset>(path: string, type?: typeof Asset): Promise<T> {
        console.log(`📦 加载资源: ${path}`);
        
        // 检查缓存
        const cached = this._resourceCache.get(path);
        if (cached) {
            cached.refCount++;
            console.log(`📦 从缓存获取资源: ${path}`);
            return cached.resource as T;
        }
        
        // 检查是否正在加载
        if (this._loadingResources.has(path)) {
            console.log(`📦 资源正在加载中: ${path}`);
            return this.waitForResourceLoad<T>(path);
        }
        
        // 开始加载
        this._loadingResources.add(path);
        
        return new Promise<T>((resolve, reject) => {
            resources.load(path, type, (error, asset) => {
                this._loadingResources.delete(path);
                
                if (error) {
                    console.error(`❌ 资源加载失败: ${path}`, error);
                    reject(error);
                    return;
                }
                
                // 添加到缓存
                this.addToCache(path, asset);
                
                console.log(`✅ 资源加载成功: ${path}`);
                resolve(asset as T);
            });
        });
    }

    /**
     * 批量加载资源
     */
    public async loadResources<T extends Asset>(
        paths: string[], 
        type?: typeof Asset,
        onProgress?: (progress: LoadProgress) => void
    ): Promise<T[]> {
        console.log(`📦 批量加载资源: ${paths.length}个`);
        
        const results: T[] = [];
        let loaded = 0;
        
        for (const path of paths) {
            try {
                const asset = await this.loadResource<T>(path, type);
                results.push(asset);
                loaded++;
                
                // 触发进度回调
                if (onProgress) {
                    onProgress({
                        loaded,
                        total: paths.length,
                        progress: (loaded / paths.length) * 100,
                        currentPath: path
                    });
                }
            } catch (error) {
                console.error(`❌ 批量加载资源失败: ${path}`, error);
                // 继续加载其他资源
            }
        }
        
        console.log(`✅ 批量加载完成: ${results.length}/${paths.length}`);
        return results;
    }

    /**
     * 等待资源加载完成
     */
    private async waitForResourceLoad<T extends Asset>(path: string): Promise<T> {
        return new Promise<T>((resolve, reject) => {
            const checkInterval = setInterval(() => {
                if (!this._loadingResources.has(path)) {
                    clearInterval(checkInterval);
                    
                    const cached = this._resourceCache.get(path);
                    if (cached) {
                        cached.refCount++;
                        resolve(cached.resource as T);
                    } else {
                        reject(new Error(`资源加载失败: ${path}`));
                    }
                }
            }, 10);
        });
    }

    /**
     * 添加资源到缓存
     */
    private addToCache(path: string, asset: Asset): void {
        // 估算资源大小(简化实现)
        const estimatedSize = this.estimateAssetSize(asset);
        
        // 检查缓存限制
        if (this.shouldEvictCache(estimatedSize)) {
            this.evictLeastRecentlyUsed();
        }
        
        const cacheInfo: ResourceCacheInfo = {
            path,
            resource: asset,
            type: asset.constructor.name,
            cacheTime: Date.now(),
            refCount: 1
        };
        
        this._resourceCache.set(path, cacheInfo);
        this._currentCacheSize += estimatedSize;
        
        console.log(`📦 资源已缓存: ${path} (${this.formatSize(estimatedSize)})`);
    }

    /**
     * 估算资源大小
     */
    private estimateAssetSize(asset: Asset): number {
        // 这是一个简化的实现，实际项目中可能需要更精确的计算
        if (asset.constructor.name.includes('Texture')) {
            return 1024 * 1024; // 1MB
        } else if (asset.constructor.name.includes('Audio')) {
            return 512 * 1024; // 512KB
        } else {
            return 100 * 1024; // 100KB
        }
    }

    /**
     * 检查是否需要清理缓存
     */
    private shouldEvictCache(newAssetSize: number): boolean {
        const limitBytes = (this._config.cacheLimit || 100) * 1024 * 1024;
        return (this._currentCacheSize + newAssetSize) > limitBytes;
    }

    /**
     * 清理最近最少使用的资源
     */
    private evictLeastRecentlyUsed(): void {
        console.log('🗑️ 清理最近最少使用的资源...');
        
        // 找到引用计数为0且最久未使用的资源
        let oldestTime = Date.now();
        let oldestPath = '';
        
        for (const [path, info] of this._resourceCache) {
            if (info.refCount === 0 && info.cacheTime < oldestTime) {
                oldestTime = info.cacheTime;
                oldestPath = path;
            }
        }
        
        if (oldestPath) {
            this.removeFromCache(oldestPath);
        }
    }

    /**
     * 从缓存中移除资源
     */
    private removeFromCache(path: string): void {
        const cached = this._resourceCache.get(path);
        if (cached) {
            // 释放资源
            if (cached.resource && typeof cached.resource.destroy === 'function') {
                cached.resource.destroy();
            }
            
            this._currentCacheSize -= this.estimateAssetSize(cached.resource);
            this._resourceCache.delete(path);
            
            console.log(`🗑️ 资源已从缓存移除: ${path}`);
        }
    }

    /**
     * 释放资源引用
     */
    public releaseResource(path: string): void {
        const cached = this._resourceCache.get(path);
        if (cached) {
            cached.refCount = Math.max(0, cached.refCount - 1);
            console.log(`📦 释放资源引用: ${path} (引用计数: ${cached.refCount})`);
            
            // 如果引用计数为0，可以考虑延迟清理
            if (cached.refCount === 0) {
                // 这里可以设置一个延迟清理的定时器
            }
        }
    }

    /**
     * 清理所有缓存
     */
    public clearCache(): void {
        console.log('🗑️ 清理所有资源缓存...');
        
        for (const [path, info] of this._resourceCache) {
            if (info.resource && typeof info.resource.destroy === 'function') {
                info.resource.destroy();
            }
        }
        
        this._resourceCache.clear();
        this._currentCacheSize = 0;
        
        console.log('✅ 资源缓存已清理');
    }

    /**
     * 获取资源缓存信息
     */
    public getCacheInfo(path: string): ResourceCacheInfo | null {
        const cached = this._resourceCache.get(path);
        return cached ? { ...cached } : null;
    }

    /**
     * 检查资源是否已缓存
     */
    public isResourceCached(path: string): boolean {
        return this._resourceCache.has(path);
    }

    /**
     * 获取缓存统计信息
     */
    public getCacheStats(): {
        totalResources: number;
        totalSize: number;
        formattedSize: string;
        cacheLimit: number;
        usagePercentage: number;
    } {
        const limitBytes = (this._config.cacheLimit || 100) * 1024 * 1024;
        
        return {
            totalResources: this._resourceCache.size,
            totalSize: this._currentCacheSize,
            formattedSize: this.formatSize(this._currentCacheSize),
            cacheLimit: this._config.cacheLimit || 100,
            usagePercentage: (this._currentCacheSize / limitBytes) * 100
        };
    }

    /**
     * 格式化文件大小
     */
    private formatSize(bytes: number): string {
        if (bytes < 1024) return `${bytes}B`;
        if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
        return `${(bytes / (1024 * 1024)).toFixed(1)}MB`;
    }

    /**
     * 获取资源管理器配置
     */
    public getConfig(): ResourceManagerConfig {
        return { ...this._config };
    }

    /**
     * 更新资源管理器配置
     */
    public updateConfig(config: Partial<ResourceManagerConfig>): void {
        this._config = { ...this._config, ...config };
        console.log('⚙️ 资源管理器配置已更新:', config);
    }

    /**
     * 获取所有缓存的资源路径
     */
    public getCachedResourcePaths(): string[] {
        return Array.from(this._resourceCache.keys());
    }

    /**
     * 强制清理未使用的资源
     */
    public forceCleanUnusedResources(): void {
        console.log('🗑️ 强制清理未使用的资源...');
        
        const pathsToRemove: string[] = [];
        
        for (const [path, info] of this._resourceCache) {
            if (info.refCount === 0) {
                pathsToRemove.push(path);
            }
        }
        
        pathsToRemove.forEach(path => this.removeFromCache(path));
        
        console.log(`✅ 已清理${pathsToRemove.length}个未使用的资源`);
    }
}
