// 简单的API测试脚本
const http = require('http');

console.log('🔍 开始前后端联调测试...');

// 启动简单的测试服务器
const server = http.createServer((req, res) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Content-Type', 'application/json');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const url = req.url;
  const method = req.method;
  console.log(`📥 收到请求: ${method} ${url}`);

  // 健康检查
  if (url === '/health') {
    res.writeHead(200);
    res.end(JSON.stringify({
      status: 'ok',
      timestamp: new Date().toISOString(),
      message: '服务器运行正常'
    }));
    return;
  }

  // 用户登录
  if (url === '/api/v1/auth/login' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk.toString());
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('📝 登录请求数据:', data);
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: '登录成功',
          data: {
            userId: 'test_user_123',
            username: data.username || 'test_user',
            token: 'test_jwt_token_' + Date.now()
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: '请求数据格式错误' }));
      }
    });
    return;
  }

  // 获取技能列表
  if (url === '/api/v1/skills' && method === 'GET') {
    console.log('📝 获取技能列表请求');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: '获取技能列表成功',
      data: [
        { 
          id: 'fireball', 
          name: '火球术', 
          description: '发射一个火球攻击敌人',
          manaCost: 20, 
          cooldown: 3,
          damageType: 'magical',
          baseDamageMultiplier: 1.2
        },
        { 
          id: 'heal', 
          name: '治疗术', 
          description: '恢复生命值',
          manaCost: 15, 
          cooldown: 5,
          damageType: 'healing',
          baseDamageMultiplier: 0.8
        }
      ]
    }));
    return;
  }

  // 使用技能
  if (url === '/api/v1/skills/use' && method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk.toString());
    req.on('end', () => {
      try {
        const data = JSON.parse(body);
        console.log('📝 使用技能请求:', data);
        res.writeHead(200);
        res.end(JSON.stringify({
          success: true,
          message: '技能使用成功',
          data: {
            skillId: data.skillId,
            damage: Math.floor(Math.random() * 100) + 50,
            critical: Math.random() > 0.8,
            remainingCooldown: 3,
            usedAt: new Date().toISOString()
          }
        }));
      } catch (error) {
        res.writeHead(400);
        res.end(JSON.stringify({ success: false, message: '请求数据格式错误' }));
      }
    });
    return;
  }

  // 获取用户信息
  if (url === '/api/v1/users/profile' && method === 'GET') {
    console.log('📝 获取用户信息请求');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: '获取用户信息成功',
      data: {
        userId: 'test_user_123',
        username: 'test_user',
        level: 5,
        experience: 1250,
        coins: 5000,
        gems: 150
      }
    }));
    return;
  }

  // 获取游戏状态
  if (url === '/api/v1/game/status' && method === 'GET') {
    console.log('📝 获取游戏状态请求');
    res.writeHead(200);
    res.end(JSON.stringify({
      success: true,
      message: '获取游戏状态成功',
      data: {
        isOnline: true,
        serverTime: new Date().toISOString(),
        playerCount: Math.floor(Math.random() * 1000) + 500,
        serverStatus: 'healthy',
        version: '1.0.0'
      }
    }));
    return;
  }

  // 404处理
  console.log(`❌ 未找到路由: ${method} ${url}`);
  res.writeHead(404);
  res.end(JSON.stringify({ 
    success: false, 
    message: `路由 ${url} 不存在`,
    error: 'NOT_FOUND'
  }));
});

const port = 3001;
server.listen(port, () => {
  console.log(`🚀 测试API服务器启动成功`);
  console.log(`   端口: ${port}`);
  console.log(`   健康检查: http://localhost:${port}/health`);
  console.log(`   API接口: http://localhost:${port}/api/v1`);
  console.log('');
  console.log('📋 可用的API端点:');
  console.log('   GET  /health - 健康检查');
  console.log('   POST /api/v1/auth/login - 用户登录');
  console.log('   GET  /api/v1/users/profile - 获取用户信息');
  console.log('   GET  /api/v1/skills - 获取技能列表');
  console.log('   POST /api/v1/skills/use - 使用技能');
  console.log('   GET  /api/v1/game/status - 获取游戏状态');
  console.log('');
  console.log('🔍 服务器日志将显示所有请求...');
  console.log('');
  console.log('💡 测试建议:');
  console.log('   1. 在浏览器中访问 http://localhost:3001/health');
  console.log('   2. 使用Postman或curl测试POST接口');
  console.log('   3. 检查Cocos Creator前端是否能正常调用这些API');
  console.log('');
});

module.exports = server;
