import { _decorator, Component, Node, Label, Button, EditBox } from 'cc';
import { SkillSlot } from '../ui/skill/SkillSlot';

const { ccclass, property } = _decorator;

/**
 * SkillSlot调试工具
 * 提供实时调试和测试功能
 */
@ccclass('SkillSlotDebugger')
export class SkillSlotDebugger extends Component {
    
    @property(SkillSlot)
    targetSkillSlot: SkillSlot = null;
    
    @property(Label)
    statusLabel: Label = null;
    
    @property(EditBox)
    levelInput: EditBox = null;
    
    @property(EditBox)
    cooldownInput: EditBox = null;
    
    @property(Button)
    startCooldownBtn: Button = null;
    
    @property(Button)
    setLevelBtn: Button = null;
    
    @property(Button)
    toggleEnableBtn: Button = null;
    
    @property(Button)
    resetBtn: Button = null;
    
    private debugInfo: any = {};
    
    start() {
        this.initializeDebugger();
        this.setupEventListeners();
        this.startStatusMonitoring();
    }
    
    /**
     * 初始化调试器
     */
    private initializeDebugger() {
        console.log('SkillSlot调试器初始化');
        
        if (!this.targetSkillSlot) {
            console.warn('未设置目标SkillSlot，请在编辑器中指定');
            return;
        }
        
        this.updateDebugInfo();
    }
    
    /**
     * 设置事件监听
     */
    private setupEventListeners() {
        // 开始冷却按钮
        this.startCooldownBtn?.node.on(Button.EventType.CLICK, () => {
            this.onStartCooldown();
        });
        
        // 设置等级按钮
        this.setLevelBtn?.node.on(Button.EventType.CLICK, () => {
            this.onSetLevel();
        });
        
        // 切换启用状态按钮
        this.toggleEnableBtn?.node.on(Button.EventType.CLICK, () => {
            this.onToggleEnable();
        });
        
        // 重置按钮
        this.resetBtn?.node.on(Button.EventType.CLICK, () => {
            this.onReset();
        });
    }
    
    /**
     * 开始状态监控
     */
    private startStatusMonitoring() {
        this.schedule(() => {
            this.updateDebugInfo();
            this.updateStatusDisplay();
        }, 0.1); // 每100ms更新一次
    }
    
    /**
     * 更新调试信息
     */
    private updateDebugInfo() {
        if (!this.targetSkillSlot) return;
        
        // 获取技能槽的当前状态
        this.debugInfo = {
            isEnabled: this.targetSkillSlot.isEnabled(),
            isOnCooldown: this.targetSkillSlot.isOnCooldown(),
            currentLevel: this.targetSkillSlot.getCurrentLevel(),
            maxLevel: this.targetSkillSlot.getMaxLevel(),
            cooldownRemaining: this.targetSkillSlot.getCooldownRemaining(),
            skillName: this.targetSkillSlot.getSkillName(),
            hotkey: this.targetSkillSlot.getHotkey()
        };
    }
    
    /**
     * 更新状态显示
     */
    private updateStatusDisplay() {
        if (!this.statusLabel) return;
        
        const info = this.debugInfo;
        const statusText = `
技能名称: ${info.skillName || 'N/A'}
当前等级: ${info.currentLevel}/${info.maxLevel}
快捷键: ${info.hotkey || 'N/A'}
启用状态: ${info.isEnabled ? '启用' : '禁用'}
冷却状态: ${info.isOnCooldown ? '冷却中' : '可用'}
剩余冷却: ${info.cooldownRemaining?.toFixed(1) || '0.0'}s
        `.trim();
        
        this.statusLabel.string = statusText;
    }
    
    /**
     * 开始冷却测试
     */
    private onStartCooldown() {
        if (!this.targetSkillSlot) return;
        
        let cooldownTime = 3.0; // 默认冷却时间
        
        if (this.cooldownInput && this.cooldownInput.string) {
            const inputTime = parseFloat(this.cooldownInput.string);
            if (!isNaN(inputTime) && inputTime > 0) {
                cooldownTime = inputTime;
            }
        }
        
        console.log(`开始 ${cooldownTime}s 冷却测试`);
        this.targetSkillSlot.startCooldown();
    }
    
    /**
     * 设置等级
     */
    private onSetLevel() {
        if (!this.targetSkillSlot || !this.levelInput) return;
        
        const newLevel = parseInt(this.levelInput.string);
        if (isNaN(newLevel) || newLevel < 0) {
            console.warn('无效的等级值:', this.levelInput.string);
            return;
        }
        
        // 创建新的技能数据
        const currentData = this.targetSkillSlot.getSkillData();
        if (currentData) {
            const newData = { ...currentData };
            newData.currentLevel = Math.min(newLevel, newData.maxLevel);
            this.targetSkillSlot.updateSkillData(newData);
            console.log(`设置技能等级为: ${newData.currentLevel}`);
        }
    }
    
    /**
     * 切换启用状态
     */
    private onToggleEnable() {
        if (!this.targetSkillSlot) return;
        
        const currentState = this.targetSkillSlot.isEnabled();
        this.targetSkillSlot.setEnabled(!currentState);
        console.log(`技能槽状态切换为: ${!currentState ? '启用' : '禁用'}`);
    }
    
    /**
     * 重置技能槽
     */
    private onReset() {
        if (!this.targetSkillSlot) return;
        
        // 重置到初始状态
        this.targetSkillSlot.setEnabled(true);
        this.targetSkillSlot.stopCooldown();
        
        console.log('技能槽已重置');
    }
    
    /**
     * 设置目标技能槽（运行时调用）
     */
    public setTargetSkillSlot(skillSlot: SkillSlot) {
        this.targetSkillSlot = skillSlot;
        this.updateDebugInfo();
        console.log('设置新的调试目标:', skillSlot.node.name);
    }
    
    /**
     * 获取调试报告
     */
    public getDebugReport(): string {
        const info = this.debugInfo;
        return JSON.stringify(info, null, 2);
    }
    
    /**
     * 执行性能测试
     */
    public performanceTest() {
        if (!this.targetSkillSlot) return;
        
        console.log('开始性能测试...');
        const startTime = Date.now();
        
        // 快速切换状态测试
        for (let i = 0; i < 100; i++) {
            this.targetSkillSlot.setEnabled(i % 2 === 0);
        }
        
        // 快速更新测试
        const testData = this.targetSkillSlot.getSkillData();
        if (testData) {
            for (let i = 0; i < 100; i++) {
                const newData = { ...testData };
                newData.currentLevel = (i % testData.maxLevel) + 1;
                this.targetSkillSlot.updateSkillData(newData);
            }
        }
        
        const endTime = Date.now();
        console.log(`性能测试完成，耗时: ${endTime - startTime}ms`);
    }
}
