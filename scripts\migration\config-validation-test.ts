#!/usr/bin/env ts-node

/**
 * 配置数据一致性验证测试
 * 使用AI测试框架验证Godot和Cocos Creator版本的配置数据一致性
 */

import * as fs from 'fs';
import * as path from 'path';
import { MasterTestBot } from '../ai-testing/core/MasterTestBot';
import { TestGenerationAgent } from '../ai-testing/agents/TestGenerationAgent';
import { TestReportGenerator } from '../ai-testing/core/TestReportGenerator';
import chalk from 'chalk';

interface ConfigValidationOptions {
    godotConfigPath: string;
    cocosConfigPath: string;
    outputDir: string;
    verbose?: boolean;
    tolerance?: number;
}

interface ConfigComparisonResult {
    configType: string;
    godotData: any;
    cocosData: any;
    differences: ConfigDifference[];
    consistency: number;
    passed: boolean;
}

interface ConfigDifference {
    path: string;
    godotValue: any;
    cocosValue: any;
    type: 'missing' | 'different' | 'extra';
    severity: 'low' | 'medium' | 'high';
}

async function validateConfigConsistency(options: ConfigValidationOptions): Promise<void> {
    console.log(chalk.blue('🔍 配置数据一致性验证测试'));
    console.log(chalk.gray(`Godot配置路径: ${options.godotConfigPath}`));
    console.log(chalk.gray(`Cocos配置路径: ${options.cocosConfigPath}`));
    console.log('');

    try {
        // 1. 初始化AI测试框架
        console.log(chalk.blue('📋 步骤1: 初始化AI测试框架'));
        const masterBot = new MasterTestBot();
        const testGenerator = new TestGenerationAgent();
        const reportGenerator = new TestReportGenerator();
        console.log('✅ AI测试框架初始化完成\n');

        // 2. 发现配置文件
        console.log(chalk.blue('🔍 步骤2: 发现配置文件'));
        const godotConfigs = discoverConfigFiles(options.godotConfigPath);
        const cocosConfigs = discoverConfigFiles(options.cocosConfigPath);
        
        console.log(`📁 Godot配置文件: ${godotConfigs.length} 个`);
        console.log(`📁 Cocos配置文件: ${cocosConfigs.length} 个`);
        console.log('');

        // 3. 配对配置文件
        console.log(chalk.blue('🔗 步骤3: 配对配置文件'));
        const configPairs = pairConfigFiles(godotConfigs, cocosConfigs);
        console.log(`🔗 找到 ${configPairs.length} 对配置文件\n`);

        // 4. 执行一致性验证
        console.log(chalk.blue('⚡ 步骤4: 执行一致性验证'));
        const validationResults: ConfigComparisonResult[] = [];
        
        for (const pair of configPairs) {
            console.log(chalk.blue(`🔄 验证: ${pair.configType}`));
            const result = await validateConfigPair(pair, options);
            validationResults.push(result);
            
            if (result.passed) {
                console.log(chalk.green(`✅ ${pair.configType}: 一致性 ${(result.consistency * 100).toFixed(1)}%`));
            } else {
                console.log(chalk.red(`❌ ${pair.configType}: 一致性 ${(result.consistency * 100).toFixed(1)}% (${result.differences.length} 个差异)`));
            }
        }
        console.log('');

        // 5. 生成AI测试用例
        console.log(chalk.blue('🧪 步骤5: 生成AI测试用例'));
        const testCases = await generateConfigTestCases(validationResults, testGenerator);
        console.log(`📝 生成 ${testCases.length} 个测试用例\n`);

        // 6. 生成验证报告
        console.log(chalk.blue('📊 步骤6: 生成验证报告'));
        await generateValidationReport(validationResults, testCases, options, reportGenerator);

        // 7. 显示总结
        const totalConfigs = validationResults.length;
        const passedConfigs = validationResults.filter(r => r.passed).length;
        const overallConsistency = validationResults.reduce((sum, r) => sum + r.consistency, 0) / totalConfigs;
        
        console.log(chalk.green('\n🎉 配置数据一致性验证完成!'));
        console.log(chalk.gray(`📊 总体结果:`));
        console.log(chalk.gray(`  配置文件对数: ${totalConfigs}`));
        console.log(chalk.gray(`  通过验证: ${passedConfigs}`));
        console.log(chalk.gray(`  失败验证: ${totalConfigs - passedConfigs}`));
        console.log(chalk.gray(`  总体一致性: ${(overallConsistency * 100).toFixed(1)}%`));
        
        if (overallConsistency >= 0.95) {
            console.log(chalk.green('🎯 优秀的配置一致性!'));
        } else if (overallConsistency >= 0.85) {
            console.log(chalk.yellow('⚠️ 良好的一致性，有少量差异需要注意'));
        } else {
            console.log(chalk.red('❌ 配置差异较大，需要仔细检查'));
        }

    } catch (error) {
        console.error(chalk.red('\n❌ 配置验证失败:'), error);
        process.exit(1);
    }
}

function discoverConfigFiles(configPath: string): string[] {
    const configFiles: string[] = [];
    
    if (!fs.existsSync(configPath)) {
        console.warn(chalk.yellow(`⚠️ 配置路径不存在: ${configPath}`));
        return configFiles;
    }
    
    function scanDirectory(dir: string) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (isConfigFile(item)) {
                configFiles.push(fullPath);
            }
        }
    }
    
    scanDirectory(configPath);
    return configFiles;
}

function isConfigFile(fileName: string): boolean {
    const configExtensions = ['.xml', '.json'];
    const configNames = ['skill', 'entity', 'item', 'quest', 'level'];
    
    const ext = path.extname(fileName).toLowerCase();
    const name = path.basename(fileName, ext).toLowerCase();
    
    return configExtensions.includes(ext) && 
           configNames.some(configName => name.includes(configName));
}

function pairConfigFiles(godotConfigs: string[], cocosConfigs: string[]): any[] {
    const pairs: any[] = [];
    
    for (const godotConfig of godotConfigs) {
        const godotName = getConfigType(godotConfig);
        
        for (const cocosConfig of cocosConfigs) {
            const cocosName = getConfigType(cocosConfig);
            
            if (godotName === cocosName) {
                pairs.push({
                    configType: godotName,
                    godotPath: godotConfig,
                    cocosPath: cocosConfig
                });
                break;
            }
        }
    }
    
    return pairs;
}

function getConfigType(filePath: string): string {
    const fileName = path.basename(filePath, path.extname(filePath)).toLowerCase();
    
    if (fileName.includes('skill')) return 'skills';
    if (fileName.includes('entity') || fileName.includes('entities')) return 'entities';
    if (fileName.includes('item')) return 'items';
    if (fileName.includes('quest')) return 'quests';
    if (fileName.includes('level')) return 'levels';
    
    return fileName;
}

async function validateConfigPair(pair: any, options: ConfigValidationOptions): Promise<ConfigComparisonResult> {
    try {
        // 加载配置数据
        const godotData = loadConfigData(pair.godotPath);
        const cocosData = loadConfigData(pair.cocosPath);
        
        // 比较配置数据
        const differences = compareConfigData(godotData, cocosData, '');
        
        // 计算一致性分数
        const consistency = calculateConsistency(godotData, cocosData, differences);
        
        // 判断是否通过
        const tolerance = options.tolerance || 0.95;
        const passed = consistency >= tolerance;
        
        return {
            configType: pair.configType,
            godotData,
            cocosData,
            differences,
            consistency,
            passed
        };
        
    } catch (error) {
        console.error(chalk.red(`❌ 验证配置对失败: ${pair.configType}`), error);
        
        return {
            configType: pair.configType,
            godotData: null,
            cocosData: null,
            differences: [],
            consistency: 0,
            passed: false
        };
    }
}

function loadConfigData(filePath: string): any {
    const content = fs.readFileSync(filePath, 'utf8');
    const ext = path.extname(filePath).toLowerCase();
    
    if (ext === '.json') {
        return JSON.parse(content);
    } else if (ext === '.xml') {
        // 简化的XML解析，实际项目中应使用xml2js
        return { xml: content };
    }
    
    throw new Error(`不支持的配置文件格式: ${ext}`);
}

function compareConfigData(godotData: any, cocosData: any, basePath: string): ConfigDifference[] {
    const differences: ConfigDifference[] = [];
    
    // 简化的比较逻辑
    if (typeof godotData !== typeof cocosData) {
        differences.push({
            path: basePath,
            godotValue: godotData,
            cocosValue: cocosData,
            type: 'different',
            severity: 'high'
        });
        return differences;
    }
    
    if (typeof godotData === 'object' && godotData !== null) {
        // 比较对象属性
        const allKeys = new Set([...Object.keys(godotData), ...Object.keys(cocosData)]);
        
        for (const key of allKeys) {
            const currentPath = basePath ? `${basePath}.${key}` : key;
            
            if (!(key in godotData)) {
                differences.push({
                    path: currentPath,
                    godotValue: undefined,
                    cocosValue: cocosData[key],
                    type: 'missing',
                    severity: 'medium'
                });
            } else if (!(key in cocosData)) {
                differences.push({
                    path: currentPath,
                    godotValue: godotData[key],
                    cocosValue: undefined,
                    type: 'extra',
                    severity: 'medium'
                });
            } else {
                differences.push(...compareConfigData(godotData[key], cocosData[key], currentPath));
            }
        }
    } else if (godotData !== cocosData) {
        differences.push({
            path: basePath,
            godotValue: godotData,
            cocosValue: cocosData,
            type: 'different',
            severity: 'low'
        });
    }
    
    return differences;
}

function calculateConsistency(godotData: any, cocosData: any, differences: ConfigDifference[]): number {
    if (!godotData || !cocosData) return 0;
    
    // 计算总字段数
    const totalFields = countFields(godotData) + countFields(cocosData);
    if (totalFields === 0) return 1;
    
    // 计算差异权重
    let totalWeight = 0;
    for (const diff of differences) {
        switch (diff.severity) {
            case 'high': totalWeight += 3; break;
            case 'medium': totalWeight += 2; break;
            case 'low': totalWeight += 1; break;
        }
    }
    
    // 计算一致性分数
    const consistency = Math.max(0, 1 - (totalWeight / totalFields));
    return consistency;
}

function countFields(obj: any): number {
    if (typeof obj !== 'object' || obj === null) return 1;
    
    let count = 0;
    for (const key in obj) {
        count += countFields(obj[key]);
    }
    return count;
}

async function generateConfigTestCases(results: ConfigComparisonResult[], testGenerator: TestGenerationAgent): Promise<any[]> {
    const testCases: any[] = [];
    
    for (const result of results) {
        // 为每个配置类型生成测试用例
        const configTestCases = await testGenerator.generateAlgorithmConsistencyTests(
            JSON.stringify(result.godotData),
            JSON.stringify(result.cocosData),
            result.configType
        );
        
        testCases.push(...configTestCases);
    }
    
    return testCases;
}

async function generateValidationReport(
    results: ConfigComparisonResult[],
    testCases: any[],
    options: ConfigValidationOptions,
    reportGenerator: TestReportGenerator
): Promise<void> {
    // 转换为测试结果格式
    const testResults = results.map(result => ({
        id: `config-validation-${result.configType}`,
        name: `${result.configType} Configuration Validation`,
        status: result.passed ? 'passed' : 'failed' as const,
        executionTime: 100,
        errorMessage: result.passed ? undefined : `一致性不足: ${(result.consistency * 100).toFixed(1)}%`,
        assertions: [{
            description: 'Configuration consistency check',
            passed: result.passed,
            expected: '>=95% consistency',
            actual: `${(result.consistency * 100).toFixed(1)}% consistency`
        }]
    }));
    
    // 生成报告
    const report = await reportGenerator.generateReport(testResults, {
        projectName: '武侠放置游戏配置验证',
        projectPath: options.cocosConfigPath,
        testFramework: 'ai-testing'
    });
    
    // 保存报告
    const outputDir = options.outputDir || './validation-reports';
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }
    
    const htmlReportPath = path.join(outputDir, `config-validation-${Date.now()}.html`);
    const jsonReportPath = path.join(outputDir, `config-validation-${Date.now()}.json`);
    
    await reportGenerator.generateHTMLReport(report, htmlReportPath);
    await reportGenerator.generateJSONReport(report, jsonReportPath);
    
    console.log(chalk.green(`📄 HTML报告: ${htmlReportPath}`));
    console.log(chalk.green(`📄 JSON报告: ${jsonReportPath}`));
}

// 主函数
async function main(): Promise<void> {
    const options: ConfigValidationOptions = {
        godotConfigPath: '../../idlegame/Data',
        cocosConfigPath: '../../assets/resources/config',
        outputDir: './validation-reports',
        verbose: true,
        tolerance: 0.95
    };
    
    await validateConfigConsistency(options);
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 配置验证失败:', error);
        process.exit(1);
    });
}

export { validateConfigConsistency, ConfigValidationOptions, ConfigComparisonResult };
