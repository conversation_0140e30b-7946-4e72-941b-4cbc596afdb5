{"timestamp": "2025-07-24T14:32:59.773Z", "fixedFiles": ["assets\\scripts\\AutoRegister.ts", "assets\\scripts\\config\\interfaces\\IEntityData.ts", "assets\\scripts\\config\\interfaces\\IItemData.ts", "assets\\scripts\\config\\interfaces\\IQuestData.ts", "assets\\scripts\\config\\interfaces\\IRewardData.ts", "assets\\scripts\\config\\interfaces\\ISkillData.ts", "assets\\scripts\\core\\utils\\TestRunner.ts", "assets\\scripts\\main.ts", "assets\\scripts\\managers\\AudioManager.ts", "assets\\scripts\\managers\\BaseManager.ts", "assets\\scripts\\managers\\ConfigManager.ts", "assets\\scripts\\managers\\EventManager.ts", "assets\\scripts\\managers\\GameManager.ts", "assets\\scripts\\managers\\index.ts", "assets\\scripts\\managers\\InputManager.ts", "assets\\scripts\\managers\\ResourceManager.ts", "assets\\scripts\\managers\\SceneManager.ts", "assets\\scripts\\network\\HttpClient.ts", "assets\\scripts\\network\\NetworkManager.ts", "assets\\scripts\\network\\WebSocketClient.ts", "assets\\scripts\\scenes\\BattleScene.ts", "assets\\scripts\\scenes\\LaunchScene.ts", "assets\\scripts\\scenes\\MainScene.ts", "assets\\scripts\\systems\\characters\\Character.ts", "assets\\scripts\\test\\BasicTest.ts", "assets\\scripts\\test\\ConfigManagerQuickTest.ts", "assets\\scripts\\test\\ConfigManagerTest.ts", "assets\\scripts\\test\\Day2IntegrationTest.ts", "assets\\scripts\\test\\EmergencyKeyboardTest.ts", "assets\\scripts\\test\\KeyboardInputTest.ts", "assets\\scripts\\test\\ManagerTest.ts", "assets\\scripts\\test\\NetworkIntegrationTest.ts", "assets\\scripts\\test\\NetworkTest.ts", "assets\\scripts\\test\\PerformanceBenchmarkTest.ts", "assets\\scripts\\test\\QuickConfigVerify.ts", "assets\\scripts\\test\\SimpleConfigTest.ts", "assets\\scripts\\test\\SimpleKeyboardTest.ts", "assets\\scripts\\test\\SimpleLaunchTest.ts", "assets\\scripts\\test\\SimpleTest.ts", "assets\\scripts\\test\\StandaloneTest.ts", "assets\\scripts\\test\\SystemIntegrationTest.ts", "assets\\scripts\\test\\TestManager.ts", "assets\\scripts\\ui\\components\\SceneSwitchButtons.ts"], "issues": {"consoleLogs": 1122, "todos": 0, "longLines": 6, "trailingSpaces": 35, "emptyLines": 3}, "totalIssues": 1166}