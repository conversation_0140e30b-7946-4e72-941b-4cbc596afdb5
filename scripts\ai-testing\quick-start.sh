#!/bin/bash

# AI测试框架快速启动脚本
# 用于快速设置和运行AI测试框架

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_header() {
    echo ""
    print_message $BLUE "🤖 AI测试框架快速启动"
    print_message $BLUE "=================================="
    echo ""
}

print_step() {
    local step=$1
    local description=$2
    print_message $YELLOW "📋 步骤${step}: ${description}"
}

print_success() {
    print_message $GREEN "✅ $1"
}

print_error() {
    print_message $RED "❌ $1"
}

print_warning() {
    print_message $YELLOW "⚠️ $1"
}

# 检查Node.js环境
check_nodejs() {
    print_step "1" "检查Node.js环境"
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js未安装，请先安装Node.js (版本 >= 16.0.0)"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2)
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1)
    
    if [ "$MAJOR_VERSION" -lt 16 ]; then
        print_error "Node.js版本过低 ($NODE_VERSION)，需要版本 >= 16.0.0"
        exit 1
    fi
    
    print_success "Node.js版本: $NODE_VERSION"
    
    if ! command -v npm &> /dev/null; then
        print_error "npm未安装"
        exit 1
    fi
    
    NPM_VERSION=$(npm --version)
    print_success "npm版本: $NPM_VERSION"
}

# 安装依赖
install_dependencies() {
    print_step "2" "安装项目依赖"
    
    if [ ! -f "package.json" ]; then
        print_error "package.json文件不存在，请确保在正确的目录中运行此脚本"
        exit 1
    fi
    
    print_message $BLUE "正在安装依赖..."
    npm install
    
    if [ $? -eq 0 ]; then
        print_success "依赖安装完成"
    else
        print_error "依赖安装失败"
        exit 1
    fi
}

# 构建项目
build_project() {
    print_step "3" "构建TypeScript项目"
    
    if [ -f "tsconfig.json" ]; then
        print_message $BLUE "正在编译TypeScript..."
        npm run build
        
        if [ $? -eq 0 ]; then
            print_success "TypeScript编译完成"
        else
            print_warning "TypeScript编译失败，但可以继续使用ts-node运行"
        fi
    else
        print_warning "未找到tsconfig.json，跳过编译步骤"
    fi
}

# 创建输出目录
create_output_dirs() {
    print_step "4" "创建输出目录"
    
    mkdir -p test-results
    mkdir -p validation-reports
    mkdir -p examples/output
    
    print_success "输出目录创建完成"
}

# 运行快速检查
run_quick_check() {
    print_step "5" "运行快速项目检查"
    
    print_message $BLUE "正在执行快速检查..."
    npm run ai-test:quick-check -- --project-path ../../
    
    if [ $? -eq 0 ]; then
        print_success "快速检查完成"
    else
        print_warning "快速检查发现一些问题，但不影响继续使用"
    fi
}

# 运行示例
run_example() {
    print_step "6" "运行使用示例"
    
    if [ -f "examples/usage-example.ts" ]; then
        print_message $BLUE "正在运行使用示例..."
        npx ts-node examples/usage-example.ts
        
        if [ $? -eq 0 ]; then
            print_success "示例运行完成"
        else
            print_warning "示例运行遇到问题"
        fi
    else
        print_warning "未找到使用示例文件"
    fi
}

# 显示使用指南
show_usage_guide() {
    print_step "7" "显示使用指南"
    
    echo ""
    print_message $GREEN "🎉 AI测试框架安装完成！"
    echo ""
    print_message $BLUE "📚 常用命令:"
    echo "  npm run ai-test:setup                    # 设置AI测试系统"
    echo "  npm run ai-test:discover                 # 发现系统并生成测试"
    echo "  npm run ai-test:validate-algorithms      # 验证算法一致性"
    echo "  npm run ai-test:quick-check              # 快速项目检查"
    echo "  npm run ai-test:report                   # 生成测试报告"
    echo ""
    print_message $BLUE "🔧 CLI工具:"
    echo "  node index.js setup --project ../../    # 设置项目测试"
    echo "  node index.js discover --project ../../ # 发现并测试系统"
    echo "  node index.js validate                  # 验证算法一致性"
    echo "  node index.js stats                     # 显示统计信息"
    echo "  node index.js clean                     # 清理缓存"
    echo ""
    print_message $BLUE "📁 输出目录:"
    echo "  ./test-results/          # 测试结果和报告"
    echo "  ./validation-reports/    # 算法验证报告"
    echo "  ./examples/output/       # 示例输出文件"
    echo ""
    print_message $BLUE "📖 文档:"
    echo "  ./README.md              # 详细使用文档"
    echo "  ./examples/              # 使用示例"
    echo "  ../../plans/ai-testing/  # 设计文档"
    echo ""
}

# 主函数
main() {
    print_header
    
    # 检查是否在正确的目录
    if [ ! -f "package.json" ] || [ ! -d "core" ] || [ ! -d "agents" ]; then
        print_error "请在AI测试框架目录中运行此脚本 (scripts/ai-testing/)"
        exit 1
    fi
    
    # 执行安装步骤
    check_nodejs
    install_dependencies
    build_project
    create_output_dirs
    run_quick_check
    
    # 询问是否运行示例
    echo ""
    read -p "是否运行使用示例? (y/n): " -n 1 -r
    echo ""
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        run_example
    fi
    
    show_usage_guide
    
    print_message $GREEN "🚀 准备就绪！开始使用AI测试框架吧！"
}

# 错误处理
trap 'print_error "脚本执行被中断"; exit 1' INT

# 运行主函数
main "$@"
