/**
 * 测试模板注册表 - 管理和提供各种测试模板
 * 支持动态模板选择和自定义模板注册
 */
import { TestTemplate, SystemInfo } from '../types';
export declare class TestTemplateRegistry {
    private templates;
    private templatesByType;
    private templatesByComplexity;
    constructor();
    /**
     * 为系统选择适用的测试模板
     */
    selectTemplatesForSystem(systemInfo: SystemInfo): TestTemplate[];
    /**
     * 注册新的测试模板
     */
    registerTemplate(template: TestTemplate): void;
    /**
     * 获取模板
     */
    getTemplate(templateId: string): TestTemplate | undefined;
    /**
     * 获取所有模板
     */
    getAllTemplates(): TestTemplate[];
    /**
     * 根据类型获取模板
     */
    getTemplatesByType(systemType: string): TestTemplate[];
    /**
     * 搜索模板
     */
    searchTemplates(query: string): TestTemplate[];
    /**
     * 动态创建模板
     */
    createTemplateFromSystem(systemInfo: SystemInfo): Promise<TestTemplate>;
    /**
     * 初始化默认模板
     */
    private initializeDefaultTemplates;
    /**
     * 构建索引
     */
    private buildIndices;
    /**
     * 更新索引
     */
    private updateIndices;
    /**
     * 检查模板是否适用
     */
    private isTemplateApplicable;
    /**
     * 按优先级排序模板
     */
    private prioritizeTemplates;
    /**
     * 推断测试类型
     */
    private inferTestTypes;
    /**
     * 生成模板定义
     */
    private generateTemplateDefinition;
}
//# sourceMappingURL=TestTemplateRegistry.d.ts.map