import { _decorator, Component, Node, input, Input, EventKeyboard, KeyCode, director } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 简化的启动场景测试组件
 * 用于替代复杂的LaunchScene，避免依赖问题
 */
@ccclass('SimpleLaunchTest')
export class SimpleLaunchTest extends Component {

    protected onLoad(): void {
        console.log('🚀 简化启动测试组件加载');
        this.initializeKeyboardInput();
    }

    protected start(): void {
        console.log('🚀 简化启动测试组件开始');
        this.showTestInstructions();
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ 键盘输入已初始化');
    }

    /**
     * 显示测试说明
     */
    private showTestInstructions(): void {
        console.log('🎮 ========== 简化场景测试 ==========');
        console.log('📍 当前场景: Launch (启动场景)');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 切换到 Launch 场景');
        console.log('   按 2 键 - 切换到 Main 场景');
        console.log('   按 3 键 - 切换到 Battle 场景');
        console.log('   按 T 键 - 测试键盘输入');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🎮 ===================================');
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        console.log(`⌨️ 检测到按键: ${event.keyCode}`);
        
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.switchScene('Launch');
                break;
            case KeyCode.DIGIT_2:
                this.switchScene('Main');
                break;
            case KeyCode.DIGIT_3:
                this.switchScene('Battle');
                break;
            case KeyCode.KEY_T:
                console.log('🧪 键盘输入测试成功！');
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 简单的场景切换方法
     */
    private switchScene(sceneName: string): void {
        console.log(`🔄 切换到场景: ${sceneName}`);
        director.loadScene(sceneName, (error) => {
            if (error) {
                console.error(`❌ 场景切换失败: ${sceneName}`, error);
            } else {
                console.log(`✅ 场景切换成功: ${sceneName}`);
            }
        });
    }

    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🚀 简化启动测试组件销毁');
    }
}
