/**
 * 微信云开发云函数入口文件
 * 完整的游戏API实现，基于后端Express架构
 */

const tcb = require('@cloudbase/node-sdk');
const express = require('express');
const cors = require('cors');

// 初始化云开发
const app = tcb.init({
  env: tcb.SYMBOL_CURRENT_ENV
});

const db = app.database();

// 创建Express应用
const expressApp = express();

// 中间件配置
expressApp.use(cors());
expressApp.use(express.json({ limit: '10mb' }));
expressApp.use(express.urlencoded({ extended: true }));

// 请求日志中间件
expressApp.use((req, res, next) => {
  console.log(`${req.method} ${req.path}`, {
    body: req.body,
    query: req.query,
    timestamp: new Date().toISOString()
  });
  next();
});

// 健康检查
expressApp.get('/api/health', (_req, res) => {
  res.json({
    status: 'ok',
    message: 'IdleGame API is running on WeChat Cloud Functions',
    timestamp: new Date().toISOString(),
    environment: 'cloud-function',
    version: '1.0.0'
  });
});

// ==================== 认证相关API ====================

/**
 * 用户注册
 */
expressApp.post('/api/v1/auth/register', async (req, res) => {
  try {
    const { username, email, password } = req.body;

    if (!username || !email || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名、邮箱和密码都是必填项',
        error: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // 检查用户是否已存在
    const userCollection = db.collection('users');
    const existingUser = await userCollection.where({
      $or: [{ username }, { email }]
    }).get();

    if (existingUser.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: '用户名或邮箱已存在',
        error: 'USER_ALREADY_EXISTS'
      });
    }

    // 创建新用户
    const newUser = {
      username,
      email,
      password, // 实际应用中需要加密
      role: 'user',
      isActive: true,
      isEmailVerified: false,
      profile: {
        nickname: username,
        avatar: 'https://via.placeholder.com/100',
        bio: '',
        location: '',
        website: ''
      },
      gameData: {
        charactersCreated: 0,
        totalGoldEarned: 0,
        totalBattlesWon: 0,
        totalBattlesLost: 0,
        achievements: [],
        statistics: {
          monstersKilled: 0,
          itemsCollected: 0,
          skillsLearned: 0,
          questsCompleted: 0
        }
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await userCollection.add(newUser);

    res.status(201).json({
      success: true,
      message: '用户注册成功',
      data: {
        user: {
          id: result.id,
          username: newUser.username,
          email: newUser.email,
          profile: newUser.profile
        },
        token: `jwt_token_${result.id}_${Date.now()}`
      }
    });
  } catch (error) {
    console.error('用户注册失败:', error);
    res.status(500).json({
      success: false,
      message: '用户注册失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * 用户登录
 */
expressApp.post('/api/v1/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码都是必填项',
        error: 'MISSING_CREDENTIALS'
      });
    }

    // 查找用户
    const userCollection = db.collection('users');
    const userResult = await userCollection.where({
      $or: [{ username }, { email: username }]
    }).get();

    if (userResult.data.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误',
        error: 'INVALID_CREDENTIALS'
      });
    }

    const user = userResult.data[0];

    // 验证密码（实际应用中需要使用加密比较）
    if (user.password !== password) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误',
        error: 'INVALID_CREDENTIALS'
      });
    }

    // 更新最后登录时间
    await userCollection.doc(user._id).update({
      lastLoginAt: new Date()
    });

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: {
          id: user._id,
          username: user.username,
          email: user.email,
          profile: user.profile,
          gameData: user.gameData
        },
        token: `jwt_token_${user._id}_${Date.now()}`
      }
    });
  } catch (error) {
    console.error('用户登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * 微信小程序登录
 */
expressApp.post('/api/auth/wechat-login', async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({
        success: false,
        message: '缺少微信登录code',
        error: 'MISSING_WECHAT_CODE'
      });
    }

    // 这里应该调用微信API获取用户信息
    // 暂时返回模拟数据
    const mockUser = {
      id: 'user_' + Date.now(),
      openid: 'mock_openid_' + Math.random().toString(36).substring(2, 11),
      nickname: '游戏玩家',
      avatar: 'https://via.placeholder.com/100',
      createdAt: new Date()
    };

    // 保存用户到数据库
    const userCollection = db.collection('users');
    await userCollection.add(mockUser);

    res.json({
      success: true,
      message: '微信登录成功',
      data: {
        user: mockUser,
        token: 'mock_jwt_token_' + mockUser.id
      }
    });
  } catch (error) {
    console.error('微信登录失败:', error);
    res.status(500).json({
      success: false,
      message: '微信登录失败',
      error: 'WECHAT_LOGIN_FAILED'
    });
  }
});

// ==================== 用户管理API ====================

/**
 * 获取用户资料
 */
expressApp.get('/api/v1/users/profile', async (req, res) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '缺少用户ID',
        error: 'MISSING_USER_ID'
      });
    }

    const userCollection = db.collection('users');
    const userResult = await userCollection.doc(userId).get();

    if (!userResult.data || userResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在',
        error: 'USER_NOT_FOUND'
      });
    }

    const user = userResult.data[0];

    res.json({
      success: true,
      message: '获取用户资料成功',
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        profile: user.profile,
        gameData: user.gameData,
        createdAt: user.createdAt
      }
    });
  } catch (error) {
    console.error('获取用户资料失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户资料失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * 更新用户资料
 */
expressApp.put('/api/v1/users/profile', async (req, res) => {
  try {
    const { userId, profile } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '缺少用户ID',
        error: 'MISSING_USER_ID'
      });
    }

    const userCollection = db.collection('users');
    const updateData = {
      'profile.nickname': profile.nickname,
      'profile.bio': profile.bio,
      'profile.location': profile.location,
      'profile.website': profile.website,
      updatedAt: new Date()
    };

    await userCollection.doc(userId).update(updateData);

    res.json({
      success: true,
      message: '用户资料更新成功',
      data: { updated: true }
    });
  } catch (error) {
    console.error('更新用户资料失败:', error);
    res.status(500).json({
      success: false,
      message: '更新用户资料失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

// ==================== 角色管理API ====================

/**
 * 获取角色列表
 */
expressApp.get('/api/v1/characters', async (req, res) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '缺少用户ID',
        error: 'MISSING_USER_ID'
      });
    }

    const characterCollection = db.collection('characters');
    const charactersResult = await characterCollection.where({ userId }).get();

    res.json({
      success: true,
      message: '获取角色列表成功',
      data: charactersResult.data
    });
  } catch (error) {
    console.error('获取角色列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取角色列表失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * 创建角色
 */
expressApp.post('/api/v1/characters', async (req, res) => {
  try {
    const { userId, name, characterClass } = req.body;

    if (!userId || !name || !characterClass) {
      return res.status(400).json({
        success: false,
        message: '用户ID、角色名称和职业都是必填项',
        error: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // 检查角色名是否已存在
    const characterCollection = db.collection('characters');
    const existingCharacter = await characterCollection.where({ name }).get();

    if (existingCharacter.data.length > 0) {
      return res.status(409).json({
        success: false,
        message: '角色名已存在',
        error: 'CHARACTER_NAME_EXISTS'
      });
    }

    // 创建新角色
    const newCharacter = {
      userId,
      name,
      class: characterClass,
      level: 1,
      experience: 0,
      attributes: {
        health: { current: 100, max: 100 },
        mana: { current: 50, max: 50 },
        attack: 10,
        defense: 5,
        speed: 8,
        luck: 5
      },
      skills: [],
      inventory: {
        items: [],
        capacity: 20,
        gold: 100
      },
      equipment: {
        weapon: null,
        armor: null,
        accessory: null
      },
      statistics: {
        monstersKilled: 0,
        itemsFound: 0,
        questsCompleted: 0,
        battlesFought: 0,
        battlesWon: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await characterCollection.add(newCharacter);

    res.status(201).json({
      success: true,
      message: '角色创建成功',
      data: {
        id: result.id,
        ...newCharacter
      }
    });
  } catch (error) {
    console.error('创建角色失败:', error);
    res.status(500).json({
      success: false,
      message: '创建角色失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * 获取角色详情
 */
expressApp.get('/api/v1/characters/:characterId', async (req, res) => {
  try {
    const { characterId } = req.params;

    const characterCollection = db.collection('characters');
    const characterResult = await characterCollection.doc(characterId).get();

    if (!characterResult.data || characterResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: '角色不存在',
        error: 'CHARACTER_NOT_FOUND'
      });
    }

    res.json({
      success: true,
      message: '获取角色详情成功',
      data: characterResult.data[0]
    });
  } catch (error) {
    console.error('获取角色详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取角色详情失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

// ==================== 技能管理API ====================

/**
 * 获取技能列表
 */
expressApp.get('/api/v1/skills', async (_req, res) => {
  try {
    const skillCollection = db.collection('skills');
    const skillsResult = await skillCollection.get();

    res.json({
      success: true,
      message: '获取技能列表成功',
      data: skillsResult.data
    });
  } catch (error) {
    console.error('获取技能列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取技能列表失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * 获取角色技能
 */
expressApp.get('/api/v1/skills/characters/:characterId', async (req, res) => {
  try {
    const { characterId } = req.params;

    const characterCollection = db.collection('characters');
    const characterResult = await characterCollection.doc(characterId).get();

    if (!characterResult.data || characterResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: '角色不存在',
        error: 'CHARACTER_NOT_FOUND'
      });
    }

    const character = characterResult.data[0];

    res.json({
      success: true,
      message: '获取角色技能成功',
      data: {
        skills: character.skills || [],
        skillPoints: character.skillPoints || 0
      }
    });
  } catch (error) {
    console.error('获取角色技能失败:', error);
    res.status(500).json({
      success: false,
      message: '获取角色技能失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * 学习技能
 */
expressApp.post('/api/v1/skills/characters/:characterId/learn', async (req, res) => {
  try {
    const { characterId } = req.params;
    const { skillId } = req.body;

    if (!skillId) {
      return res.status(400).json({
        success: false,
        message: '缺少技能ID',
        error: 'MISSING_SKILL_ID'
      });
    }

    // 获取角色信息
    const characterCollection = db.collection('characters');
    const characterResult = await characterCollection.doc(characterId).get();

    if (!characterResult.data || characterResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: '角色不存在',
        error: 'CHARACTER_NOT_FOUND'
      });
    }

    const character = characterResult.data[0];

    // 检查是否已学习该技能
    const hasSkill = character.skills.some(skill => skill.skillId === skillId);
    if (hasSkill) {
      return res.status(409).json({
        success: false,
        message: '已学习该技能',
        error: 'SKILL_ALREADY_LEARNED'
      });
    }

    // 获取技能信息
    const skillCollection = db.collection('skills');
    const skillResult = await skillCollection.where({ id: skillId }).get();

    if (skillResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: '技能不存在',
        error: 'SKILL_NOT_FOUND'
      });
    }

    const skill = skillResult.data[0];

    // 检查技能点是否足够
    const requiredPoints = skill.requirements?.skillPoints || 1;
    const currentPoints = character.skillPoints || 0;

    if (currentPoints < requiredPoints) {
      return res.status(400).json({
        success: false,
        message: '技能点不足',
        error: 'INSUFFICIENT_SKILL_POINTS'
      });
    }

    // 学习技能
    const newSkill = {
      skillId,
      level: 1,
      experience: 0,
      learnedAt: new Date()
    };

    await characterCollection.doc(characterId).update({
      skills: db.command.push(newSkill),
      skillPoints: db.command.inc(-requiredPoints),
      updatedAt: new Date()
    });

    res.json({
      success: true,
      message: '技能学习成功',
      data: {
        skill: newSkill,
        remainingSkillPoints: currentPoints - requiredPoints
      }
    });
  } catch (error) {
    console.error('学习技能失败:', error);
    res.status(500).json({
      success: false,
      message: '学习技能失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

// ==================== 物品管理API ====================

/**
 * 获取物品列表
 */
expressApp.get('/api/v1/items', async (_req, res) => {
  try {
    const itemCollection = db.collection('items');
    const itemsResult = await itemCollection.get();

    res.json({
      success: true,
      message: '获取物品列表成功',
      data: itemsResult.data
    });
  } catch (error) {
    console.error('获取物品列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取物品列表失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * 获取角色背包
 */
expressApp.get('/api/v1/items/characters/:characterId/inventory', async (req, res) => {
  try {
    const { characterId } = req.params;

    const characterCollection = db.collection('characters');
    const characterResult = await characterCollection.doc(characterId).get();

    if (!characterResult.data || characterResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: '角色不存在',
        error: 'CHARACTER_NOT_FOUND'
      });
    }

    const character = characterResult.data[0];

    res.json({
      success: true,
      message: '获取背包成功',
      data: {
        items: character.inventory?.items || [],
        capacity: character.inventory?.capacity || 20,
        gold: character.inventory?.gold || 0
      }
    });
  } catch (error) {
    console.error('获取背包失败:', error);
    res.status(500).json({
      success: false,
      message: '获取背包失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * 添加物品到背包
 */
expressApp.post('/api/v1/items/characters/:characterId/add', async (req, res) => {
  try {
    const { characterId } = req.params;
    const { itemId, quantity = 1 } = req.body;

    if (!itemId) {
      return res.status(400).json({
        success: false,
        message: '缺少物品ID',
        error: 'MISSING_ITEM_ID'
      });
    }

    // 获取角色信息
    const characterCollection = db.collection('characters');
    const characterResult = await characterCollection.doc(characterId).get();

    if (!characterResult.data || characterResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: '角色不存在',
        error: 'CHARACTER_NOT_FOUND'
      });
    }

    const character = characterResult.data[0];
    const inventory = character.inventory || { items: [], capacity: 20, gold: 0 };

    // 检查背包容量
    if (inventory.items.length >= inventory.capacity) {
      return res.status(400).json({
        success: false,
        message: '背包已满',
        error: 'INVENTORY_FULL'
      });
    }

    // 检查物品是否存在
    const itemCollection = db.collection('items');
    const itemResult = await itemCollection.where({ id: itemId }).get();

    if (itemResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: '物品不存在',
        error: 'ITEM_NOT_FOUND'
      });
    }

    const item = itemResult.data[0];

    // 检查是否可堆叠
    if (item.stackable) {
      const existingItem = inventory.items.find(invItem => invItem.itemId === itemId);
      if (existingItem) {
        existingItem.quantity += quantity;
      } else {
        inventory.items.push({
          itemId,
          quantity,
          obtainedAt: new Date()
        });
      }
    } else {
      for (let i = 0; i < quantity; i++) {
        inventory.items.push({
          itemId,
          quantity: 1,
          obtainedAt: new Date()
        });
      }
    }

    // 更新角色背包
    await characterCollection.doc(characterId).update({
      inventory,
      updatedAt: new Date()
    });

    res.json({
      success: true,
      message: '物品添加成功',
      data: {
        item: {
          ...item,
          quantity
        },
        inventory
      }
    });
  } catch (error) {
    console.error('添加物品失败:', error);
    res.status(500).json({
      success: false,
      message: '添加物品失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

// ==================== 游戏数据同步API ====================

/**
 * 游戏数据同步
 */
expressApp.post('/api/v1/game/sync', async (req, res) => {
  try {
    const { userId, gameData } = req.body;

    if (!userId || !gameData) {
      return res.status(400).json({
        success: false,
        message: '缺少必要参数',
        error: 'MISSING_REQUIRED_FIELDS'
      });
    }

    // 保存游戏数据
    const gameCollection = db.collection('game_sessions');
    const result = await gameCollection.add({
      userId,
      gameData,
      timestamp: new Date(),
      syncType: 'manual'
    });

    res.json({
      success: true,
      message: '游戏数据同步成功',
      data: {
        syncId: result.id,
        timestamp: new Date()
      }
    });
  } catch (error) {
    console.error('数据同步失败:', error);
    res.status(500).json({
      success: false,
      message: '数据同步失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

/**
 * 获取玩家数据
 */
expressApp.get('/api/v1/player/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    // 从数据库获取玩家数据
    const userCollection = db.collection('users');
    const userResult = await userCollection.doc(userId).get();

    if (!userResult.data || userResult.data.length === 0) {
      return res.status(404).json({
        success: false,
        message: '玩家不存在',
        error: 'PLAYER_NOT_FOUND'
      });
    }

    const user = userResult.data[0];

    // 获取游戏数据
    const gameCollection = db.collection('game_sessions');
    const gameResult = await gameCollection
      .where({ userId })
      .orderBy('timestamp', 'desc')
      .limit(1)
      .get();

    res.json({
      success: true,
      message: '获取玩家数据成功',
      data: {
        player: user,
        gameData: gameResult.data[0] || null
      }
    });
  } catch (error) {
    console.error('获取玩家数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取玩家数据失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

// ==================== 排行榜API ====================

/**
 * 获取排行榜
 */
expressApp.get('/api/v1/leaderboard', async (req, res) => {
  try {
    const { type = 'level', limit = 10 } = req.query;

    const characterCollection = db.collection('characters');
    let query = characterCollection;

    // 根据类型排序
    switch (type) {
      case 'level':
        query = query.orderBy('level', 'desc');
        break;
      case 'gold':
        query = query.orderBy('inventory.gold', 'desc');
        break;
      default:
        query = query.orderBy('level', 'desc');
    }

    const result = await query.limit(parseInt(limit)).get();

    const leaderboard = result.data.map((character, index) => ({
      rank: index + 1,
      characterId: character._id,
      name: character.name,
      level: character.level,
      class: character.class,
      score: type === 'gold' ? character.inventory?.gold || 0 : character.level
    }));

    res.json({
      success: true,
      message: '获取排行榜成功',
      data: {
        leaderboard,
        type,
        total: leaderboard.length
      }
    });
  } catch (error) {
    console.error('获取排行榜失败:', error);
    res.status(500).json({
      success: false,
      message: '获取排行榜失败',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
});

// 错误处理中间件
expressApp.use((error, _req, res, _next) => {
  console.error('API错误:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: 'INTERNAL_SERVER_ERROR'
  });
});

// 404处理
expressApp.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    error: 'ENDPOINT_NOT_FOUND',
    path: req.originalUrl
  });
});

/**
 * 测试数据库权限的函数
 */
async function testDatabasePermissions(_event, context) {
  console.log('🧪 开始测试数据库权限...');

  try {
    const db = app.database();
    const testResults = [];

    // 获取当前用户信息（从微信小程序context中）
    const { OPENID } = context;
    console.log('当前用户OPENID:', OPENID);

    // 测试1：创建测试用户数据
    console.log('📝 测试1：创建用户数据...');
    try {
      const createResult = await db.collection('users').add({
        uid: OPENID,
        nickname: '测试用户',
        level: 1,
        exp: 0,
        createdAt: new Date(),
        testFlag: true
      });
      testResults.push({
        test: '创建用户数据',
        status: 'success',
        message: `创建成功，ID: ${createResult.id}`
      });
      console.log('✅ 创建用户数据成功');
    } catch (error) {
      testResults.push({
        test: '创建用户数据',
        status: 'failed',
        message: error.message
      });
      console.log('❌ 创建用户数据失败:', error.message);
    }

    // 测试2：读取自己的数据（应该成功）
    console.log('📖 测试2：读取自己的数据...');
    try {
      const userResult = await db.collection('users')
        .where({ uid: OPENID })
        .get();
      testResults.push({
        test: '读取自己的数据',
        status: 'success',
        message: `找到 ${userResult.data.length} 条记录`
      });
      console.log('✅ 读取自己的数据成功:', userResult.data.length, '条');
    } catch (error) {
      testResults.push({
        test: '读取自己的数据',
        status: 'failed',
        message: error.message
      });
      console.log('❌ 读取自己的数据失败:', error.message);
    }

    // 测试3：尝试读取他人数据（应该失败或返回空）
    console.log('🚫 测试3：尝试读取他人数据...');
    try {
      const otherUserResult = await db.collection('users')
        .where({ uid: 'fake_other_user_id' })
        .get();

      if (otherUserResult.data.length === 0) {
        testResults.push({
          test: '读取他人数据',
          status: 'success',
          message: '正确阻止了访问他人数据（返回空结果）'
        });
        console.log('✅ 权限正常：无法访问他人数据');
      } else {
        testResults.push({
          test: '读取他人数据',
          status: 'warning',
          message: `意外获取到 ${otherUserResult.data.length} 条他人数据`
        });
        console.log('⚠️ 权限警告：获取到他人数据');
      }
    } catch (error) {
      testResults.push({
        test: '读取他人数据',
        status: 'success',
        message: `正确阻止了访问：${error.message}`
      });
      console.log('✅ 权限正常：', error.message);
    }

    // 测试4：测试公共数据读取（skills集合）
    console.log('🌐 测试4：测试公共数据读取...');
    try {
      // 先创建一些测试技能数据
      await db.collection('skills').add({
        id: 'test_skill_1',
        name: '测试技能',
        description: '这是一个测试技能',
        level: 1,
        public: true
      });

      const skillsResult = await db.collection('skills')
        .where({ public: true })
        .get();

      testResults.push({
        test: '读取公共数据',
        status: 'success',
        message: `成功读取 ${skillsResult.data.length} 个技能`
      });
      console.log('✅ 读取公共数据成功');
    } catch (error) {
      testResults.push({
        test: '读取公共数据',
        status: 'failed',
        message: error.message
      });
      console.log('❌ 读取公共数据失败:', error.message);
    }

    // 测试5：测试游戏数据同步
    console.log('🎮 测试5：测试游戏数据同步...');
    try {
      const gameData = {
        userId: OPENID,
        level: 5,
        exp: 1200,
        coins: 5000,
        lastSaveTime: new Date(),
        testFlag: true
      };

      const syncResult = await db.collection('game_sessions').add(gameData);
      testResults.push({
        test: '游戏数据同步',
        status: 'success',
        message: `同步成功，ID: ${syncResult.id}`
      });
      console.log('✅ 游戏数据同步成功');
    } catch (error) {
      testResults.push({
        test: '游戏数据同步',
        status: 'failed',
        message: error.message
      });
      console.log('❌ 游戏数据同步失败:', error.message);
    }

    return {
      success: true,
      message: '数据库权限测试完成',
      testResults,
      userInfo: {
        openid: OPENID,
        timestamp: new Date().toISOString()
      }
    };

  } catch (error) {
    console.error('❌ 测试过程出错:', error);
    return {
      success: false,
      message: '测试过程出错',
      error: error.message
    };
  }
}

/**
 * 云函数入口函数
 */
exports.main = async (event, context) => {
  console.log('云函数被调用:', {
    path: event.path,
    method: event.httpMethod,
    headers: event.headers,
    openid: context.OPENID
  });

  try {
    // 将云函数事件转换为Express请求
    const req = {
      method: event.httpMethod || 'GET',
      url: event.path || '/',
      headers: event.headers || {},
      body: event.body ? (typeof event.body === 'string' ? JSON.parse(event.body) : event.body) : {},
      query: event.queryStringParameters || {}
    };

    // 创建响应对象
    const createResponse = (statusCode, data) => {
      return {
        statusCode: statusCode || 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type,Authorization'
        },
        body: JSON.stringify(data)
      };
    };

    // 特殊路由：数据库权限测试
    if (req.url.startsWith('/api/test/database') && req.method === 'GET') {
      const testResult = await testDatabasePermissions(event, context);
      return createResponse(200, testResult);
    }

    // 模拟Express路由处理
    if (req.url.startsWith('/api/health') && req.method === 'GET') {
      return createResponse(200, {
        status: 'ok',
        message: 'IdleGame API is running on WeChat Cloud Functions',
        timestamp: new Date().toISOString(),
        environment: 'cloud-function',
        openid: context.OPENID || 'anonymous'
      });
    } else if (req.url.startsWith('/api/auth/login') && req.method === 'POST') {
      // 处理登录逻辑
      const mockUser = {
        id: 'user_' + Date.now(),
        openid: 'mock_openid_' + Math.random().toString(36).substring(2, 11),
        nickname: '游戏玩家',
        avatar: 'https://via.placeholder.com/100',
        createdAt: new Date()
      };

      return createResponse(200, {
        success: true,
        user: mockUser,
        token: 'mock_jwt_token_' + mockUser.id
      });
    } else {
      return createResponse(404, {
        error: '接口不存在',
        path: req.url
      });
    }

  } catch (error) {
    console.error('处理请求失败:', error);
    return createResponse(500, {
      error: '服务器内部错误',
      message: error.message
    });
  }
};
