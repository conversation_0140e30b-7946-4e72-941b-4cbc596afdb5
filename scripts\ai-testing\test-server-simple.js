#!/usr/bin/env node

/**
 * 简单测试服务器 - 用于AI测试框架
 */

const http = require('http');

const server = http.createServer((req, res) => {
    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
    res.setHeader('Content-Type', 'application/json');

    const url = req.url;
    const method = req.method;

    console.log(`${new Date().toISOString()} - ${method} ${url}`);

    // 健康检查端点
    if (url === '/health') {
        res.statusCode = 200;
        res.end(JSON.stringify({
            status: 'ok',
            message: 'Test server is running',
            timestamp: new Date().toISOString(),
            uptime: process.uptime()
        }));
        return;
    }

    // API状态端点
    if (url === '/api/status') {
        res.statusCode = 200;
        res.end(JSON.stringify({
            api: 'active',
            version: '1.0.0',
            endpoints: ['/health', '/api/status', '/api/test']
        }));
        return;
    }

    // 测试端点
    if (url === '/api/test') {
        res.statusCode = 200;
        res.end(JSON.stringify({
            test: 'success',
            message: 'AI测试框架连接正常',
            data: {
                timestamp: new Date().toISOString(),
                random: Math.random()
            }
        }));
        return;
    }

    // 404 处理
    res.statusCode = 404;
    res.end(JSON.stringify({
        error: 'Not Found',
        message: `Endpoint ${url} not found`
    }));
});

const PORT = 3001;

server.listen(PORT, () => {
    console.log(`🚀 简单测试服务器启动成功！`);
    console.log(`📡 监听端口: ${PORT}`);
    console.log(`🔗 健康检查: http://localhost:${PORT}/health`);
    console.log(`📊 API状态: http://localhost:${PORT}/api/status`);
    console.log(`🧪 测试端点: http://localhost:${PORT}/api/test`);
    console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
    console.log('按 Ctrl+C 停止服务器\n');
});

server.on('error', (err) => {
    console.error('❌ 服务器错误:', err);
    process.exit(1);
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});

process.on('SIGTERM', () => {
    console.log('\n🛑 收到终止信号，正在关闭服务器...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});
