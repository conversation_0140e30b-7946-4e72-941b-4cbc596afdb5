import { _decorator, Component, Node } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 基础管理器抽象类
 * 提供单例模式和生命周期管理的基础功能
 */
@ccclass('BaseManager')
export abstract class BaseManager extends Component {
    
    /**
     * 单例实例存储
     */
    protected static _instances: Map<string, BaseManager> = new Map();
    
    /**
     * 管理器是否已初始化
     */
    protected _isInitialized: boolean = false;
    
    /**
     * 管理器是否已销毁
     */
    protected _isDestroyed: boolean = false;

    /**
     * 获取管理器单例实例
     * @returns 管理器实例
     */
    public static getInstance<T extends BaseManager>(this: new () => T): T {
        const className = this.name;
        
        if (!BaseManager._instances.has(className)) {
            const instance = new this();
            BaseManager._instances.set(className, instance);
            console.log(`🎯 创建管理器实例: ${className}`);
        }
        
        return BaseManager._instances.get(className) as T;
    }

    /**
     * 检查管理器是否存在
     * @returns 是否存在实例
     */
    public static hasInstance<T extends BaseManager>(this: new () => T): boolean {
        return BaseManager._instances.has(this.name);
    }

    /**
     * 销毁管理器实例
     */
    public static destroyInstance<T extends BaseManager>(this: new () => T): void {
        const className = this.name;
        const instance = BaseManager._instances.get(className);
        
        if (instance) {
            instance.destroyManager();
            BaseManager._instances.delete(className);
            console.log(`🗑️ 销毁管理器实例: ${className}`);
        }
    }

    /**
     * 组件生命周期 - 加载时调用
     */
    protected onLoad(): void {
        console.log(`📦 ${this.constructor.name} 组件加载`);
    }

    /**
     * 组件生命周期 - 启动时调用
     */
    protected start(): void {
        if (!this._isInitialized) {
            this.initializeManager().then(() => {
                this._isInitialized = true;
                console.log(`✅ ${this.constructor.name} 初始化完成`);
            }).catch((error) => {
                console.error(`❌ ${this.constructor.name} 初始化失败:`, error);
            });
        }
    }

    /**
     * 组件生命周期 - 销毁时调用
     */
    protected onDestroy(): void {
        if (!this._isDestroyed) {
            this.destroyManager();
            this._isDestroyed = true;
            console.log(`🗑️ ${this.constructor.name} 组件销毁`);
        }
    }

    /**
     * 抽象方法：初始化管理器
     * 子类必须实现此方法来定义具体的初始化逻辑
     */
    protected abstract initializeManager(): Promise<void>;

    /**
     * 抽象方法：销毁管理器
     * 子类必须实现此方法来定义具体的清理逻辑
     */
    public abstract destroyManager(): void;

    /**
     * 获取管理器是否已初始化
     */
    public isInitialized(): boolean {
        return this._isInitialized;
    }

    /**
     * 获取管理器是否已销毁
     */
    public isDestroyed(): boolean {
        return this._isDestroyed;
    }

    /**
     * 等待管理器初始化完成
     */
    public async waitForInitialization(): Promise<void> {
        return new Promise<void>((resolve) => {
            if (this._isInitialized) {
                resolve();
                return;
            }

            const checkInterval = setInterval(() => {
                if (this._isInitialized) {
                    clearInterval(checkInterval);
                    resolve();
                }
            }, 10);
        });
    }

    /**
     * 重新初始化管理器
     */
    public async reinitialize(): Promise<void> {
        if (this._isInitialized) {
            this.destroyManager();
            this._isInitialized = false;
            this._isDestroyed = false;
        }

        await this.initializeManager();
        this._isInitialized = true;
        console.log(`🔄 ${this.constructor.name} 重新初始化完成`);
    }

    /**
     * 获取管理器状态信息
     */
    public getStatus(): { initialized: boolean; destroyed: boolean; className: string } {
        return {
            initialized: this._isInitialized,
            destroyed: this._isDestroyed,
            className: this.constructor.name
        };
    }
}
