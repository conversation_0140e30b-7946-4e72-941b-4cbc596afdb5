import { Logger } from './logger';
import { CacheAlert } from './cacheMonitor';
import { CacheMonitorConfig, NotificationConfig } from '../config/cacheMonitorConfig';

/**
 * 通知渠道枚举
 */
export enum NotificationChannel {
  EMAIL = 'email',
  SLACK = 'slack',
  WEBHOOK = 'webhook',
  LOG = 'log',
}

/**
 * 通知消息接口
 */
export interface NotificationMessage {
  title: string;
  content: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  metadata?: any;
}

/**
 * 缓存通知器类
 */
export class CacheNotifier {
  private static instance: CacheNotifier;
  private config: NotificationConfig;
  private sentNotifications: Map<string, Date> = new Map();

  private constructor() {
    const monitorConfig = CacheMonitorConfig.getInstance();
    this.config = monitorConfig.getNotificationConfig();
  }

  public static getInstance(): CacheNotifier {
    if (!CacheNotifier.instance) {
      CacheNotifier.instance = new CacheNotifier();
    }
    return CacheNotifier.instance;
  }

  /**
   * 发送告警通知
   */
  public async sendAlert(alert: CacheAlert): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    // 检查是否是重复通知
    if (this.isDuplicateNotification(alert)) {
      return;
    }

    const message = this.formatAlertMessage(alert);
    const channels = this.getNotificationChannels(alert.severity);

    for (const channel of channels) {
      try {
        await this.sendNotification(channel, message);
        Logger.info('告警通知发送成功', {
          channel,
          alertType: alert.type,
          severity: alert.severity,
        });
      } catch (error) {
        Logger.error('告警通知发送失败', {
          channel,
          alertType: alert.type,
          error,
        });
      }
    }

    // 记录已发送的通知
    this.recordSentNotification(alert);
  }

  /**
   * 发送监控报告
   */
  public async sendReport(report: any): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    const message = this.formatReportMessage(report);
    const channels = [NotificationChannel.EMAIL, NotificationChannel.SLACK];

    for (const channel of channels) {
      try {
        await this.sendNotification(channel, message);
        Logger.info('监控报告发送成功', { channel });
      } catch (error) {
        Logger.error('监控报告发送失败', { channel, error });
      }
    }
  }

  /**
   * 发送健康检查通知
   */
  public async sendHealthCheckNotification(isHealthy: boolean, details: any): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    const message = this.formatHealthCheckMessage(isHealthy, details);
    const channels = isHealthy ? 
      [NotificationChannel.LOG] : 
      [NotificationChannel.EMAIL, NotificationChannel.SLACK, NotificationChannel.WEBHOOK];

    for (const channel of channels) {
      try {
        await this.sendNotification(channel, message);
      } catch (error) {
        Logger.error('健康检查通知发送失败', { channel, error });
      }
    }
  }

  /**
   * 检查是否是重复通知
   */
  private isDuplicateNotification(alert: CacheAlert): boolean {
    const key = `${alert.type}_${alert.severity}`;
    const lastSent = this.sentNotifications.get(key);
    
    if (!lastSent) {
      return false;
    }

    const duplicateWindow = 5 * 60 * 1000; // 5分钟
    return (Date.now() - lastSent.getTime()) < duplicateWindow;
  }

  /**
   * 记录已发送的通知
   */
  private recordSentNotification(alert: CacheAlert): void {
    const key = `${alert.type}_${alert.severity}`;
    this.sentNotifications.set(key, new Date());

    // 清理过期记录
    const cutoffTime = new Date(Date.now() - 60 * 60 * 1000); // 1小时前
    for (const [k, timestamp] of this.sentNotifications.entries()) {
      if (timestamp < cutoffTime) {
        this.sentNotifications.delete(k);
      }
    }
  }

  /**
   * 获取通知渠道
   */
  private getNotificationChannels(severity: string): NotificationChannel[] {
    const channels: NotificationChannel[] = [NotificationChannel.LOG];

    switch (severity) {
      case 'critical':
        channels.push(NotificationChannel.EMAIL, NotificationChannel.SLACK, NotificationChannel.WEBHOOK);
        break;
      case 'high':
        channels.push(NotificationChannel.EMAIL, NotificationChannel.SLACK);
        break;
      case 'medium':
        channels.push(NotificationChannel.SLACK);
        break;
      case 'low':
        // 只记录日志
        break;
    }

    return channels;
  }

  /**
   * 发送通知
   */
  private async sendNotification(channel: NotificationChannel, message: NotificationMessage): Promise<void> {
    switch (channel) {
      case NotificationChannel.EMAIL:
        await this.sendEmailNotification(message);
        break;
      case NotificationChannel.SLACK:
        await this.sendSlackNotification(message);
        break;
      case NotificationChannel.WEBHOOK:
        await this.sendWebhookNotification(message);
        break;
      case NotificationChannel.LOG:
        this.sendLogNotification(message);
        break;
    }
  }

  /**
   * 发送邮件通知
   */
  private async sendEmailNotification(message: NotificationMessage): Promise<void> {
    if (!this.config.emailEnabled || this.config.emailRecipients.length === 0) {
      return;
    }

    // 这里应该集成实际的邮件服务
    Logger.info('发送邮件通知', {
      recipients: this.config.emailRecipients,
      title: message.title,
      severity: message.severity,
    });

    // 模拟邮件发送
    // await emailService.send({
    //   to: this.config.emailRecipients,
    //   subject: message.title,
    //   html: this.formatEmailContent(message),
    // });
  }

  /**
   * 发送Slack通知
   */
  private async sendSlackNotification(message: NotificationMessage): Promise<void> {
    if (!this.config.slackEnabled || !this.config.slackWebhook) {
      return;
    }

    const payload = {
      text: message.title,
      attachments: [
        {
          color: this.getSeverityColor(message.severity),
          fields: [
            {
              title: '详细信息',
              value: message.content,
              short: false,
            },
            {
              title: '时间',
              value: message.timestamp.toISOString(),
              short: true,
            },
            {
              title: '严重程度',
              value: message.severity.toUpperCase(),
              short: true,
            },
          ],
        },
      ],
    };

    // 这里应该发送到实际的Slack Webhook
    Logger.info('发送Slack通知', {
      webhook: this.config.slackWebhook,
      title: message.title,
      severity: message.severity,
    });

    // 模拟Slack发送
    // await fetch(this.config.slackWebhook, {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(payload),
    // });
  }

  /**
   * 发送Webhook通知
   */
  private async sendWebhookNotification(message: NotificationMessage): Promise<void> {
    if (!this.config.webhookUrl) {
      return;
    }

    const payload = {
      type: 'cache_alert',
      message,
      timestamp: new Date().toISOString(),
    };

    // 这里应该发送到实际的Webhook
    Logger.info('发送Webhook通知', {
      url: this.config.webhookUrl,
      title: message.title,
      severity: message.severity,
    });

    // 模拟Webhook发送
    // await fetch(this.config.webhookUrl, {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(payload),
    // });
  }

  /**
   * 发送日志通知
   */
  private sendLogNotification(message: NotificationMessage): void {
    const logLevel = message.severity === 'critical' ? 'error' : 
                    message.severity === 'high' ? 'warn' : 'info';
    
    Logger[logLevel]('缓存监控通知', {
      title: message.title,
      content: message.content,
      severity: message.severity,
      timestamp: message.timestamp,
    });
  }

  /**
   * 格式化告警消息
   */
  private formatAlertMessage(alert: CacheAlert): NotificationMessage {
    return {
      title: `缓存告警: ${alert.message}`,
      content: `告警类型: ${alert.type}\n严重程度: ${alert.severity}\n当前值: ${alert.value}\n阈值: ${alert.threshold}\n时间: ${alert.timestamp.toISOString()}`,
      severity: alert.severity,
      timestamp: alert.timestamp,
      metadata: { alert },
    };
  }

  /**
   * 格式化报告消息
   */
  private formatReportMessage(report: any): NotificationMessage {
    return {
      title: `缓存监控报告 - ${report.period}`,
      content: `监控周期: ${report.period}\n总指标数: ${report.summary?.totalMetrics || 0}\n总告警数: ${report.summary?.totalAlerts || 0}\n平均命中率: ${report.summary?.avgHitRate?.toFixed(2) || 0}%`,
      severity: 'low',
      timestamp: new Date(),
      metadata: { report },
    };
  }

  /**
   * 格式化健康检查消息
   */
  private formatHealthCheckMessage(isHealthy: boolean, details: any): NotificationMessage {
    return {
      title: `缓存系统健康检查 - ${isHealthy ? '正常' : '异常'}`,
      content: `健康状态: ${isHealthy ? '正常' : '异常'}\n详细信息: ${JSON.stringify(details, null, 2)}`,
      severity: isHealthy ? 'low' : 'high',
      timestamp: new Date(),
      metadata: { isHealthy, details },
    };
  }

  /**
   * 获取严重程度颜色
   */
  private getSeverityColor(severity: string): string {
    switch (severity) {
      case 'critical': return 'danger';
      case 'high': return 'warning';
      case 'medium': return 'good';
      case 'low': return '#36a64f';
      default: return '#36a64f';
    }
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 测试通知
   */
  public async testNotification(channel: NotificationChannel): Promise<boolean> {
    const testMessage: NotificationMessage = {
      title: '缓存监控测试通知',
      content: '这是一条测试通知，用于验证通知渠道是否正常工作。',
      severity: 'low',
      timestamp: new Date(),
    };

    try {
      await this.sendNotification(channel, testMessage);
      return true;
    } catch (error) {
      Logger.error('测试通知发送失败', { channel, error });
      return false;
    }
  }

  /**
   * 获取通知统计
   */
  public getNotificationStats(): any {
    return {
      totalSent: this.sentNotifications.size,
      recentNotifications: Array.from(this.sentNotifications.entries()).map(([key, timestamp]) => ({
        key,
        timestamp,
      })),
      config: this.config,
    };
  }
}
