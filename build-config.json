{"buildConfigs": {"wechatgame": {"platform": "wechatgame", "debug": false, "sourceMaps": false, "minify": true, "orientation": "portrait", "separateEngine": true, "startScene": "Launch", "packageName": "com.wuxia.idlegame", "appid": "your_wechat_appid", "buildPath": "build/wechatgame", "optimization": {"mergeStartScene": true, "optimizeHotUpdate": true, "md5Cache": true, "compressTexture": true, "compressAudio": true}, "performance": {"maxTextureSize": 1024, "maxAudioSize": 512, "enableBatching": true, "enableCulling": true}}, "bytedance-mini-game": {"platform": "bytedance-mini-game", "debug": false, "sourceMaps": false, "minify": true, "orientation": "portrait", "separateEngine": true, "startScene": "Launch", "packageName": "com.wuxia.idlegame", "appid": "your_douyin_appid", "buildPath": "build/bytedance-mini-game", "optimization": {"mergeStartScene": true, "optimizeHotUpdate": true, "md5Cache": true, "compressTexture": true, "compressAudio": true}, "performance": {"maxTextureSize": 1024, "maxAudioSize": 512, "enableBatching": true, "enableCulling": true}}}, "commonSettings": {"excludeModules": ["3d", "physics-cannon", "physics-builtin"], "includeModules": ["2d", "ui", "audio", "network", "tween", "animation"]}}