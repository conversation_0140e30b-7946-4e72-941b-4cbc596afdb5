"use strict";
/**
 * 代码分析代理 - 智能代码分析和理解
 * 分析代码结构、复杂度、依赖关系等
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.CodeAnalysisAgent = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class CodeAnalysisAgent {
    constructor() {
        this.analysisCache = new Map();
    }
    /**
     * 分析代码文件或目录
     */
    async analyzeCode(codePath) {
        console.log(`🔍 CodeAnalysisAgent: Analyzing code at ${codePath}`);
        // 检查缓存
        if (this.analysisCache.has(codePath)) {
            return this.analysisCache.get(codePath);
        }
        try {
            let result;
            if (fs.statSync(codePath).isDirectory()) {
                result = await this.analyzeDirectory(codePath);
            }
            else {
                result = await this.analyzeFile(codePath);
            }
            // 缓存结果
            this.analysisCache.set(codePath, result);
            console.log(`✅ Code analysis complete: ${result.complexity} complexity`);
            return result;
        }
        catch (error) {
            console.error(`❌ Failed to analyze code at ${codePath}:`, error);
            throw error;
        }
    }
    /**
     * 分析单个文件
     */
    async analyzeFile(filePath) {
        const content = fs.readFileSync(filePath, 'utf8');
        const extension = path.extname(filePath);
        const features = this.extractCodeFeatures(content, extension);
        const metrics = this.calculateCodeMetrics(content, extension);
        const dependencies = this.extractDependencies(content, extension);
        const complexity = this.determineComplexity(metrics, features);
        const suggestions = this.generateSuggestions(metrics, features);
        return {
            complexity,
            features,
            dependencies,
            metrics,
            suggestions
        };
    }
    /**
     * 分析目录
     */
    async analyzeDirectory(dirPath) {
        const files = this.getCodeFiles(dirPath);
        const allResults = [];
        for (const file of files) {
            try {
                const result = await this.analyzeFile(file);
                allResults.push(result);
            }
            catch (error) {
                console.warn(`⚠️ Failed to analyze ${file}:`, error);
            }
        }
        return this.aggregateResults(allResults);
    }
    /**
     * 提取函数信息
     */
    extractFunctions(content, extension) {
        const functions = [];
        if (extension === '.ts' || extension === '.js') {
            // TypeScript/JavaScript 函数提取
            const functionRegex = /(?:export\s+)?(?:async\s+)?function\s+(\w+)\s*\(([^)]*)\)(?:\s*:\s*([^{]+))?\s*{/g;
            let match;
            while ((match = functionRegex.exec(content)) !== null) {
                const [, name, params, returnType] = match;
                functions.push({
                    name,
                    parameters: this.parseParameters(params),
                    returnType: returnType?.trim() || 'any',
                    complexity: this.calculateFunctionComplexity(content, name),
                    isAsync: match[0].includes('async'),
                    isExported: match[0].includes('export')
                });
            }
            // 箭头函数
            const arrowFunctionRegex = /(?:export\s+)?(?:const|let|var)\s+(\w+)\s*=\s*(?:async\s+)?\(([^)]*)\)(?:\s*:\s*([^=]+))?\s*=>/g;
            while ((match = arrowFunctionRegex.exec(content)) !== null) {
                const [, name, params, returnType] = match;
                functions.push({
                    name,
                    parameters: this.parseParameters(params),
                    returnType: returnType?.trim() || 'any',
                    complexity: this.calculateFunctionComplexity(content, name),
                    isAsync: match[0].includes('async'),
                    isExported: match[0].includes('export')
                });
            }
        }
        return functions;
    }
    /**
     * 提取代码特征
     */
    extractCodeFeatures(content, extension) {
        return {
            hasClasses: /class\s+\w+/.test(content),
            hasInterfaces: /interface\s+\w+/.test(content),
            hasAsyncCode: /async\s+|await\s+|Promise/.test(content),
            hasEventHandlers: /addEventListener|on\w+\s*=|emit\(/.test(content),
            hasAlgorithms: /for\s*\(|while\s*\(|forEach|map\(|filter\(|reduce\(/.test(content),
            hasDataStructures: /Array|Map|Set|Object|class\s+\w+/.test(content),
            hasNetworking: /fetch\(|axios|http|websocket|XMLHttpRequest/.test(content),
            hasUI: /component|render|createElement|@Component|cc\.|node\./.test(content)
        };
    }
    /**
     * 计算代码指标
     */
    calculateCodeMetrics(content, extension) {
        const lines = content.split('\n');
        const linesOfCode = lines.filter(line => line.trim() &&
            !line.trim().startsWith('//') &&
            !line.trim().startsWith('/*') &&
            !line.trim().startsWith('*')).length;
        const cyclomaticComplexity = this.calculateCyclomaticComplexity(content);
        const maintainabilityIndex = this.calculateMaintainabilityIndex(linesOfCode, cyclomaticComplexity);
        const duplicateCodePercentage = this.calculateDuplicateCode(content);
        return {
            linesOfCode,
            cyclomaticComplexity,
            maintainabilityIndex,
            testCoverage: 0, // 默认值，实际需要从测试工具获取
            duplicateCodePercentage
        };
    }
    /**
     * 提取依赖关系
     */
    extractDependencies(content, extension) {
        const dependencies = [];
        if (extension === '.ts' || extension === '.js') {
            // import 语句
            const importRegex = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
            let match;
            while ((match = importRegex.exec(content)) !== null) {
                dependencies.push(match[1]);
            }
            // require 语句
            const requireRegex = /require\(['"]([^'"]+)['"]\)/g;
            while ((match = requireRegex.exec(content)) !== null) {
                dependencies.push(match[1]);
            }
        }
        return [...new Set(dependencies)]; // 去重
    }
    /**
     * 确定复杂度等级
     */
    determineComplexity(metrics, features) {
        let complexityScore = 0;
        // 基于代码行数
        if (metrics.linesOfCode > 500)
            complexityScore += 3;
        else if (metrics.linesOfCode > 200)
            complexityScore += 2;
        else if (metrics.linesOfCode > 50)
            complexityScore += 1;
        // 基于圈复杂度
        if (metrics.cyclomaticComplexity > 20)
            complexityScore += 3;
        else if (metrics.cyclomaticComplexity > 10)
            complexityScore += 2;
        else if (metrics.cyclomaticComplexity > 5)
            complexityScore += 1;
        // 基于特征
        if (features.hasAlgorithms)
            complexityScore += 1;
        if (features.hasAsyncCode)
            complexityScore += 1;
        if (features.hasNetworking)
            complexityScore += 1;
        if (features.hasUI)
            complexityScore += 1;
        if (complexityScore >= 6)
            return 'complex';
        if (complexityScore >= 3)
            return 'medium';
        return 'simple';
    }
    /**
     * 生成改进建议
     */
    generateSuggestions(metrics, features) {
        const suggestions = [];
        if (metrics.linesOfCode > 300) {
            suggestions.push('Consider breaking down large files into smaller modules');
        }
        if (metrics.cyclomaticComplexity > 15) {
            suggestions.push('High cyclomatic complexity detected - consider refactoring complex functions');
        }
        if (metrics.maintainabilityIndex < 60) {
            suggestions.push('Low maintainability index - consider improving code structure');
        }
        if (metrics.duplicateCodePercentage > 10) {
            suggestions.push('High code duplication detected - consider extracting common functionality');
        }
        if (features.hasAsyncCode && !features.hasEventHandlers) {
            suggestions.push('Consider adding proper error handling for async operations');
        }
        return suggestions;
    }
    /**
     * 计算圈复杂度
     */
    calculateCyclomaticComplexity(content) {
        let complexity = 1; // 基础复杂度
        // 计算决策点
        const decisionPoints = [
            /if\s*\(/g,
            /else\s+if\s*\(/g,
            /while\s*\(/g,
            /for\s*\(/g,
            /switch\s*\(/g,
            /case\s+/g,
            /catch\s*\(/g,
            /\?\s*.*?\s*:/g, // 三元操作符
            /&&/g,
            /\|\|/g
        ];
        for (const pattern of decisionPoints) {
            const matches = content.match(pattern);
            if (matches) {
                complexity += matches.length;
            }
        }
        return complexity;
    }
    /**
     * 计算可维护性指数
     */
    calculateMaintainabilityIndex(linesOfCode, cyclomaticComplexity) {
        // 简化的可维护性指数计算
        const halsteadVolume = Math.log2(linesOfCode) * 10; // 简化的Halstead体积
        const maintainabilityIndex = Math.max(0, 171 - 5.2 * Math.log(halsteadVolume) - 0.23 * cyclomaticComplexity - 16.2 * Math.log(linesOfCode));
        return Math.round(maintainabilityIndex);
    }
    /**
     * 计算重复代码百分比
     */
    calculateDuplicateCode(content) {
        const lines = content.split('\n').map(line => line.trim()).filter(line => line);
        const lineCount = new Map();
        for (const line of lines) {
            if (line.length > 10) { // 只考虑较长的行
                lineCount.set(line, (lineCount.get(line) || 0) + 1);
            }
        }
        let duplicateLines = 0;
        for (const [line, count] of lineCount) {
            if (count > 1) {
                duplicateLines += count - 1;
            }
        }
        return lines.length > 0 ? Math.round((duplicateLines / lines.length) * 100) : 0;
    }
    /**
     * 解析函数参数
     */
    parseParameters(paramString) {
        if (!paramString.trim())
            return [];
        return paramString.split(',').map(param => {
            const trimmed = param.trim();
            const optional = trimmed.includes('?');
            const hasDefault = trimmed.includes('=');
            let name = trimmed.split(':')[0].replace('?', '').split('=')[0].trim();
            let type = 'any';
            if (trimmed.includes(':')) {
                const typePart = trimmed.split(':')[1];
                type = typePart.split('=')[0].trim();
            }
            return {
                name,
                type,
                optional: optional || hasDefault,
                defaultValue: hasDefault ? trimmed.split('=')[1]?.trim() : undefined
            };
        });
    }
    /**
     * 计算函数复杂度
     */
    calculateFunctionComplexity(content, functionName) {
        // 简化实现：查找函数体并计算其复杂度
        const functionRegex = new RegExp(`function\\s+${functionName}\\s*\\([^)]*\\)\\s*{([^}]*)}`, 's');
        const match = content.match(functionRegex);
        if (match) {
            return this.calculateCyclomaticComplexity(match[1]);
        }
        return 1;
    }
    /**
     * 获取目录中的代码文件
     */
    getCodeFiles(dirPath) {
        const files = [];
        const codeExtensions = ['.ts', '.js', '.gd', '.cs'];
        function scanDir(currentDir) {
            try {
                const items = fs.readdirSync(currentDir);
                for (const item of items) {
                    const fullPath = path.join(currentDir, item);
                    const stat = fs.statSync(fullPath);
                    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                        scanDir(fullPath);
                    }
                    else if (stat.isFile() && codeExtensions.includes(path.extname(item))) {
                        files.push(fullPath);
                    }
                }
            }
            catch (error) {
                // 忽略权限错误等
            }
        }
        scanDir(dirPath);
        return files;
    }
    /**
     * 聚合多个分析结果
     */
    aggregateResults(results) {
        if (results.length === 0) {
            throw new Error('No analysis results to aggregate');
        }
        if (results.length === 1) {
            return results[0];
        }
        // 聚合指标
        const totalLOC = results.reduce((sum, r) => sum + r.metrics.linesOfCode, 0);
        const avgComplexity = results.reduce((sum, r) => sum + r.metrics.cyclomaticComplexity, 0) / results.length;
        const avgMaintainability = results.reduce((sum, r) => sum + r.metrics.maintainabilityIndex, 0) / results.length;
        const avgDuplication = results.reduce((sum, r) => sum + r.metrics.duplicateCodePercentage, 0) / results.length;
        // 聚合特征
        const aggregatedFeatures = {
            hasClasses: results.some(r => r.features.hasClasses),
            hasInterfaces: results.some(r => r.features.hasInterfaces),
            hasAsyncCode: results.some(r => r.features.hasAsyncCode),
            hasEventHandlers: results.some(r => r.features.hasEventHandlers),
            hasAlgorithms: results.some(r => r.features.hasAlgorithms),
            hasDataStructures: results.some(r => r.features.hasDataStructures),
            hasNetworking: results.some(r => r.features.hasNetworking),
            hasUI: results.some(r => r.features.hasUI)
        };
        // 聚合依赖
        const allDependencies = results.flatMap(r => r.dependencies);
        const uniqueDependencies = [...new Set(allDependencies)];
        // 聚合建议
        const allSuggestions = results.flatMap(r => r.suggestions);
        const uniqueSuggestions = [...new Set(allSuggestions)];
        const aggregatedMetrics = {
            linesOfCode: totalLOC,
            cyclomaticComplexity: Math.round(avgComplexity),
            maintainabilityIndex: Math.round(avgMaintainability),
            testCoverage: 0, // 默认值，实际需要从测试工具获取
            duplicateCodePercentage: Math.round(avgDuplication)
        };
        const complexity = this.determineComplexity(aggregatedMetrics, aggregatedFeatures);
        return {
            complexity,
            features: aggregatedFeatures,
            dependencies: uniqueDependencies,
            metrics: aggregatedMetrics,
            suggestions: uniqueSuggestions
        };
    }
}
exports.CodeAnalysisAgent = CodeAnalysisAgent;
//# sourceMappingURL=CodeAnalysisAgent.js.map