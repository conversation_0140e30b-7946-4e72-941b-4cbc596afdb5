/**
 * 技能槽组件
 * 负责显示单个技能槽的UI和交互
 */

import { _decorator, Node, Sprite, Label, Button, ProgressBar, Color, tween, Vec3 } from 'cc';
import { BaseUIComponent } from '../base/BaseUIComponent';
import { ISkillData, IPlayerSkill } from '../../config/interfaces/ISkillData';
import { ResourceManager } from '../../managers/ResourceManager';

const { ccclass, property } = _decorator;

/**
 * 技能槽配置接口
 */
export interface ISkillSlotConfig {
    /** 槽位索引 */
    slotIndex: number;
    
    /** 是否显示快捷键 */
    showHotkey: boolean;
    
    /** 是否启用拖拽 */
    enableDrag: boolean;
    
    /** 冷却时间显示模式 */
    cooldownDisplayMode: 'text' | 'progress' | 'both';
    
    /** 快捷键 */
    hotkey: string;
}

/**
 * 技能槽状态枚举
 */
export enum SkillSlotState {
    Empty = 'empty',
    Normal = 'normal',
    Cooldown = 'cooldown',
    Disabled = 'disabled',
    Highlighted = 'highlighted'
}

@ccclass('SkillSlot')
export class SkillSlot extends BaseUIComponent {
    
    @property({ type: Node, tooltip: '技能图标容器' })
    public iconContainer: Node = null!;
    
    @property({ type: Sprite, tooltip: '技能图标' })
    public skillIcon: Sprite = null!;
    
    @property({ type: Sprite, tooltip: '槽位背景' })
    public slotBackground: Sprite = null!;
    
    @property({ type: Label, tooltip: '技能等级标签' })
    public levelLabel: Label = null!;
    
    @property({ type: Label, tooltip: '冷却时间标签' })
    public cooldownLabel: Label = null!;
    
    @property({ type: Label, tooltip: '快捷键标签' })
    public hotkeyLabel: Label = null!;
    
    @property({ type: ProgressBar, tooltip: '冷却进度条' })
    public cooldownProgress: ProgressBar = null!;
    
    @property({ type: Node, tooltip: '冷却遮罩' })
    public cooldownMask: Node = null!;
    
    @property({ type: Button, tooltip: '点击按钮' })
    public clickButton: Button = null!;
    
    // 配置
    private _config: ISkillSlotConfig = {
        slotIndex: 0,
        showHotkey: true,
        enableDrag: false,
        cooldownDisplayMode: 'both',
        hotkey: ''
    };
    
    // 当前状态
    private _currentState: SkillSlotState = SkillSlotState.Empty;
    
    // 当前技能数据
    private _currentSkillData: ISkillData | null = null;
    private _currentPlayerSkill: IPlayerSkill | null = null;
    
    // 冷却时间相关
    private _currentCooldown: number = 0;
    private _maxCooldown: number = 0;
    
    // 事件回调
    public onSlotClick: ((slotIndex: number) => void) | null = null;
    public onSlotDragStart: ((slotIndex: number) => void) | null = null;
    public onSlotDragEnd: ((fromIndex: number, toIndex: number) => void) | null = null;
    
    // 动画相关
    private _pulseAnimation: any = null;

    protected onComponentLoad(): void {
        console.log(`⚔️ SkillSlot: 组件加载 - 槽位 ${this._config.slotIndex}`);
        
        // 初始化UI状态
        this.initializeUI();
    }

    protected onComponentEnable(): void {
        console.log(`⚔️ SkillSlot: 组件启用 - 槽位 ${this._config.slotIndex}`);
        
        // 绑定点击事件
        this.bindClickEvents();
        
        // 更新显示
        this.updateDisplay();
    }

    protected bindEvents(): void {
        // 绑定按钮点击事件
        if (this.clickButton) {
            this.clickButton.node.on(Button.EventType.CLICK, this.onButtonClick, this);
        }
    }

    protected unbindEvents(): void {
        // 解绑按钮点击事件
        if (this.clickButton) {
            this.clickButton.node.off(Button.EventType.CLICK, this.onButtonClick, this);
        }
    }

    /**
     * 初始化技能槽
     */
    public async initialize(config: ISkillSlotConfig): Promise<void> {
        console.log(`⚔️ 初始化技能槽: ${config.slotIndex}`);
        
        this._config = { ...config };
        
        // 设置快捷键显示
        if (this.hotkeyLabel) {
            this.hotkeyLabel.string = this._config.showHotkey ? this._config.hotkey : '';
            this.hotkeyLabel.node.active = this._config.showHotkey && !!this._config.hotkey;
        }
        
        // 设置冷却显示模式
        this.setupCooldownDisplay();
        
        // 初始化为空状态
        this.setState(SkillSlotState.Empty);
        
        console.log(`✅ 技能槽初始化完成: ${config.slotIndex}`);
    }

    /**
     * 初始化UI
     */
    private initializeUI(): void {
        // 确保所有UI组件都存在
        if (!this.skillIcon) {
            console.warn(`⚠️ 技能槽缺少技能图标组件: ${this._config.slotIndex}`);
        }
        
        if (!this.slotBackground) {
            console.warn(`⚠️ 技能槽缺少背景组件: ${this._config.slotIndex}`);
        }
        
        // 初始化冷却遮罩
        if (this.cooldownMask) {
            this.cooldownMask.active = false;
        }
        
        // 初始化进度条
        if (this.cooldownProgress) {
            this.cooldownProgress.progress = 0;
            this.cooldownProgress.node.active = false;
        }
    }

    /**
     * 绑定点击事件
     */
    private bindClickEvents(): void {
        if (this.clickButton) {
            this.clickButton.interactable = true;
        }
    }

    /**
     * 设置冷却显示模式
     */
    private setupCooldownDisplay(): void {
        const mode = this._config.cooldownDisplayMode;
        
        // 根据模式显示/隐藏相应组件
        if (this.cooldownLabel) {
            this.cooldownLabel.node.active = mode === 'text' || mode === 'both';
        }
        
        if (this.cooldownProgress) {
            this.cooldownProgress.node.active = mode === 'progress' || mode === 'both';
        }
    }

    /**
     * 设置技能
     */
    public setSkill(skillData: ISkillData, playerSkill: IPlayerSkill): void {
        console.log(`⚔️ 设置技能: ${skillData.name} Lv.${playerSkill.level}`);
        
        this._currentSkillData = skillData;
        this._currentPlayerSkill = playerSkill;
        
        // 更新技能图标
        this.updateSkillIcon(skillData);
        
        // 更新技能等级
        this.updateSkillLevel(playerSkill.level);
        
        // 设置为正常状态
        this.setState(SkillSlotState.Normal);
        
        // 更新显示
        this.updateDisplay();
    }

    /**
     * 清空技能槽
     */
    public clearSkill(): void {
        console.log(`⚔️ 清空技能槽: ${this._config.slotIndex}`);
        
        this._currentSkillData = null;
        this._currentPlayerSkill = null;
        this._currentCooldown = 0;
        this._maxCooldown = 0;
        
        // 清空图标
        if (this.skillIcon) {
            this.skillIcon.spriteFrame = null;
        }
        
        // 清空等级
        if (this.levelLabel) {
            this.levelLabel.string = '';
        }
        
        // 设置为空状态
        this.setState(SkillSlotState.Empty);
        
        // 更新显示
        this.updateDisplay();
    }

    /**
     * 更新技能图标
     */
    private async updateSkillIcon(skillData: ISkillData): Promise<void> {
        if (!this.skillIcon) {
            return;
        }
        
        try {
            // 构建图标路径
            const iconPath = `textures/skills/${skillData.id}`;
            
            // 加载技能图标
            const spriteFrame = await ResourceManager.getInstance().loadSpriteFrame(iconPath);
            this.skillIcon.spriteFrame = spriteFrame;
            
        } catch (error) {
            console.warn(`⚠️ 加载技能图标失败: ${skillData.id}`, error);
            
            // 使用默认图标
            this.skillIcon.spriteFrame = null;
        }
    }

    /**
     * 更新技能等级显示
     */
    private updateSkillLevel(level: number): void {
        if (this.levelLabel) {
            this.levelLabel.string = level > 1 ? level.toString() : '';
            this.levelLabel.node.active = level > 1;
        }
    }

    /**
     * 更新冷却时间
     */
    public updateCooldown(remainingTime: number): void {
        this._currentCooldown = remainingTime;
        
        if (this._currentSkillData) {
            this._maxCooldown = this._currentSkillData.cooldown;
        }
        
        // 更新冷却显示
        this.updateCooldownDisplay();
        
        // 更新状态
        if (remainingTime > 0) {
            this.setState(SkillSlotState.Cooldown);
        } else if (this._currentSkillData) {
            this.setState(SkillSlotState.Normal);
        }
    }

    /**
     * 更新冷却显示
     */
    private updateCooldownDisplay(): void {
        const isOnCooldown = this._currentCooldown > 0;
        
        // 更新冷却文本
        if (this.cooldownLabel) {
            if (isOnCooldown) {
                this.cooldownLabel.string = this.formatCooldownTime(this._currentCooldown);
                this.cooldownLabel.node.active = true;
            } else {
                this.cooldownLabel.node.active = false;
            }
        }
        
        // 更新冷却进度条
        if (this.cooldownProgress) {
            if (isOnCooldown && this._maxCooldown > 0) {
                const progress = 1 - (this._currentCooldown / this._maxCooldown);
                this.cooldownProgress.progress = progress;
                this.cooldownProgress.node.active = true;
            } else {
                this.cooldownProgress.node.active = false;
            }
        }
        
        // 更新冷却遮罩
        if (this.cooldownMask) {
            this.cooldownMask.active = isOnCooldown;
            
            if (isOnCooldown) {
                // 设置遮罩透明度
                const opacity = Math.floor(128 * (this._currentCooldown / this._maxCooldown));
                this.cooldownMask.getComponent(Sprite)?.setColor(new Color(0, 0, 0, opacity));
            }
        }
    }

    /**
     * 格式化冷却时间显示
     */
    private formatCooldownTime(seconds: number): string {
        if (seconds < 1) {
            return (seconds * 10).toFixed(0);
        } else if (seconds < 60) {
            return Math.ceil(seconds).toString();
        } else {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = Math.ceil(seconds % 60);
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }
    }

    /**
     * 设置技能槽状态
     */
    private setState(state: SkillSlotState): void {
        if (this._currentState === state) {
            return;
        }
        
        const oldState = this._currentState;
        this._currentState = state;
        
        console.log(`⚔️ 技能槽状态变化: ${oldState} → ${state}`);
        
        // 更新视觉状态
        this.updateVisualState();
        
        // 发送状态变化事件
        this.emitComponentEvent('state_changed', {
            slotIndex: this._config.slotIndex,
            oldState: oldState,
            newState: state
        });
    }

    /**
     * 更新视觉状态
     */
    private updateVisualState(): void {
        if (!this.slotBackground) {
            return;
        }
        
        // 停止之前的动画
        this.stopPulseAnimation();
        
        // 根据状态设置背景颜色和效果
        switch (this._currentState) {
            case SkillSlotState.Empty:
                this.slotBackground.color = new Color(100, 100, 100, 255);
                break;
                
            case SkillSlotState.Normal:
                this.slotBackground.color = new Color(255, 255, 255, 255);
                break;
                
            case SkillSlotState.Cooldown:
                this.slotBackground.color = new Color(150, 150, 150, 255);
                break;
                
            case SkillSlotState.Disabled:
                this.slotBackground.color = new Color(80, 80, 80, 255);
                break;
                
            case SkillSlotState.Highlighted:
                this.slotBackground.color = new Color(255, 255, 100, 255);
                this.startPulseAnimation();
                break;
        }
        
        // 更新按钮交互状态
        if (this.clickButton) {
            this.clickButton.interactable = this._currentState !== SkillSlotState.Disabled;
        }
    }

    /**
     * 开始脉冲动画
     */
    private startPulseAnimation(): void {
        if (!this.iconContainer) {
            return;
        }
        
        this._pulseAnimation = tween(this.iconContainer)
            .to(0.5, { scale: new Vec3(1.1, 1.1, 1) })
            .to(0.5, { scale: new Vec3(1, 1, 1) })
            .union()
            .repeatForever()
            .start();
    }

    /**
     * 停止脉冲动画
     */
    private stopPulseAnimation(): void {
        if (this._pulseAnimation) {
            this._pulseAnimation.stop();
            this._pulseAnimation = null;
        }
        
        if (this.iconContainer) {
            this.iconContainer.setScale(1, 1, 1);
        }
    }

    /**
     * 更新显示
     */
    private updateDisplay(): void {
        // 更新所有UI元素的显示状态
        this.updateVisualState();
        this.updateCooldownDisplay();
    }

    // ==================== 事件处理 ====================

    /**
     * 按钮点击事件处理
     */
    private onButtonClick(): void {
        console.log(`⚔️ 技能槽点击: ${this._config.slotIndex}`);
        
        if (this.onSlotClick) {
            this.onSlotClick(this._config.slotIndex);
        }
        
        // 发送点击事件
        this.emitComponentEvent('slot_clicked', {
            slotIndex: this._config.slotIndex,
            skillId: this._currentSkillData?.id || null,
            state: this._currentState
        });
    }

    // ==================== 公共API ====================

    /**
     * 获取当前技能数据
     */
    public getCurrentSkillData(): ISkillData | null {
        return this._currentSkillData;
    }

    /**
     * 获取当前玩家技能数据
     */
    public getCurrentPlayerSkill(): IPlayerSkill | null {
        return this._currentPlayerSkill;
    }

    /**
     * 获取当前状态
     */
    public getCurrentState(): SkillSlotState {
        return this._currentState;
    }

    /**
     * 获取槽位索引
     */
    public getSlotIndex(): number {
        return this._config.slotIndex;
    }

    /**
     * 设置高亮状态
     */
    public setHighlighted(highlighted: boolean): void {
        if (highlighted) {
            this.setState(SkillSlotState.Highlighted);
        } else if (this._currentState === SkillSlotState.Highlighted) {
            // 恢复到之前的状态
            if (this._currentCooldown > 0) {
                this.setState(SkillSlotState.Cooldown);
            } else if (this._currentSkillData) {
                this.setState(SkillSlotState.Normal);
            } else {
                this.setState(SkillSlotState.Empty);
            }
        }
    }

    /**
     * 设置启用状态
     */
    public setEnabled(enabled: boolean): void {
        if (enabled) {
            // 恢复到适当的状态
            if (this._currentCooldown > 0) {
                this.setState(SkillSlotState.Cooldown);
            } else if (this._currentSkillData) {
                this.setState(SkillSlotState.Normal);
            } else {
                this.setState(SkillSlotState.Empty);
            }
        } else {
            this.setState(SkillSlotState.Disabled);
        }
    }

    /**
     * 检查是否可用
     */
    public isAvailable(): boolean {
        return this._currentState === SkillSlotState.Normal && this._currentSkillData !== null;
    }

    /**
     * 检查是否在冷却中
     */
    public isOnCooldown(): boolean {
        return this._currentState === SkillSlotState.Cooldown;
    }

    /**
     * 检查是否为空
     */
    public isEmpty(): boolean {
        return this._currentState === SkillSlotState.Empty;
    }
}
