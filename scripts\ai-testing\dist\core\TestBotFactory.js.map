{"version": 3, "file": "TestBotFactory.js", "sourceRoot": "", "sources": ["../../core/TestBotFactory.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,qDAAkD;AAClD,yEAAsE;AACtE,4EAAyE;AASzE,MAAa,cAAc;IAMvB;QAHQ,aAAQ,GAAgC,IAAI,GAAG,EAAE,CAAC;QAClD,uBAAkB,GAA+B,IAAI,GAAG,EAAE,CAAC;QAG/D,IAAI,CAAC,eAAe,GAAG,IAAI,2CAAoB,EAAE,CAAC;QAClD,IAAI,CAAC,gBAAgB,GAAG,IAAI,2CAAoB,EAAE,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,sBAAsB,CAAC,UAAkB;QAClD,OAAO,CAAC,GAAG,CAAC,sDAAsD,UAAU,EAAE,CAAC,CAAC;QAEhF,OAAO;QACP,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC;QAC1C,CAAC;QAED,IAAI,CAAC;YACD,aAAa;YACb,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAE3D,eAAe;YACf,MAAM,OAAO,GAAG,IAAI,+BAAc,CAAC,aAAa,CAAC,CAAC;YAElD,gBAAgB;YAChB,MAAM,OAAO,CAAC,iBAAiB,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;YAE1D,eAAe;YACf,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;YAE3D,aAAa;YACb,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YACvC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;YAEvD,OAAO,CAAC,GAAG,CAAC,0BAA0B,aAAa,CAAC,IAAI,KAAK,aAAa,CAAC,UAAU,GAAG,CAAC,CAAC;YAC1F,OAAO,OAAO,CAAC;QAEnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;YACvE,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,wBAAwB,CAAC,WAAmB;QACrD,OAAO,CAAC,GAAG,CAAC,+DAA+D,WAAW,EAAE,CAAC,CAAC;QAE1F,MAAM,QAAQ,GAAG,IAAI,GAAG,EAA0B,CAAC;QAEnD,IAAI,CAAC;YACD,gBAAgB;YAChB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;YACxE,OAAO,CAAC,GAAG,CAAC,iBAAiB,OAAO,CAAC,MAAM,qBAAqB,CAAC,CAAC;YAElE,kBAAkB;YAClB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC3B,IAAI,CAAC;oBACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;oBAC/D,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;oBACnC,OAAO,CAAC,GAAG,CAAC,0BAA0B,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;gBACzD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,mCAAmC,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;oBACxE,mBAAmB;gBACvB,CAAC;YACL,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,2BAA2B,QAAQ,CAAC,IAAI,wBAAwB,CAAC,CAAC;YAC9E,OAAO,QAAQ,CAAC;QAEpB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAClE,MAAM,KAAK,CAAC;QAChB,CAAC;IACL,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,aAAa,CAAC,UAAkB,EAAE,OAAqB;QAChE,OAAO,CAAC,GAAG,CAAC,4CAA4C,UAAU,EAAE,CAAC,CAAC;QAEtE,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAC9C,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;YAChE,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAC9C,OAAO;QACX,CAAC;QAED,IAAI,CAAC;YACD,YAAY;YACZ,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAE3E,qBAAqB;YACrB,IAAI,cAAc,CAAC,iBAAiB,EAAE,CAAC;gBACnC,OAAO,CAAC,GAAG,CAAC,6BAA6B,cAAc,CAAC,aAAa,mBAAmB,CAAC,CAAC;gBAC1F,MAAM,OAAO,CAAC,iBAAiB,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;gBAE9D,YAAY;gBACZ,MAAM,aAAa,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;gBAC9D,IAAI,aAAa,EAAE,CAAC;oBAChB,aAAa,CAAC,UAAU,GAAG,cAAc,CAAC,aAAa,CAAC;gBAC5D,CAAC;YACL,CAAC;YAED,YAAY;YACZ,IAAI,cAAc,CAAC,oBAAoB,EAAE,CAAC;gBACtC,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,cAAc,CAAC,eAAe,CAAC,CAAC;YAC5E,CAAC;YAED,YAAY;YACZ,IAAI,cAAc,CAAC,mBAAmB,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;gBACxC,MAAM,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACrC,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;QAEnD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACb,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,mBAAmB;YACnB,OAAO,CAAC,GAAG,CAAC,8CAA8C,CAAC,CAAC;YAC5D,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YACjC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;YAC3C,MAAM,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;QAClD,CAAC;IACL,CAAC;IAED;;OAEG;IACI,mBAAmB;QACtB,MAAM,KAAK,GAAqB;YAC5B,YAAY,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI;YAChC,WAAW,EAAE,IAAI,GAAG,EAAE;YACtB,sBAAsB,EAAE,IAAI,GAAG,EAAE;YACjC,mBAAmB,EAAE,CAAC;SACzB,CAAC;QAEF,WAAW;QACX,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAAE,CAAC;YACrD,MAAM,YAAY,GAAG,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YACpE,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,YAAY,GAAG,CAAC,CAAC,CAAC;YAE5D,MAAM,eAAe,GAAG,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;YAClF,KAAK,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,eAAe,GAAG,CAAC,CAAC,CAAC;QAC9E,CAAC;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACI,UAAU;QACb,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtB,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,CAAC;IACpC,CAAC;IAED,SAAS;IAET;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,UAAkB;QAC1C,OAAO,CAAC,GAAG,CAAC,0BAA0B,UAAU,EAAE,CAAC,CAAC;QAEpD,eAAe;QACf,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,eAAe,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAE9E,SAAS;QACT,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;QAE9D,SAAS;QACT,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAEhE,MAAM,aAAa,GAAkB;YACjC,IAAI,EAAE,UAAU,CAAC,IAAI;YACrB,IAAI,EAAE,UAAU;YAChB,UAAU,EAAE,UAAU,CAAC,IAAkB;YACzC,UAAU,EAAE,UAAU,CAAC,UAA6B;YACpD,QAAQ;YACR,YAAY;SACf,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,aAAa,CAAC,CAAC;QAC1D,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,UAAkB;QAClD,6BAA6B;QAC7B,uBAAuB;QAEvB,OAAO;YACH,aAAa,EAAE,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC;YACtD,OAAO,EAAE,MAAM,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC;YAC1C,KAAK,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;YACtC,WAAW,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;YAClD,WAAW,EAAE,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;YACnD,UAAU,EAAE,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;SACpD,CAAC;IACN,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,UAAkB;QAChD,iCAAiC;QACjC,OAAO,EAAE,CAAC;IACd,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,uBAAuB,CAAC,OAAuB,EAAE,aAA4B;QACvF,MAAM,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC;YACvE,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,IAAI,EAAE,aAAa,CAAC,UAAU;YAC9B,UAAU,EAAE,aAAa,CAAC,UAAU;YACpC,QAAQ,EAAE,aAAa,CAAC,QAAQ;SACnC,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,UAAkB,EAAE,OAAqB;QACvE,uBAAuB;QACvB,OAAO;YACH,iBAAiB,EAAE,KAAK;YACxB,aAAa,EAAE,YAAY;YAC3B,oBAAoB,EAAE,KAAK;YAC3B,mBAAmB,EAAE,IAAI;YACzB,eAAe,EAAE,EAAE;SACtB,CAAC;IACN,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,mBAAmB,CAAC,OAAuB,EAAE,YAAsB;QAC7E,cAAc;QACd,OAAO,CAAC,GAAG,CAAC,gDAAgD,EAAE,YAAY,CAAC,CAAC;IAChF,CAAC;IAED,eAAe;IACP,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QAC7C,aAAa;QACb,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,UAAU,CAAC,UAAkB;QACvC,cAAc;QACd,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,QAAQ,CAAC,UAAkB;QACrC,aAAa;QACb,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,UAAkB;QAC3C,cAAc;QACd,OAAO,KAAK,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,eAAe,CAAC,UAAkB;QAC5C,aAAa;QACb,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,UAAkB;QAC3C,aAAa;QACb,OAAO,KAAK,CAAC;IACjB,CAAC;CACJ;AApSD,wCAoSC"}