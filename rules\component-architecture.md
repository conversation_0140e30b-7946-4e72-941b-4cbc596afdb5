# 组件架构规范

> 📖 **导航**: [返回主页](./README.md) | [代码标准](./code-standards.md) | [数据管理](./data-management.md)

## 🏗️ 基础组件架构

### 组件继承体系
```typescript
// 基础组件类
@ccclass('BaseComponent')
export abstract class BaseComponent extends Component {
    @property({ type: String, tooltip: '组件名称' })
    protected componentName: string = '';
    
    @property({ type: <PERSON>olean, tooltip: '是否启用调试模式' })
    protected debugMode: boolean = false;
    
    // 组件状态
    protected _isInitialized: boolean = false;
    protected _isEnabled: boolean = true;
    
    // 生命周期方法
    protected onLoad(): void {
        this.initializeComponent();
    }
    
    protected start(): void {
        this.startComponent();
    }
    
    protected onEnable(): void {
        this.enableComponent();
    }
    
    protected onDisable(): void {
        this.disableComponent();
    }
    
    protected onDestroy(): void {
        this.destroyComponent();
    }
    
    // 抽象方法 - 子类必须实现
    protected abstract initializeComponent(): void;
    protected abstract startComponent(): void;
    protected abstract enableComponent(): void;
    protected abstract disableComponent(): void;
    protected abstract destroyComponent(): void;
    
    // 通用方法
    public getComponentName(): string {
        return this.componentName;
    }
    
    public isInitialized(): boolean {
        return this._isInitialized;
    }
    
    protected log(message: string, ...args: any[]): void {
        if (this.debugMode) {
            console.log(`[${this.componentName}] ${message}`, ...args);
        }
    }
}

// 管理器基类
@ccclass('BaseManager')
export abstract class BaseManager extends BaseComponent {
    protected static _instance: BaseManager = null;
    
    public static getInstance<T extends BaseManager>(this: new () => T): T {
        if (!this._instance) {
            this._instance = new this();
        }
        return this._instance as T;
    }
    
    // 管理器特有的生命周期
    protected abstract initializeManager(): Promise<void>;
    protected abstract startManager(): void;
    protected abstract stopManager(): void;
    protected abstract resetManager(): void;
    
    protected async initializeComponent(): Promise<void> {
        await this.initializeManager();
        this._isInitialized = true;
    }
    
    protected startComponent(): void {
        this.startManager();
    }
    
    protected disableComponent(): void {
        this.stopManager();
    }
    
    protected destroyComponent(): void {
        this.resetManager();
        BaseManager._instance = null;
    }
}

// 系统基类
@ccclass('BaseSystem')
export abstract class BaseSystem extends BaseComponent {
    @property({ type: Number, tooltip: '系统优先级' })
    protected priority: number = 0;
    
    @property({ type: Boolean, tooltip: '是否自动启动' })
    protected autoStart: boolean = true;
    
    // 系统状态
    protected _systemStatus: SystemStatus = SystemStatus.UNINITIALIZED;
    
    // 系统特有方法
    protected abstract initializeSystem(): Promise<void>;
    protected abstract updateSystem(deltaTime: number): void;
    protected abstract pauseSystem(): void;
    protected abstract resumeSystem(): void;
    protected abstract shutdownSystem(): void;
    
    protected async initializeComponent(): Promise<void> {
        this._systemStatus = SystemStatus.INITIALIZING;
        await this.initializeSystem();
        this._systemStatus = SystemStatus.READY;
        this._isInitialized = true;
    }
    
    protected startComponent(): void {
        if (this.autoStart && this._systemStatus === SystemStatus.READY) {
            this._systemStatus = SystemStatus.RUNNING;
        }
    }
    
    protected update(deltaTime: number): void {
        if (this._systemStatus === SystemStatus.RUNNING) {
            this.updateSystem(deltaTime);
        }
    }
    
    public pause(): void {
        if (this._systemStatus === SystemStatus.RUNNING) {
            this._systemStatus = SystemStatus.PAUSED;
            this.pauseSystem();
        }
    }
    
    public resume(): void {
        if (this._systemStatus === SystemStatus.PAUSED) {
            this._systemStatus = SystemStatus.RUNNING;
            this.resumeSystem();
        }
    }
    
    public getSystemStatus(): SystemStatus {
        return this._systemStatus;
    }
}

// 系统状态枚举
export enum SystemStatus {
    UNINITIALIZED = 'uninitialized',
    INITIALIZING = 'initializing',
    READY = 'ready',
    RUNNING = 'running',
    PAUSED = 'paused',
    ERROR = 'error',
    SHUTDOWN = 'shutdown'
}
```

## 🎮 游戏专用组件

### 角色组件
```typescript
// 角色基类
@ccclass('GameCharacter')
export class GameCharacter extends BaseComponent {
    @property({ type: String, tooltip: '角色ID' })
    protected characterId: string = '';

    @property({ type: String, tooltip: '角色类型' })
    protected characterType: string = '';

    @property({ type: Number, tooltip: '角色等级' })
    protected level: number = 1;

    // 角色属性
    protected _health: number = 100;          // 生命值
    protected _mana: number = 50;             // 魔法值
    protected _attack: number = 10;           // 攻击力
    protected _defense: number = 5;           // 防御力
    protected _experience: number = 0;        // 经验值
    
    // 技能相关
    protected _learnedSkills: Map<string, ISkillData> = new Map();
    protected _equippedSkills: string[] = [];
    protected _skillCooldowns: Map<string, number> = new Map();
    
    protected initializeComponent(): void {
        this.loadCharacterData();
        this.initializeSkills();
        this.initializeAttributes();
    }

    // 角色方法
    public levelUp(): boolean {
        if (this._experience >= this.getRequiredExperience()) {
            this.level++;
            this._experience = 0;
            this.onLevelUp();
            return true;
        }
        return false;
    }

    public learnSkill(skillId: string): boolean {
        const skill = DataManager.getSkill(skillId);
        if (!this.canLearnSkill(skill)) {
            return false;
        }

        this._learnedSkills.set(skillId, skill);
        this.onSkillLearned(skillId);
        return true;
    }

    public gainExperience(amount: number): void {
        this._experience += amount;
        this.checkLevelUp();
    }

    protected canLearnSkill(skill: ISkillData): boolean {
        // 检查等级要求
        if (this.level < skill.requirements.level) {
            return false;
        }

        // 检查前置技能
        for (const prereq of skill.requirements.prerequisiteSkills) {
            if (!this._learnedSkills.has(prereq)) {
                return false;
            }
        }
        
        return true;
    }
    
    protected onSectJoined(sectId: string): void {
        EventManager.emit(GameEvents.PLAYER_JOINED_SECT, {
            characterId: this.characterId,
            sectId: sectId
        });
    }
    
    protected onSkillLearned(skillId: string): void {
        EventManager.emit(GameEvents.SKILL_LEARNED, {
            characterId: this.characterId,
            skillId: skillId
        });
    }
}
```

### 技能系统组件
```typescript
// 技能系统组件
@ccclass('SkillSystem')
export class SkillSystem extends BaseSystem {
    // 技能数据缓存
    private _skillDatabase: Map<string, ISkillData> = new Map();
    private _playerSkills: Map<string, Map<string, IPlayerSkill>> = new Map();
    
    protected async initializeSystem(): Promise<void> {
        await this.loadSkillDatabase();
        this.setupSkillEvents();
    }
    
    protected updateSystem(deltaTime: number): void {
        this.updateSkillCooldowns(deltaTime);
        this.processSkillEffects(deltaTime);
    }
    
    // 技能学习
    public learnSkill(playerId: string, skillId: string): boolean {
        const player = DataManager.getPlayer(playerId);
        const skill = this._skillDatabase.get(skillId);
        
        if (!skill || !this.canPlayerLearnSkill(player, skill)) {
            return false;
        }
        
        // 添加到玩家技能列表
        if (!this._playerSkills.has(playerId)) {
            this._playerSkills.set(playerId, new Map());
        }
        
        const playerSkillMap = this._playerSkills.get(playerId);
        playerSkillMap.set(skillId, {
            id: skillId,
            level: 1,
            experience: 0,
            lastUsed: 0
        });
        
        this.onSkillLearned(playerId, skillId);
        return true;
    }
    
    // 技能使用
    public async useSkill(
        casterId: string, 
        skillId: string, 
        targetIds: string[]
    ): Promise<ISkillResult> {
        const skill = this._skillDatabase.get(skillId);
        const caster = DataManager.getPlayer(casterId);
        
        if (!this.canUseSkill(caster, skill)) {
            return { success: false, reason: 'Cannot use skill' };
        }
        
        // 开始施法
        this.startCasting(casterId, skillId);
        
        // 等待施法时间
        await this.waitForCastTime(skill.castTime);
        
        // 执行技能效果
        const result = await this.executeSkillEffects(caster, skill, targetIds);
        
        // 设置冷却时间
        this.setCooldown(casterId, skillId, skill.cooldown);
        
        return result;
    }
    
    private canPlayerLearnSkill(player: IPlayerData, skill: ISkillData): boolean {
        // 检查等级要求
        if (player.level < skill.requirements.level) {
            return false;
        }
        
        // 检查门派要求
        if (skill.sect !== '' && player.sect !== skill.sect) {
            return false;
        }
        
        // 检查修炼境界要求
        if (player.cultivation.level < skill.requirements.cultivationLevel) {
            return false;
        }
        
        return true;
    }
    
    private async executeSkillEffects(
        caster: IPlayerData, 
        skill: ISkillData, 
        targetIds: string[]
    ): Promise<ISkillResult> {
        const results: ISkillEffectResult[] = [];
        
        for (const targetId of targetIds) {
            const target = DataManager.getPlayer(targetId);
            
            for (const effect of skill.effects) {
                const effectResult = await this.applySkillEffect(caster, target, effect);
                results.push(effectResult);
            }
        }
        
        return {
            success: true,
            effects: results,
            casterId: caster.id,
            skillId: skill.id,
            targetIds: targetIds
        };
    }
}
```

---

> 📖 **相关文档**: [数据管理](./data-management.md) | [UI设计规范](./ui-guidelines.md) | [网络通信](./network-architecture.md)
