/**
 * MongoDB到微信云数据库迁移脚本
 * 使用方法: node migrate-to-cloudbase.js
 */

const mongoose = require('mongoose');
const tcb = require('@cloudbase/node-sdk');
const fs = require('fs');
const path = require('path');

// 配置
const config = {
  // MongoDB配置
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/idlegame',
  },
  // 云开发配置
  cloudbase: {
    env: process.env.TCB_ENV || 'your-env-id', // 替换为您的环境ID
    secretId: process.env.TCB_SECRET_ID,
    secretKey: process.env.TCB_SECRET_KEY,
  }
};

class DatabaseMigration {
  constructor() {
    this.mongoConnection = null;
    this.tcbApp = null;
    this.db = null;
  }

  /**
   * 初始化连接
   */
  async initialize() {
    console.log('🔗 初始化数据库连接...');
    
    // 连接MongoDB
    this.mongoConnection = await mongoose.connect(config.mongodb.uri);
    console.log('✅ MongoDB连接成功');
    
    // 初始化云开发
    this.tcbApp = tcb.init({
      env: config.cloudbase.env,
      secretId: config.cloudbase.secretId,
      secretKey: config.cloudbase.secretKey,
    });
    this.db = this.tcbApp.database();
    console.log('✅ 云开发连接成功');
  }

  /**
   * 获取MongoDB集合数据
   */
  async getMongoData(collectionName) {
    console.log(`📊 获取MongoDB集合: ${collectionName}`);
    
    const collection = mongoose.connection.db.collection(collectionName);
    const data = await collection.find({}).toArray();
    
    console.log(`✅ 获取到 ${data.length} 条记录`);
    return data;
  }

  /**
   * 迁移数据到云数据库
   */
  async migrateCollection(collectionName, data) {
    console.log(`🚀 迁移集合: ${collectionName}`);
    
    if (data.length === 0) {
      console.log(`⚠️  集合 ${collectionName} 为空，跳过迁移`);
      return;
    }

    const collection = this.db.collection(collectionName);
    
    // 批量插入数据
    const batchSize = 100;
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      
      try {
        // 转换MongoDB的_id为云数据库格式
        const convertedBatch = batch.map(doc => {
          if (doc._id) {
            doc._id = doc._id.toString();
          }
          return doc;
        });
        
        await collection.add(convertedBatch);
        console.log(`✅ 批次 ${Math.floor(i/batchSize) + 1} 迁移成功 (${batch.length} 条记录)`);
      } catch (error) {
        console.error(`❌ 批次迁移失败:`, error);
      }
    }
    
    console.log(`🎉 集合 ${collectionName} 迁移完成`);
  }

  /**
   * 验证迁移结果
   */
  async validateMigration(collectionName, originalCount) {
    console.log(`🔍 验证集合: ${collectionName}`);
    
    const collection = this.db.collection(collectionName);
    const result = await collection.count();
    const cloudCount = result.total;
    
    if (cloudCount === originalCount) {
      console.log(`✅ 验证成功: ${collectionName} (${cloudCount}/${originalCount})`);
      return true;
    } else {
      console.log(`❌ 验证失败: ${collectionName} (${cloudCount}/${originalCount})`);
      return false;
    }
  }

  /**
   * 执行完整迁移
   */
  async migrate() {
    try {
      await this.initialize();
      
      // 定义需要迁移的集合
      const collections = [
        'users',
        'skills', 
        'items',
        'quests',
        'user_skills',
        'user_inventory',
        'user_equipment',
        'game_sessions'
      ];
      
      const migrationResults = [];
      
      for (const collectionName of collections) {
        try {
          // 获取原始数据
          const data = await this.getMongoData(collectionName);
          const originalCount = data.length;
          
          // 迁移数据
          await this.migrateCollection(collectionName, data);
          
          // 验证迁移
          const isValid = await this.validateMigration(collectionName, originalCount);
          
          migrationResults.push({
            collection: collectionName,
            originalCount,
            success: isValid
          });
          
        } catch (error) {
          console.error(`❌ 集合 ${collectionName} 迁移失败:`, error);
          migrationResults.push({
            collection: collectionName,
            originalCount: 0,
            success: false,
            error: error.message
          });
        }
      }
      
      // 生成迁移报告
      this.generateReport(migrationResults);
      
    } catch (error) {
      console.error('❌ 迁移过程出错:', error);
    } finally {
      // 关闭连接
      if (this.mongoConnection) {
        await mongoose.disconnect();
      }
    }
  }

  /**
   * 生成迁移报告
   */
  generateReport(results) {
    console.log('\n📊 迁移报告');
    console.log('='.repeat(50));
    
    let successCount = 0;
    let totalRecords = 0;
    
    results.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.collection}: ${result.originalCount} 条记录`);
      
      if (result.success) {
        successCount++;
        totalRecords += result.originalCount;
      }
      
      if (result.error) {
        console.log(`   错误: ${result.error}`);
      }
    });
    
    console.log('='.repeat(50));
    console.log(`成功迁移: ${successCount}/${results.length} 个集合`);
    console.log(`总记录数: ${totalRecords} 条`);
    
    // 保存报告到文件
    const reportPath = path.join(__dirname, 'migration-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    console.log(`📄 详细报告已保存到: ${reportPath}`);
  }
}

// 执行迁移
if (require.main === module) {
  const migration = new DatabaseMigration();
  migration.migrate().catch(console.error);
}

module.exports = DatabaseMigration;
