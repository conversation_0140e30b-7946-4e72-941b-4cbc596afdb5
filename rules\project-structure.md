# 项目结构规范

> 📖 **导航**: [返回主页](./README.md) | [技术架构](./technical-architecture.md) | [代码标准](./code-standards.md)

## 📁 标准目录结构

### 完整项目结构
```
IdleGame/
├── assets/                           # 游戏资源根目录
│   ├── scenes/                       # 场景文件
│   │   ├── launch/                   # 启动相关场景
│   │   │   ├── Launch.scene          # 启动场景
│   │   │   └── Loading.scene         # 加载场景
│   │   ├── main/                     # 主要游戏场景
│   │   │   ├── Main.scene            # 主界面场景
│   │   │   ├── Game.scene            # 游戏世界场景
│   │   │   └── Shop.scene            # 商店场景
│   │   ├── battle/                   # 战斗相关场景
│   │   │   ├── Battle.scene          # 战斗场景
│   │   │   ├── Arena.scene           # 竞技场场景
│   │   │   └── Dungeon.scene         # 副本场景
│   │   ├── character/                # 角色相关场景
│   │   │   ├── Character.scene       # 角色界面
│   │   │   ├── Skills.scene          # 技能界面
│   │   │   └── Progression.scene     # 成长界面
│   │   └── social/                   # 社交相关场景
│   │       ├── Guild.scene           # 公会场景
│   │       ├── Market.scene          # 市场场景
│   │       └── Chat.scene            # 聊天场景
│   ├── scripts/                      # 脚本代码目录
│   │   ├── core/                     # 核心系统
│   │   │   ├── managers/             # 核心管理器
│   │   │   ├── base/                 # 基础类
│   │   │   └── constants/            # 常量定义
│   │   ├── systems/                  # 游戏系统
│   │   │   ├── character/           # 角色系统
│   │   │   ├── battle/              # 战斗系统
│   │   │   ├── idle/                # 放置系统
│   │   │   ├── social/              # 社交系统
│   │   │   ├── economy/             # 经济系统
│   │   │   └── progression/         # 成长系统
│   │   ├── entities/                # 实体类
│   │   │   ├── characters/          # 角色实体
│   │   │   ├── items/               # 物品实体
│   │   │   ├── skills/              # 技能实体
│   │   │   └── world/               # 世界实体
│   │   ├── ui/                      # UI系统
│   │   │   ├── framework/           # UI框架
│   │   │   ├── components/          # UI组件
│   │   │   └── panels/              # UI面板
│   │   ├── network/                 # 网络通信
│   │   │   ├── core/               # 网络核心
│   │   │   ├── services/           # 网络服务
│   │   │   └── protocols/          # 通信协议
│   │   ├── platform/               # 平台适配
│   │   │   ├── core/               # 平台核心
│   │   │   ├── wechat/             # 微信平台
│   │   │   ├── douyin/             # 抖音平台
│   │   │   └── common/             # 通用平台功能
│   │   ├── utils/                  # 工具类
│   │   │   ├── math/               # 数学工具
│   │   │   ├── string/             # 字符串工具
│   │   │   ├── time/               # 时间工具
│   │   │   ├── data/               # 数据工具
│   │   │   └── debug/              # 调试工具
│   │   └── config/                 # 配置文件
│   │       ├── game/               # 游戏配置
│   │       ├── ui/                 # UI配置
│   │       └── platform/           # 平台配置
│   ├── prefabs/                    # 预制体资源
│   │   ├── ui/                     # UI预制体
│   │   ├── characters/             # 角色预制体
│   │   ├── items/                  # 物品预制体
│   │   ├── effects/                # 特效预制体
│   │   └── world/                  # 世界预制体
│   ├── resources/                  # 资源文件
│   │   ├── textures/               # 纹理资源
│   │   │   ├── ui/                 # UI纹理
│   │   │   ├── characters/         # 角色纹理
│   │   │   ├── items/              # 物品纹理
│   │   │   ├── skills/             # 技能纹理
│   │   │   ├── world/              # 世界纹理
│   │   │   └── effects/            # 特效纹理
│   │   ├── audio/                  # 音频资源
│   │   │   ├── bgm/                # 背景音乐
│   │   │   ├── sfx/                # 音效
│   │   │   └── voice/              # 语音
│   │   ├── data/                   # 数据配置
│   │   │   ├── game/               # 游戏数据
│   │   │   ├── balance/            # 平衡性数据
│   │   │   ├── localization/       # 本地化数据
│   │   │   └── platform/           # 平台数据
│   │   └── fonts/                  # 字体资源
│   └── animations/                 # 动画资源
├── extensions/                     # 扩展插件
├── settings/                       # 项目设置
├── build-templates/                # 构建模板
├── cloud-functions/                # 云函数 (开发时)
├── docs/                          # 项目文档
├── tools/                         # 开发工具
└── tests/                         # 测试文件
```

## 📝 目录命名规范

### 基本命名规则
- **文件夹**: 使用小写字母和连字符 (`kebab-case`)
  ```
  ✅ 正确: battle-system, skill-manager, ui-components
  ❌ 错误: BattleSystem, skill_manager, UIComponents
  ```

- **TypeScript文件**: 使用大驼峰命名 (`PascalCase.ts`)
  ```
  ✅ 正确: BattleManager.ts, SkillSystem.ts, PlayerCharacter.ts
  ❌ 错误: battleManager.ts, skill_system.ts, player-character.ts
  ```

- **场景文件**: 使用大驼峰命名 (`PascalCase.scene`)
  ```
  ✅ 正确: MainScene.scene, BattleArena.scene, SectHall.scene
  ❌ 错误: main_scene.scene, battle-arena.scene, secthall.scene
  ```

- **预制体文件**: 使用大驼峰命名 (`PascalCase.prefab`)
  ```
  ✅ 正确: PlayerCharacter.prefab, SkillButton.prefab, ItemSlot.prefab
  ❌ 错误: player_character.prefab, skill-button.prefab, itemslot.prefab
  ```

- **资源文件**: 使用小写字母和下划线 (`snake_case`)
  ```
  ✅ 正确: player_avatar.png, skill_fireball.png, bgm_main_theme.mp3
  ❌ 错误: PlayerAvatar.png, skill-fireball.png, bgmMainTheme.mp3
  ```

- **配置文件**: 使用小写字母和下划线 (`snake_case.json`)
  ```
  ✅ 正确: game_config.json, skill_data.json, item_database.json
  ❌ 错误: GameConfig.json, skill-data.json, ItemDatabase.json
  ```

## 🎮 武侠游戏特殊目录说明

### 核心武侠系统目录
```typescript
// 武侠特色系统目录结构
const WuxiaDirectories = {
    // 武侠特色系统
    'systems/wuxia/': {
        'sect-system/': '门派系统 - 六大门派管理',
        'cultivation-system/': '修炼系统 - 境界提升',
        'martial-arts-system/': '武学系统 - 武功技能',
        'reputation-system/': '声望系统 - 江湖地位',
        'master-disciple/': '师承系统 - 师父弟子关系'
    },
    
    // 放置游戏机制
    'systems/idle/': {
        'offline-rewards/': '离线收益系统',
        'auto-battle/': '自动战斗系统',
        'auto-cultivation/': '自动修炼系统',
        'resource-collection/': '资源收集系统',
        'idle-optimization/': '放置优化系统'
    },
    
    // 社交系统
    'systems/social/': {
        'guild-system/': '帮派系统',
        'friend-system/': '好友系统',
        'chat-system/': '聊天系统',
        'ranking-system/': '排行榜系统',
        'marriage-system/': '结义系统'
    },
    
    // 平台适配
    'platform/': {
        'wechat/': '微信小程序适配',
        'douyin/': '抖音小程序适配',
        'common/': '通用平台功能'
    }
};
```

### 资源组织规范
```typescript
// 资源文件组织规范
const ResourceOrganization = {
    // 纹理资源分类
    'textures/': {
        'ui/': {
            'common/': '通用UI元素',
            'panels/': '面板背景',
            'buttons/': '按钮样式',
            'icons/': '图标资源'
        },
        'characters/': {
            'avatars/': '角色头像',
            'portraits/': '角色立绘',
            'animations/': '角色动画帧'
        },
        'skills/': {
            'icons/': '技能图标',
            'effects/': '技能特效',
            'animations/': '技能动画'
        },
        'world/': {
            'backgrounds/': '场景背景',
            'environments/': '环境元素',
            'decorations/': '装饰物品'
        }
    },
    
    // 数据配置分类
    'data/': {
        'game/': {
            'characters.json': '角色数据',
            'skills.json': '技能数据',
            'items.json': '物品数据',
            'quests.json': '任务数据',
            'sects.json': '门派数据'
        },
        'balance/': {
            'experience_tables.json': '经验表',
            'damage_formulas.json': '伤害公式',
            'drop_rates.json': '掉落概率'
        },
        'localization/': {
            'zh_cn.json': '简体中文',
            'zh_tw.json': '繁体中文',
            'en_us.json': '英文'
        }
    }
};
```

## 🔧 文件组织最佳实践

### 1. 单一职责原则
- 每个文件只负责一个明确的功能
- 避免在单个文件中混合多种职责
- 保持文件大小适中（建议不超过500行）

### 2. 依赖关系管理
```typescript
// 推荐的依赖导入顺序
// 1. Cocos Creator 核心模块
import { Component, Node, Label } from 'cc';

// 2. 第三方库
import * as lodash from 'lodash';

// 3. 项目核心模块
import { GameManager } from '../core/managers/GameManager';

// 4. 项目系统模块
import { BattleSystem } from '../systems/battle/BattleSystem';

// 5. 项目实体模块
import { PlayerCharacter } from '../entities/characters/PlayerCharacter';

// 6. 项目工具模块
import { MathUtils } from '../utils/math/MathUtils';

// 7. 类型定义
import { ISkillData, IBattleResult } from '../types/GameTypes';
```

### 3. 模块导出规范
```typescript
// 推荐的模块导出方式
// 默认导出主要类
export default class BattleSystem extends Component {
    // 类实现
}

// 命名导出辅助类型和常量
export { BattleResult, BattleStatus } from './BattleTypes';
export { BATTLE_CONSTANTS } from './BattleConstants';

// 统一导出文件 (index.ts)
export { default as BattleSystem } from './BattleSystem';
export { default as SkillSystem } from './SkillSystem';
export * from './BattleTypes';
```

---

> 📖 **相关文档**: [代码标准](./code-standards.md) | [组件架构](./component-architecture.md) | [资源管理](./resource-management.md)
