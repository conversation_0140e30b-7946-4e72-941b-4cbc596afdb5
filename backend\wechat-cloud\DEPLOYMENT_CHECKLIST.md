# 📋 微信云开发部署检查清单

> 确保部署过程顺利进行的完整检查清单

## 🔍 部署前检查

### ✅ 环境准备
- [ ] 已安装Node.js 18+
- [ ] 已安装Docker
- [ ] 已安装腾讯云CLI工具 (`npm install -g @cloudbase/cli`)
- [ ] 已登录腾讯云 (`tcb login`)
- [ ] 已获取微信小程序AppID
- [ ] 已开通微信云开发服务
- [ ] 已记录云开发环境ID

### ✅ 项目配置
- [ ] 已安装云开发SDK (`npm install @cloudbase/node-sdk`)
- [ ] 已复制环境变量模板 (`cp .env.wechat.example .env.wechat`)
- [ ] 已配置所有必要的环境变量
- [ ] 已设置JWT密钥
- [ ] 已配置云开发环境ID
- [ ] 已测试本地构建 (`npm run build`)

### ✅ 代码检查
- [ ] 所有TypeScript编译无错误
- [ ] 所有单元测试通过
- [ ] 代码已提交到版本控制
- [ ] 已创建发布标签
- [ ] 已更新版本号

## 🚀 部署过程检查

### ✅ 部署准备
- [ ] 已设置部署脚本执行权限 (`chmod +x wechat-cloud/deploy.sh`)
- [ ] 已确认部署环境（dev/prod）
- [ ] 已确认部署版本号
- [ ] 已备份重要数据（如果需要）

### ✅ 执行部署
- [ ] Docker镜像构建成功
- [ ] 镜像推送到云托管成功
- [ ] 服务部署成功
- [ ] 健康检查通过
- [ ] 服务状态为"运行中"

### ✅ 功能验证
- [ ] 健康检查接口响应正常 (`/api/health`)
- [ ] API文档可以访问 (`/api-docs`)
- [ ] 用户注册/登录功能正常
- [ ] 核心游戏功能正常
- [ ] 数据库读写正常
- [ ] 日志记录正常

## 🔧 部署后检查

### ✅ 性能监控
- [ ] CPU使用率正常（<80%）
- [ ] 内存使用率正常（<90%）
- [ ] 响应时间正常（<500ms）
- [ ] 错误率正常（<1%）
- [ ] 并发处理正常

### ✅ 安全检查
- [ ] HTTPS证书有效
- [ ] 安全头配置正确
- [ ] 敏感信息未暴露
- [ ] 访问控制正常
- [ ] 日志不包含敏感数据

### ✅ 数据检查
- [ ] 数据库连接正常
- [ ] 数据迁移完整（如果有）
- [ ] 数据备份正常
- [ ] 缓存功能正常（如果有）

## 📊 监控配置检查

### ✅ 基础监控
- [ ] 服务状态监控已启用
- [ ] 资源使用监控已启用
- [ ] 错误日志监控已启用
- [ ] 性能指标监控已启用

### ✅ 告警配置
- [ ] 高CPU使用率告警
- [ ] 高内存使用率告警
- [ ] 高错误率告警
- [ ] 服务不可用告警
- [ ] 响应时间过长告警

### ✅ 日志管理
- [ ] 应用日志正常输出
- [ ] 访问日志正常记录
- [ ] 错误日志正常记录
- [ ] 日志轮转配置正确
- [ ] 日志级别配置合理

## 🔄 运维准备检查

### ✅ 备份策略
- [ ] 数据库备份策略已制定
- [ ] 配置文件备份已完成
- [ ] 代码版本已标记
- [ ] 回滚方案已准备

### ✅ 扩容准备
- [ ] 自动扩缩容规则已配置
- [ ] 负载均衡配置正确
- [ ] 数据库性能可支撑扩容
- [ ] 监控阈值已调整

### ✅ 文档更新
- [ ] 部署文档已更新
- [ ] API文档已更新
- [ ] 运维手册已更新
- [ ] 故障处理文档已准备

## 🚨 应急准备检查

### ✅ 故障处理
- [ ] 故障联系人清单已准备
- [ ] 紧急回滚流程已测试
- [ ] 数据恢复流程已验证
- [ ] 故障通知机制已配置

### ✅ 性能优化
- [ ] 数据库索引已优化
- [ ] 缓存策略已配置
- [ ] CDN配置已启用（如果需要）
- [ ] 代码性能已优化

## ✅ 最终确认

### 部署成功标准
- [ ] 所有核心功能正常运行
- [ ] 性能指标达到预期
- [ ] 监控告警正常工作
- [ ] 用户可以正常使用
- [ ] 团队已知晓部署完成

### 部署完成后续工作
- [ ] 通知相关团队部署完成
- [ ] 更新项目状态
- [ ] 安排用户验收测试
- [ ] 准备下一阶段开发计划
- [ ] 总结部署经验和改进点

---

## 📞 紧急联系方式

**技术负责人**: [姓名] - [电话] - [微信]
**运维负责人**: [姓名] - [电话] - [微信]
**产品负责人**: [姓名] - [电话] - [微信]

## 🔗 重要链接

- 微信开发者工具: https://developers.weixin.qq.com/miniprogram/dev/devtools/
- 云开发控制台: https://console.cloud.tencent.com/tcb
- 监控面板: [您的监控面板URL]
- 项目文档: [您的项目文档URL]

---

**✅ 检查清单完成后，您的IdleGame就可以安全稳定地在微信云开发环境中运行了！**
