#!/bin/bash

# 微信云托管部署脚本
# 使用方法: ./deploy.sh [环境] [版本]
# 示例: ./deploy.sh prod v1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认参数
ENVIRONMENT=${1:-dev}
VERSION=${2:-latest}
CONFIG_TYPE=${3:-standard}  # standard 或 free
SERVICE_NAME="idlegame-backend"

# 根据配置类型选择配置文件
if [ "$CONFIG_TYPE" = "free" ]; then
    CONFIG_FILE="wechat-cloud/container.config.free.json"
    SERVICE_NAME="idlegame-backend-free"
    echo -e "${GREEN}🆓 使用免费版配置${NC}"
else
    CONFIG_FILE="wechat-cloud/container.config.json"
    echo -e "${GREEN}💼 使用标准版配置${NC}"
fi

echo -e "${GREEN}🚀 开始部署微信云托管服务${NC}"
echo -e "${YELLOW}环境: $ENVIRONMENT${NC}"
echo -e "${YELLOW}版本: $VERSION${NC}"
echo -e "${YELLOW}配置: $CONFIG_TYPE${NC}"
echo -e "${YELLOW}服务名: $SERVICE_NAME${NC}"

# 检查必要工具
check_tools() {
    echo -e "${YELLOW}📋 检查部署工具...${NC}"
    
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker 未安装${NC}"
        exit 1
    fi
    
    if ! command -v tcb &> /dev/null; then
        echo -e "${RED}❌ 腾讯云CLI工具未安装${NC}"
        echo -e "${YELLOW}请安装: npm install -g @cloudbase/cli${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 工具检查完成${NC}"
}

# 构建Docker镜像
build_image() {
    echo -e "${YELLOW}🔨 构建Docker镜像...${NC}"
    
    # 使用微信云专用Dockerfile
    docker build -f wechat-cloud/Dockerfile.wechat -t $SERVICE_NAME:$VERSION .
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 镜像构建成功${NC}"
    else
        echo -e "${RED}❌ 镜像构建失败${NC}"
        exit 1
    fi
}

# 登录云开发
login_cloudbase() {
    echo -e "${YELLOW}🔐 登录云开发...${NC}"
    
    # 检查是否已登录
    if tcb auth list | grep -q "已登录"; then
        echo -e "${GREEN}✅ 已登录云开发${NC}"
    else
        echo -e "${YELLOW}请在浏览器中完成登录...${NC}"
        tcb login
    fi
}

# 部署到云托管
deploy_to_cloudrun() {
    echo -e "${YELLOW}🚀 部署到云托管...${NC}"
    
    # 推送镜像并部署
    tcb run deploy \
        --name $SERVICE_NAME \
        --image $SERVICE_NAME:$VERSION \
        --config $CONFIG_FILE \
        --env $ENVIRONMENT
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 部署成功${NC}"
    else
        echo -e "${RED}❌ 部署失败${NC}"
        exit 1
    fi
}

# 检查服务状态
check_service() {
    echo -e "${YELLOW}🔍 检查服务状态...${NC}"
    
    # 等待服务启动
    sleep 30
    
    # 获取服务信息
    tcb run describe --name $SERVICE_NAME
    
    echo -e "${GREEN}✅ 服务状态检查完成${NC}"
}

# 主流程
main() {
    echo -e "${GREEN}开始部署流程...${NC}"
    
    check_tools
    build_image
    login_cloudbase
    deploy_to_cloudrun
    check_service
    
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo -e "${YELLOW}请在微信开发者工具中查看服务状态${NC}"
}

# 执行主流程
main
