# 🚀 微信云开发快速部署指南

> 5分钟快速将您的IdleGame后端部署到微信云开发！
>
> 🆓 **完全免费**：使用微信云开发免费额度，无需任何费用！

## 🎯 选择部署方案

### 🆓 免费版（推荐新手）
- **完全免费**：无需绑定信用卡
- **适用场景**：日活<500人的小型游戏
- **资源配置**：0.25核CPU + 0.5GB内存
- **数据库**：2GB存储 + 每天8万次操作

### 💼 标准版
- **按量付费**：根据实际使用量计费
- **适用场景**：中大型游戏项目
- **资源配置**：可自定义配置
- **数据库**：无限制

## ⚡ 免费版快速开始（推荐）

### 1. 环境准备（2分钟）
```bash
# 安装腾讯云CLI工具
npm install -g @cloudbase/cli

# 安装云开发SDK
cd backend
npm install @cloudbase/node-sdk

# 登录腾讯云（会打开浏览器）
tcb login
```

### 2. 获取环境信息（1分钟）
1. 打开微信开发者工具
2. 创建小程序项目（如果没有）
3. 点击"云开发"，开通云开发服务
4. 记录环境ID（格式：your-env-xxxxxx）

### 3. 配置环境变量（1分钟）
```bash
# 复制环境变量模板
cp .env.example .env.wechat

# 编辑环境变量
nano .env.wechat
```

在 `.env.wechat` 中设置：
```bash
NODE_ENV=production
WECHAT_CLOUD=true
TCB_ENV=your-env-xxxxxx  # 替换为您的环境ID
LOG_LEVEL=info
JWT_SECRET=your-super-secret-jwt-key
```

### 4. 一键部署（1分钟）
```bash
# 设置执行权限
chmod +x wechat-cloud/deploy.sh

# 🆓 免费版部署（推荐）
./wechat-cloud/deploy.sh prod v1.0.0 free

# 💼 标准版部署
# ./wechat-cloud/deploy.sh prod v1.0.0 standard
```

## ✅ 验证部署

### 检查服务状态
```bash
# 查看服务信息
tcb run describe --name idlegame-backend

# 查看服务日志
tcb run logs --name idlegame-backend --tail
```

### 测试API接口
```bash
# 获取服务URL（从上面的describe命令输出中获取）
SERVICE_URL="https://your-service-url"

# 测试健康检查
curl $SERVICE_URL/api/health

# 测试API文档
curl $SERVICE_URL/api-docs
```

## 🔧 数据库迁移（可选）

如果您需要迁移现有的MongoDB数据：

```bash
# 配置数据库连接
export MONGODB_URI="mongodb://localhost:27017/idlegame"
export TCB_ENV="your-env-xxxxxx"

# 执行迁移
cd wechat-cloud/database-migration
node migrate-to-cloudbase.js
```

## 📊 监控面板

部署完成后，您可以在以下位置监控服务：

1. **微信开发者工具**
   - 云开发 → 云托管 → 服务列表
   - 查看实时监控数据

2. **腾讯云控制台**
   - 登录 https://console.cloud.tencent.com/
   - 云开发 → 云托管
   - 查看详细监控指标

## 🚨 常见问题

### Q: 部署失败怎么办？
A: 查看详细错误信息：
```bash
tcb run logs --name idlegame-backend --tail
```

### Q: 如何更新服务？
A: 重新执行部署命令：
```bash
./wechat-cloud/deploy.sh prod v1.0.1
```

### Q: 如何回滚版本？
A: 在云托管控制台选择历史版本进行回滚

### Q: 服务无法访问？
A: 检查以下项目：
1. 服务是否正常运行
2. 端口配置是否正确（3000）
3. 健康检查是否通过
4. 环境变量是否配置正确

## 💡 优化建议

### 性能优化
- 根据用户量调整实例配置
- 启用CDN加速静态资源
- 配置数据库索引

### 成本优化
- 设置合理的自动扩缩容策略
- 监控资源使用情况
- 定期清理无用数据

### 安全优化
- 定期更新依赖包
- 配置访问控制
- 启用HTTPS

## 📞 获取帮助

- 📖 详细文档：查看 `README.md`
- 🐛 问题反馈：提交GitHub Issue
- 💬 技术交流：加入微信开发者群

## 🆓 免费版特别说明

### 免费额度监控
```bash
# 查看免费额度使用情况
tcb database:usage    # 数据库使用量
tcb storage:usage     # 存储使用量
tcb run describe --name idlegame-backend-free  # 云托管使用量
```

### 免费版优化建议
1. **启用自动休眠**：服务会在无请求时自动休眠，节省资源
2. **批量操作**：减少数据库读写次数
3. **本地缓存**：缓存静态配置数据
4. **定时清理**：定期清理过期数据

### 升级到付费版
当用户增长超过免费额度时：
```bash
# 升级到标准版
./wechat-cloud/deploy.sh prod v1.0.0 standard
```

---

**🎉 恭喜！您的IdleGame后端已成功部署到微信云开发！**

### 🆓 免费版用户可以享受：
- ✅ **完全免费**：无任何隐藏费用
- ✅ **24小时运行**：0.25核CPU + 0.5GB内存
- ✅ **2GB数据库**：足够小型游戏使用
- ✅ **自动扩缩容**：根据流量自动调整
- ✅ **专业监控**：实时监控服务状态

### 💼 标准版用户可以享受：
- ✅ 在小程序中调用后端API
- ✅ 通过监控面板查看服务状态
- ✅ 根据用户量自动扩缩容
- ✅ 享受微信云开发的完整生态

**💡 建议**：先使用免费版验证游戏概念，用户增长后再升级到付费版！
