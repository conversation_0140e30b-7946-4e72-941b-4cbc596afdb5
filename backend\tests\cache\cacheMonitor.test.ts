import { CacheMonitor, CacheMetrics, CacheAlert } from '../../src/utils/cacheMonitor';
import { CacheManager } from '../../src/utils/cache';
import { CacheNotifier, NotificationChannel } from '../../src/utils/cacheNotifier';
import { CacheMonitorConfig } from '../../src/config/cacheMonitorConfig';

describe('缓存监控系统测试', () => {
  let cacheMonitor: CacheMonitor;
  let cacheManager: CacheManager;
  let cacheNotifier: CacheNotifier;
  let monitorConfig: CacheMonitorConfig;

  beforeAll(async () => {
    // 设置测试环境
    process.env['USE_REAL_REDIS'] = 'false';
    process.env['CACHE_MONITORING_ENABLED'] = 'true';
    process.env['CACHE_ALERTS_ENABLED'] = 'true';
    process.env['CACHE_METRICS_INTERVAL'] = '1000'; // 1秒，用于测试
    process.env['CACHE_ALERT_INTERVAL'] = '500'; // 0.5秒，用于测试

    cacheMonitor = CacheMonitor.getInstance();
    cacheManager = CacheManager.getInstance();
    cacheNotifier = CacheNotifier.getInstance();
    monitorConfig = CacheMonitorConfig.getInstance();
  });

  afterAll(async () => {
    try {
      cacheMonitor.stopMonitoring();
      await cacheManager.flushall();
    } catch (error) {
      console.warn('清理缓存监控测试失败');
    }
  });

  beforeEach(async () => {
    // 每个测试前重置状态
    cacheMonitor.stopMonitoring();
    cacheManager.resetStats();
  });

  describe('缓存监控器基础功能测试', () => {
    it('应该启动和停止监控', async () => {
      expect(cacheMonitor.getMonitoringStatus().isMonitoring).toBe(false);

      cacheMonitor.startMonitoring();
      expect(cacheMonitor.getMonitoringStatus().isMonitoring).toBe(true);

      cacheMonitor.stopMonitoring();
      expect(cacheMonitor.getMonitoringStatus().isMonitoring).toBe(false);
    });

    it('应该收集缓存指标', async () => {
      // 执行一些缓存操作以生成指标
      await cacheManager.set('test_key1', 'value1');
      await cacheManager.get('test_key1'); // 命中
      await cacheManager.get('test_key2'); // 未命中

      cacheMonitor.startMonitoring();
      
      // 等待指标收集
      await new Promise(resolve => setTimeout(resolve, 1500));

      const latestMetrics = cacheMonitor.getLatestMetrics();
      expect(latestMetrics).not.toBeNull();
      
      if (latestMetrics) {
        expect(latestMetrics.timestamp).toBeInstanceOf(Date);
        expect(typeof latestMetrics.hitRate).toBe('number');
        expect(typeof latestMetrics.totalOperations).toBe('number');
      }

      cacheMonitor.stopMonitoring();
    });

    it('应该获取历史指标', async () => {
      cacheMonitor.startMonitoring();
      
      // 等待收集一些指标
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const historicalMetrics = cacheMonitor.getHistoricalMetrics(1); // 最近1小时
      expect(Array.isArray(historicalMetrics)).toBe(true);

      cacheMonitor.stopMonitoring();
    });

    it('应该更新告警配置', async () => {
      const newConfig = {
        hitRateThreshold: 90,
        errorRateThreshold: 2,
        enabled: true,
      };

      cacheMonitor.updateAlertConfig(newConfig);
      const status = cacheMonitor.getMonitoringStatus();
      
      expect(status.alertConfig.hitRateThreshold).toBe(90);
      expect(status.alertConfig.errorRateThreshold).toBe(2);
      expect(status.alertConfig.enabled).toBe(true);
    });

    it('应该生成监控报告', async () => {
      // 模拟一些指标数据
      const mockMetrics: CacheMetrics[] = [
        {
          timestamp: new Date(Date.now() - 3600000), // 1小时前
          hitRate: 85,
          missRate: 15,
          totalOperations: 100,
          averageResponseTime: 50,
          errorRate: 2,
          memoryUsage: 70,
          keyCount: 1000,
          expiredKeys: 10,
        },
        {
          timestamp: new Date(),
          hitRate: 90,
          missRate: 10,
          totalOperations: 150,
          averageResponseTime: 45,
          errorRate: 1,
          memoryUsage: 75,
          keyCount: 1200,
          expiredKeys: 15,
        },
      ];

      // 手动添加指标（用于测试）
      (cacheMonitor as any).metrics = mockMetrics;

      const report = cacheMonitor.generateReport(24);
      
      expect(report).toHaveProperty('period');
      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('trends');
      expect(report).toHaveProperty('recommendations');
      expect(report.summary.totalMetrics).toBe(2);
    });

    it('应该获取性能分析', async () => {
      // 模拟指标数据
      const mockMetrics: CacheMetrics[] = [
        {
          timestamp: new Date(Date.now() - 1800000), // 30分钟前
          hitRate: 80,
          missRate: 20,
          totalOperations: 100,
          averageResponseTime: 60,
          errorRate: 3,
          memoryUsage: 65,
          keyCount: 800,
          expiredKeys: 5,
        },
        {
          timestamp: new Date(),
          hitRate: 85,
          missRate: 15,
          totalOperations: 120,
          averageResponseTime: 55,
          errorRate: 2,
          memoryUsage: 70,
          keyCount: 900,
          expiredKeys: 8,
        },
      ];

      (cacheMonitor as any).metrics = mockMetrics;

      const analysis = cacheMonitor.getPerformanceAnalysis(1);
      
      expect(analysis).toHaveProperty('hitRateAnalysis');
      expect(analysis).toHaveProperty('responseTimeAnalysis');
      expect(analysis).toHaveProperty('errorRateAnalysis');
      expect(analysis).toHaveProperty('throughputAnalysis');
    });

    it('应该清理历史数据', async () => {
      // 添加一些旧数据
      const oldMetrics: CacheMetrics[] = [
        {
          timestamp: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000), // 8天前
          hitRate: 70,
          missRate: 30,
          totalOperations: 50,
          averageResponseTime: 80,
          errorRate: 5,
          memoryUsage: 60,
          keyCount: 500,
          expiredKeys: 20,
        },
      ];

      (cacheMonitor as any).metrics = oldMetrics;
      
      const originalCount = (cacheMonitor as any).metrics.length;
      cacheMonitor.cleanup(168); // 保留7天
      const newCount = (cacheMonitor as any).metrics.length;
      
      expect(newCount).toBeLessThan(originalCount);
    });

    it('应该导出监控数据', async () => {
      const exportData = cacheMonitor.exportData(24);
      
      expect(exportData).toHaveProperty('exportTime');
      expect(exportData).toHaveProperty('period');
      expect(exportData).toHaveProperty('metrics');
      expect(exportData).toHaveProperty('alerts');
      expect(exportData).toHaveProperty('config');
      expect(exportData).toHaveProperty('status');
      expect(exportData).toHaveProperty('report');
    });

    it('应该通过健康检查', async () => {
      const isHealthy = await cacheMonitor.healthCheck();
      // 由于监控未启动，健康检查可能失败，这是正常的
      expect(typeof isHealthy).toBe('boolean');
    });
  });

  describe('缓存监控配置测试', () => {
    it('应该获取监控配置', async () => {
      const monitoringConfig = monitorConfig.getMonitoringConfig();
      
      expect(monitoringConfig).toHaveProperty('enabled');
      expect(monitoringConfig).toHaveProperty('metricsInterval');
      expect(monitoringConfig).toHaveProperty('alertInterval');
      expect(monitoringConfig).toHaveProperty('retentionHours');
    });

    it('应该获取告警配置', async () => {
      const alertConfig = monitorConfig.getAlertConfig();
      
      expect(alertConfig).toHaveProperty('enabled');
      expect(alertConfig).toHaveProperty('hitRateThreshold');
      expect(alertConfig).toHaveProperty('errorRateThreshold');
      expect(alertConfig).toHaveProperty('responseTimeThreshold');
    });

    it('应该更新配置', async () => {
      const originalConfig = monitorConfig.getMonitoringConfig();
      
      monitorConfig.updateMonitoringConfig({
        metricsInterval: 30000,
        retentionHours: 48,
      });

      const updatedConfig = monitorConfig.getMonitoringConfig();
      expect(updatedConfig.metricsInterval).toBe(30000);
      expect(updatedConfig.retentionHours).toBe(48);
      
      // 恢复原配置
      monitorConfig.updateMonitoringConfig(originalConfig);
    });

    it('应该验证配置', async () => {
      const validation = monitorConfig.validateConfig();
      
      expect(validation).toHaveProperty('isValid');
      expect(validation).toHaveProperty('errors');
      expect(validation).toHaveProperty('warnings');
      expect(Array.isArray(validation.errors)).toBe(true);
      expect(Array.isArray(validation.warnings)).toBe(true);
    });

    it('应该导出和导入配置', async () => {
      const exportedConfig = monitorConfig.exportConfig();
      expect(typeof exportedConfig).toBe('string');
      
      const originalConfig = monitorConfig.getFullConfig();
      
      // 修改配置
      monitorConfig.updateMonitoringConfig({ metricsInterval: 45000 });
      
      // 导入原配置
      monitorConfig.importConfig(exportedConfig);
      
      const restoredConfig = monitorConfig.getFullConfig();
      expect(restoredConfig.monitoring.metricsInterval).toBe(originalConfig.monitoring.metricsInterval);
    });
  });

  describe('缓存通知器测试', () => {
    it('应该发送告警通知', async () => {
      const mockAlert: CacheAlert = {
        type: 'hit_rate',
        severity: 'high',
        message: '缓存命中率过低',
        value: 60,
        threshold: 80,
        timestamp: new Date(),
      };

      // 这个测试主要验证方法不会抛出异常
      await expect(cacheNotifier.sendAlert(mockAlert)).resolves.not.toThrow();
    });

    it('应该测试通知渠道', async () => {
      const testResult = await cacheNotifier.testNotification(NotificationChannel.LOG);
      expect(typeof testResult).toBe('boolean');
    });

    it('应该获取通知统计', async () => {
      const stats = cacheNotifier.getNotificationStats();
      
      expect(stats).toHaveProperty('totalSent');
      expect(stats).toHaveProperty('recentNotifications');
      expect(stats).toHaveProperty('config');
      expect(Array.isArray(stats.recentNotifications)).toBe(true);
    });

    it('应该更新通知配置', async () => {
      const newConfig = {
        enabled: true,
        emailEnabled: true,
        slackEnabled: false,
      };

      cacheNotifier.updateConfig(newConfig);
      const stats = cacheNotifier.getNotificationStats();
      
      expect(stats.config.enabled).toBe(true);
      expect(stats.config.emailEnabled).toBe(true);
      expect(stats.config.slackEnabled).toBe(false);
    });
  });

  describe('集成测试', () => {
    it('应该完整的监控流程工作正常', async () => {
      // 启动监控
      cacheMonitor.startMonitoring();

      // 执行一些缓存操作
      await cacheManager.set('integration_test1', 'value1');
      await cacheManager.set('integration_test2', 'value2');
      await cacheManager.get('integration_test1'); // 命中
      await cacheManager.get('nonexistent_key'); // 未命中

      // 等待指标收集
      await new Promise(resolve => setTimeout(resolve, 1500));

      // 检查监控状态
      const status = cacheMonitor.getMonitoringStatus();
      expect(status.isMonitoring).toBe(true);

      // 检查指标
      const latestMetrics = cacheMonitor.getLatestMetrics();
      expect(latestMetrics).not.toBeNull();

      // 生成报告
      const report = cacheMonitor.generateReport(1);
      expect(report).toHaveProperty('summary');

      // 停止监控
      cacheMonitor.stopMonitoring();
      expect(cacheMonitor.getMonitoringStatus().isMonitoring).toBe(false);
    });
  });
});
