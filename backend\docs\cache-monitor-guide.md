# 缓存监控系统使用指南

> 📚 **文档**: 缓存监控系统完整使用指南  
> 🔧 **工具**: CacheMonitor, CacheNotifier, CacheMonitorConfig  
> 📅 **更新**: 2025年7月23日

## 📋 概述

缓存监控系统提供了全面的Redis缓存监控、告警和通知功能，包括实时指标收集、智能告警、性能分析和自动报告生成。

## 🚀 快速开始

### 基础监控

```typescript
import { CacheMonitor } from '../utils/cacheMonitor';

const cacheMonitor = CacheMonitor.getInstance();

// 启动监控
cacheMonitor.startMonitoring();

// 获取监控状态
const status = cacheMonitor.getMonitoringStatus();
console.log('监控状态:', status);

// 获取最新指标
const metrics = cacheMonitor.getLatestMetrics();
console.log('最新指标:', metrics);
```

### 配置管理

```typescript
import { CacheMonitorConfig } from '../config/cacheMonitorConfig';

const config = CacheMonitorConfig.getInstance();

// 获取当前配置
const monitoringConfig = config.getMonitoringConfig();
const alertConfig = config.getAlertConfig();

// 更新配置
config.updateAlertConfig({
  hitRateThreshold: 85,
  errorRateThreshold: 3,
  enabled: true,
});
```

## 📊 监控指标

### 核心指标

```typescript
interface CacheMetrics {
  timestamp: Date;           // 时间戳
  hitRate: number;          // 命中率 (%)
  missRate: number;         // 未命中率 (%)
  totalOperations: number;  // 总操作数
  averageResponseTime: number; // 平均响应时间 (ms)
  errorRate: number;        // 错误率 (%)
  memoryUsage: number;      // 内存使用率 (%)
  keyCount: number;         // 键数量
  expiredKeys: number;      // 过期键数量
}
```

### 获取历史指标

```typescript
// 获取最近24小时的指标
const historicalMetrics = cacheMonitor.getHistoricalMetrics(24);

// 获取最近1小时的指标
const recentMetrics = cacheMonitor.getHistoricalMetrics(1);

// 获取性能分析
const analysis = cacheMonitor.getPerformanceAnalysis(24);
console.log('性能分析:', analysis);
```

## 🚨 告警系统

### 告警配置

```typescript
// 更新告警阈值
cacheMonitor.updateAlertConfig({
  hitRateThreshold: 80,      // 命中率低于80%告警
  errorRateThreshold: 5,     // 错误率高于5%告警
  responseTimeThreshold: 100, // 响应时间超过100ms告警
  memoryUsageThreshold: 80,  // 内存使用率超过80%告警
  keyCountThreshold: 100000, // 键数量超过10万告警
  enabled: true,
});
```

### 告警类型

- **hit_rate**: 缓存命中率告警
- **error_rate**: 缓存错误率告警
- **response_time**: 响应时间告警
- **memory_usage**: 内存使用告警
- **key_count**: 键数量告警

### 告警严重程度

- **critical**: 严重告警，需要立即处理
- **high**: 高级告警，需要尽快处理
- **medium**: 中级告警，需要关注
- **low**: 低级告警，仅记录

### 获取告警历史

```typescript
// 获取最近24小时的告警
const alerts = cacheMonitor.getAlerts(24);

// 按严重程度过滤
const criticalAlerts = alerts.filter(alert => alert.severity === 'critical');
```

## 📈 报告生成

### 生成监控报告

```typescript
// 生成24小时报告
const report = cacheMonitor.generateReport(24);

console.log('报告摘要:', report.summary);
console.log('趋势分析:', report.trends);
console.log('优化建议:', report.recommendations);
```

### 报告内容

```typescript
interface MonitoringReport {
  period: string;           // 报告周期
  summary: {               // 摘要信息
    totalMetrics: number;
    totalAlerts: number;
    avgHitRate: number;
    avgResponseTime: number;
    avgErrorRate: number;
  };
  trends: {                // 趋势分析
    hitRateTrend: 'improving' | 'stable' | 'declining';
    responseTimeTrend: 'improving' | 'stable' | 'declining';
    errorRateTrend: 'improving' | 'stable' | 'declining';
  };
  alerts: {                // 告警统计
    total: number;
    bySeverity: Record<string, number>;
    byType: Record<string, number>;
  };
  recommendations: string[]; // 优化建议
}
```

### 导出数据

```typescript
// 导出监控数据
const exportData = cacheMonitor.exportData(24);

// 保存到文件
import fs from 'fs';
fs.writeFileSync('cache-monitor-data.json', JSON.stringify(exportData, null, 2));
```

## 🔔 通知系统

### 配置通知

```typescript
import { CacheNotifier, NotificationChannel } from '../utils/cacheNotifier';

const notifier = CacheNotifier.getInstance();

// 更新通知配置
notifier.updateConfig({
  enabled: true,
  emailEnabled: true,
  emailRecipients: ['<EMAIL>', '<EMAIL>'],
  slackEnabled: true,
  slackWebhook: 'https://hooks.slack.com/services/...',
});
```

### 通知渠道

- **EMAIL**: 邮件通知
- **SLACK**: Slack通知
- **WEBHOOK**: Webhook通知
- **LOG**: 日志通知

### 测试通知

```typescript
// 测试各种通知渠道
const emailTest = await notifier.testNotification(NotificationChannel.EMAIL);
const slackTest = await notifier.testNotification(NotificationChannel.SLACK);
const logTest = await notifier.testNotification(NotificationChannel.LOG);

console.log('通知测试结果:', { emailTest, slackTest, logTest });
```

## ⚙️ 高级配置

### 环境变量配置

```bash
# 监控配置
CACHE_MONITORING_ENABLED=true
CACHE_METRICS_INTERVAL=60000      # 指标收集间隔(ms)
CACHE_ALERT_INTERVAL=30000        # 告警检查间隔(ms)
CACHE_RETENTION_HOURS=168         # 数据保留时间(小时)

# 告警阈值
CACHE_HIT_RATE_THRESHOLD=80       # 命中率阈值(%)
CACHE_ERROR_RATE_THRESHOLD=5      # 错误率阈值(%)
CACHE_RESPONSE_TIME_THRESHOLD=100 # 响应时间阈值(ms)
CACHE_MEMORY_USAGE_THRESHOLD=80   # 内存使用阈值(%)
CACHE_KEY_COUNT_THRESHOLD=100000  # 键数量阈值

# 通知配置
CACHE_NOTIFICATIONS_ENABLED=true
CACHE_EMAIL_ENABLED=true
CACHE_EMAIL_RECIPIENTS=<EMAIL>,<EMAIL>
CACHE_SLACK_ENABLED=true
CACHE_SLACK_WEBHOOK=https://hooks.slack.com/services/...
```

### 配置模板

```typescript
import { ConfigTemplates } from '../config/cacheMonitorConfig';

// 使用开发环境配置
const devConfig = ConfigTemplates.development;

// 使用生产环境配置
const prodConfig = ConfigTemplates.production;

// 使用测试环境配置
const testConfig = ConfigTemplates.testing;
```

## 🔍 性能分析

### 获取性能分析

```typescript
const analysis = cacheMonitor.getPerformanceAnalysis(24);

console.log('命中率分析:', analysis.hitRateAnalysis);
console.log('响应时间分析:', analysis.responseTimeAnalysis);
console.log('错误率分析:', analysis.errorRateAnalysis);
console.log('吞吐量分析:', analysis.throughputAnalysis);
```

### 分析指标

- **current**: 当前值
- **average**: 平均值
- **min/max**: 最小/最大值
- **trend**: 趋势（improving/stable/declining）
- **stability**: 稳定性（stable/unstable/volatile）
- **p95/p99**: 95%/99%分位数
- **errorSpikes**: 错误峰值数量

## 🧹 数据管理

### 清理历史数据

```typescript
// 清理7天前的数据
cacheMonitor.cleanup(168);

// 清理1天前的数据
cacheMonitor.cleanup(24);
```

### 健康检查

```typescript
// 检查监控系统健康状态
const isHealthy = await cacheMonitor.healthCheck();

if (!isHealthy) {
  console.log('监控系统异常，请检查配置和连接');
}
```

## 📝 最佳实践

### 1. 监控配置
- 根据业务需求调整指标收集间隔
- 设置合理的告警阈值，避免告警风暴
- 定期清理历史数据，控制存储空间

### 2. 告警管理
- 按严重程度配置不同的通知渠道
- 设置告警去重时间窗口
- 定期审查和调整告警规则

### 3. 性能优化
- 监控缓存命中率，优化缓存策略
- 关注响应时间趋势，及时扩容
- 定期分析错误日志，排查问题

### 4. 报告分析
- 定期生成监控报告
- 关注性能趋势变化
- 根据建议优化缓存配置

## 🔧 故障排除

### 常见问题

1. **监控未启动**
   ```typescript
   // 检查监控状态
   const status = cacheMonitor.getMonitoringStatus();
   if (!status.isMonitoring) {
     cacheMonitor.startMonitoring();
   }
   ```

2. **指标收集异常**
   ```typescript
   // 检查缓存连接
   const cacheHealthy = await cacheManager.healthCheck();
   if (!cacheHealthy) {
     console.log('缓存连接异常');
   }
   ```

3. **告警不生效**
   ```typescript
   // 检查告警配置
   const alertConfig = cacheMonitor.getMonitoringStatus().alertConfig;
   if (!alertConfig.enabled) {
     cacheMonitor.updateAlertConfig({ enabled: true });
   }
   ```

4. **通知发送失败**
   ```typescript
   // 测试通知渠道
   const testResult = await notifier.testNotification(NotificationChannel.EMAIL);
   if (!testResult) {
     console.log('邮件通知配置有误');
   }
   ```

---

**📞 技术支持**: 如有问题请联系开发团队  
**📖 更多文档**: 查看 `backend/docs/` 目录
