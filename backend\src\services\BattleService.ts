import { Character, <PERSON><PERSON>haracter } from '../models/Character';
import { User } from '../models/User';
import { skillService, ISkillResult } from './SkillService';
import { AppError, ErrorCodes } from '../utils/errors';
import { Logger } from '../utils/logger';
import { CacheManager } from '../utils/cache';

/**
 * 战斗状态枚举
 */
export enum BattleState {
  WAITING = 'waiting',
  ACTIVE = 'active',
  FINISHED = 'finished',
  CANCELLED = 'cancelled',
}

/**
 * 战斗类型枚举
 */
export enum BattleType {
  PVE = 'pve',
  PVP = 'pvp',
  TRAINING = 'training',
}

/**
 * 行动类型枚举
 */
export enum ActionType {
  ATTACK = 'attack',
  SKILL = 'skill',
  DEFEND = 'defend',
  ITEM = 'item',
  FLEE = 'flee',
}

/**
 * 战斗参与者接口
 */
export interface IBattleParticipant {
  characterId: string;
  character: I<PERSON>haracter;
  isPlayer: boolean;
  currentHp: number;
  currentMp: number;
  statusEffects: IStatusEffect[];
  actionQueue: IBattleAction[];
}

/**
 * 状态效果接口
 */
export interface IStatusEffect {
  id: string;
  type: string;
  value: number;
  duration: number;
  startTurn: number;
  description: string;
}

/**
 * 战斗行动接口
 */
export interface IBattleAction {
  participantId: string;
  actionType: ActionType;
  targetId?: string;
  skillId?: string;
  itemId?: string;
  timestamp: Date;
}

/**
 * 战斗结果接口
 */
export interface IBattleResult {
  battleId: string;
  winner: string;
  loser: string;
  battleType: BattleType;
  duration: number;
  totalTurns: number;
  expGained: number;
  goldGained: number;
  itemsGained: string[];
  battleLog: IBattleLogEntry[];
}

/**
 * 战斗日志条目接口
 */
export interface IBattleLogEntry {
  turn: number;
  participantId: string;
  action: IBattleAction;
  result: ISkillResult | IDamageResult;
  timestamp: Date;
}

/**
 * 伤害结果接口
 */
export interface IDamageResult {
  damage: number;
  isCritical: boolean;
  damageType: string;
  blocked: boolean;
  targetHp: number;
}

/**
 * 战斗实例接口
 */
export interface IBattle {
  id: string;
  type: BattleType;
  state: BattleState;
  participants: IBattleParticipant[];
  currentTurn: number;
  maxTurns: number;
  startTime: Date;
  endTime?: Date;
  battleLog: IBattleLogEntry[];
  winner?: string;
}

/**
 * 战斗服务类
 */
export class BattleService {
  private static instance: BattleService;
  private cacheManager: CacheManager;
  private activeBattles: Map<string, IBattle> = new Map();

  private constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  public static getInstance(): BattleService {
    if (!BattleService.instance) {
      BattleService.instance = new BattleService();
    }
    return BattleService.instance;
  }

  /**
   * 创建战斗
   */
  public async createBattle(
    attackerId: string,
    defenderId: string,
    battleType: BattleType = BattleType.PVE
  ): Promise<IBattle> {
    try {
      // 获取参与者角色
      const attacker = await Character.findById(attackerId);
      const defender = await Character.findById(defenderId);

      if (!attacker || !defender) {
        throw new AppError('参与者不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 检查角色是否已在战斗中
      if (attacker.battleStatus.isInBattle || defender.battleStatus.isInBattle) {
        throw new AppError('角色已在战斗中', 400, ErrorCodes.INVALID_ACTION);
      }

      // 创建战斗实例
      const battleId = this.generateBattleId();
      const battle: IBattle = {
        id: battleId,
        type: battleType,
        state: BattleState.ACTIVE,
        participants: [
          this.createParticipant(attacker, true),
          this.createParticipant(defender, false),
        ],
        currentTurn: 1,
        maxTurns: 50, // 最大回合数
        startTime: new Date(),
        battleLog: [],
      };

      // 更新角色战斗状态
      attacker.battleStatus.isInBattle = true;
      attacker.battleStatus.battleId = battleId as any;
      defender.battleStatus.isInBattle = true;
      defender.battleStatus.battleId = battleId as any;

      await Promise.all([attacker.save(), defender.save()]);

      // 缓存战斗实例
      this.activeBattles.set(battleId, battle);
      await this.cacheBattle(battle);

      Logger.info('战斗创建成功', {
        battleId,
        battleType,
        attackerId,
        defenderId,
      });

      return battle;
    } catch (error) {
      Logger.error('创建战斗失败', { attackerId, defenderId, battleType, error });
      throw error;
    }
  }

  /**
   * 执行战斗行动
   */
  public async executeBattleAction(
    battleId: string,
    participantId: string,
    action: IBattleAction
  ): Promise<IBattleLogEntry> {
    try {
      const battle = await this.getBattle(battleId);
      if (!battle) {
        throw new AppError('战斗不存在', 404, ErrorCodes.NOT_FOUND);
      }

      if (battle.state !== BattleState.ACTIVE) {
        throw new AppError('战斗已结束', 400, ErrorCodes.INVALID_ACTION);
      }

      const participant = battle.participants.find(p => p.characterId === participantId);
      if (!participant) {
        throw new AppError('参与者不存在', 404, ErrorCodes.NOT_FOUND);
      }

      // 执行行动
      const result = await this.processAction(battle, participant, action);

      // 创建战斗日志条目
      const logEntry: IBattleLogEntry = {
        turn: battle.currentTurn,
        participantId,
        action,
        result,
        timestamp: new Date(),
      };

      battle.battleLog.push(logEntry);

      // 检查战斗是否结束
      const battleEnded = this.checkBattleEnd(battle);
      if (battleEnded) {
        await this.endBattle(battle);
      } else {
        // 进入下一回合
        battle.currentTurn++;
        if (battle.currentTurn > battle.maxTurns) {
          await this.endBattle(battle, 'timeout');
        }
      }

      // 更新缓存
      await this.cacheBattle(battle);

      Logger.info('战斗行动执行', {
        battleId,
        participantId,
        actionType: action.actionType,
        turn: battle.currentTurn,
      });

      return logEntry;
    } catch (error) {
      Logger.error('执行战斗行动失败', { battleId, participantId, action, error });
      throw error;
    }
  }

  /**
   * 处理行动
   */
  private async processAction(
    battle: IBattle,
    participant: IBattleParticipant,
    action: IBattleAction
  ): Promise<ISkillResult | IDamageResult> {
    switch (action.actionType) {
      case ActionType.ATTACK:
        return this.processAttack(battle, participant, action);
      
      case ActionType.SKILL:
        return this.processSkill(battle, participant, action);
      
      case ActionType.DEFEND:
        return this.processDefend(battle, participant, action);
      
      case ActionType.ITEM:
        return this.processItem(battle, participant, action);
      
      case ActionType.FLEE:
        return this.processFlee(battle, participant, action);
      
      default:
        throw new AppError('无效的行动类型', 400, ErrorCodes.INVALID_ACTION);
    }
  }

  /**
   * 处理普通攻击
   */
  private async processAttack(
    battle: IBattle,
    attacker: IBattleParticipant,
    action: IBattleAction
  ): Promise<IDamageResult> {
    const target = battle.participants.find(p => p.characterId === action.targetId);
    if (!target) {
      throw new AppError('目标不存在', 404, ErrorCodes.NOT_FOUND);
    }

    // 计算伤害
    const damage = this.calculateDamage(attacker.character, target.character);
    const isCritical = this.calculateCritical(attacker.character);
    const finalDamage = isCritical ? damage * 2 : damage;

    // 应用伤害
    target.currentHp = Math.max(0, target.currentHp - finalDamage);

    return {
      damage: finalDamage,
      isCritical,
      damageType: 'physical',
      blocked: false,
      targetHp: target.currentHp,
    };
  }

  /**
   * 处理技能使用
   */
  private async processSkill(
    battle: IBattle,
    caster: IBattleParticipant,
    action: IBattleAction
  ): Promise<ISkillResult> {
    if (!action.skillId) {
      throw new AppError('技能ID不能为空', 400, ErrorCodes.VALIDATION_ERROR);
    }

    // 使用技能服务
    const result = await skillService.useSkill(
      caster.character.userId.toString(),
      action.skillId,
      action.targetId
    );

    // 更新参与者状态
    if (result.damage && action.targetId) {
      const target = battle.participants.find(p => p.characterId === action.targetId);
      if (target) {
        target.currentHp = Math.max(0, target.currentHp - result.damage);
      }
    }

    if (result.healing) {
      caster.currentHp = Math.min(
        caster.character.attributes.maxHp,
        caster.currentHp + result.healing
      );
    }

    return result;
  }

  /**
   * 处理防御
   */
  private async processDefend(
    battle: IBattle,
    participant: IBattleParticipant,
    action: IBattleAction
  ): Promise<IDamageResult> {
    // 防御状态，下回合受到伤害减半
    participant.statusEffects.push({
      id: 'defend',
      type: 'defense_boost',
      value: 0.5,
      duration: 1,
      startTurn: battle.currentTurn,
      description: '防御状态，受到伤害减半',
    });

    return {
      damage: 0,
      isCritical: false,
      damageType: 'none',
      blocked: false,
      targetHp: participant.currentHp,
    };
  }

  /**
   * 处理物品使用
   */
  private async processItem(
    battle: IBattle,
    participant: IBattleParticipant,
    action: IBattleAction
  ): Promise<IDamageResult> {
    // 简化实现，实际应该调用物品服务
    if (action.itemId === 'health_potion') {
      const healing = 50;
      participant.currentHp = Math.min(
        participant.character.attributes.maxHp,
        participant.currentHp + healing
      );
    }

    return {
      damage: 0,
      isCritical: false,
      damageType: 'healing',
      blocked: false,
      targetHp: participant.currentHp,
    };
  }

  /**
   * 处理逃跑
   */
  private async processFlee(
    battle: IBattle,
    participant: IBattleParticipant,
    action: IBattleAction
  ): Promise<IDamageResult> {
    // 逃跑成功率计算
    const fleeChance = 0.7; // 70%成功率
    const success = Math.random() < fleeChance;

    if (success) {
      battle.state = BattleState.FINISHED;
      battle.winner = battle.participants.find(p => p.characterId !== participant.characterId)?.characterId;
    }

    return {
      damage: 0,
      isCritical: false,
      damageType: 'flee',
      blocked: !success,
      targetHp: participant.currentHp,
    };
  }

  /**
   * 计算伤害
   */
  private calculateDamage(attacker: ICharacter, defender: ICharacter): number {
    const attackPower = attacker.attributes.damage;
    const defense = defender.attributes.def;
    
    // 基础伤害计算
    const baseDamage = attackPower * (1 + attacker.attributes.strength * 0.01);
    
    // 防御减免
    const defenseReduction = defense / (defense + 100);
    const finalDamage = baseDamage * (1 - defenseReduction);
    
    // 随机波动 ±10%
    const randomFactor = 0.9 + Math.random() * 0.2;
    
    return Math.max(1, Math.floor(finalDamage * randomFactor));
  }

  /**
   * 计算暴击
   */
  private calculateCritical(attacker: ICharacter): boolean {
    const criticalRate = attacker.attributes.criticalHit || 0.1;
    return Math.random() < criticalRate;
  }

  /**
   * 检查战斗是否结束
   */
  private checkBattleEnd(battle: IBattle): boolean {
    return battle.participants.some(p => p.currentHp <= 0);
  }

  /**
   * 结束战斗
   */
  private async endBattle(battle: IBattle, reason: string = 'defeat'): Promise<void> {
    battle.state = BattleState.FINISHED;
    battle.endTime = new Date();

    // 确定胜负
    if (reason === 'timeout') {
      // 超时平局，血量高的获胜
      const winner = battle.participants.reduce((prev, current) => 
        prev.currentHp > current.currentHp ? prev : current
      );
      battle.winner = winner.characterId;
    } else {
      const winner = battle.participants.find(p => p.currentHp > 0);
      battle.winner = winner?.characterId;
    }

    // 更新角色状态
    for (const participant of battle.participants) {
      const character = await Character.findById(participant.characterId);
      if (character) {
        character.battleStatus.isInBattle = false;
        character.battleStatus.battleId = undefined;
        character.attributes.currentHp = participant.currentHp;
        character.attributes.currentMp = participant.currentMp;
        await character.save();
      }
    }

    // 处理战斗奖励
    await this.processBattleRewards(battle);

    // 清理缓存
    this.activeBattles.delete(battle.id);
    await this.cacheManager.del(`battle:${battle.id}`);

    Logger.info('战斗结束', {
      battleId: battle.id,
      winner: battle.winner,
      reason,
      duration: battle.endTime.getTime() - battle.startTime.getTime(),
    });
  }

  /**
   * 处理战斗奖励
   */
  private async processBattleRewards(battle: IBattle): Promise<void> {
    const winner = battle.participants.find(p => p.characterId === battle.winner);
    if (!winner || !winner.isPlayer) return;

    // 计算奖励
    const expGained = Math.floor(50 + battle.currentTurn * 5);
    const goldGained = Math.floor(20 + battle.currentTurn * 2);

    // 更新用户数据
    const user = await User.findById(winner.character.userId);
    if (user) {
      user.gameData.totalBattlesWon += 1;
      user.gameData.totalGoldEarned += goldGained;
      user.profile.experience += expGained;
      await user.save();
    }

    Logger.info('战斗奖励发放', {
      battleId: battle.id,
      winnerId: winner.characterId,
      expGained,
      goldGained,
    });
  }

  /**
   * 创建参与者
   */
  private createParticipant(character: ICharacter, isPlayer: boolean): IBattleParticipant {
    return {
      characterId: character._id.toString(),
      character,
      isPlayer,
      currentHp: character.attributes.currentHp,
      currentMp: character.attributes.currentMp,
      statusEffects: [],
      actionQueue: [],
    };
  }

  /**
   * 获取战斗
   */
  private async getBattle(battleId: string): Promise<IBattle | null> {
    // 先从内存获取
    let battle = this.activeBattles.get(battleId);
    if (battle) return battle;

    // 从缓存获取
    const cachedBattle = await this.cacheManager.get(`battle:${battleId}`);
    if (cachedBattle) {
      battle = JSON.parse(cachedBattle);
      this.activeBattles.set(battleId, battle);
      return battle;
    }

    return null;
  }

  /**
   * 缓存战斗
   */
  private async cacheBattle(battle: IBattle): Promise<void> {
    await this.cacheManager.set(
      `battle:${battle.id}`,
      JSON.stringify(battle),
      1800 // 30分钟
    );
  }

  /**
   * 生成战斗ID
   */
  private generateBattleId(): string {
    return `battle_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const battleService = BattleService.getInstance();
