import { _decorator, Component, Node, input, Input, EventKeyboard, KeyCode, director } from 'cc';
import { ManagerInitializer, Managers } from '../managers';

const { ccclass, property } = _decorator;

/**
 * 紧急键盘测试组件
 * 可以添加到任何场景的任何节点上进行键盘输入测试
 */
@ccclass('EmergencyKeyboardTest')
export class EmergencyKeyboardTest extends Component {

    private isManagersInitialized: boolean = false;

    protected onLoad(): void {
        console.log('🚨 紧急键盘测试组件加载');
        this.initializeKeyboardInput();
    }

    protected start(): void {
        console.log('🚨 紧急键盘测试组件开始');
        this.showEmergencyInstructions();
        this.initializeManagers();
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        console.log('⌨️ 正在初始化紧急键盘输入...');
        
        try {
            input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
            console.log('✅ 紧急键盘输入初始化成功');
        } catch (error) {
            console.error('❌ 紧急键盘输入初始化失败:', error);
        }
    }

    /**
     * 显示紧急测试说明
     */
    private showEmergencyInstructions(): void {
        console.log('🚨 ========== 紧急键盘测试 ==========');
        console.log('📍 这是紧急键盘测试组件');
        console.log('💡 请确保游戏窗口有焦点，然后按以下按键：');
        console.log('⌨️ 基础测试:');
        console.log('   按 T 键 - 测试键盘输入');
        console.log('   按 1 键 - 数字键测试');
        console.log('   按 A 键 - 字母键测试');
        console.log('   按 空格键 - 空格键测试');
        console.log('⌨️ 管理器测试 (需要先初始化):');
        console.log('   按 M 键 - 显示管理器状态');
        console.log('   按 G 键 - 测试GameManager');
        console.log('   按 S 键 - 测试SceneManager');
        console.log('   按 E 键 - 测试EventManager');
        console.log('   按 R 键 - 测试ResourceManager');
        console.log('⌨️ 场景切换测试:');
        console.log('   按 2 键 - 切换到Main场景');
        console.log('   按 3 键 - 切换到Battle场景');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🚨 ===================================');
    }

    /**
     * 初始化管理器系统
     */
    private async initializeManagers(): Promise<void> {
        try {
            console.log('🎯 紧急测试：初始化管理器系统...');
            await ManagerInitializer.initializeAllManagers();
            
            // 启动游戏
            await Managers.Game.startGame();
            
            this.isManagersInitialized = true;
            console.log('✅ 紧急测试：管理器系统初始化完成');
        } catch (error) {
            console.error('❌ 紧急测试：管理器系统初始化失败:', error);
            console.log('⚠️ 管理器功能不可用，但基础键盘测试仍可使用');
        }
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        console.log(`⌨️ 紧急测试检测到按键: ${event.keyCode} (${KeyCode[event.keyCode] || '未知按键'})`);
        
        switch (event.keyCode) {
            case KeyCode.KEY_T:
                this.testBasicKeyboard();
                break;
            case KeyCode.DIGIT_1:
                console.log('🔢 数字键1测试成功！');
                break;
            case KeyCode.KEY_A:
                console.log('🔤 字母键A测试成功！');
                break;
            case KeyCode.SPACE:
                console.log('⭐ 空格键测试成功！');
                break;
            case KeyCode.KEY_M:
                this.testManagerStatus();
                break;
            case KeyCode.KEY_G:
                this.testGameManager();
                break;
            case KeyCode.KEY_S:
                this.testSceneManager();
                break;
            case KeyCode.KEY_E:
                this.testEventManager();
                break;
            case KeyCode.KEY_R:
                this.testResourceManager();
                break;
            case KeyCode.DIGIT_2:
                this.switchToScene('Main');
                break;
            case KeyCode.DIGIT_3:
                this.switchToScene('Battle');
                break;
            case KeyCode.KEY_H:
                this.showEmergencyInstructions();
                break;
            default:
                console.log(`⌨️ 其他按键测试: ${event.keyCode}`);
                break;
        }
    }

    /**
     * 测试基础键盘功能
     */
    private testBasicKeyboard(): void {
        console.log('🧪 ========== 基础键盘功能测试 ==========');
        console.log('✅ 键盘输入监听器正常工作');
        console.log('✅ 按键事件正确触发');
        console.log('✅ 事件处理函数正常执行');
        console.log('🎉 基础键盘输入测试通过！');
        console.log('💡 现在可以测试其他功能了');
        console.log('🧪 ======================================');
    }

    /**
     * 测试管理器状态
     */
    private testManagerStatus(): void {
        if (!this.isManagersInitialized) {
            console.log('⚠️ 管理器系统未初始化，请等待初始化完成');
            return;
        }

        console.log('📊 ========== 管理器状态测试 ==========');
        
        try {
            const status = ManagerInitializer.getManagersStatus();
            console.log(`📈 已初始化管理器: ${status.initialized.length}/${status.total}`);
            console.log(`📋 管理器列表: ${status.initialized.join(', ')}`);
            
            // 显示各个管理器的详细状态
            for (const [name, info] of Object.entries(status.status)) {
                if (info.initialized && info.instance) {
                    console.log(`📊 ${name}:`, info.instance.getStatus());
                } else {
                    console.log(`📊 ${name}: 未初始化`);
                }
            }
            
            console.log('✅ 管理器状态测试完成');
        } catch (error) {
            console.error('❌ 管理器状态测试失败:', error);
        }
        
        console.log('📊 ====================================');
    }

    /**
     * 测试GameManager
     */
    private testGameManager(): void {
        if (!this.isManagersInitialized) {
            console.log('⚠️ 管理器系统未初始化');
            return;
        }

        console.log('🎮 ========== GameManager测试 ==========');
        
        try {
            const gameManager = Managers.Game;
            const gameInfo = gameManager.getGameInfo();
            
            console.log('🎮 游戏信息:', gameInfo);
            console.log(`🎮 当前状态: ${gameInfo.state}`);
            console.log(`🎮 运行时间: ${gameInfo.runTime}ms`);
            console.log(`🎮 是否运行中: ${gameInfo.isRunning}`);
            console.log('✅ GameManager测试完成');
            
        } catch (error) {
            console.error('❌ GameManager测试失败:', error);
        }
        
        console.log('🎮 ==================================');
    }

    /**
     * 测试SceneManager
     */
    private testSceneManager(): void {
        if (!this.isManagersInitialized) {
            console.log('⚠️ 管理器系统未初始化');
            return;
        }

        console.log('🎬 ========== SceneManager测试 ==========');
        
        try {
            const sceneManager = Managers.Scene;
            const currentScene = sceneManager.getCurrentScene();
            
            console.log('🎬 当前场景:', currentScene);
            console.log(`🎬 是否正在切换: ${sceneManager.isTransitioning()}`);
            console.log('✅ SceneManager测试完成');
            
        } catch (error) {
            console.error('❌ SceneManager测试失败:', error);
        }
        
        console.log('🎬 ====================================');
    }

    /**
     * 测试EventManager
     */
    private testEventManager(): void {
        if (!this.isManagersInitialized) {
            console.log('⚠️ 管理器系统未初始化');
            return;
        }

        console.log('📡 ========== EventManager测试 ==========');
        
        try {
            const eventManager = Managers.Event;
            
            // 测试事件系统
            const testEventName = 'emergency-test-event';
            
            // 添加测试监听器
            eventManager.on(testEventName, (data) => {
                console.log('📡 收到紧急测试事件:', data);
            });
            
            // 触发测试事件
            eventManager.emit(testEventName, { 
                message: '这是紧急测试事件', 
                timestamp: Date.now() 
            });
            
            const status = eventManager.getStatus();
            console.log(`📡 事件总数: ${status.totalEvents}`);
            console.log(`📡 监听器总数: ${status.totalListeners}`);
            console.log('✅ EventManager测试完成');
            
        } catch (error) {
            console.error('❌ EventManager测试失败:', error);
        }
        
        console.log('📡 ====================================');
    }

    /**
     * 测试ResourceManager
     */
    private testResourceManager(): void {
        if (!this.isManagersInitialized) {
            console.log('⚠️ 管理器系统未初始化');
            return;
        }

        console.log('📦 ========== ResourceManager测试 ==========');
        
        try {
            const resourceManager = Managers.Resource;
            const cacheStats = resourceManager.getCacheStats();
            
            console.log('📦 资源缓存统计:', cacheStats);
            console.log(`📦 缓存资源数: ${cacheStats.totalResources}`);
            console.log(`📦 缓存大小: ${cacheStats.formattedSize}`);
            console.log('✅ ResourceManager测试完成');
            
        } catch (error) {
            console.error('❌ ResourceManager测试失败:', error);
        }
        
        console.log('📦 ========================================');
    }

    /**
     * 切换场景
     */
    private async switchToScene(sceneName: string): Promise<void> {
        console.log(`🔄 尝试切换到场景: ${sceneName}`);
        
        try {
            if (this.isManagersInitialized) {
                await Managers.Scene.switchScene(sceneName);
            } else {
                // 降级到直接使用director
                director.loadScene(sceneName, (error) => {
                    if (error) {
                        console.error(`❌ 场景切换失败: ${sceneName}`, error);
                    } else {
                        console.log(`✅ 场景切换成功: ${sceneName}`);
                    }
                });
            }
        } catch (error) {
            console.error(`❌ 场景切换异常: ${sceneName}`, error);
        }
    }

    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🚨 紧急键盘测试组件销毁');
    }
}
