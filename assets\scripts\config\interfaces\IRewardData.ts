/**
 * 奖励数据接口定义
 * 基于Godot项目中的rewards.xml结构
 */

/**
 * 奖励表数据接口
 */
export interface IRewardTableData {
    /** 奖励表唯一标识符 */
    id: string;
    
    /** 奖励表名称 */
    name: string;
    
    /** 奖励表描述 */
    description: string;
    
    /** 奖励类型 */
    type: RewardTableType;
    
    /** 奖励项列表 */
    rewards: IRewardItem[];
    
    /** 是否允许重复奖励 */
    allowDuplicates: boolean;
    
    /** 最大奖励数量 */
    maxRewards: number;
    
    /** 最小奖励数量 */
    minRewards: number;
}

/**
 * 奖励表类型枚举
 */
export enum RewardTableType {
    Random = 'random',           // 随机奖励
    Sequential = 'sequential',   // 顺序奖励
    Choice = 'choice',          // 选择奖励
    All = 'all'                 // 全部奖励
}

/**
 * 奖励项接口
 */
export interface IRewardItem {
    /** 奖励类型 */
    type: RewardType;
    
    /** 奖励ID（物品ID、技能ID等） */
    rewardId: string;
    
    /** 奖励数量 */
    quantity: number;
    
    /** 最小数量 */
    minQuantity?: number;
    
    /** 最大数量 */
    maxQuantity?: number;
    
    /** 获得概率（0-1） */
    probability: number;
    
    /** 权重（用于加权随机） */
    weight: number;
    
    /** 等级要求 */
    levelRequirement?: number;
    
    /** 条件检查 */
    conditions?: IRewardCondition[];
}

/**
 * 奖励类型枚举
 */
export enum RewardType {
    Item = 'item',
    Gold = 'gold',
    Experience = 'experience',
    Skill = 'skill',
    Title = 'title',
    Reputation = 'reputation',
    Attribute = 'attribute'
}

/**
 * 奖励条件接口
 */
export interface IRewardCondition {
    /** 条件类型 */
    type: RewardConditionType;
    
    /** 条件参数 */
    parameter: string;
    
    /** 条件值 */
    value: any;
    
    /** 比较操作符 */
    operator: ComparisonOperator;
}

/**
 * 奖励条件类型枚举
 */
export enum RewardConditionType {
    PlayerLevel = 'player_level',
    HasItem = 'has_item',
    HasSkill = 'has_skill',
    QuestCompleted = 'quest_completed',
    TimeOfDay = 'time_of_day',
    Random = 'random'
}

/**
 * 比较操作符枚举
 */
export enum ComparisonOperator {
    Equal = 'equal',
    NotEqual = 'not_equal',
    Greater = 'greater',
    GreaterEqual = 'greater_equal',
    Less = 'less',
    LessEqual = 'less_equal'
}

/**
 * 奖励结果接口
 */
export interface IRewardResult {
    /** 是否成功 */
    success: boolean;
    
    /** 获得的奖励 */
    rewards: IReceivedReward[];
    
    /** 错误信息 */
    error?: string;
    
    /** 奖励来源 */
    source: string;
    
    /** 获得时间 */
    timestamp: number;
}

/**
 * 已获得奖励接口
 */
export interface IReceivedReward {
    /** 奖励类型 */
    type: RewardType;
    
    /** 奖励ID */
    rewardId: string;
    
    /** 实际获得数量 */
    actualQuantity: number;
    
    /** 奖励名称 */
    name: string;
    
    /** 奖励描述 */
    description: string;
    
    /** 是否为稀有奖励 */
    isRare: boolean;
}

/**
 * 奖励配置文件根接口
 */
export interface IRewardConfig {
    /** 配置版本 */
    version: string;
    
    /** 最后更新时间 */
    lastUpdated: string;
    
    /** 奖励表列表 */
    rewardTables: IRewardTableData[];
}

/**
 * 奖励事件接口
 */
export interface IRewardEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 奖励表ID */
    rewardTableId: string;
    
    /** 事件类型 */
    eventType: RewardEventType;
    
    /** 奖励结果 */
    result: IRewardResult;
    
    /** 时间戳 */
    timestamp: number;
}

/**
 * 奖励事件类型枚举
 */
export enum RewardEventType {
    Open = 'open',
    Receive = 'receive',
    Fail = 'fail'
}

/**
 * 奖励统计接口
 */
export interface IRewardStatistics {
    /** 奖励表ID */
    rewardTableId: string;
    
    /** 总开启次数 */
    totalOpens: number;
    
    /** 各奖励获得次数 */
    rewardCounts: Map<string, number>;
    
    /** 平均奖励价值 */
    averageValue: number;
    
    /** 最后更新时间 */
    lastUpdated: number;
}

/**
 * 奖励池接口（用于动态奖励生成）
 */
export interface IRewardPool {
    /** 奖励池ID */
    id: string;
    
    /** 奖励池名称 */
    name: string;
    
    /** 可用奖励列表 */
    availableRewards: IRewardItem[];
    
    /** 刷新间隔（秒） */
    refreshInterval: number;
    
    /** 最后刷新时间 */
    lastRefresh: number;
    
    /** 是否启用 */
    enabled: boolean;
}

/**
 * 特殊奖励接口（用于节日、活动等特殊奖励）
 */
export interface ISpecialReward {
    /** 特殊奖励ID */
    id: string;
    
    /** 奖励名称 */
    name: string;
    
    /** 奖励描述 */
    description: string;
    
    /** 奖励内容 */
    rewards: IRewardItem[];
    
    /** 开始时间 */
    startTime: number;
    
    /** 结束时间 */
    endTime: number;
    
    /** 是否限时 */
    isLimited: boolean;
    
    /** 每人限制次数 */
    limitPerPlayer: number;
    
    /** 全服限制次数 */
    limitGlobal: number;
}
