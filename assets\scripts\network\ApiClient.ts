import { NetworkManager } from './NetworkManager';
import { ISkillData, ISkillResult } from '../data/ISkillData';
import { IEntityData } from '../data/IEntityData';
import { IItemData } from '../data/IItemData';
import { Logger } from '../core/utils/Logger';

/**
 * API响应接口
 */
export interface IApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
    code?: number;
}

/**
 * 用户认证响应
 */
export interface IAuthResponse {
    user: IEntityData;
    token: string;
}

/**
 * 技能学习响应
 */
export interface ISkillLearnResponse {
    skill: any;
    remainingSkillPoints: number;
}

/**
 * 用户技能列表响应
 */
export interface IUserSkillsResponse {
    skills: any[];
    skillPoints: number;
}

/**
 * 背包响应
 */
export interface IInventoryResponse {
    inventory: {
        items: any[];
        maxSlots: number;
    };
}

/**
 * API客户端
 * 负责与后端API的通信
 */
export class ApiClient {
    private static instance: ApiClient;
    private networkManager: NetworkManager;
    private baseUrl: string = 'http://localhost:3000/api/v1';
    private authToken: string | null = null;

    private constructor() {
        this.networkManager = NetworkManager.getInstance();
    }

    public static getInstance(): ApiClient {
        if (!ApiClient.instance) {
            ApiClient.instance = new ApiClient();
        }
        return ApiClient.instance;
    }

    /**
     * 设置认证令牌
     */
    public setAuthToken(token: string): void {
        this.authToken = token;
    }

    /**
     * 获取请求头
     */
    private getHeaders(): Record<string, string> {
        const headers: Record<string, string> = {
            'Content-Type': 'application/json',
        };

        if (this.authToken) {
            headers['Authorization'] = `Bearer ${this.authToken}`;
        }

        return headers;
    }

    /**
     * 发送API请求
     */
    private async request<T>(
        endpoint: string,
        method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
        data?: any
    ): Promise<IApiResponse<T>> {
        try {
            const url = `${this.baseUrl}${endpoint}`;
            const options = {
                method,
                headers: this.getHeaders(),
                body: data ? JSON.stringify(data) : undefined,
            };

            Logger.debug('API请求', { url, method, data });

            const response = await this.networkManager.request(url, options);
            
            Logger.debug('API响应', { url, response });

            return response as IApiResponse<T>;
        } catch (error) {
            Logger.error('API请求失败', { endpoint, method, data, error });
            throw error;
        }
    }

    // ==================== 用户相关API ====================

    /**
     * 用户注册
     */
    public async register(userData: {
        username: string;
        email: string;
        password: string;
    }): Promise<IApiResponse<IAuthResponse>> {
        return this.request<IAuthResponse>('/users/register', 'POST', userData);
    }

    /**
     * 用户登录
     */
    public async login(credentials: {
        username: string;
        password: string;
    }): Promise<IApiResponse<IAuthResponse>> {
        const response = await this.request<IAuthResponse>('/users/login', 'POST', credentials);
        
        if (response.success && response.data?.token) {
            this.setAuthToken(response.data.token);
        }
        
        return response;
    }

    /**
     * 获取用户资料
     */
    public async getUserProfile(): Promise<IApiResponse<IEntityData>> {
        return this.request<IEntityData>('/users/profile');
    }

    /**
     * 更新用户资料
     */
    public async updateUserProfile(profileData: Partial<IEntityData>): Promise<IApiResponse<IEntityData>> {
        return this.request<IEntityData>('/users/profile', 'PUT', profileData);
    }

    /**
     * 修改密码
     */
    public async changePassword(passwordData: {
        currentPassword: string;
        newPassword: string;
    }): Promise<IApiResponse<void>> {
        return this.request<void>('/users/change-password', 'POST', passwordData);
    }

    // ==================== 角色相关API ====================

    /**
     * 获取角色列表
     */
    public async getCharacters(): Promise<IApiResponse<IEntityData[]>> {
        return this.request<IEntityData[]>('/characters');
    }

    /**
     * 创建角色
     */
    public async createCharacter(characterData: {
        name: string;
        class: string;
    }): Promise<IApiResponse<IEntityData>> {
        return this.request<IEntityData>('/characters', 'POST', characterData);
    }

    /**
     * 获取角色详情
     */
    public async getCharacter(characterId: string): Promise<IApiResponse<IEntityData>> {
        return this.request<IEntityData>(`/characters/${characterId}`);
    }

    /**
     * 更新角色信息
     */
    public async updateCharacter(
        characterId: string,
        characterData: Partial<IEntityData>
    ): Promise<IApiResponse<IEntityData>> {
        return this.request<IEntityData>(`/characters/${characterId}`, 'PUT', characterData);
    }

    /**
     * 设置当前角色
     */
    public async setCurrentCharacter(characterId: string): Promise<IApiResponse<void>> {
        return this.request<void>(`/characters/${characterId}/set-current`, 'POST');
    }

    // ==================== 技能相关API ====================

    /**
     * 获取技能配置列表
     */
    public async getSkills(params?: {
        page?: number;
        limit?: number;
        damageType?: string;
        targetType?: string;
    }): Promise<IApiResponse<{ skills: ISkillData[]; pagination: any }>> {
        const queryString = params ? new URLSearchParams(params as any).toString() : '';
        const endpoint = queryString ? `/skills?${queryString}` : '/skills';
        return this.request<{ skills: ISkillData[]; pagination: any }>(endpoint);
    }

    /**
     * 获取技能详情
     */
    public async getSkill(skillId: string): Promise<IApiResponse<{ skill: ISkillData }>> {
        return this.request<{ skill: ISkillData }>(`/skills/${skillId}`);
    }

    /**
     * 获取角色技能列表
     */
    public async getUserSkills(characterId: string): Promise<IApiResponse<IUserSkillsResponse>> {
        return this.request<IUserSkillsResponse>(`/skills/characters/${characterId}`);
    }

    /**
     * 学习技能
     */
    public async learnSkill(characterId: string, skillId: string): Promise<IApiResponse<ISkillLearnResponse>> {
        return this.request<ISkillLearnResponse>(
            `/skills/characters/${characterId}/learn`,
            'POST',
            { skillId }
        );
    }

    /**
     * 使用技能
     */
    public async useSkill(
        characterId: string,
        skillId: string,
        targetId?: string
    ): Promise<IApiResponse<ISkillResult>> {
        return this.request<ISkillResult>(
            `/skills/characters/${characterId}/use`,
            'POST',
            { skillId, targetId }
        );
    }

    // ==================== 物品相关API ====================

    /**
     * 获取物品配置列表
     */
    public async getItems(params?: {
        page?: number;
        limit?: number;
        type?: string;
        rarity?: string;
    }): Promise<IApiResponse<{ items: IItemData[]; pagination: any }>> {
        const queryString = params ? new URLSearchParams(params as any).toString() : '';
        const endpoint = queryString ? `/items?${queryString}` : '/items';
        return this.request<{ items: IItemData[]; pagination: any }>(endpoint);
    }

    /**
     * 获取物品详情
     */
    public async getItem(itemId: string): Promise<IApiResponse<{ item: IItemData }>> {
        return this.request<{ item: IItemData }>(`/items/${itemId}`);
    }

    /**
     * 获取角色背包
     */
    public async getInventory(characterId: string): Promise<IApiResponse<IInventoryResponse>> {
        return this.request<IInventoryResponse>(`/items/characters/${characterId}/inventory`);
    }

    /**
     * 添加物品到背包
     */
    public async addItemToInventory(
        characterId: string,
        itemId: string,
        quantity: number = 1
    ): Promise<IApiResponse<any>> {
        return this.request<any>(
            `/items/characters/${characterId}/add`,
            'POST',
            { itemId, quantity }
        );
    }

    /**
     * 使用物品
     */
    public async useItem(characterId: string, instanceId: string): Promise<IApiResponse<any>> {
        return this.request<any>(`/items/characters/${characterId}/use/${instanceId}`, 'POST');
    }

    /**
     * 装备物品
     */
    public async equipItem(characterId: string, instanceId: string): Promise<IApiResponse<any>> {
        return this.request<any>(`/items/characters/${characterId}/equip/${instanceId}`, 'POST');
    }

    /**
     * 卸下装备
     */
    public async unequipItem(characterId: string, equipSlot: string): Promise<IApiResponse<any>> {
        return this.request<any>(`/items/characters/${characterId}/unequip/${equipSlot}`, 'POST');
    }

    // ==================== 工具方法 ====================

    /**
     * 检查网络连接状态
     */
    public async checkConnection(): Promise<boolean> {
        try {
            const response = await this.request('/health');
            return response.success;
        } catch (error) {
            return false;
        }
    }

    /**
     * 清除认证信息
     */
    public clearAuth(): void {
        this.authToken = null;
    }
}

// 导出单例实例
export const apiClient = ApiClient.getInstance();
