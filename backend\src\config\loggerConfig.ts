import winston = require('winston');
import DailyRotateFile = require('winston-daily-rotate-file');
import path = require('path');
import fs = require('fs');
import { LogLevel, LoggerConfig } from '../utils/logger';

/**
 * 日志传输器类型枚举
 */
export enum TransportType {
  CONSOLE = 'console',
  FILE = 'file',
  DAILY_ROTATE_FILE = 'daily_rotate_file',
  HTTP = 'http',
  ELASTICSEARCH = 'elasticsearch',
  MONGODB = 'mongodb',
}

/**
 * 控制台传输器配置
 */
export interface ConsoleTransportConfig {
  level?: LogLevel;
  colorize?: boolean;
  timestamp?: boolean;
  prettyPrint?: boolean;
  silent?: boolean;
}

/**
 * 文件传输器配置
 */
export interface FileTransportConfig {
  filename: string;
  level?: LogLevel;
  maxsize?: number;
  maxFiles?: number;
  tailable?: boolean;
  zippedArchive?: boolean;
  silent?: boolean;
}

/**
 * 日志轮转传输器配置
 */
export interface DailyRotateFileConfig {
  filename: string;
  datePattern?: string;
  level?: LogLevel;
  maxSize?: string;
  maxFiles?: string;
  auditFile?: string;
  zippedArchive?: boolean;
  createSymlink?: boolean;
  symlinkName?: string;
}

/**
 * HTTP传输器配置
 */
export interface HttpTransportConfig {
  host: string;
  port: number;
  path?: string;
  level?: LogLevel;
  ssl?: boolean;
  auth?: {
    username: string;
    password: string;
  };
}

/**
 * 日志输出配置管理器
 */
export class LoggerOutputConfig {
  private static instance: LoggerOutputConfig;
  private config: LoggerConfig;
  private logDir: string;

  private constructor() {
    this.logDir = this.ensureLogDirectory();
    this.config = this.loadConfiguration();
  }

  public static getInstance(): LoggerOutputConfig {
    if (!LoggerOutputConfig.instance) {
      LoggerOutputConfig.instance = new LoggerOutputConfig();
    }
    return LoggerOutputConfig.instance;
  }

  /**
   * 确保日志目录存在
   */
  private ensureLogDirectory(): string {
    const logDir = process.env['LOG_DIR'] || path.join(process.cwd(), 'logs');
    
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // 创建子目录
    const subDirs = ['error', 'business', 'performance', 'security', 'audit'];
    subDirs.forEach(dir => {
      const subDir = path.join(logDir, dir);
      if (!fs.existsSync(subDir)) {
        fs.mkdirSync(subDir, { recursive: true });
      }
    });

    return logDir;
  }

  /**
   * 加载配置
   */
  private loadConfiguration(): LoggerConfig {
    return {
      level: (process.env['LOG_LEVEL'] as LogLevel) || LogLevel.INFO,
      enableConsole: process.env['LOG_ENABLE_CONSOLE'] !== 'false',
      enableFile: process.env['LOG_ENABLE_FILE'] !== 'false',
      enableRotation: process.env['LOG_ENABLE_ROTATION'] !== 'false',
      maxFileSize: process.env['LOG_MAX_FILE_SIZE'] || '20m',
      maxFiles: process.env['LOG_MAX_FILES'] || '14d',
      datePattern: process.env['LOG_DATE_PATTERN'] || 'YYYY-MM-DD',
      logDir: this.logDir,
      enableRemoteLogging: process.env['LOG_ENABLE_REMOTE'] === 'true',
      remoteEndpoint: process.env['LOG_REMOTE_ENDPOINT'],
    };
  }

  /**
   * 创建控制台传输器
   */
  public createConsoleTransport(config?: ConsoleTransportConfig): winston.transports.ConsoleTransportInstance {
    const defaultConfig: ConsoleTransportConfig = {
      level: this.config.level,
      colorize: true,
      timestamp: true,
      prettyPrint: true,
      silent: false,
    };

    const finalConfig = { ...defaultConfig, ...config };

    return new winston.transports.Console({
      level: finalConfig.level,
      silent: finalConfig.silent,
      format: winston.format.combine(
        finalConfig.colorize ? winston.format.colorize() : winston.format.uncolorize(),
        finalConfig.timestamp ? winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss.SSS'
        }) : winston.format.uncolorize(),
        winston.format.errors({ stack: true }),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          let log = finalConfig.timestamp ? 
            `${timestamp} [${level}]: ${message}` : 
            `[${level}]: ${message}`;
          
          if (Object.keys(meta).length > 0) {
            log += finalConfig.prettyPrint ? 
              `\n${JSON.stringify(meta, null, 2)}` : 
              ` ${JSON.stringify(meta)}`;
          }
          
          return log;
        })
      ),
    });
  }

  /**
   * 创建文件传输器
   */
  public createFileTransport(config: FileTransportConfig): winston.transports.FileTransportInstance {
    const defaultConfig: Partial<FileTransportConfig> = {
      level: this.config.level,
      maxsize: 5242880, // 5MB
      maxFiles: 5,
      tailable: true,
      zippedArchive: false,
      silent: false,
    };

    const finalConfig = { ...defaultConfig, ...config };

    return new winston.transports.File({
      filename: path.isAbsolute(finalConfig.filename) ? 
        finalConfig.filename : 
        path.join(this.logDir, finalConfig.filename),
      level: finalConfig.level,
      maxsize: finalConfig.maxsize,
      maxFiles: finalConfig.maxFiles,
      tailable: finalConfig.tailable,
      zippedArchive: finalConfig.zippedArchive,
      silent: finalConfig.silent,
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss.SSS'
        }),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
    });
  }

  /**
   * 创建日志轮转传输器
   */
  public createDailyRotateFileTransport(config: DailyRotateFileConfig): DailyRotateFile {
    const defaultConfig: Partial<DailyRotateFileConfig> = {
      datePattern: this.config.datePattern,
      level: this.config.level,
      maxSize: this.config.maxFileSize,
      maxFiles: this.config.maxFiles,
      zippedArchive: true,
      createSymlink: true,
    };

    const finalConfig = { ...defaultConfig, ...config };

    return new DailyRotateFile({
      filename: path.isAbsolute(finalConfig.filename) ? 
        finalConfig.filename : 
        path.join(this.logDir, finalConfig.filename),
      datePattern: finalConfig.datePattern,
      level: finalConfig.level,
      maxSize: finalConfig.maxSize,
      maxFiles: finalConfig.maxFiles,
      auditFile: finalConfig.auditFile ? 
        path.join(this.logDir, finalConfig.auditFile) : 
        undefined,
      zippedArchive: finalConfig.zippedArchive,
      createSymlink: finalConfig.createSymlink,
      symlinkName: finalConfig.symlinkName,
      format: winston.format.combine(
        winston.format.timestamp({
          format: 'YYYY-MM-DD HH:mm:ss.SSS'
        }),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
    });
  }

  /**
   * 创建HTTP传输器
   */
  public createHttpTransport(config: HttpTransportConfig): winston.transports.HttpTransportInstance {
    return new winston.transports.Http({
      host: config.host,
      port: config.port,
      path: config.path || '/logs',
      level: config.level || this.config.level,
      ssl: config.ssl || false,
      auth: config.auth,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.json()
      ),
    });
  }

  /**
   * 获取默认传输器配置
   */
  public getDefaultTransports(): winston.transport[] {
    const transports: winston.transport[] = [];

    // 控制台传输器
    if (this.config.enableConsole) {
      transports.push(this.createConsoleTransport());
    }

    // 文件传输器
    if (this.config.enableFile) {
      if (this.config.enableRotation) {
        // 错误日志轮转
        transports.push(this.createDailyRotateFileTransport({
          filename: 'error/error-%DATE%.log',
          level: LogLevel.ERROR,
          auditFile: 'error-audit.json',
        }));

        // 组合日志轮转
        transports.push(this.createDailyRotateFileTransport({
          filename: 'combined-%DATE%.log',
          auditFile: 'combined-audit.json',
        }));

        // 业务日志轮转
        transports.push(this.createDailyRotateFileTransport({
          filename: 'business/business-%DATE%.log',
          level: LogLevel.INFO,
          auditFile: 'business-audit.json',
        }));

        // 性能日志轮转
        transports.push(this.createDailyRotateFileTransport({
          filename: 'performance/performance-%DATE%.log',
          level: LogLevel.DEBUG,
          auditFile: 'performance-audit.json',
        }));

        // 安全日志轮转
        transports.push(this.createDailyRotateFileTransport({
          filename: 'security/security-%DATE%.log',
          level: LogLevel.WARN,
          auditFile: 'security-audit.json',
        }));

        // 审计日志轮转
        transports.push(this.createDailyRotateFileTransport({
          filename: 'audit/audit-%DATE%.log',
          level: LogLevel.INFO,
          auditFile: 'audit-audit.json',
        }));
      } else {
        // 普通文件传输器
        transports.push(
          this.createFileTransport({
            filename: 'error.log',
            level: LogLevel.ERROR,
          }),
          this.createFileTransport({
            filename: 'combined.log',
          })
        );
      }
    }

    // 远程日志传输器
    if (this.config.enableRemoteLogging && this.config.remoteEndpoint) {
      try {
        const url = new URL(this.config.remoteEndpoint);
        transports.push(this.createHttpTransport({
          host: url.hostname,
          port: parseInt(url.port) || (url.protocol === 'https:' ? 443 : 80),
          path: url.pathname,
          ssl: url.protocol === 'https:',
        }));
      } catch (error) {
        console.warn('无效的远程日志端点:', this.config.remoteEndpoint);
      }
    }

    return transports;
  }

  /**
   * 获取配置
   */
  public getConfig(): LoggerConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  public updateConfig(newConfig: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取日志目录
   */
  public getLogDir(): string {
    return this.logDir;
  }

  /**
   * 清理旧日志文件
   */
  public async cleanupOldLogs(retentionDays: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const cleanupDir = (dir: string) => {
      if (!fs.existsSync(dir)) return;

      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.isFile() && stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          console.log(`已删除旧日志文件: ${filePath}`);
        }
      });
    };

    // 清理主日志目录
    cleanupDir(this.logDir);

    // 清理子目录
    const subDirs = ['error', 'business', 'performance', 'security', 'audit'];
    subDirs.forEach(subDir => {
      cleanupDir(path.join(this.logDir, subDir));
    });
  }
}
