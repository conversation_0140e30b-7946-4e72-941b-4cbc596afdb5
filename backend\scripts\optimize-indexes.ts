import { databaseManager } from '../src/config/database';
import { User } from '../src/models/User';
import { Character } from '../src/models/Character';
import { Skill, UserSkill } from '../src/models/Skill';
import { Item, UserItem } from '../src/models/Item';
import { Logger } from '../src/utils/logger';

/**
 * 数据库索引优化脚本
 * 为提升查询性能创建必要的复合索引
 */
class IndexOptimizer {
  
  /**
   * 执行所有索引优化
   */
  public async optimizeAllIndexes(): Promise<void> {
    try {
      Logger.info('开始数据库索引优化');

      // 连接数据库
      await databaseManager.connect();

      // 优化用户相关索引
      await this.optimizeUserIndexes();

      // 优化角色相关索引
      await this.optimizeCharacterIndexes();

      // 优化技能相关索引
      await this.optimizeSkillIndexes();

      // 优化物品相关索引
      await this.optimizeItemIndexes();

      Logger.info('数据库索引优化完成');

    } catch (error) {
      Logger.error('数据库索引优化失败', error);
      throw error;
    } finally {
      await databaseManager.disconnect();
    }
  }

  /**
   * 优化用户相关索引
   */
  private async optimizeUserIndexes(): Promise<void> {
    Logger.info('优化用户索引...');

    const userCollection = User.collection;

    // 1. 用户名和邮箱复合索引 (用于登录查询)
    await userCollection.createIndex(
      { username: 1, email: 1 },
      { 
        name: 'username_email_compound',
        background: true,
        sparse: true
      }
    );

    // 2. 邮箱唯一索引
    await userCollection.createIndex(
      { email: 1 },
      { 
        name: 'email_unique',
        unique: true,
        background: true
      }
    );

    // 3. 用户名唯一索引
    await userCollection.createIndex(
      { username: 1 },
      { 
        name: 'username_unique',
        unique: true,
        background: true
      }
    );

    // 4. 活跃状态和最后登录时间索引
    await userCollection.createIndex(
      { isActive: 1, 'profile.lastLoginAt': -1 },
      { 
        name: 'active_lastlogin',
        background: true
      }
    );

    // 5. 用户等级索引 (用于排行榜)
    await userCollection.createIndex(
      { 'profile.level': -1, 'profile.experience': -1 },
      { 
        name: 'level_experience_ranking',
        background: true
      }
    );

    Logger.info('用户索引优化完成');
  }

  /**
   * 优化角色相关索引
   */
  private async optimizeCharacterIndexes(): Promise<void> {
    Logger.info('优化角色索引...');

    const characterCollection = Character.collection;

    // 1. 用户ID和活跃状态复合索引 (最常用查询)
    await characterCollection.createIndex(
      { userId: 1, isActive: 1 },
      { 
        name: 'userId_isActive_compound',
        background: true
      }
    );

    // 2. 用户ID和角色名称复合索引
    await characterCollection.createIndex(
      { userId: 1, name: 1 },
      { 
        name: 'userId_name_compound',
        background: true
      }
    );

    // 3. 角色职业和等级索引 (用于统计和排行)
    await characterCollection.createIndex(
      { class: 1, level: -1 },
      { 
        name: 'class_level_ranking',
        background: true
      }
    );

    // 4. 战斗状态索引
    await characterCollection.createIndex(
      { 'battleStatus.isInBattle': 1, 'battleStatus.battleId': 1 },
      { 
        name: 'battle_status',
        background: true,
        sparse: true
      }
    );

    // 5. 位置索引 (用于地图查询)
    await characterCollection.createIndex(
      { 'location.mapId': 1, 'location.x': 1, 'location.y': 1 },
      { 
        name: 'location_compound',
        background: true
      }
    );

    Logger.info('角色索引优化完成');
  }

  /**
   * 优化技能相关索引
   */
  private async optimizeSkillIndexes(): Promise<void> {
    Logger.info('优化技能索引...');

    const skillCollection = Skill.collection;
    const userSkillCollection = UserSkill.collection;

    // 技能配置索引
    // 1. 技能ID唯一索引
    await skillCollection.createIndex(
      { id: 1 },
      { 
        name: 'skill_id_unique',
        unique: true,
        background: true
      }
    );

    // 2. 技能类型和等级索引
    await skillCollection.createIndex(
      { damageType: 1, targetType: 1, 'requirements.level': 1 },
      { 
        name: 'skill_type_level',
        background: true
      }
    );

    // 3. 活跃状态索引
    await skillCollection.createIndex(
      { isActive: 1 },
      { 
        name: 'skill_active',
        background: true
      }
    );

    // 用户技能索引
    // 1. 用户ID和技能ID复合索引 (最常用查询)
    await userSkillCollection.createIndex(
      { userId: 1, skillId: 1 },
      { 
        name: 'userId_skillId_compound',
        unique: true,
        background: true
      }
    );

    // 2. 用户ID和学习状态索引
    await userSkillCollection.createIndex(
      { userId: 1, learned: 1 },
      { 
        name: 'userId_learned',
        background: true
      }
    );

    // 3. 技能等级和经验索引
    await userSkillCollection.createIndex(
      { userId: 1, level: -1, experience: -1 },
      { 
        name: 'userId_level_exp',
        background: true
      }
    );

    // 4. 冷却时间索引
    await userSkillCollection.createIndex(
      { userId: 1, lastUsed: -1 },
      { 
        name: 'userId_lastUsed',
        background: true,
        sparse: true
      }
    );

    Logger.info('技能索引优化完成');
  }

  /**
   * 优化物品相关索引
   */
  private async optimizeItemIndexes(): Promise<void> {
    Logger.info('优化物品索引...');

    const itemCollection = Item.collection;
    const userItemCollection = UserItem.collection;

    // 物品配置索引
    // 1. 物品ID唯一索引
    await itemCollection.createIndex(
      { id: 1 },
      { 
        name: 'item_id_unique',
        unique: true,
        background: true
      }
    );

    // 2. 物品类型和稀有度索引
    await itemCollection.createIndex(
      { type: 1, rarity: 1 },
      { 
        name: 'item_type_rarity',
        background: true
      }
    );

    // 3. 活跃状态和可装备索引
    await itemCollection.createIndex(
      { isActive: 1, equipable: 1, equipSlot: 1 },
      { 
        name: 'item_active_equipable',
        background: true
      }
    );

    // 用户物品索引
    // 1. 用户ID和物品ID复合索引
    await userItemCollection.createIndex(
      { userId: 1, itemId: 1 },
      { 
        name: 'userId_itemId_compound',
        background: true
      }
    );

    // 2. 用户ID和背包位置索引
    await userItemCollection.createIndex(
      { userId: 1, slot: 1 },
      { 
        name: 'userId_slot',
        background: true
      }
    );

    // 3. 用户ID和装备状态索引
    await userItemCollection.createIndex(
      { userId: 1, equipped: 1 },
      { 
        name: 'userId_equipped',
        background: true
      }
    );

    // 4. 实例ID索引 (用于快速查找特定物品实例)
    await userItemCollection.createIndex(
      { instanceId: 1 },
      { 
        name: 'instanceId_unique',
        unique: true,
        background: true,
        sparse: true
      }
    );

    Logger.info('物品索引优化完成');
  }

  /**
   * 获取索引统计信息
   */
  public async getIndexStats(): Promise<any> {
    try {
      const collections = [
        { name: 'users', collection: User.collection },
        { name: 'characters', collection: Character.collection },
        { name: 'skills', collection: Skill.collection },
        { name: 'userskills', collection: UserSkill.collection },
        { name: 'items', collection: Item.collection },
        { name: 'useritems', collection: UserItem.collection },
      ];

      const stats: any = {};

      for (const { name, collection } of collections) {
        const indexes = await collection.listIndexes().toArray();
        stats[name] = {
          indexCount: indexes.length,
          indexes: indexes.map(idx => ({
            name: idx.name,
            key: idx.key,
            unique: idx.unique || false,
            sparse: idx.sparse || false,
          })),
        };
      }

      return stats;
    } catch (error) {
      Logger.error('获取索引统计失败', error);
      throw error;
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const optimizer = new IndexOptimizer();
  
  optimizer.optimizeAllIndexes()
    .then(() => {
      console.log('✅ 数据库索引优化完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 数据库索引优化失败:', error);
      process.exit(1);
    });
}

export { IndexOptimizer };
