# 项目概述

> 📖 **导航**: [返回主页](./README.md) | [技术架构](./technical-architecture.md) | [项目结构](./project-structure.md)

## 🎮 项目背景

本项目是一款**多人放置游戏**，从Godot 4.4引擎迁移至Cocos Creator 3.8.6，目标平台为微信小程序和抖音小程序。

### 迁移原因
- **平台适配**: 更好地支持小程序平台的特性和限制
- **生态优势**: Cocos Creator在小程序开发方面更加成熟
- **性能优化**: 针对移动端和小程序的性能优化
- **开发效率**: TypeScript开发体验和工具链支持

## 🏮 核心特性

### 角色成长
- **装备系统**: 多种装备类型，不同的路线和发展方向
- **技能系统**: 攻击、防御、辅助等多种技能体系
- **成长系统**: 等级提升、属性强化、技能解锁
- **成就系统**: 完整的成就和里程碑体系

### 放置玩法
- **离线收益**: 玩家离线时继续获得经验和资源
- **自动战斗**: AI控制角色进行战斗，玩家可设置策略
- **自动成长**: 自动提升等级、强化属性
- **资源收集**: 自动采集材料、完成任务

### 多人互动
- **公会社交**: 创建或加入公会，参与公会活动
- **好友系统**: 添加好友、互相协助、赠送礼品
- **实时聊天**: 世界频道、公会频道、私聊系统
- **排行榜**: 战力榜、财富榜、公会榜等多维度排行

### 核心玩法
- **战斗系统**: 回合制战斗，技能组合，策略性强
- **制作系统**: 炼丹、锻造、烹饪等生活技能
- **交易系统**: 玩家间物品交易、拍卖行
- **任务系统**: 主线任务、支线任务、日常任务

## 🎯 游戏特色元素

```typescript
// 游戏核心元素定义
export const GameElements = {


    // 技能类型
    skillTypes: [
        'attack',     // 攻击技能
        'defense',    // 防御技能
        'heal',       // 治疗技能
        'buff',       // 增益技能
        'debuff',     // 减益技能
        'special'     // 特殊技能
    ],

    // 等级阶段
    levelTiers: [
        'novice',     // 新手期 (1-10级)
        'beginner',   // 初级期 (11-30级)
        'intermediate', // 中级期 (31-60级)
        'advanced'    // 高级期 (61-100级)
    ],

    // 成就类型
    achievementTypes: [
        'combat',     // 战斗成就
        'social',     // 社交成就
        'collection', // 收集成就
        'progression' // 成长成就
    ],
    
    // 物品品质
    itemQualities: [
        'common',     // 普通 - 白色
        'uncommon',   // 优秀 - 绿色
        'rare',       // 稀有 - 蓝色
        'epic',       // 史诗 - 紫色
        'legendary',  // 传说 - 橙色
        'mythical'    // 神话 - 红色
    ]
};
```

## 💻 技术栈

### 前端技术栈
```typescript
const FrontendTechStack = {
    // 核心引擎
    engine: 'Cocos Creator 3.8.6',
    language: 'TypeScript 4.x',
    
    // UI系统
    ui: 'Cocos Creator UI System',
    animation: 'Cocos Creator Animation + Tween',
    physics: 'Cocos Creator Physics (Optional)',
    
    // 状态管理
    stateManagement: 'Custom Event System + Singleton Pattern',
    
    // 数据处理
    dataFormat: 'JSON',
    serialization: 'Native JSON + Custom Serializers'
};
```

### 后端技术栈
```typescript
const BackendTechStack = {
    // 云服务
    cloud: {
        wechat: '微信云开发',
        douyin: '抖音云服务',
        database: 'MongoDB',
        functions: 'Node.js',
        storage: '云存储'
    },
    
    // 实时通信
    realtime: {
        protocol: 'WebSocket',
        framework: 'Socket.io',
        messageFormat: 'JSON'
    },
    
    // 数据同步
    sync: {
        strategy: 'Event-driven + Periodic Sync',
        conflict_resolution: 'Server Authority',
        offline_support: true
    }
};
```

### 发布平台
```typescript
const Platforms = {
    primary: [
        '微信小程序',  // 主要目标平台
        '抖音小程序'   // 次要目标平台
    ],
    future: [
        'B站小程序',   // 未来扩展
        'QQ小程序',    // 未来扩展
        'Web版本'      // 可能的Web版本
    ]
};
```

### 开发工具
```typescript
const DevelopmentTools = {
    // 开发环境
    ide: 'VS Code',
    extensions: [
        'Cocos Creator Extension',
        'TypeScript Hero',
        'GitLens',
        'Prettier'
    ],
    
    // 版本控制
    version_control: 'Git',
    repository: 'GitHub',
    ci_cd: 'GitHub Actions',
    
    // 测试工具
    testing: {
        unit: 'Jest',
        integration: 'Custom Test Framework',
        e2e: 'Manual Testing'
    },
    
    // 文档工具
    documentation: {
        api: 'TypeDoc',
        design: 'Markdown',
        diagrams: 'Mermaid'
    },
    
    // 构建工具
    build: {
        bundler: 'Cocos Creator Build System',
        minification: 'Built-in',
        optimization: 'Custom Scripts'
    }
};
```

## 🎯 项目目标

### 短期目标 (3个月)
- [ ] 完成Godot到Cocos Creator的核心功能迁移
- [ ] 实现基础的武侠游戏玩法
- [ ] 完成微信小程序平台适配
- [ ] 进行内部测试和优化

### 中期目标 (6个月)
- [ ] 完善所有游戏功能和系统
- [ ] 完成抖音小程序平台适配
- [ ] 进行公开测试和用户反馈收集
- [ ] 优化性能和用户体验

### 长期目标 (12个月)
- [ ] 正式发布并运营
- [ ] 扩展到其他小程序平台
- [ ] 持续内容更新和功能迭代
- [ ] 建立稳定的用户社区

---

> 📖 **相关文档**: [技术架构](./technical-architecture.md) | [项目结构](./project-structure.md) | [代码标准](./code-standards.md)
