import request from 'supertest';
import { app } from '../../src/app';
import { databaseManager } from '../../src/config/database';
import { Logger } from '../../src/utils/logger';

/**
 * 业务流程测试结果
 */
export interface IBusinessFlowResult {
    flowName: string;
    success: boolean;
    steps: IBusinessStepResult[];
    totalTime: number;
    error?: string;
}

/**
 * 业务步骤测试结果
 */
export interface IBusinessStepResult {
    stepName: string;
    success: boolean;
    responseTime: number;
    statusCode: number;
    responseData?: any;
    error?: string;
    validations?: IValidationResult[];
}

/**
 * 验证结果
 */
export interface IValidationResult {
    field: string;
    expected: any;
    actual: any;
    passed: boolean;
    message?: string;
}

/**
 * 业务逻辑验证测试
 */
export class BusinessLogicTest {
    private authToken: string | null = null;
    private testUserId: string | null = null;
    private testCharacterId: string | null = null;

    /**
     * 运行所有业务逻辑测试
     */
    public async runAllBusinessTests(): Promise<IBusinessFlowResult[]> {
        try {
            Logger.info('开始业务逻辑验证测试');

            // 连接数据库
            await databaseManager.connect();

            const results: IBusinessFlowResult[] = [];

            // 1. 用户注册登录流程
            results.push(await this.testUserRegistrationFlow());

            // 2. 角色创建和管理流程
            results.push(await this.testCharacterManagementFlow());

            // 3. 技能学习和使用流程
            results.push(await this.testSkillLearningFlow());

            // 4. 物品和背包管理流程
            results.push(await this.testInventoryManagementFlow());

            // 5. 数据一致性验证
            results.push(await this.testDataConsistencyFlow());

            Logger.info('业务逻辑测试完成', {
                totalFlows: results.length,
                successfulFlows: results.filter(r => r.success).length,
                failedFlows: results.filter(r => !r.success).length,
            });

            return results;

        } catch (error) {
            Logger.error('业务逻辑测试失败', error);
            throw error;
        } finally {
            await databaseManager.disconnect();
        }
    }

    /**
     * 测试用户注册登录流程
     */
    private async testUserRegistrationFlow(): Promise<IBusinessFlowResult> {
        const flowName = '用户注册登录流程';
        const startTime = Date.now();
        const steps: IBusinessStepResult[] = [];

        try {
            // 步骤1: 用户注册
            const registerData = {
                username: 'business_test_user_' + Date.now(),
                email: 'businesstest_' + Date.now() + '@test.com',
                password: 'BusinessTest123!',
            };

            const registerStep = await this.executeStep(
                '用户注册',
                'POST',
                '/api/v1/users/register',
                registerData
            );
            steps.push(registerStep);

            if (!registerStep.success) {
                throw new Error('用户注册失败');
            }

            // 验证注册响应
            const registerValidations = this.validateUserRegistration(registerStep.responseData);
            registerStep.validations = registerValidations;

            // 保存认证令牌和用户ID
            if (registerStep.responseData?.data?.token) {
                this.authToken = registerStep.responseData.data.token;
            }
            if (registerStep.responseData?.data?.user?.id) {
                this.testUserId = registerStep.responseData.data.user.id;
            }

            // 步骤2: 用户登录
            const loginStep = await this.executeStep(
                '用户登录',
                'POST',
                '/api/v1/users/login',
                {
                    username: registerData.username,
                    password: registerData.password,
                }
            );
            steps.push(loginStep);

            if (!loginStep.success) {
                throw new Error('用户登录失败');
            }

            // 验证登录响应
            const loginValidations = this.validateUserLogin(loginStep.responseData);
            loginStep.validations = loginValidations;

            // 步骤3: 获取用户资料
            const profileStep = await this.executeStep(
                '获取用户资料',
                'GET',
                '/api/v1/users/profile',
                null,
                true
            );
            steps.push(profileStep);

            // 验证用户资料
            if (profileStep.success) {
                const profileValidations = this.validateUserProfile(profileStep.responseData);
                profileStep.validations = profileValidations;
            }

            const totalTime = Date.now() - startTime;
            const success = steps.every(step => step.success && 
                (!step.validations || step.validations.every(v => v.passed)));

            return {
                flowName,
                success,
                steps,
                totalTime,
            };

        } catch (error) {
            return {
                flowName,
                success: false,
                steps,
                totalTime: Date.now() - startTime,
                error: error.message,
            };
        }
    }

    /**
     * 测试角色创建和管理流程
     */
    private async testCharacterManagementFlow(): Promise<IBusinessFlowResult> {
        const flowName = '角色创建和管理流程';
        const startTime = Date.now();
        const steps: IBusinessStepResult[] = [];

        try {
            // 步骤1: 创建角色
            const characterData = {
                name: 'TestCharacter_' + Date.now(),
                class: 'warrior',
            };

            const createStep = await this.executeStep(
                '创建角色',
                'POST',
                '/api/v1/characters',
                characterData,
                true
            );
            steps.push(createStep);

            if (!createStep.success) {
                throw new Error('角色创建失败');
            }

            // 保存角色ID
            if (createStep.responseData?.data?.id) {
                this.testCharacterId = createStep.responseData.data.id;
            }

            // 验证角色创建响应
            const createValidations = this.validateCharacterCreation(createStep.responseData, characterData);
            createStep.validations = createValidations;

            // 步骤2: 获取角色列表
            const listStep = await this.executeStep(
                '获取角色列表',
                'GET',
                '/api/v1/characters',
                null,
                true
            );
            steps.push(listStep);

            // 步骤3: 获取角色详情
            if (this.testCharacterId) {
                const detailStep = await this.executeStep(
                    '获取角色详情',
                    'GET',
                    `/api/v1/characters/${this.testCharacterId}`,
                    null,
                    true
                );
                steps.push(detailStep);

                // 验证角色详情
                if (detailStep.success) {
                    const detailValidations = this.validateCharacterDetail(detailStep.responseData);
                    detailStep.validations = detailValidations;
                }
            }

            // 步骤4: 设置当前角色
            if (this.testCharacterId) {
                const setCurrentStep = await this.executeStep(
                    '设置当前角色',
                    'POST',
                    `/api/v1/characters/${this.testCharacterId}/set-current`,
                    {},
                    true
                );
                steps.push(setCurrentStep);
            }

            const totalTime = Date.now() - startTime;
            const success = steps.every(step => step.success && 
                (!step.validations || step.validations.every(v => v.passed)));

            return {
                flowName,
                success,
                steps,
                totalTime,
            };

        } catch (error) {
            return {
                flowName,
                success: false,
                steps,
                totalTime: Date.now() - startTime,
                error: error.message,
            };
        }
    }

    /**
     * 测试技能学习和使用流程
     */
    private async testSkillLearningFlow(): Promise<IBusinessFlowResult> {
        const flowName = '技能学习和使用流程';
        const startTime = Date.now();
        const steps: IBusinessStepResult[] = [];

        try {
            // 步骤1: 获取技能列表
            const skillListStep = await this.executeStep(
                '获取技能列表',
                'GET',
                '/api/v1/skills',
                null,
                false
            );
            steps.push(skillListStep);

            // 步骤2: 获取角色技能
            if (this.testCharacterId) {
                const userSkillsStep = await this.executeStep(
                    '获取角色技能',
                    'GET',
                    `/api/v1/skills/characters/${this.testCharacterId}`,
                    null,
                    true
                );
                steps.push(userSkillsStep);
            }

            // 步骤3: 学习技能（如果有可用技能）
            if (this.testCharacterId && skillListStep.success && skillListStep.responseData?.data?.length > 0) {
                const firstSkill = skillListStep.responseData.data[0];
                const learnStep = await this.executeStep(
                    '学习技能',
                    'POST',
                    `/api/v1/skills/characters/${this.testCharacterId}/learn`,
                    { skillId: firstSkill.id },
                    true
                );
                steps.push(learnStep);

                // 验证技能学习结果
                if (learnStep.success) {
                    const learnValidations = this.validateSkillLearning(learnStep.responseData);
                    learnStep.validations = learnValidations;
                }
            }

            const totalTime = Date.now() - startTime;
            const success = steps.every(step => step.success && 
                (!step.validations || step.validations.every(v => v.passed)));

            return {
                flowName,
                success,
                steps,
                totalTime,
            };

        } catch (error) {
            return {
                flowName,
                success: false,
                steps,
                totalTime: Date.now() - startTime,
                error: error.message,
            };
        }
    }

    /**
     * 测试物品和背包管理流程
     */
    private async testInventoryManagementFlow(): Promise<IBusinessFlowResult> {
        const flowName = '物品和背包管理流程';
        const startTime = Date.now();
        const steps: IBusinessStepResult[] = [];

        try {
            // 步骤1: 获取物品列表
            const itemListStep = await this.executeStep(
                '获取物品列表',
                'GET',
                '/api/v1/items',
                null,
                false
            );
            steps.push(itemListStep);

            // 步骤2: 获取背包
            if (this.testCharacterId) {
                const inventoryStep = await this.executeStep(
                    '获取背包',
                    'GET',
                    `/api/v1/items/characters/${this.testCharacterId}/inventory`,
                    null,
                    true
                );
                steps.push(inventoryStep);

                // 验证背包结构
                if (inventoryStep.success) {
                    const inventoryValidations = this.validateInventoryStructure(inventoryStep.responseData);
                    inventoryStep.validations = inventoryValidations;
                }
            }

            // 步骤3: 添加物品到背包（如果有可用物品）
            if (this.testCharacterId && itemListStep.success && itemListStep.responseData?.data?.length > 0) {
                const firstItem = itemListStep.responseData.data[0];
                const addItemStep = await this.executeStep(
                    '添加物品到背包',
                    'POST',
                    `/api/v1/items/characters/${this.testCharacterId}/add`,
                    { itemId: firstItem.id, quantity: 1 },
                    true
                );
                steps.push(addItemStep);
            }

            const totalTime = Date.now() - startTime;
            const success = steps.every(step => step.success && 
                (!step.validations || step.validations.every(v => v.passed)));

            return {
                flowName,
                success,
                steps,
                totalTime,
            };

        } catch (error) {
            return {
                flowName,
                success: false,
                steps,
                totalTime: Date.now() - startTime,
                error: error.message,
            };
        }
    }

    /**
     * 测试数据一致性
     */
    private async testDataConsistencyFlow(): Promise<IBusinessFlowResult> {
        const flowName = '数据一致性验证';
        const startTime = Date.now();
        const steps: IBusinessStepResult[] = [];

        try {
            // 验证用户数据一致性
            if (this.testUserId) {
                const userConsistencyStep = await this.executeStep(
                    '验证用户数据一致性',
                    'GET',
                    '/api/v1/users/profile',
                    null,
                    true
                );
                steps.push(userConsistencyStep);
            }

            // 验证角色数据一致性
            if (this.testCharacterId) {
                const characterConsistencyStep = await this.executeStep(
                    '验证角色数据一致性',
                    'GET',
                    `/api/v1/characters/${this.testCharacterId}`,
                    null,
                    true
                );
                steps.push(characterConsistencyStep);
            }

            const totalTime = Date.now() - startTime;
            const success = steps.every(step => step.success);

            return {
                flowName,
                success,
                steps,
                totalTime,
            };

        } catch (error) {
            return {
                flowName,
                success: false,
                steps,
                totalTime: Date.now() - startTime,
                error: error.message,
            };
        }
    }

    /**
     * 执行单个测试步骤
     */
    private async executeStep(
        stepName: string,
        method: string,
        path: string,
        data: any,
        requiresAuth: boolean = false
    ): Promise<IBusinessStepResult> {
        const startTime = Date.now();

        try {
            let requestBuilder = request(app)[method.toLowerCase()](path);

            if (requiresAuth && this.authToken) {
                requestBuilder = requestBuilder.set('Authorization', `Bearer ${this.authToken}`);
            }

            if (data && ['POST', 'PUT', 'PATCH'].includes(method.toUpperCase())) {
                requestBuilder = requestBuilder.send(data);
            }

            const response = await requestBuilder;
            const responseTime = Date.now() - startTime;

            return {
                stepName,
                success: response.status < 400,
                responseTime,
                statusCode: response.status,
                responseData: response.body,
            };

        } catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                stepName,
                success: false,
                responseTime,
                statusCode: error.status || 0,
                error: error.message,
            };
        }
    }

    /**
     * 验证用户注册响应
     */
    private validateUserRegistration(responseData: any): IValidationResult[] {
        const validations: IValidationResult[] = [];

        validations.push({
            field: 'success',
            expected: true,
            actual: responseData?.success,
            passed: responseData?.success === true,
        });

        validations.push({
            field: 'token',
            expected: 'string',
            actual: typeof responseData?.data?.token,
            passed: typeof responseData?.data?.token === 'string',
        });

        validations.push({
            field: 'user.id',
            expected: 'string',
            actual: typeof responseData?.data?.user?.id,
            passed: typeof responseData?.data?.user?.id === 'string',
        });

        return validations;
    }

    /**
     * 验证用户登录响应
     */
    private validateUserLogin(responseData: any): IValidationResult[] {
        return this.validateUserRegistration(responseData); // 结构相同
    }

    /**
     * 验证用户资料响应
     */
    private validateUserProfile(responseData: any): IValidationResult[] {
        const validations: IValidationResult[] = [];

        validations.push({
            field: 'success',
            expected: true,
            actual: responseData?.success,
            passed: responseData?.success === true,
        });

        validations.push({
            field: 'user.username',
            expected: 'string',
            actual: typeof responseData?.data?.username,
            passed: typeof responseData?.data?.username === 'string',
        });

        return validations;
    }

    /**
     * 验证角色创建响应
     */
    private validateCharacterCreation(responseData: any, characterData: any): IValidationResult[] {
        const validations: IValidationResult[] = [];

        validations.push({
            field: 'success',
            expected: true,
            actual: responseData?.success,
            passed: responseData?.success === true,
        });

        validations.push({
            field: 'character.name',
            expected: characterData.name,
            actual: responseData?.data?.name,
            passed: responseData?.data?.name === characterData.name,
        });

        validations.push({
            field: 'character.class',
            expected: characterData.class,
            actual: responseData?.data?.class,
            passed: responseData?.data?.class === characterData.class,
        });

        return validations;
    }

    /**
     * 验证角色详情响应
     */
    private validateCharacterDetail(responseData: any): IValidationResult[] {
        const validations: IValidationResult[] = [];

        validations.push({
            field: 'success',
            expected: true,
            actual: responseData?.success,
            passed: responseData?.success === true,
        });

        validations.push({
            field: 'character.attributes',
            expected: 'object',
            actual: typeof responseData?.data?.attributes,
            passed: typeof responseData?.data?.attributes === 'object',
        });

        return validations;
    }

    /**
     * 验证技能学习响应
     */
    private validateSkillLearning(responseData: any): IValidationResult[] {
        const validations: IValidationResult[] = [];

        validations.push({
            field: 'success',
            expected: true,
            actual: responseData?.success,
            passed: responseData?.success === true,
        });

        validations.push({
            field: 'remainingSkillPoints',
            expected: 'number',
            actual: typeof responseData?.data?.remainingSkillPoints,
            passed: typeof responseData?.data?.remainingSkillPoints === 'number',
        });

        return validations;
    }

    /**
     * 验证背包结构
     */
    private validateInventoryStructure(responseData: any): IValidationResult[] {
        const validations: IValidationResult[] = [];

        validations.push({
            field: 'success',
            expected: true,
            actual: responseData?.success,
            passed: responseData?.success === true,
        });

        validations.push({
            field: 'inventory.items',
            expected: 'array',
            actual: Array.isArray(responseData?.data?.inventory?.items) ? 'array' : typeof responseData?.data?.inventory?.items,
            passed: Array.isArray(responseData?.data?.inventory?.items),
        });

        validations.push({
            field: 'inventory.maxSlots',
            expected: 'number',
            actual: typeof responseData?.data?.inventory?.maxSlots,
            passed: typeof responseData?.data?.inventory?.maxSlots === 'number',
        });

        return validations;
    }
}

export const businessLogicTest = new BusinessLogicTest();
