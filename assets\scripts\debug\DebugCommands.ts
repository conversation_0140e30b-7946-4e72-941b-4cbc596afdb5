import { _decorator, Component, director, Scene } from 'cc';
import { SkillSlot } from '../ui/skill/SkillSlot';
import { SkillSlotDebugger } from './SkillSlotDebugger';

const { ccclass } = _decorator;

/**
 * 调试命令管理器
 * 提供控制台命令来调试SkillSlot
 */
@ccclass('DebugCommands')
export class DebugCommands extends Component {
    
    private static instance: DebugCommands = null;
    
    onLoad() {
        DebugCommands.instance = this;
        this.registerGlobalCommands();
    }
    
    /**
     * 注册全局调试命令
     */
    private registerGlobalCommands() {
        // 将调试命令添加到全局对象，方便在控制台调用
        (window as any).SkillSlotDebug = {
            // 查找所有SkillSlot
            findAllSkillSlots: () => {
                return this.findAllSkillSlots();
            },
            
            // 测试指定的SkillSlot
            testSkillSlot: (index: number = 0) => {
                return this.testSkillSlot(index);
            },
            
            // 开始冷却测试
            startCooldown: (index: number = 0, duration: number = 3.0) => {
                return this.startCooldownTest(index, duration);
            },
            
            // 设置技能等级
            setLevel: (index: number = 0, level: number = 1) => {
                return this.setSkillLevel(index, level);
            },
            
            // 切换启用状态
            toggleEnable: (index: number = 0) => {
                return this.toggleSkillSlot(index);
            },
            
            // 获取状态报告
            getStatus: (index: number = 0) => {
                return this.getSkillSlotStatus(index);
            },
            
            // 性能测试
            performanceTest: (index: number = 0) => {
                return this.runPerformanceTest(index);
            },
            
            // 帮助信息
            help: () => {
                return this.showHelp();
            }
        };
        
        console.log('SkillSlot调试命令已注册到 window.SkillSlotDebug');
        console.log('输入 SkillSlotDebug.help() 查看可用命令');
    }
    
    /**
     * 查找场景中的所有SkillSlot
     */
    private findAllSkillSlots(): SkillSlot[] {
        const scene = director.getScene();
        const skillSlots: SkillSlot[] = [];
        
        const findInNode = (node: any) => {
            const skillSlot = node.getComponent(SkillSlot);
            if (skillSlot) {
                skillSlots.push(skillSlot);
            }
            
            for (const child of node.children) {
                findInNode(child);
            }
        };
        
        findInNode(scene);
        
        console.log(`找到 ${skillSlots.length} 个SkillSlot组件`);
        return skillSlots;
    }
    
    /**
     * 测试指定的SkillSlot
     */
    private testSkillSlot(index: number): any {
        const skillSlots = this.findAllSkillSlots();
        
        if (index >= skillSlots.length) {
            console.error(`索引 ${index} 超出范围，共有 ${skillSlots.length} 个SkillSlot`);
            return null;
        }
        
        const skillSlot = skillSlots[index];
        const status = {
            index: index,
            nodeName: skillSlot.node.name,
            isEnabled: skillSlot.isEnabled(),
            isOnCooldown: skillSlot.isOnCooldown(),
            currentLevel: skillSlot.getCurrentLevel(),
            maxLevel: skillSlot.getMaxLevel(),
            skillName: skillSlot.getSkillName(),
            hotkey: skillSlot.getHotkey()
        };
        
        console.log(`SkillSlot[${index}] 状态:`, status);
        return status;
    }
    
    /**
     * 开始冷却测试
     */
    private startCooldownTest(index: number, duration: number): boolean {
        const skillSlots = this.findAllSkillSlots();
        
        if (index >= skillSlots.length) {
            console.error(`索引 ${index} 超出范围`);
            return false;
        }
        
        const skillSlot = skillSlots[index];
        skillSlot.startCooldown();
        
        console.log(`SkillSlot[${index}] 开始 ${duration}s 冷却`);
        return true;
    }
    
    /**
     * 设置技能等级
     */
    private setSkillLevel(index: number, level: number): boolean {
        const skillSlots = this.findAllSkillSlots();
        
        if (index >= skillSlots.length) {
            console.error(`索引 ${index} 超出范围`);
            return false;
        }
        
        const skillSlot = skillSlots[index];
        const currentData = skillSlot.getSkillData();
        
        if (!currentData) {
            console.error(`SkillSlot[${index}] 没有技能数据`);
            return false;
        }
        
        const newData = { ...currentData };
        newData.currentLevel = Math.max(0, Math.min(level, newData.maxLevel));
        skillSlot.updateSkillData(newData);
        
        console.log(`SkillSlot[${index}] 等级设置为: ${newData.currentLevel}`);
        return true;
    }
    
    /**
     * 切换启用状态
     */
    private toggleSkillSlot(index: number): boolean {
        const skillSlots = this.findAllSkillSlots();
        
        if (index >= skillSlots.length) {
            console.error(`索引 ${index} 超出范围`);
            return false;
        }
        
        const skillSlot = skillSlots[index];
        const currentState = skillSlot.isEnabled();
        skillSlot.setEnabled(!currentState);
        
        console.log(`SkillSlot[${index}] 状态切换为: ${!currentState ? '启用' : '禁用'}`);
        return true;
    }
    
    /**
     * 获取技能槽状态
     */
    private getSkillSlotStatus(index: number): any {
        return this.testSkillSlot(index);
    }
    
    /**
     * 运行性能测试
     */
    private runPerformanceTest(index: number): any {
        const skillSlots = this.findAllSkillSlots();
        
        if (index >= skillSlots.length) {
            console.error(`索引 ${index} 超出范围`);
            return null;
        }
        
        const skillSlot = skillSlots[index];
        const startTime = performance.now();
        
        // 状态切换测试
        for (let i = 0; i < 1000; i++) {
            skillSlot.setEnabled(i % 2 === 0);
        }
        
        // 数据更新测试
        const testData = skillSlot.getSkillData();
        if (testData) {
            for (let i = 0; i < 1000; i++) {
                const newData = { ...testData };
                newData.currentLevel = (i % testData.maxLevel) + 1;
                skillSlot.updateSkillData(newData);
            }
        }
        
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        const result = {
            index: index,
            testOperations: 2000,
            duration: duration,
            operationsPerSecond: (2000 / duration * 1000).toFixed(2)
        };
        
        console.log(`SkillSlot[${index}] 性能测试结果:`, result);
        return result;
    }
    
    /**
     * 显示帮助信息
     */
    private showHelp(): string {
        const help = `
=== SkillSlot 调试命令帮助 ===

可用命令:
• findAllSkillSlots() - 查找所有SkillSlot组件
• testSkillSlot(index) - 测试指定索引的SkillSlot
• startCooldown(index, duration) - 开始冷却测试
• setLevel(index, level) - 设置技能等级
• toggleEnable(index) - 切换启用状态
• getStatus(index) - 获取状态信息
• performanceTest(index) - 运行性能测试
• help() - 显示此帮助信息

使用示例:
SkillSlotDebug.findAllSkillSlots()
SkillSlotDebug.testSkillSlot(0)
SkillSlotDebug.startCooldown(0, 5.0)
SkillSlotDebug.setLevel(0, 3)
        `;
        
        console.log(help);
        return help;
    }
    
    /**
     * 获取实例
     */
    public static getInstance(): DebugCommands {
        return DebugCommands.instance;
    }
}
