# UI设计规范

> 📖 **导航**: [返回主页](./README.md) | [组件架构](./component-architecture.md) | [小程序优化](./miniprogram-optimization.md)

## 🎨 UI设计原则

### 游戏风格设计指导
- **颜色主题**: 蓝色(主要)、橙色(强调)、绿色(点缀)、深灰色(背景)、白色(文字)、浅灰色(边框)
- **字体设置**: 微软雅黑(主要)、<PERSON><PERSON>(次要)、<PERSON><PERSON>(英文)
- **尺寸规范**: 标题栏60px、按钮48px、图标32px、内边距12px、外边距8px、圆角4px

### 响应式设计规范
- **设计基准**: 750x1334
- **断点设置**: 小屏320px、中等375px、大屏414px、平板768px
- **适配策略**: SHOW_ALL模式，fitHeight + fitWidth

## 🖼️ UI组件规范

### 基础UI组件
```typescript
// UI组件基类
@ccclass('BaseUIComponent')
export abstract class BaseUIComponent extends Component {
    @property({ type: Bo<PERSON>an, tooltip: '是否启用动画' })
    protected enableAnimation: boolean = true;
    
    @property({ type: Number, tooltip: '动画持续时间' })
    protected animationDuration: number = 0.3;
    
    // UI状态
    protected _isVisible: boolean = false;
    protected _isInteractable: boolean = true;
    
    // 显示/隐藏动画
    public async show(animated: boolean = true): Promise<void> {
        if (this._isVisible) return;
        
        this.node.active = true;
        this._isVisible = true;
        
        if (animated && this.enableAnimation) {
            await this.playShowAnimation();
        }
        
        this.onShown();
    }
    
    public async hide(animated: boolean = true): Promise<void> {
        if (!this._isVisible) return;
        
        this._isVisible = false;
        
        if (animated && this.enableAnimation) {
            await this.playHideAnimation();
        }
        
        this.node.active = false;
        this.onHidden();
    }
    
    // 抽象方法
    protected abstract playShowAnimation(): Promise<void>;
    protected abstract playHideAnimation(): Promise<void>;
    protected abstract onShown(): void;
    protected abstract onHidden(): void;
}

// 面板基类
@ccclass('BasePanel')
export class BasePanel extends BaseUIComponent {
    @property({ type: Node, tooltip: '背景节点' })
    protected background: Node = null;
    
    @property({ type: Node, tooltip: '内容容器' })
    protected contentContainer: Node = null;
    
    @property({ type: Button, tooltip: '关闭按钮' })
    protected closeButton: Button = null;
    
    protected onLoad(): void {
        this.setupPanel();
        this.bindEvents();
    }
    
    protected setupPanel(): void {
        // 设置背景点击关闭
        if (this.background) {
            this.background.on(Node.EventType.TOUCH_END, this.onBackgroundClick, this);
        }
        
        // 设置关闭按钮
        if (this.closeButton) {
            this.closeButton.node.on(Button.EventType.CLICK, this.onCloseButtonClick, this);
        }
    }
    
    protected async playShowAnimation(): Promise<void> {
        // 淡入 + 缩放动画
        this.node.setScale(0.8, 0.8, 1);
        this.node.getComponent(UIOpacity).opacity = 0;
        
        return new Promise(resolve => {
            tween(this.node)
                .parallel(
                    tween().to(this.animationDuration, { scale: v3(1, 1, 1) }),
                    tween(this.node.getComponent(UIOpacity))
                        .to(this.animationDuration, { opacity: 255 })
                )
                .call(resolve)
                .start();
        });
    }
    
    protected async playHideAnimation(): Promise<void> {
        return new Promise(resolve => {
            tween(this.node)
                .parallel(
                    tween().to(this.animationDuration, { scale: v3(0.8, 0.8, 1) }),
                    tween(this.node.getComponent(UIOpacity))
                        .to(this.animationDuration, { opacity: 0 })
                )
                .call(resolve)
                .start();
        });
    }
    
    protected onBackgroundClick(): void {
        this.hide();
    }
    
    protected onCloseButtonClick(): void {
        this.hide();
    }
}
```

### 游戏UI组件
- **SkillSlot**: 技能槽组件，显示技能图标、等级、冷却时间，支持技能设置和冷却倒计时
- **BasePanel**: 面板基类，包含背景、内容容器、关闭按钮，支持淡入淡出+缩放动画

## 📱 小程序UI适配

### 安全区域适配
```typescript
// 安全区域适配组件
@ccclass('SafeAreaAdapter')
export class SafeAreaAdapter extends Component {
    @property({ type: Boolean, tooltip: '适配顶部安全区域' })
    protected adaptTop: boolean = true;
    
    @property({ type: Boolean, tooltip: '适配底部安全区域' })
    protected adaptBottom: boolean = true;
    
    protected onLoad(): void {
        this.adaptToSafeArea();
    }
    
    private adaptToSafeArea(): void {
        const safeArea = this.getSafeAreaInsets();
        const widget = this.node.getComponent(Widget);
        
        if (widget) {
            if (this.adaptTop && safeArea.top > 0) {
                widget.top += safeArea.top;
            }
            
            if (this.adaptBottom && safeArea.bottom > 0) {
                widget.bottom += safeArea.bottom;
            }
            
            widget.updateAlignment();
        }
    }
    
    private getSafeAreaInsets(): { top: number; bottom: number; left: number; right: number } {
        // 获取平台安全区域信息
        if (sys.platform === sys.Platform.WECHAT_GAME) {
            // 微信小程序安全区域
            return this.getWeChatSafeArea();
        } else if (sys.platform === sys.Platform.BYTEDANCE_MINI_GAME) {
            // 抖音小程序安全区域
            return this.getDouyinSafeArea();
        }
        
        return { top: 0, bottom: 0, left: 0, right: 0 };
    }
}
```

---

> 📖 **相关文档**: [小程序优化](./miniprogram-optimization.md) | [资源管理](./resource-management.md) | [开发工作流](./development-workflow.md)
