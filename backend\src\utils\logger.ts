import winston = require('winston');
import path = require('path');
import fs = require('fs');
import DailyRotateFile = require('winston-daily-rotate-file');

/**
 * 日志级别枚举
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  HTTP = 'http',
  VERBOSE = 'verbose',
  DEBUG = 'debug',
  SILLY = 'silly',
}

/**
 * 日志类型枚举
 */
export enum LogType {
  SYSTEM = 'system',
  USER_ACTION = 'user_action',
  GAME_EVENT = 'game_event',
  SECURITY_EVENT = 'security_event',
  PERFORMANCE = 'performance',
  API_REQUEST = 'api_request',
  DB_OPERATION = 'db_operation',
  CACHE_OPERATION = 'cache_operation',
  BUSINESS = 'business',
  AUDIT = 'audit',
}

/**
 * 日志配置接口
 */
export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableFile: boolean;
  enableRotation: boolean;
  maxFileSize: string;
  maxFiles: string;
  datePattern: string;
  logDir: string;
  enableRemoteLogging: boolean;
  remoteEndpoint?: string;
}

// 确保日志目录存在
const logDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logDir)) {
    fs.mkdirSync(logDir, { recursive: true });
}

// 获取日志配置
const getLoggerConfig = (): LoggerConfig => ({
  level: (process.env['LOG_LEVEL'] as LogLevel) || LogLevel.INFO,
  enableConsole: process.env['LOG_ENABLE_CONSOLE'] !== 'false',
  enableFile: process.env['LOG_ENABLE_FILE'] !== 'false',
  enableRotation: process.env['LOG_ENABLE_ROTATION'] !== 'false',
  maxFileSize: process.env['LOG_MAX_FILE_SIZE'] || '20m',
  maxFiles: process.env['LOG_MAX_FILES'] || '14d',
  datePattern: process.env['LOG_DATE_PATTERN'] || 'YYYY-MM-DD',
  logDir: process.env['LOG_DIR'] || logDir,
  enableRemoteLogging: process.env['LOG_ENABLE_REMOTE'] === 'true',
  remoteEndpoint: process.env['LOG_REMOTE_ENDPOINT'],
});

// 自定义日志格式
const logFormat = winston.format.combine(
    winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.json(),
    winston.format.printf(({ timestamp, level, message, ...meta }) => {
        let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
        
        if (Object.keys(meta).length > 0) {
            log += ` ${JSON.stringify(meta)}`;
        }
        
        return log;
    })
);

// 创建传输器数组
const createTransports = (): winston.transport[] => {
  const config = getLoggerConfig();
  const transports: winston.transport[] = [];

  // 控制台传输器
  if (config.enableConsole) {
    transports.push(
      new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.printf(({ timestamp, level, message, ...meta }) => {
            let log = `${timestamp} [${level}]: ${message}`;
            if (Object.keys(meta).length > 0) {
              log += ` ${JSON.stringify(meta)}`;
            }
            return log;
          })
        ),
      })
    );
  }

  // 文件传输器
  if (config.enableFile) {
    if (config.enableRotation) {
      // 使用日志轮转
      transports.push(
        // 错误日志轮转
        new DailyRotateFile({
          filename: path.join(config.logDir, 'error-%DATE%.log'),
          datePattern: config.datePattern,
          level: 'error',
          maxSize: config.maxFileSize,
          maxFiles: config.maxFiles,
          format: winston.format.json(),
        }),
        // 组合日志轮转
        new DailyRotateFile({
          filename: path.join(config.logDir, 'combined-%DATE%.log'),
          datePattern: config.datePattern,
          maxSize: config.maxFileSize,
          maxFiles: config.maxFiles,
          format: winston.format.json(),
        }),
        // 业务日志轮转
        new DailyRotateFile({
          filename: path.join(config.logDir, 'business-%DATE%.log'),
          datePattern: config.datePattern,
          level: 'info',
          maxSize: config.maxFileSize,
          maxFiles: config.maxFiles,
          format: winston.format.json(),
        })
      );
    } else {
      // 普通文件传输器
      transports.push(
        new winston.transports.File({
          filename: path.join(config.logDir, 'error.log'),
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5,
          format: winston.format.json(),
        }),
        new winston.transports.File({
          filename: path.join(config.logDir, 'combined.log'),
          maxsize: 5242880, // 5MB
          maxFiles: 5,
          format: winston.format.json(),
        })
      );
    }
  }

  return transports;
};

// 创建Winston logger实例
const winstonLogger = winston.createLogger({
    level: getLoggerConfig().level,
    format: logFormat,
    defaultMeta: {
      service: 'idlegame-backend',
      environment: process.env['NODE_ENV'] || 'development',
      version: process.env['APP_VERSION'] || '1.0.0',
    },
    transports: createTransports(),
});

/**
 * 日志上下文接口
 */
export interface LogContext {
  userId?: string;
  sessionId?: string;
  requestId?: string;
  traceId?: string;
  operation?: string;
  module?: string;
  [key: string]: any;
}

/**
 * 性能监控接口
 */
export interface PerformanceMetrics {
  operation: string;
  duration: number;
  startTime: number;
  endTime: number;
  memoryUsage?: NodeJS.MemoryUsage;
  cpuUsage?: NodeJS.CpuUsage;
  metadata?: any;
}

export class Logger {
    private static context: LogContext = {};
    private static performanceTimers: Map<string, number> = new Map();

    /**
     * 设置日志上下文
     */
    public static setContext(context: LogContext): void {
        Logger.context = { ...Logger.context, ...context };
    }

    /**
     * 清除日志上下文
     */
    public static clearContext(): void {
        Logger.context = {};
    }

    /**
     * 获取当前上下文
     */
    public static getContext(): LogContext {
        return { ...Logger.context };
    }

    /**
     * 创建带上下文的日志元数据
     */
    private static createMeta(meta?: any, logType?: LogType): any {
        return {
            ...Logger.context,
            type: logType || LogType.SYSTEM,
            timestamp: new Date().toISOString(),
            pid: process.pid,
            ...meta,
        };
    }

    /**
     * 调试日志
     */
    public static debug(message: string, meta?: any): void {
        winstonLogger.debug(message, Logger.createMeta(meta, LogType.SYSTEM));
    }

    /**
     * 信息日志
     */
    public static info(message: string, meta?: any): void {
        winstonLogger.info(message, Logger.createMeta(meta, LogType.SYSTEM));
    }

    /**
     * 警告日志
     */
    public static warn(message: string, meta?: any): void {
        winstonLogger.warn(message, Logger.createMeta(meta, LogType.SYSTEM));
    }

    /**
     * 错误日志
     */
    public static error(message: string, error?: any): void {
        let errorMeta: any = {};

        if (error instanceof Error) {
            errorMeta = {
                error: error.message,
                stack: error.stack,
                name: error.name,
            };
        } else if (error) {
            errorMeta = { error };
        }

        winstonLogger.error(message, Logger.createMeta(errorMeta, LogType.SYSTEM));
    }

    /**
     * 用户行为日志
     */
    public static userAction(userId: string, action: string, data?: any): void {
        winstonLogger.info(`用户行为: ${action}`, Logger.createMeta({
            userId,
            action,
            data,
        }, LogType.USER_ACTION));
    }

    /**
     * 游戏事件日志
     */
    public static gameEvent(eventType: string, data: any): void {
        winstonLogger.info(`游戏事件: ${eventType}`, Logger.createMeta({
            eventType,
            data,
        }, LogType.GAME_EVENT));
    }

    /**
     * 安全事件日志
     */
    public static securityEvent(eventType: string, data: any): void {
        winstonLogger.warn(`安全事件: ${eventType}`, Logger.createMeta({
            eventType,
            data,
            severity: 'high',
        }, LogType.SECURITY_EVENT));
    }

    /**
     * 业务日志
     */
    public static business(operation: string, data?: any, level: LogLevel = LogLevel.INFO): void {
        const logMethod = winstonLogger[level] || winstonLogger.info;
        logMethod.call(winstonLogger, `业务操作: ${operation}`, Logger.createMeta({
            operation,
            data,
        }, LogType.BUSINESS));
    }

    /**
     * 审计日志
     */
    public static audit(userId: string, operation: string, resource: string, result: 'success' | 'failure', data?: any): void {
        winstonLogger.info(`审计: ${operation}`, Logger.createMeta({
            userId,
            operation,
            resource,
            result,
            data,
        }, LogType.AUDIT));
    }

    /**
     * 性能日志
     */
    public static performance(operation: string, duration: number, data?: any): void {
        const level = duration > 1000 ? LogLevel.WARN : LogLevel.INFO;
        const logMethod = winstonLogger[level] || winstonLogger.info;

        logMethod.call(winstonLogger, `性能监控: ${operation}`, Logger.createMeta({
            operation,
            duration,
            unit: 'ms',
            data,
        }, LogType.PERFORMANCE));
    }

    /**
     * 开始性能计时
     */
    public static startTimer(operation: string): void {
        Logger.performanceTimers.set(operation, Date.now());
    }

    /**
     * 结束性能计时并记录
     */
    public static endTimer(operation: string, data?: any): number {
        const startTime = Logger.performanceTimers.get(operation);
        if (!startTime) {
            Logger.warn(`性能计时器未找到: ${operation}`);
            return 0;
        }

        const duration = Date.now() - startTime;
        Logger.performanceTimers.delete(operation);

        Logger.performance(operation, duration, data);
        return duration;
    }

    /**
     * 性能监控装饰器
     */
    public static performanceDecorator(operation?: string) {
        return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
            const method = descriptor.value;
            const operationName = operation || `${target.constructor.name}.${propertyName}`;

            descriptor.value = async function (...args: any[]) {
                const startTime = Date.now();
                const startMemory = process.memoryUsage();

                try {
                    const result = await method.apply(this, args);
                    const duration = Date.now() - startTime;
                    const endMemory = process.memoryUsage();

                    Logger.performance(operationName, duration, {
                        args: args.length,
                        memoryDelta: {
                            rss: endMemory.rss - startMemory.rss,
                            heapUsed: endMemory.heapUsed - startMemory.heapUsed,
                        },
                        success: true,
                    });

                    return result;
                } catch (error) {
                    const duration = Date.now() - startTime;
                    Logger.performance(operationName, duration, {
                        args: args.length,
                        success: false,
                        error: error instanceof Error ? error.message : error,
                    });
                    throw error;
                }
            };
        };
    }

    /**
     * API请求日志
     */
    public static apiRequest(method: string, url: string, statusCode: number, duration: number, userId?: string, data?: any): void {
        const level = statusCode >= 400 ? LogLevel.WARN : LogLevel.INFO;
        const logMethod = winstonLogger[level] || winstonLogger.info;

        logMethod.call(winstonLogger, `API请求: ${method} ${url}`, Logger.createMeta({
            method,
            url,
            statusCode,
            duration,
            userId,
            data,
        }, LogType.API_REQUEST));
    }

    /**
     * 数据库操作日志
     */
    public static dbOperation(operation: string, collection: string, duration: number, data?: any): void {
        const level = duration > 100 ? LogLevel.WARN : LogLevel.DEBUG;
        const logMethod = winstonLogger[level] || winstonLogger.debug;

        logMethod.call(winstonLogger, `数据库操作: ${operation} ${collection}`, Logger.createMeta({
            operation,
            collection,
            duration,
            data,
        }, LogType.DB_OPERATION));
    }

    /**
     * 缓存操作日志
     */
    public static cacheOperation(operation: string, key: string, hit: boolean, duration?: number, data?: any): void {
        winstonLogger.debug(`缓存操作: ${operation} ${key}`, Logger.createMeta({
            operation,
            key,
            hit,
            duration,
            data,
        }, LogType.CACHE_OPERATION));
    }

    /**
     * HTTP请求详细日志
     */
    public static httpRequest(req: any, res: any, duration: number): void {
        const { method, url, headers, query, body } = req;
        const { statusCode } = res;

        Logger.apiRequest(method, url, statusCode, duration, req.user?.id, {
            headers: {
                'user-agent': headers['user-agent'],
                'content-type': headers['content-type'],
                'authorization': headers.authorization ? '[REDACTED]' : undefined,
            },
            query,
            body: Logger.sanitizeBody(body),
            ip: req.ip || req.connection?.remoteAddress,
        });
    }

    /**
     * 清理敏感数据
     */
    private static sanitizeBody(body: any): any {
        if (!body || typeof body !== 'object') {
            return body;
        }

        const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
        const sanitized = { ...body };

        for (const field of sensitiveFields) {
            if (sanitized[field]) {
                sanitized[field] = '[REDACTED]';
            }
        }

        return sanitized;
    }

    /**
     * 结构化日志
     */
    public static structured(level: LogLevel, message: string, data: any, logType: LogType = LogType.SYSTEM): void {
        const logMethod = winstonLogger[level] || winstonLogger.info;
        logMethod.call(winstonLogger, message, Logger.createMeta(data, logType));
    }

    /**
     * 批量日志
     */
    public static batch(logs: Array<{ level: LogLevel; message: string; meta?: any; type?: LogType }>): void {
        logs.forEach(log => {
            Logger.structured(log.level, log.message, log.meta, log.type);
        });
    }

    /**
     * 条件日志
     */
    public static conditional(condition: boolean, level: LogLevel, message: string, meta?: any): void {
        if (condition) {
            Logger.structured(level, message, meta);
        }
    }

    /**
     * 获取日志统计信息
     */
    public static getStats(): any {
        return {
            activeTimers: Logger.performanceTimers.size,
            context: Logger.context,
            config: getLoggerConfig(),
            transports: winstonLogger.transports.length,
        };
    }

    /**
     * 更新日志配置
     */
    public static updateConfig(newConfig: Partial<LoggerConfig>): void {
        // 重新创建传输器
        winstonLogger.clear();
        const transports = createTransports();
        transports.forEach(transport => winstonLogger.add(transport));

        Logger.info('日志配置已更新', { newConfig });
    }

    /**
     * 健康检查
     */
    public static async healthCheck(): Promise<boolean> {
        try {
            Logger.debug('日志系统健康检查');
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 清理资源
     */
    public static cleanup(): void {
        Logger.performanceTimers.clear();
        Logger.clearContext();
        winstonLogger.end();
    }

    /**
     * 获取Winston logger实例
     */
    public static getWinstonLogger(): winston.Logger {
        return winstonLogger;
    }

    /**
     * 创建子日志器
     */
    public static createChild(defaultMeta: any): winston.Logger {
        return winstonLogger.child(defaultMeta);
    }

    /**
     * 日志级别检查
     */
    public static isLevelEnabled(level: LogLevel): boolean {
        return winstonLogger.isLevelEnabled(level);
    }

    /**
     * 格式化错误对象
     */
    public static formatError(error: Error): any {
        return {
            name: error.name,
            message: error.message,
            stack: error.stack,
            ...(error as any),
        };
    }

    /**
     * 创建请求ID
     */
    public static generateRequestId(): string {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 创建跟踪ID
     */
    public static generateTraceId(): string {
        return `trace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
