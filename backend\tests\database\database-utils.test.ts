import mongoose = require('mongoose');
import { DatabaseConfig } from '../../src/config/database';
import { DatabaseUtils } from '../../src/utils/database';
import { createBaseSchema } from '../../src/models/BaseSchema';

// 测试模型定义
interface ITestUtilsDocument extends mongoose.Document {
  name: string;
  value: number;
  category: string;
  isActive: boolean;
  email?: string;
  createdAt: Date;
  updatedAt: Date;
}

const testUtilsSchema = createBaseSchema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  value: {
    type: Number,
    required: true,
    min: 0,
  },
  category: {
    type: String,
    enum: ['A', 'B', 'C'],
    default: 'A',
  },
  isActive: {
    type: Boolean,
    default: true,
  },
  email: {
    type: String,
    trim: true,
    lowercase: true,
  },
});

const TestUtilsModel = mongoose.model<ITestUtilsDocument>('TestUtils', testUtilsSchema);

describe('数据库工具类功能测试', () => {
  let dbConfig: DatabaseConfig;
  let dbUtils: DatabaseUtils;

  beforeAll(async () => {
    // 设置测试环境
    process.env['NODE_ENV'] = 'test';
    process.env['MONGODB_URI'] = 'mongodb://localhost:27017/idlegame_test_utils';

    dbConfig = DatabaseConfig.getInstance();
    dbUtils = DatabaseUtils.getInstance();

    try {
      await dbConfig.connect();
    } catch (error) {
      console.warn('数据库连接失败，跳过数据库工具测试');
    }
  });

  afterAll(async () => {
    try {
      // 清理测试数据
      await TestUtilsModel.deleteMany({});
      await dbConfig.disconnect();
    } catch (error) {
      console.warn('清理测试数据失败');
    }
  });

  beforeEach(async () => {
    // 每个测试前清理数据
    try {
      await TestUtilsModel.deleteMany({});
    } catch (error) {
      // 忽略清理错误
    }
  });

  describe('事务处理测试', () => {
    it('应该成功执行事务', async () => {
      const result = await dbUtils.withTransaction(async (session) => {
        const doc1 = new TestUtilsModel({
          name: '事务测试1',
          value: 100,
          category: 'A',
        });
        await doc1.save({ session });

        const doc2 = new TestUtilsModel({
          name: '事务测试2',
          value: 200,
          category: 'B',
        });
        await doc2.save({ session });

        return { doc1: doc1._id, doc2: doc2._id };
      });

      expect(result).toHaveProperty('doc1');
      expect(result).toHaveProperty('doc2');

      // 验证文档已创建
      const count = await TestUtilsModel.countDocuments();
      expect(count).toBe(2);
    });

    it('应该在事务失败时回滚', async () => {
      try {
        await dbUtils.withTransaction(async (session) => {
          const doc1 = new TestUtilsModel({
            name: '事务回滚测试1',
            value: 100,
            category: 'A',
          });
          await doc1.save({ session });

          // 故意抛出错误
          throw new Error('测试事务回滚');
        });
        
        fail('事务应该失败');
      } catch (error) {
        expect((error as Error).message).toBe('测试事务回滚');
      }

      // 验证没有文档被创建
      const count = await TestUtilsModel.countDocuments();
      expect(count).toBe(0);
    });
  });

  describe('批量操作测试', () => {
    it('应该执行批量插入操作', async () => {
      const operations = [
        {
          insertOne: {
            document: {
              name: '批量插入1',
              value: 10,
              category: 'A',
            },
          },
        },
        {
          insertOne: {
            document: {
              name: '批量插入2',
              value: 20,
              category: 'B',
            },
          },
        },
        {
          insertOne: {
            document: {
              name: '批量插入3',
              value: 30,
              category: 'C',
            },
          },
        },
      ];

      const result = await dbUtils.bulkWrite(TestUtilsModel, operations);
      
      expect(result.insertedCount).toBe(3);
      expect(result.modifiedCount).toBe(0);
      expect(result.deletedCount).toBe(0);

      const count = await TestUtilsModel.countDocuments();
      expect(count).toBe(3);
    });

    it('应该执行混合批量操作', async () => {
      // 先创建一些测试数据
      const testDoc = await TestUtilsModel.create({
        name: '待更新文档',
        value: 100,
        category: 'A',
      });

      const operations = [
        {
          insertOne: {
            document: {
              name: '新插入文档',
              value: 50,
              category: 'B',
            },
          },
        },
        {
          updateOne: {
            filter: { _id: testDoc._id },
            update: { $set: { value: 150 } },
          },
        },
      ];

      const result = await dbUtils.bulkWrite(TestUtilsModel, operations);
      
      expect(result.insertedCount).toBe(1);
      expect(result.modifiedCount).toBe(1);

      // 验证更新结果
      const updatedDoc = await TestUtilsModel.findById(testDoc._id);
      expect(updatedDoc?.value).toBe(150);
    });
  });

  describe('聚合查询测试', () => {
    beforeEach(async () => {
      // 创建测试数据
      const testData = [
        { name: '文档1', value: 10, category: 'A', isActive: true },
        { name: '文档2', value: 20, category: 'A', isActive: false },
        { name: '文档3', value: 30, category: 'B', isActive: true },
        { name: '文档4', value: 40, category: 'B', isActive: true },
        { name: '文档5', value: 50, category: 'C', isActive: false },
      ];

      await TestUtilsModel.insertMany(testData);
    });

    it('应该执行分组聚合查询', async () => {
      const pipeline = [
        {
          $match: { isDeleted: { $ne: true } },
        },
        {
          $group: {
            _id: '$category',
            totalValue: { $sum: '$value' },
            count: { $sum: 1 },
            avgValue: { $avg: '$value' },
            activeCount: {
              $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] },
            },
          },
        },
        {
          $sort: { _id: 1 },
        },
      ];

      const results = await dbUtils.aggregate(TestUtilsModel, pipeline);
      
      expect(results).toHaveLength(3);
      
      const categoryA = results.find((r: any) => r._id === 'A');
      expect(categoryA?.totalValue).toBe(30);
      expect(categoryA?.count).toBe(2);
      expect(categoryA?.activeCount).toBe(1);

      const categoryB = results.find((r: any) => r._id === 'B');
      expect(categoryB?.totalValue).toBe(70);
      expect(categoryB?.count).toBe(2);
      expect(categoryB?.activeCount).toBe(2);
    });

    it('应该执行复杂聚合查询', async () => {
      const pipeline = [
        {
          $match: { isActive: true },
        },
        {
          $group: {
            _id: null,
            totalValue: { $sum: '$value' },
            maxValue: { $max: '$value' },
            minValue: { $min: '$value' },
            categories: { $addToSet: '$category' },
          },
        },
      ];

      const results = await dbUtils.aggregate(TestUtilsModel, pipeline);
      
      expect(results).toHaveLength(1);
      expect((results[0] as any).totalValue).toBe(80); // 10 + 30 + 40
      expect((results[0] as any).maxValue).toBe(40);
      expect((results[0] as any).minValue).toBe(10);
      expect((results[0] as any).categories).toContain('A');
      expect((results[0] as any).categories).toContain('B');
    });
  });

  describe('分页查询测试', () => {
    beforeEach(async () => {
      // 创建25个测试文档
      const testData = Array.from({ length: 25 }, (_, i) => ({
        name: `分页测试${i + 1}`,
        value: (i + 1) * 10,
        category: ['A', 'B', 'C'][i % 3] as 'A' | 'B' | 'C',
        isActive: i % 2 === 0,
      }));

      await TestUtilsModel.insertMany(testData);
    });

    it('应该正确执行分页查询', async () => {
      const page1 = await dbUtils.paginate(TestUtilsModel, {}, { page: 1, limit: 10 });
      
      expect(page1.docs).toHaveLength(10);
      expect(page1.totalDocs).toBe(25);
      expect(page1.totalPages).toBe(3);
      expect(page1.page).toBe(1);
      expect(page1.hasNextPage).toBe(true);
      expect(page1.hasPrevPage).toBe(false);
      expect(page1.nextPage).toBe(2);
      expect(page1.pagingCounter).toBe(1);
    });

    it('应该支持条件过滤的分页查询', async () => {
      const result = await dbUtils.paginate(
        TestUtilsModel,
        { category: 'A' },
        { page: 1, limit: 5, sort: { value: 1 } }
      );
      
      expect(result.docs.length).toBeGreaterThan(0);
      expect(result.docs.length).toBeLessThanOrEqual(5);
      
      // 验证所有文档都是category A
      result.docs.forEach((doc: any) => {
        expect(doc.category).toBe('A');
      });

      // 验证排序
      for (let i = 1; i < result.docs.length; i++) {
        expect((result.docs[i] as any).value).toBeGreaterThanOrEqual((result.docs[i - 1] as any).value);
      }
    });

    it('应该支持选择字段的分页查询', async () => {
      const result = await dbUtils.paginate(
        TestUtilsModel,
        {},
        { 
          page: 1, 
          limit: 5, 
          select: 'name value',
          lean: true 
        }
      );
      
      expect(result.docs).toHaveLength(5);
      
      result.docs.forEach(doc => {
        expect(doc).toHaveProperty('name');
        expect(doc).toHaveProperty('value');
        expect(doc).not.toHaveProperty('category');
        expect(doc).not.toHaveProperty('isActive');
      });
    });
  });

  describe('性能测试', () => {
    it('应该执行性能测试', async () => {
      const result = await dbUtils.performanceTest(TestUtilsModel, {
        insert: 10,
        find: 5,
        update: 3,
      });

      expect(result).toHaveProperty('insert');
      expect(result).toHaveProperty('find');
      expect(result).toHaveProperty('update');

      expect(result.insert.count).toBe(10);
      expect(result.insert.duration).toBeGreaterThan(0);
      expect(result.insert.docsPerSecond).toBeGreaterThan(0);

      expect(result.find.count).toBe(5);
      expect(result.find.duration).toBeGreaterThan(0);

      expect(result.update.count).toBe(3);
      expect(result.update.duration).toBeGreaterThan(0);
    });
  });

  describe('连接信息测试', () => {
    it('应该获取数据库连接信息', async () => {
      const connectionInfo = dbUtils.getConnectionInfo();

      expect(connectionInfo).toHaveProperty('connectionState');
      expect(connectionInfo).toHaveProperty('activeConnections');
    });
  });
});
