/**
 * UI按钮组件
 * 增强的按钮组件，支持音效、动画和状态管理
 */

import { _decorator, Component, Button, Node, Label, Sprite, tween, Vec3, AudioClip } from 'cc';
import { IUIButtonConfig } from '../types/UITypes';
import { AudioManager } from '../../managers/AudioManager';

const { ccclass, property } = _decorator;

@ccclass('UIButton')
export class UIButton extends Component {
    
    @property({ type: Button, tooltip: '按钮组件' })
    public button: Button | null = null;
    
    @property({ type: Label, tooltip: '按钮文本' })
    public label: Label | null = null;
    
    @property({ type: Sprite, tooltip: '按钮图标' })
    public icon: Sprite | null = null;
    
    @property({ tooltip: '是否启用点击音效' })
    public enableClickSound: boolean = true;
    
    @property({ tooltip: '是否启用点击动画' })
    public enableClickAnimation: boolean = true;
    
    @property({ tooltip: '是否启用悬停效果' })
    public enableHoverEffect: boolean = true;
    
    @property({ tooltip: '点击音效名称' })
    public clickSoundName: string = 'button_click';
    
    @property({ tooltip: '悬停音效名称' })
    public hoverSoundName: string = 'button_hover';
    
    // 私有属性
    private _config: IUIButtonConfig | null = null;
    private _originalScale: Vec3 = new Vec3();
    private _isHovered: boolean = false;
    private _clickCallback: (() => void) | null = null;

    protected onLoad(): void {
        // 自动查找组件
        if (!this.button) {
            this.button = this.getComponent(Button);
        }
        
        if (!this.label) {
            this.label = this.getComponentInChildren(Label);
        }
        
        if (!this.icon) {
            this.icon = this.getComponentInChildren(Sprite);
        }
        
        // 保存原始缩放
        this._originalScale.set(this.node.scale);
        
        // 绑定事件
        this.bindEvents();
    }

    protected onDestroy(): void {
        this.unbindEvents();
    }

    /**
     * 绑定事件
     */
    private bindEvents(): void {
        if (this.button) {
            this.button.node.on(Button.EventType.CLICK, this.onButtonClick, this);
            
            if (this.enableHoverEffect) {
                this.button.node.on(Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
                this.button.node.on(Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);
            }
        }
    }

    /**
     * 解绑事件
     */
    private unbindEvents(): void {
        if (this.button) {
            this.button.node.off(Button.EventType.CLICK, this.onButtonClick, this);
            this.button.node.off(Node.EventType.MOUSE_ENTER, this.onMouseEnter, this);
            this.button.node.off(Node.EventType.MOUSE_LEAVE, this.onMouseLeave, this);
        }
    }

    /**
     * 按钮点击事件
     */
    private onButtonClick(): void {
        // 播放点击音效
        if (this.enableClickSound) {
            this.playClickSound();
        }
        
        // 播放点击动画
        if (this.enableClickAnimation) {
            this.playClickAnimation();
        }
        
        // 执行回调
        if (this._clickCallback) {
            this._clickCallback();
        }
        
        // 执行配置中的回调
        if (this._config?.onClick) {
            this._config.onClick();
        }
    }

    /**
     * 鼠标进入事件
     */
    private onMouseEnter(): void {
        if (!this._isHovered && this.button?.interactable) {
            this._isHovered = true;
            
            // 播放悬停音效
            if (this.hoverSoundName) {
                this.playHoverSound();
            }
            
            // 播放悬停动画
            this.playHoverAnimation();
        }
    }

    /**
     * 鼠标离开事件
     */
    private onMouseLeave(): void {
        if (this._isHovered) {
            this._isHovered = false;
            
            // 恢复原始状态
            this.resetHoverAnimation();
        }
    }

    /**
     * 播放点击音效
     */
    private playClickSound(): void {
        try {
            const audioManager = AudioManager.getInstance();
            audioManager.playSFX(this.clickSoundName);
        } catch (error) {
            console.warn('播放按钮点击音效失败:', error);
        }
    }

    /**
     * 播放悬停音效
     */
    private playHoverSound(): void {
        try {
            const audioManager = AudioManager.getInstance();
            audioManager.playSFX(this.hoverSoundName);
        } catch (error) {
            console.warn('播放按钮悬停音效失败:', error);
        }
    }

    /**
     * 播放点击动画
     */
    private playClickAnimation(): void {
        const clickScale = new Vec3(this._originalScale.x * 0.95, this._originalScale.y * 0.95, 1);
        
        tween(this.node)
            .to(0.1, { scale: clickScale })
            .to(0.1, { scale: this._originalScale })
            .start();
    }

    /**
     * 播放悬停动画
     */
    private playHoverAnimation(): void {
        const hoverScale = new Vec3(this._originalScale.x * 1.05, this._originalScale.y * 1.05, 1);
        
        tween(this.node)
            .to(0.2, { scale: hoverScale })
            .start();
    }

    /**
     * 重置悬停动画
     */
    private resetHoverAnimation(): void {
        tween(this.node)
            .to(0.2, { scale: this._originalScale })
            .start();
    }

    // ==================== 公共API ====================

    /**
     * 设置按钮配置
     */
    public setConfig(config: IUIButtonConfig): void {
        this._config = config;
        
        // 应用配置
        if (config.text && this.label) {
            this.label.string = config.text;
        }
        
        if (config.enabled !== undefined && this.button) {
            this.button.interactable = config.enabled;
        }
        
        if (config.visible !== undefined) {
            this.node.active = config.visible;
        }
        
        if (config.onClick) {
            this._clickCallback = config.onClick;
        }
    }

    /**
     * 设置按钮文本
     */
    public setText(text: string): void {
        if (this.label) {
            this.label.string = text;
        }
        
        if (this._config) {
            this._config.text = text;
        }
    }

    /**
     * 设置按钮启用状态
     */
    public setEnabled(enabled: boolean): void {
        if (this.button) {
            this.button.interactable = enabled;
        }
        
        if (this._config) {
            this._config.enabled = enabled;
        }
    }

    /**
     * 设置按钮可见性
     */
    public setVisible(visible: boolean): void {
        this.node.active = visible;
        
        if (this._config) {
            this._config.visible = visible;
        }
    }

    /**
     * 设置点击回调
     */
    public setClickCallback(callback: () => void): void {
        this._clickCallback = callback;
        
        if (this._config) {
            this._config.onClick = callback;
        }
    }

    /**
     * 获取按钮文本
     */
    public getText(): string {
        return this.label ? this.label.string : '';
    }

    /**
     * 获取按钮启用状态
     */
    public isEnabled(): boolean {
        return this.button ? this.button.interactable : false;
    }

    /**
     * 获取按钮可见性
     */
    public isVisible(): boolean {
        return this.node.active;
    }

    /**
     * 模拟点击
     */
    public simulateClick(): void {
        if (this.button?.interactable) {
            this.onButtonClick();
        }
    }

    /**
     * 重置按钮状态
     */
    public reset(): void {
        this._isHovered = false;
        this.node.setScale(this._originalScale);
        
        // 停止所有动画
        tween(this.node).stop();
    }

    /**
     * 获取配置
     */
    public getConfig(): IUIButtonConfig | null {
        return this._config;
    }
}
