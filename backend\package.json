{"name": "idlegame-backend", "version": "1.0.0", "description": "江湖风放置游戏后端服务", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:ai": "ts-node tests/ai/runServiceTests.ts", "test:integration": "ts-node tests/integration/runIntegrationTests.ts", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["nodejs", "express", "mongodb", "redis", "typescript", "game", "wuxia"], "author": "IdleGame Team", "license": "MIT", "dependencies": {"@cloudbase/node-sdk": "^3.10.1", "@types/ioredis": "^4.28.10", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "helmet": "^7.2.0", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.4", "morgan": "^1.10.0", "redis": "^4.6.0", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.5", "@types/mongoose": "^5.11.96", "@types/morgan": "^1.9.9", "@types/node": "^20.10.0", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "playwright": "^1.54.1", "prettier": "^3.1.0", "puppeteer": "^24.15.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}