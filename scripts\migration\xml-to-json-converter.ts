#!/usr/bin/env ts-node

/**
 * XML转JSON转换工具
 * 将Godot项目中的XML配置文件转换为Cocos Creator可用的JSON格式
 */

import * as fs from 'fs';
import * as path from 'path';
import * as xml2js from 'xml2js';
import { Command } from 'commander';
import chalk from 'chalk';

const program = new Command();

interface ConversionOptions {
    source: string;
    output: string;
    verbose?: boolean;
    validate?: boolean;
    backup?: boolean;
}

interface ConversionResult {
    sourceFile: string;
    outputFile: string;
    success: boolean;
    recordCount: number;
    error?: string;
}

interface ConversionSummary {
    totalFiles: number;
    successCount: number;
    failureCount: number;
    totalRecords: number;
    results: ConversionResult[];
}

program
    .name('xml-to-json-converter')
    .description('Convert Godot XML configuration files to Cocos Creator JSON format')
    .version('1.0.0')
    .option('-s, --source <path>', 'Source directory containing XML files', '../../idlegame/Data')
    .option('-o, --output <path>', 'Output directory for JSON files', '../../assets/resources/config')
    .option('-v, --verbose', 'Enable verbose logging', false)
    .option('--validate', 'Validate converted JSON files', true)
    .option('--backup', 'Create backup of existing files', true)
    .action(async (options: ConversionOptions) => {
        await convertXmlToJson(options);
    });

async function convertXmlToJson(options: ConversionOptions): Promise<void> {
    console.log(chalk.blue('🔄 XML转JSON转换工具'));
    console.log(chalk.gray(`源目录: ${options.source}`));
    console.log(chalk.gray(`输出目录: ${options.output}`));
    console.log('');

    try {
        // 1. 检查源目录
        if (!fs.existsSync(options.source)) {
            throw new Error(`源目录不存在: ${options.source}`);
        }

        // 2. 创建输出目录
        if (!fs.existsSync(options.output)) {
            fs.mkdirSync(options.output, { recursive: true });
            console.log(chalk.green(`✅ 创建输出目录: ${options.output}`));
        }

        // 3. 发现XML文件
        const xmlFiles = findXmlFiles(options.source);
        console.log(chalk.blue(`📁 发现 ${xmlFiles.length} 个XML文件:`));
        xmlFiles.forEach(file => {
            console.log(chalk.gray(`  - ${path.basename(file)}`));
        });
        console.log('');

        // 4. 转换文件
        const results: ConversionResult[] = [];
        for (const xmlFile of xmlFiles) {
            console.log(chalk.blue(`🔄 转换: ${path.basename(xmlFile)}`));
            const result = await convertSingleFile(xmlFile, options);
            results.push(result);
            
            if (result.success) {
                console.log(chalk.green(`✅ 成功: ${result.recordCount} 条记录`));
            } else {
                console.log(chalk.red(`❌ 失败: ${result.error}`));
            }
        }

        // 5. 生成转换报告
        const summary = generateSummary(results);
        displaySummary(summary);

        // 6. 验证转换结果
        if (options.validate) {
            console.log(chalk.blue('\n🔍 验证转换结果...'));
            await validateConvertedFiles(results, options);
        }

        console.log(chalk.green('\n🎉 XML转JSON转换完成!'));

    } catch (error) {
        console.error(chalk.red('\n❌ 转换失败:'), error);
        process.exit(1);
    }
}

function findXmlFiles(sourceDir: string): string[] {
    const xmlFiles: string[] = [];
    
    function scanDirectory(dir: string) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (path.extname(item).toLowerCase() === '.xml') {
                xmlFiles.push(fullPath);
            }
        }
    }
    
    scanDirectory(sourceDir);
    return xmlFiles;
}

async function convertSingleFile(xmlFile: string, options: ConversionOptions): Promise<ConversionResult> {
    const fileName = path.basename(xmlFile, '.xml');
    const outputFile = path.join(options.output, `${fileName}.json`);
    
    try {
        // 1. 读取XML文件
        const xmlContent = fs.readFileSync(xmlFile, 'utf8');
        
        // 2. 解析XML
        const parser = new xml2js.Parser({
            explicitArray: false,
            ignoreAttrs: false,
            mergeAttrs: true
        });
        
        const xmlData = await parser.parseStringPromise(xmlContent);
        
        // 3. 转换数据结构
        const jsonData = transformXmlToJson(xmlData, fileName);
        
        // 4. 备份现有文件
        if (options.backup && fs.existsSync(outputFile)) {
            const backupFile = `${outputFile}.backup.${Date.now()}`;
            fs.copyFileSync(outputFile, backupFile);
            if (options.verbose) {
                console.log(chalk.yellow(`📦 备份: ${path.basename(backupFile)}`));
            }
        }
        
        // 5. 写入JSON文件
        fs.writeFileSync(outputFile, JSON.stringify(jsonData, null, 2), 'utf8');
        
        // 6. 计算记录数
        const recordCount = countRecords(jsonData);
        
        return {
            sourceFile: xmlFile,
            outputFile,
            success: true,
            recordCount
        };
        
    } catch (error) {
        return {
            sourceFile: xmlFile,
            outputFile,
            success: false,
            recordCount: 0,
            error: error.message
        };
    }
}

function transformXmlToJson(xmlData: any, fileName: string): any {
    // 根据文件类型进行特定的转换
    switch (fileName.toLowerCase()) {
        case 'skill':
        case 'skills':
            return transformSkillData(xmlData);
        case 'entities':
            return transformEntityData(xmlData);
        case 'items':
            return transformItemData(xmlData);
        case 'quests':
            return transformQuestData(xmlData);
        case 'level':
        case 'levels':
            return transformLevelData(xmlData);
        case 'reward':
        case 'rewards':
            return transformRewardData(xmlData);
        default:
            return transformGenericData(xmlData);
    }
}

function transformSkillData(xmlData: any): any {
    const skills = [];
    
    // 处理技能数据结构
    if (xmlData.skills && xmlData.skills.skill) {
        const skillArray = Array.isArray(xmlData.skills.skill) ? xmlData.skills.skill : [xmlData.skills.skill];
        
        for (const skill of skillArray) {
            skills.push({
                id: skill.id || skill.$.id,
                name: skill.name || skill.$.name,
                description: skill.description || '',
                manaCost: parseInt(skill.manaCost || skill.mana_cost || '0'),
                castTime: parseFloat(skill.castTime || skill.cast_time || '0'),
                cooldown: parseFloat(skill.cooldown || '0'),
                damageType: skill.damageType || skill.damage_type || 'physical',
                targetType: skill.targetType || skill.target_type || 'enemy',
                baseDamageMultiplier: parseFloat(skill.baseDamageMultiplier || skill.base_damage_multiplier || '1.0'),
                level: parseInt(skill.level || '1'),
                maxLevel: parseInt(skill.maxLevel || skill.max_level || '10'),
                requirements: parseRequirements(skill.requirements),
                effects: parseEffects(skill.effects)
            });
        }
    }
    
    return {
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        skills
    };
}

function transformEntityData(xmlData: any): any {
    const entities = [];
    
    if (xmlData.entities && xmlData.entities.entity) {
        const entityArray = Array.isArray(xmlData.entities.entity) ? xmlData.entities.entity : [xmlData.entities.entity];
        
        for (const entity of entityArray) {
            entities.push({
                id: entity.id || entity.$.id,
                name: entity.name || entity.$.name,
                type: entity.type || 'npc',
                level: parseInt(entity.level || '1'),
                health: parseInt(entity.health || entity.hp || '100'),
                mana: parseInt(entity.mana || entity.mp || '50'),
                attack: parseInt(entity.attack || entity.atk || '10'),
                defense: parseInt(entity.defense || entity.def || '5'),
                speed: parseInt(entity.speed || '10'),
                experience: parseInt(entity.experience || entity.exp || '0'),
                skills: parseSkillList(entity.skills),
                drops: parseDropList(entity.drops)
            });
        }
    }
    
    return {
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        entities
    };
}

function transformItemData(xmlData: any): any {
    const items = [];
    
    if (xmlData.items && xmlData.items.item) {
        const itemArray = Array.isArray(xmlData.items.item) ? xmlData.items.item : [xmlData.items.item];
        
        for (const item of itemArray) {
            items.push({
                id: item.id || item.$.id,
                name: item.name || item.$.name,
                description: item.description || '',
                type: item.type || 'misc',
                rarity: item.rarity || 'common',
                value: parseInt(item.value || '0'),
                stackable: parseBool(item.stackable),
                maxStack: parseInt(item.maxStack || item.max_stack || '1'),
                usable: parseBool(item.usable),
                equipable: parseBool(item.equipable),
                stats: parseItemStats(item.stats),
                requirements: parseRequirements(item.requirements)
            });
        }
    }
    
    return {
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        items
    };
}

function transformQuestData(xmlData: any): any {
    const quests = [];
    
    if (xmlData.quests && xmlData.quests.quest) {
        const questArray = Array.isArray(xmlData.quests.quest) ? xmlData.quests.quest : [xmlData.quests.quest];
        
        for (const quest of questArray) {
            quests.push({
                id: quest.id || quest.$.id,
                name: quest.name || quest.$.name,
                description: quest.description || '',
                type: quest.type || 'main',
                level: parseInt(quest.level || '1'),
                prerequisites: parsePrerequisites(quest.prerequisites),
                objectives: parseObjectives(quest.objectives),
                rewards: parseRewards(quest.rewards)
            });
        }
    }
    
    return {
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        quests
    };
}

function transformLevelData(xmlData: any): any {
    const levels = [];

    if (xmlData.levels && xmlData.levels.level) {
        const levelArray = Array.isArray(xmlData.levels.level) ? xmlData.levels.level : [xmlData.levels.level];

        for (const level of levelArray) {
            levels.push({
                level: parseInt(level.level || level.$.level),
                experienceRequired: parseInt(level.experienceRequired || level.experience_required || '0'),
                healthBonus: parseInt(level.healthBonus || level.health_bonus || '0'),
                manaBonus: parseInt(level.manaBonus || level.mana_bonus || '0'),
                skillPoints: parseInt(level.skillPoints || level.skill_points || '0')
            });
        }
    }

    return {
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        levels
    };
}

function transformRewardData(xmlData: any): any {
    const rewardTables = [];

    if (xmlData.rewards && xmlData.rewards.reward_table) {
        const rewardTableArray = Array.isArray(xmlData.rewards.reward_table) ? xmlData.rewards.reward_table : [xmlData.rewards.reward_table];

        for (const rewardTable of rewardTableArray) {
            const rewards = [];

            if (rewardTable.rewards && rewardTable.rewards.reward) {
                const rewardArray = Array.isArray(rewardTable.rewards.reward) ? rewardTable.rewards.reward : [rewardTable.rewards.reward];

                for (const reward of rewardArray) {
                    rewards.push({
                        type: reward.type || 'item',
                        rewardId: reward.reward_id || reward.id,
                        quantity: parseInt(reward.quantity || '1'),
                        minQuantity: parseInt(reward.min_quantity || reward.quantity || '1'),
                        maxQuantity: parseInt(reward.max_quantity || reward.quantity || '1'),
                        probability: parseFloat(reward.probability || '1.0'),
                        weight: parseInt(reward.weight || '1'),
                        levelRequirement: parseInt(reward.level_requirement || '0') || undefined
                    });
                }
            }

            rewardTables.push({
                id: rewardTable.id || rewardTable.$.id,
                name: rewardTable.name || rewardTable.$.name,
                description: rewardTable.description || '',
                type: rewardTable.type || 'random',
                allowDuplicates: parseBool(rewardTable.allow_duplicates),
                maxRewards: parseInt(rewardTable.max_rewards || '1'),
                minRewards: parseInt(rewardTable.min_rewards || '1'),
                rewards
            });
        }
    }

    return {
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        rewardTables
    };
}

function transformGenericData(xmlData: any): any {
    // 通用转换逻辑
    return {
        version: '1.0.0',
        lastUpdated: new Date().toISOString(),
        data: xmlData
    };
}

// 辅助解析函数
function parseRequirements(requirements: any): any {
    if (!requirements) return {};
    
    return {
        level: parseInt(requirements.level || '0'),
        skills: parseSkillRequirements(requirements.skills),
        items: parseItemRequirements(requirements.items)
    };
}

function parseEffects(effects: any): any[] {
    if (!effects) return [];
    
    const effectArray = Array.isArray(effects.effect) ? effects.effect : [effects.effect];
    return effectArray.map(effect => ({
        type: effect.type || 'damage',
        value: parseFloat(effect.value || '0'),
        duration: parseFloat(effect.duration || '0')
    }));
}

function parseSkillList(skills: any): string[] {
    if (!skills) return [];
    
    if (typeof skills === 'string') {
        return skills.split(',').map(s => s.trim());
    }
    
    if (Array.isArray(skills.skill)) {
        return skills.skill.map(s => s.id || s);
    }
    
    return [];
}

function parseDropList(drops: any): any[] {
    if (!drops) return [];
    
    const dropArray = Array.isArray(drops.drop) ? drops.drop : [drops.drop];
    return dropArray.map(drop => ({
        itemId: drop.itemId || drop.item_id,
        chance: parseFloat(drop.chance || '0'),
        quantity: parseInt(drop.quantity || '1')
    }));
}

function parseItemStats(stats: any): any {
    if (!stats) return {};
    
    return {
        attack: parseInt(stats.attack || '0'),
        defense: parseInt(stats.defense || '0'),
        health: parseInt(stats.health || '0'),
        mana: parseInt(stats.mana || '0'),
        speed: parseInt(stats.speed || '0')
    };
}

function parsePrerequisites(prerequisites: any): string[] {
    if (!prerequisites) return [];
    
    if (typeof prerequisites === 'string') {
        return prerequisites.split(',').map(s => s.trim());
    }
    
    return [];
}

function parseObjectives(objectives: any): any[] {
    if (!objectives) return [];
    
    const objArray = Array.isArray(objectives.objective) ? objectives.objective : [objectives.objective];
    return objArray.map(obj => ({
        type: obj.type || 'kill',
        target: obj.target,
        quantity: parseInt(obj.quantity || '1'),
        description: obj.description || ''
    }));
}

function parseRewards(rewards: any): any {
    if (!rewards) return {};
    
    return {
        experience: parseInt(rewards.experience || '0'),
        gold: parseInt(rewards.gold || '0'),
        items: parseItemRewards(rewards.items)
    };
}

function parseItemRewards(items: any): any[] {
    if (!items) return [];
    
    const itemArray = Array.isArray(items.item) ? items.item : [items.item];
    return itemArray.map(item => ({
        itemId: item.itemId || item.item_id,
        quantity: parseInt(item.quantity || '1')
    }));
}

function parseSkillRequirements(skills: any): any[] {
    if (!skills) return [];
    
    const skillArray = Array.isArray(skills.skill) ? skills.skill : [skills.skill];
    return skillArray.map(skill => ({
        skillId: skill.skillId || skill.skill_id,
        level: parseInt(skill.level || '1')
    }));
}

function parseItemRequirements(items: any): any[] {
    if (!items) return [];
    
    const itemArray = Array.isArray(items.item) ? items.item : [items.item];
    return itemArray.map(item => ({
        itemId: item.itemId || item.item_id,
        quantity: parseInt(item.quantity || '1')
    }));
}

function parseBool(value: any): boolean {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'string') {
        return value.toLowerCase() === 'true' || value === '1';
    }
    return false;
}

function countRecords(data: any): number {
    if (data.skills) return data.skills.length;
    if (data.entities) return data.entities.length;
    if (data.items) return data.items.length;
    if (data.quests) return data.quests.length;
    if (data.levels) return data.levels.length;
    if (data.rewardTables) return data.rewardTables.length;
    return 0;
}

function generateSummary(results: ConversionResult[]): ConversionSummary {
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.length - successCount;
    const totalRecords = results.reduce((sum, r) => sum + r.recordCount, 0);
    
    return {
        totalFiles: results.length,
        successCount,
        failureCount,
        totalRecords,
        results
    };
}

function displaySummary(summary: ConversionSummary): void {
    console.log(chalk.blue('\n📊 转换摘要:'));
    console.log(chalk.gray(`  总文件数: ${summary.totalFiles}`));
    console.log(chalk.green(`  成功: ${summary.successCount}`));
    console.log(chalk.red(`  失败: ${summary.failureCount}`));
    console.log(chalk.gray(`  总记录数: ${summary.totalRecords}`));
    
    if (summary.failureCount > 0) {
        console.log(chalk.red('\n❌ 失败的文件:'));
        summary.results.filter(r => !r.success).forEach(result => {
            console.log(chalk.red(`  - ${path.basename(result.sourceFile)}: ${result.error}`));
        });
    }
}

async function validateConvertedFiles(results: ConversionResult[], options: ConversionOptions): Promise<void> {
    let validCount = 0;
    
    for (const result of results.filter(r => r.success)) {
        try {
            const content = fs.readFileSync(result.outputFile, 'utf8');
            JSON.parse(content); // 验证JSON格式
            validCount++;
            
            if (options.verbose) {
                console.log(chalk.green(`✅ ${path.basename(result.outputFile)} - JSON格式有效`));
            }
        } catch (error) {
            console.log(chalk.red(`❌ ${path.basename(result.outputFile)} - JSON格式无效: ${error.message}`));
        }
    }
    
    console.log(chalk.green(`✅ 验证完成: ${validCount}/${results.filter(r => r.success).length} 文件有效`));
}

// 如果直接运行此文件
if (require.main === module) {
    program.parse();
}
