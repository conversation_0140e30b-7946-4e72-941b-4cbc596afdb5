import request from 'supertest';
import { app } from '../../src/app';
import { Logger } from '../../src/utils/logger';
import { CacheManager } from '../../src/utils/cache';

/**
 * 性能基准测试
 * 
 * 功能：
 * - API响应时间基准测试
 * - 缓存性能测试
 * - 并发处理能力测试
 * - 内存使用监控
 */

describe('性能基准测试', () => {
  let cacheManager: CacheManager;

  beforeAll(async () => {
    process.env['NODE_ENV'] = 'test';
    cacheManager = CacheManager.getInstance();
    Logger.info('开始性能基准测试');
  });

  afterAll(async () => {
    Logger.info('性能基准测试完成');
  });

  describe('API响应时间基准测试', () => {
    const performanceThresholds = {
      health: 100, // 100ms
      auth: 500, // 500ms
      crud: 300, // 300ms
      search: 1000, // 1s
    };

    it('健康检查API性能基准', async () => {
      const iterations = 100;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        
        await request(app)
          .get('/health')
          .expect(200);
        
        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      }

      const stats = calculateStats(responseTimes);
      
      Logger.info('健康检查API性能统计', stats);
      
      expect(stats.average).toBeLessThan(performanceThresholds.health);
      expect(stats.p95).toBeLessThan(performanceThresholds.health * 2);
      expect(stats.p99).toBeLessThan(performanceThresholds.health * 3);
    });

    it('认证API性能基准', async () => {
      const testUser = {
        username: 'perftest_' + Date.now(),
        email: 'perftest_' + Date.now() + '@example.com',
        password: 'TestPassword123!',
        confirmPassword: 'TestPassword123!',
      };

      // 先注册用户
      await request(app)
        .post('/api/v1/auth/register')
        .send(testUser)
        .expect(201);

      const iterations = 50;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        
        await request(app)
          .post('/api/v1/auth/login')
          .send({
            username: testUser.username,
            password: testUser.password,
          })
          .expect(200);
        
        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      }

      const stats = calculateStats(responseTimes);
      
      Logger.info('认证API性能统计', stats);
      
      expect(stats.average).toBeLessThan(performanceThresholds.auth);
      expect(stats.p95).toBeLessThan(performanceThresholds.auth * 2);
    });

    it('CRUD操作性能基准', async () => {
      // 这里应该测试实际的CRUD操作
      // 由于没有实际的数据库，我们模拟测试
      const iterations = 30;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const startTime = Date.now();
        
        // 模拟CRUD操作
        await request(app)
          .get('/health/detailed')
          .expect(200);
        
        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      }

      const stats = calculateStats(responseTimes);
      
      Logger.info('CRUD操作性能统计', stats);
      
      expect(stats.average).toBeLessThan(performanceThresholds.crud);
    });
  });

  describe('缓存性能测试', () => {
    it('缓存写入性能基准', async () => {
      const iterations = 1000;
      const startTime = Date.now();

      for (let i = 0; i < iterations; i++) {
        await cacheManager.set(`perf_test_${i}`, `value_${i}`, 60);
      }

      const totalTime = Date.now() - startTime;
      const avgTime = totalTime / iterations;

      Logger.info('缓存写入性能统计', {
        iterations,
        totalTime,
        avgTime,
        opsPerSecond: 1000 / avgTime,
      });

      expect(avgTime).toBeLessThan(10); // 平均10ms以内
    });

    it('缓存读取性能基准', async () => {
      // 先写入测试数据
      const testKeys = Array.from({ length: 1000 }, (_, i) => `perf_read_${i}`);
      
      for (const key of testKeys) {
        await cacheManager.set(key, `value_${key}`, 60);
      }

      const iterations = 1000;
      const startTime = Date.now();

      for (let i = 0; i < iterations; i++) {
        const key = testKeys[i % testKeys.length];
        await cacheManager.get(key);
      }

      const totalTime = Date.now() - startTime;
      const avgTime = totalTime / iterations;

      Logger.info('缓存读取性能统计', {
        iterations,
        totalTime,
        avgTime,
        opsPerSecond: 1000 / avgTime,
      });

      expect(avgTime).toBeLessThan(5); // 平均5ms以内
    });

    it('缓存批量操作性能基准', async () => {
      const batchSize = 100;
      const iterations = 10;
      const responseTimes: number[] = [];

      for (let i = 0; i < iterations; i++) {
        const keys = Array.from({ length: batchSize }, (_, j) => `batch_${i}_${j}`);
        const values = keys.map(key => `value_${key}`);

        const startTime = Date.now();
        
        // 批量设置
        await cacheManager.mset(keys.map((key, index) => [key, values[index]]));
        
        // 批量获取
        await cacheManager.mget(keys);

        const responseTime = Date.now() - startTime;
        responseTimes.push(responseTime);
      }

      const stats = calculateStats(responseTimes);
      
      Logger.info('缓存批量操作性能统计', {
        batchSize,
        iterations,
        ...stats,
      });

      expect(stats.average).toBeLessThan(100); // 平均100ms以内
    });
  });

  describe('并发处理能力测试', () => {
    it('并发API请求处理能力', async () => {
      const concurrency = 50;
      const requestsPerClient = 10;
      
      const startTime = Date.now();
      
      const promises = Array.from({ length: concurrency }, async () => {
        const clientResponseTimes: number[] = [];
        
        for (let i = 0; i < requestsPerClient; i++) {
          const requestStart = Date.now();
          
          await request(app)
            .get('/health')
            .expect(200);
          
          clientResponseTimes.push(Date.now() - requestStart);
        }
        
        return clientResponseTimes;
      });

      const results = await Promise.all(promises);
      const allResponseTimes = results.flat();
      const totalTime = Date.now() - startTime;
      
      const stats = calculateStats(allResponseTimes);
      
      Logger.info('并发API请求性能统计', {
        concurrency,
        requestsPerClient,
        totalRequests: concurrency * requestsPerClient,
        totalTime,
        requestsPerSecond: (concurrency * requestsPerClient) / (totalTime / 1000),
        ...stats,
      });

      expect(stats.average).toBeLessThan(500); // 平均500ms以内
      expect(stats.p99).toBeLessThan(2000); // 99%请求2秒以内
    });

    it('并发缓存操作处理能力', async () => {
      const concurrency = 20;
      const operationsPerClient = 50;
      
      const startTime = Date.now();
      
      const promises = Array.from({ length: concurrency }, async (_, clientId) => {
        const clientResponseTimes: number[] = [];
        
        for (let i = 0; i < operationsPerClient; i++) {
          const operationStart = Date.now();
          
          const key = `concurrent_${clientId}_${i}`;
          const value = `value_${clientId}_${i}`;
          
          await cacheManager.set(key, value, 60);
          await cacheManager.get(key);
          
          clientResponseTimes.push(Date.now() - operationStart);
        }
        
        return clientResponseTimes;
      });

      const results = await Promise.all(promises);
      const allResponseTimes = results.flat();
      const totalTime = Date.now() - startTime;
      
      const stats = calculateStats(allResponseTimes);
      
      Logger.info('并发缓存操作性能统计', {
        concurrency,
        operationsPerClient,
        totalOperations: concurrency * operationsPerClient,
        totalTime,
        operationsPerSecond: (concurrency * operationsPerClient) / (totalTime / 1000),
        ...stats,
      });

      expect(stats.average).toBeLessThan(50); // 平均50ms以内
    });
  });

  describe('内存使用监控测试', () => {
    it('API请求内存使用监控', async () => {
      const initialMemory = process.memoryUsage();
      
      // 执行大量API请求
      const promises = Array.from({ length: 100 }, () =>
        request(app)
          .get('/health/detailed')
          .expect(200)
      );

      await Promise.all(promises);
      
      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage();
      
      const memoryDelta = {
        rss: finalMemory.rss - initialMemory.rss,
        heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
        heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
        external: finalMemory.external - initialMemory.external,
      };
      
      Logger.info('API请求内存使用统计', {
        initial: initialMemory,
        final: finalMemory,
        delta: memoryDelta,
      });

      // 内存增长应该在合理范围内
      expect(memoryDelta.heapUsed).toBeLessThan(50 * 1024 * 1024); // 50MB
    });

    it('缓存操作内存使用监控', async () => {
      const initialMemory = process.memoryUsage();
      
      // 执行大量缓存操作
      const operations = 1000;
      for (let i = 0; i < operations; i++) {
        await cacheManager.set(`memory_test_${i}`, `value_${i}`, 60);
      }
      
      const finalMemory = process.memoryUsage();
      
      const memoryDelta = {
        rss: finalMemory.rss - initialMemory.rss,
        heapUsed: finalMemory.heapUsed - initialMemory.heapUsed,
        heapTotal: finalMemory.heapTotal - initialMemory.heapTotal,
      };
      
      Logger.info('缓存操作内存使用统计', {
        operations,
        initial: initialMemory,
        final: finalMemory,
        delta: memoryDelta,
      });

      // 内存增长应该在合理范围内
      expect(memoryDelta.heapUsed).toBeLessThan(100 * 1024 * 1024); // 100MB
    });
  });
});

/**
 * 计算统计信息
 */
function calculateStats(values: number[]): {
  min: number;
  max: number;
  average: number;
  median: number;
  p95: number;
  p99: number;
  stdDev: number;
} {
  const sorted = [...values].sort((a, b) => a - b);
  const length = sorted.length;
  
  const min = sorted[0];
  const max = sorted[length - 1];
  const average = values.reduce((sum, val) => sum + val, 0) / length;
  const median = length % 2 === 0 
    ? (sorted[length / 2 - 1] + sorted[length / 2]) / 2
    : sorted[Math.floor(length / 2)];
  
  const p95Index = Math.ceil(0.95 * length) - 1;
  const p99Index = Math.ceil(0.99 * length) - 1;
  const p95 = sorted[p95Index];
  const p99 = sorted[p99Index];
  
  const variance = values.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / length;
  const stdDev = Math.sqrt(variance);
  
  return {
    min,
    max,
    average: Math.round(average * 100) / 100,
    median,
    p95,
    p99,
    stdDev: Math.round(stdDev * 100) / 100,
  };
}
