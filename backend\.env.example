# 服务器配置
NODE_ENV=development
PORT=3000
HOST=localhost

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/idlegame_dev
MONGODB_TEST_URI=mongodb://localhost:27017/idlegame_test

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key_here
JWT_REFRESH_EXPIRES_IN=30d

# 加密配置
BCRYPT_ROUNDS=12

# 日志配置
LOG_LEVEL=debug
LOG_FILE_PATH=logs/app.log

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 抖音小程序配置
DOUYIN_APP_ID=your_douyin_app_id
DOUYIN_APP_SECRET=your_douyin_app_secret

# 游戏配置
GAME_VERSION=1.0.0
MAX_PLAYERS_PER_ROOM=100
IDLE_TIMEOUT=300000

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 安全配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:8080
