{"version": 3, "file": "TestTemplateRegistry.js", "sourceRoot": "", "sources": ["../../templates/TestTemplateRegistry.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AASH,MAAa,oBAAoB;IAK7B;QAJQ,cAAS,GAA8B,IAAI,GAAG,EAAE,CAAC;QACjD,oBAAe,GAAgC,IAAI,GAAG,EAAE,CAAC;QACzD,0BAAqB,GAAgC,IAAI,GAAG,EAAE,CAAC;QAGnE,IAAI,CAAC,0BAA0B,EAAE,CAAC;QAClC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACI,wBAAwB,CAAC,UAAsB;QAClD,OAAO,CAAC,GAAG,CAAC,8BAA8B,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,IAAI,GAAG,CAAC,CAAC;QAElF,MAAM,mBAAmB,GAAmB,EAAE,CAAC;QAE/C,cAAc;QACd,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;QACtE,mBAAmB,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;QAE3C,aAAa;QACb,MAAM,mBAAmB,GAAG,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;QACxF,KAAK,MAAM,QAAQ,IAAI,mBAAmB,EAAE,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;gBACvD,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvC,CAAC;QACL,CAAC;QAED,iBAAiB;QACjB,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAC5D,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,UAAU,CAAC,CAClD,CAAC;QAEF,YAAY;QACZ,MAAM,oBAAoB,GAAG,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;QAErF,OAAO,CAAC,GAAG,CAAC,cAAc,oBAAoB,CAAC,MAAM,YAAY,CAAC,CAAC;QACnE,OAAO,oBAAoB,CAAC;IAChC,CAAC;IAED;;OAEG;IACI,gBAAgB,CAAC,QAAsB;QAC1C,OAAO,CAAC,GAAG,CAAC,4BAA4B,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QAEzD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC1C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE7B,OAAO,CAAC,GAAG,CAAC,0BAA0B,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,UAAkB;QACjC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACI,eAAe;QAClB,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACI,kBAAkB,CAAC,UAAkB;QACxC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,eAAe,CAAC,KAAa;QAChC,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QACvC,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CACzD,QAAQ,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC;YACvD,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAC7E,CAAC;IACN,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,wBAAwB,CAAC,UAAsB;QACxD,OAAO,CAAC,GAAG,CAAC,oCAAoC,UAAU,CAAC,IAAI,EAAE,CAAC,CAAC;QAEnE,MAAM,UAAU,GAAG,UAAU,UAAU,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC;QAC7D,MAAM,QAAQ,GAAiB;YAC3B,EAAE,EAAE,UAAU;YACd,IAAI,EAAE,GAAG,UAAU,CAAC,IAAI,SAAS;YACjC,WAAW,EAAE,KAAK,UAAU,CAAC,IAAI,cAAc;YAC/C,WAAW,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC;YAC9B,UAAU,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC;YACnC,SAAS,EAAE,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC;YAC1C,QAAQ,EAAE,MAAM,IAAI,CAAC,0BAA0B,CAAC,UAAU,CAAC;YAC3D,QAAQ,EAAE;gBACN,MAAM,EAAE,cAAc;gBACtB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,IAAI,EAAE,CAAC,gBAAgB,EAAE,UAAU,CAAC,IAAI,CAAC;gBACzC,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACZ;SACJ,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QAChC,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED,OAAO;IAEP;;OAEG;IACK,0BAA0B;QAC9B,aAAa;QACb,IAAI,CAAC,gBAAgB,CAAC;YAClB,EAAE,EAAE,sBAAsB;YAC1B,IAAI,EAAE,UAAU;YAChB,WAAW,EAAE,oBAAoB;YACjC,WAAW,EAAE,CAAC,gBAAgB,EAAE,YAAY,CAAC;YAC7C,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;YAChC,SAAS,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC;YAClC,QAAQ,EAAE;gBACN,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmC3B;gBACY,cAAc,EAAE;;;;;;;;;;;EAW9B;gBACc,eAAe,EAAE;oBACb,kBAAkB;oBAClB,qBAAqB;oBACrB,mBAAmB;oBACnB,gBAAgB;iBACnB;gBACD,OAAO,EAAE;oBACL,gFAAgF;oBAChF,+DAA+D;oBAC/D,6EAA6E;iBAChF;aACJ;YACD,QAAQ,EAAE;gBACN,MAAM,EAAE,sBAAsB;gBAC9B,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,IAAI,EAAE,CAAC,gBAAgB,EAAE,OAAO,EAAE,WAAW,CAAC;gBAC9C,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACZ;SACJ,CAAC,CAAC;QAEH,YAAY;QACZ,IAAI,CAAC,gBAAgB,CAAC;YAClB,EAAE,EAAE,uBAAuB;YAC3B,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,qCAAqC;YAClD,WAAW,EAAE,CAAC,YAAY,EAAE,eAAe,EAAE,cAAc,CAAC;YAC5D,UAAU,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;YACjC,SAAS,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC;YACxC,QAAQ,EAAE;gBACN,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA2C3B;gBACY,cAAc,EAAE;;;;;;;;;;;;;;;EAe9B;gBACc,eAAe,EAAE;oBACb,qBAAqB;oBACrB,oBAAoB;oBACpB,yBAAyB;oBACzB,yBAAyB;iBAC5B;gBACD,OAAO,EAAE;oBACL,yDAAyD;oBACzD,+FAA+F;iBAClG;aACJ;YACD,QAAQ,EAAE;gBACN,MAAM,EAAE,gBAAgB;gBACxB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,IAAI,EAAE,CAAC,WAAW,EAAE,aAAa,EAAE,WAAW,CAAC;gBAC/C,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACZ;SACJ,CAAC,CAAC;QAEH,UAAU;QACV,IAAI,CAAC,gBAAgB,CAAC;YAClB,EAAE,EAAE,aAAa;YACjB,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,wBAAwB;YACrC,WAAW,EAAE,CAAC,aAAa,EAAE,iBAAiB,CAAC;YAC/C,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;YAC3C,SAAS,EAAE,CAAC,KAAK,EAAE,aAAa,CAAC;YACjC,QAAQ,EAAE;gBACN,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA+C3B;gBACY,eAAe,EAAE;oBACb,wBAAwB;oBACxB,4BAA4B;oBAC5B,sBAAsB;oBACtB,gBAAgB;iBACnB;gBACD,OAAO,EAAE;oBACL,8EAA8E;oBAC9E,kCAAkC;oBAClC,mDAAmD;iBACtD;aACJ;YACD,QAAQ,EAAE;gBACN,MAAM,EAAE,UAAU;gBAClB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,IAAI,EAAE,CAAC,KAAK,EAAE,aAAa,EAAE,MAAM,CAAC;gBACpC,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACZ;SACJ,CAAC,CAAC;QAEH,WAAW;QACX,IAAI,CAAC,gBAAgB,CAAC;YAClB,EAAE,EAAE,cAAc;YAClB,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,iBAAiB;YAC9B,WAAW,EAAE,CAAC,cAAc,EAAE,YAAY,CAAC;YAC3C,UAAU,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;YACjC,SAAS,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,YAAY,CAAC;YAChD,QAAQ,EAAE;gBACN,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAmD3B;gBACY,eAAe,EAAE;oBACb,yBAAyB;oBACzB,mBAAmB;oBACnB,qBAAqB;oBACrB,mBAAmB;iBACtB;gBACD,OAAO,EAAE;oBACL,2DAA2D;oBAC3D,gDAAgD;oBAChD,gEAAgE;iBACnE;aACJ;YACD,QAAQ,EAAE;gBACN,MAAM,EAAE,WAAW;gBACnB,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,IAAI,EAAE,CAAC,OAAO,EAAE,YAAY,EAAE,KAAK,CAAC;gBACpC,KAAK,EAAE,CAAC;gBACR,MAAM,EAAE,CAAC;aACZ;SACJ,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,YAAY;QAChB,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC7C,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,QAAsB;QACxC,UAAU;QACV,KAAK,MAAM,UAAU,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;gBACxC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YAC7C,CAAC;YACD,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzD,CAAC;QAED,SAAS;QACT,KAAK,MAAM,UAAU,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC9C,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;YACnD,CAAC;YACD,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC/D,CAAC;IACL,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,QAAsB,EAAE,UAAsB;QACvE,WAAW;QACX,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC;YAClD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,UAAU;QACV,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YACvD,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,iBAAiB;QACjB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,SAAyB,EAAE,UAAsB;QACzE,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;YAC3B,QAAQ;YACR,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBAC1C,OAAO,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC;YACjD,CAAC;YAED,UAAU;YACV,IAAI,CAAC,CAAC,QAAQ,CAAC,KAAK,KAAK,CAAC,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;gBACxC,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC;YAC/C,CAAC;YAED,UAAU;YACV,OAAO,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QAC3E,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,UAAsB;QACzC,MAAM,SAAS,GAAG,CAAC,MAAM,CAAC,CAAC;QAE3B,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAClC,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;QACzC,CAAC;QAED,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;YACjC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACnC,CAAC;QAED,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9G,SAAS,CAAC,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,UAAU,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;YACtC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;QAC5C,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,0BAA0B,CAAC,UAAsB;QAC3D,sBAAsB;QACtB,OAAO;QACP,OAAO;YACH,aAAa,EAAE;YACf,UAAU,CAAC,IAAI;;;;;IAKvB;YACQ,eAAe,EAAE,CAAC,kBAAkB,CAAC;YACrC,OAAO,EAAE,CAAC,yDAAyD,CAAC;SACvE,CAAC;IACN,CAAC;CACJ;AA1jBD,oDA0jBC"}