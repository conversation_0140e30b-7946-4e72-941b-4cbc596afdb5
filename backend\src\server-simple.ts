import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(helmet());
app.use(cors());
app.use(compression());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 健康检查端点
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '1.0.0'
    });
});

// 基础API路由
app.get('/api', (req, res) => {
    res.json({
        message: 'IdleGame API Server',
        version: '1.0.0',
        endpoints: [
            'GET /api/health - 健康检查',
            'GET /api/docs - API文档',
            'GET /api/v1/users - 用户相关API',
            'GET /api/v1/characters - 角色相关API',
            'GET /api/v1/skills - 技能相关API',
            'GET /api/v1/items - 物品相关API'
        ]
    });
});

// 模拟用户API
app.get('/api/v1/users', (req, res) => {
    res.json({
        success: true,
        message: '用户列表获取成功',
        data: [
            { id: '1', username: 'testuser1', level: 10 },
            { id: '2', username: 'testuser2', level: 15 }
        ]
    });
});

// 模拟角色API
app.get('/api/v1/characters', (req, res) => {
    res.json({
        success: true,
        message: '角色列表获取成功',
        data: [
            { id: '1', name: '测试战士', class: 'warrior', level: 12 },
            { id: '2', name: '测试法师', class: 'mage', level: 8 }
        ]
    });
});

// 模拟技能API
app.get('/api/v1/skills', (req, res) => {
    res.json({
        success: true,
        message: '技能列表获取成功',
        data: [
            { id: 'basic_attack', name: '基础攻击', damage: 100 },
            { id: 'warrior_slash', name: '战士斩击', damage: 150 },
            { id: 'mage_fireball', name: '火球术', damage: 200 }
        ]
    });
});

// 模拟物品API
app.get('/api/v1/items', (req, res) => {
    res.json({
        success: true,
        message: '物品列表获取成功',
        data: [
            { id: 'health_potion', name: '生命药水', effect: '恢复50HP' },
            { id: 'mana_potion', name: '法力药水', effect: '恢复30MP' },
            { id: 'iron_sword', name: '铁剑', damage: 15 }
        ]
    });
});

// API文档端点
app.get('/api/docs', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>IdleGame API 文档</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                h1 { color: #333; }
                .endpoint { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
                .method { color: #007bff; font-weight: bold; }
            </style>
        </head>
        <body>
            <h1>IdleGame API 文档</h1>
            <p>欢迎使用 IdleGame API！以下是可用的端点：</p>
            
            <div class="endpoint">
                <span class="method">GET</span> /api/health - 健康检查
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span> /api/v1/users - 获取用户列表
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span> /api/v1/characters - 获取角色列表
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span> /api/v1/skills - 获取技能列表
            </div>
            
            <div class="endpoint">
                <span class="method">GET</span> /api/v1/items - 获取物品列表
            </div>
            
            <h2>测试链接</h2>
            <ul>
                <li><a href="/api/health">健康检查</a></li>
                <li><a href="/api/v1/users">用户列表</a></li>
                <li><a href="/api/v1/characters">角色列表</a></li>
                <li><a href="/api/v1/skills">技能列表</a></li>
                <li><a href="/api/v1/items">物品列表</a></li>
            </ul>
        </body>
        </html>
    `);
});

// 404处理
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: '端点不存在',
        path: req.originalUrl
    });
});

// 错误处理
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
    console.error('Error:', err);
    res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log('🚀 IdleGame API Server 启动成功!');
    console.log(`📍 服务地址: http://localhost:${PORT}`);
    console.log(`🔍 健康检查: http://localhost:${PORT}/api/health`);
    console.log(`📚 API文档: http://localhost:${PORT}/api/docs`);
    console.log(`⏰ 启动时间: ${new Date().toLocaleString()}`);
    console.log('按 Ctrl+C 停止服务');
});

export default app;
