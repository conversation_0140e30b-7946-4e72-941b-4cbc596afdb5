import mongoose, { Document, Schema } from 'mongoose';

/**
 * 实体类型枚举（与前端IEntityData.ts保持一致）
 */
export enum EntityType {
  PLAYER = 'player',
  NPC = 'npc',
  ENEMY = 'enemy',
  BOSS = 'boss',
  PET = 'pet',
  SUMMON = 'summon',
}

/**
 * 角色职业枚举
 */
export enum CharacterClass {
  WARRIOR = 'warrior',
  MAGE = 'mage',
  ARCHER = 'archer',
  ASSASSIN = 'assassin',
}

/**
 * 操作类型枚举（基于Godot entities.xml）
 */
export enum OperationType {
  MELEE = 'melee',
  RANGED = 'ranged',
  MAGIC = 'magic',
}

/**
 * 伤害类型枚举
 */
export enum DamageType {
  PHYSICAL = 'physical',
  MAGICAL = 'magical',
  FIRE = 'fire',
  ICE = 'ice',
  LIGHTNING = 'lightning',
  POISON = 'poison',
}

/**
 * 角色属性接口（基于Godot entities.xml结构）
 */
export interface ICharacterAttributes {
  // 基础属性
  strength: number;      // 力量 - 影响物理攻击力和生命值
  agility: number;       // 敏捷 - 影响攻击速度和闪避
  intelligence: number;  // 智力 - 影响法术攻击力和法力值
  vitality: number;      // 体力 - 影响生命值和防御力
  spirit: number;        // 精神 - 影响法力值和法术抗性
  
  // 战斗属性（基于Godot entities.xml）
  damage: number;                    // 基础伤害
  maxHp: number;                     // 最大生命值
  maxMp: number;                     // 最大法力值
  currentHp: number;                 // 当前生命值
  currentMp: number;                 // 当前法力值
  recoverHp: number;                 // 生命恢复率
  recoverMp: number;                 // 法力恢复率
  recoveryInterval: number;          // 恢复间隔
  atkInterval: number;               // 攻击间隔
  atkSpeed: number;                  // 攻击速度
  castSpeed: number;                 // 施法速度
  def: number;                       // 物理防御
  spellResistance: number;           // 法术抗性
  
  // 命中和闪避
  meleeAccuracy: number;             // 近战命中率
  rangedAccuracy: number;            // 远程命中率
  magicAccuracy: number;             // 法术命中率
  meleeDodge: number;                // 近战闪避率
  rangedDodge: number;               // 远程闪避率
  magicDodge: number;                // 法术闪避率
  
  // 特殊属性
  penetrate: number;                 // 穿透
  criticalHit: number;               // 暴击率
  toughness: number;                 // 韧性
  enmity: number;                    // 仇恨值
  damageIncreasePhysical: number;    // 物理伤害增加
}

/**
 * 装备槽位接口
 */
export interface IEquipmentSlots {
  weapon?: mongoose.Types.ObjectId;     // 武器
  armor?: mongoose.Types.ObjectId;      // 护甲
  helmet?: mongoose.Types.ObjectId;     // 头盔
  boots?: mongoose.Types.ObjectId;      // 靴子
  gloves?: mongoose.Types.ObjectId;     // 手套
  accessory1?: mongoose.Types.ObjectId; // 饰品1
  accessory2?: mongoose.Types.ObjectId; // 饰品2
}

/**
 * 角色接口
 */
export interface ICharacter extends Document {
  _id: mongoose.Types.ObjectId;
  userId: mongoose.Types.ObjectId;
  name: string;
  class: CharacterClass;
  operationType: OperationType;
  damageType: DamageType;
  level: number;
  experience: number;
  
  // 角色属性
  attributes: ICharacterAttributes;
  
  // 技能相关
  skills: mongoose.Types.ObjectId[];    // 已学习的技能
  skillPoints: number;                  // 可用技能点
  
  // 装备
  equipment: IEquipmentSlots;
  
  // 背包
  inventory: {
    items: Array<{
      itemId: mongoose.Types.ObjectId;
      quantity: number;
      slot: number;
    }>;
    maxSlots: number;
  };
  
  // 位置和状态
  location: {
    mapId: string;
    x: number;
    y: number;
    z: number;
  };
  
  // 战斗状态
  battleStatus: {
    isInBattle: boolean;
    battleId?: mongoose.Types.ObjectId;
    statusEffects: Array<{
      effectId: string;
      duration: number;
      startTime: Date;
    }>;
  };
  
  // 统计数据
  statistics: {
    totalPlayTime: number;
    monstersKilled: number;
    itemsCollected: number;
    questsCompleted: number;
    deathCount: number;
    goldEarned: number;
  };
  
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // 方法
  calculateTotalAttributes(): ICharacterAttributes;
  canLevelUp(): boolean;
  levelUp(): Promise<void>;
  addExperience(amount: number): Promise<boolean>;
  equipItem(itemId: mongoose.Types.ObjectId, slot: string): Promise<boolean>;
  unequipItem(slot: string): Promise<boolean>;
  addToInventory(itemId: mongoose.Types.ObjectId, quantity: number): Promise<boolean>;
  removeFromInventory(itemId: mongoose.Types.ObjectId, quantity: number): Promise<boolean>;
  heal(amount: number): void;
  restoreMana(amount: number): void;
  takeDamage(amount: number): boolean;
  isAlive(): boolean;
}

/**
 * 角色Schema
 */
const CharacterSchema = new Schema<ICharacter>({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, '用户ID是必填项'],
    index: true,
  },
  name: {
    type: String,
    required: [true, '角色名称是必填项'],
    trim: true,
    minlength: [2, '角色名称至少2个字符'],
    maxlength: [20, '角色名称最多20个字符'],
    match: [/^[\u4e00-\u9fa5a-zA-Z0-9_\s]+$/, '角色名称只能包含中文、字母、数字、下划线和空格'],
  },
  class: {
    type: String,
    enum: Object.values(CharacterClass),
    required: [true, '角色职业是必填项'],
    index: true,
  },
  operationType: {
    type: String,
    enum: Object.values(OperationType),
    required: [true, '操作类型是必填项'],
  },
  damageType: {
    type: String,
    enum: Object.values(DamageType),
    required: [true, '伤害类型是必填项'],
  },
  level: {
    type: Number,
    default: 1,
    min: [1, '等级不能小于1'],
    max: [100, '等级不能超过100'],
    index: true,
  },
  experience: {
    type: Number,
    default: 0,
    min: [0, '经验值不能为负数'],
  },
  
  // 角色属性（基于Godot entities.xml结构）
  attributes: {
    // 基础属性
    strength: { type: Number, default: 10, min: 1 },
    agility: { type: Number, default: 10, min: 1 },
    intelligence: { type: Number, default: 10, min: 1 },
    vitality: { type: Number, default: 10, min: 1 },
    spirit: { type: Number, default: 10, min: 1 },
    
    // 战斗属性
    damage: { type: Number, default: 10, min: 1 },
    maxHp: { type: Number, default: 100, min: 1 },
    maxMp: { type: Number, default: 50, min: 1 },
    currentHp: { type: Number, default: 100, min: 0 },
    currentMp: { type: Number, default: 50, min: 0 },
    recoverHp: { type: Number, default: 0.02, min: 0 },
    recoverMp: { type: Number, default: 0.02, min: 0 },
    recoveryInterval: { type: Number, default: 1.0, min: 0.1 },
    atkInterval: { type: Number, default: 1.5, min: 0.1 },
    atkSpeed: { type: Number, default: 1.0, min: 0.1 },
    castSpeed: { type: Number, default: 1.0, min: 0.1 },
    def: { type: Number, default: 5, min: 0 },
    spellResistance: { type: Number, default: 5, min: 0 },
    
    // 命中和闪避
    meleeAccuracy: { type: Number, default: 0.1, min: 0, max: 1 },
    rangedAccuracy: { type: Number, default: 0.05, min: 0, max: 1 },
    magicAccuracy: { type: Number, default: 0.0, min: 0, max: 1 },
    meleeDodge: { type: Number, default: 0.05, min: 0, max: 1 },
    rangedDodge: { type: Number, default: 0.0, min: 0, max: 1 },
    magicDodge: { type: Number, default: 0.0, min: 0, max: 1 },
    
    // 特殊属性
    penetrate: { type: Number, default: 0.1, min: 0, max: 1 },
    criticalHit: { type: Number, default: 0.1, min: 0, max: 1 },
    toughness: { type: Number, default: 0.2, min: 0, max: 1 },
    enmity: { type: Number, default: 0.5, min: 0 },
    damageIncreasePhysical: { type: Number, default: 0.2, min: 0 },
  },
  
  // 技能
  skills: [{
    type: Schema.Types.ObjectId,
    ref: 'Skill',
  }],
  skillPoints: {
    type: Number,
    default: 0,
    min: [0, '技能点不能为负数'],
  },
  
  // 装备
  equipment: {
    weapon: { type: Schema.Types.ObjectId, ref: 'Item' },
    armor: { type: Schema.Types.ObjectId, ref: 'Item' },
    helmet: { type: Schema.Types.ObjectId, ref: 'Item' },
    boots: { type: Schema.Types.ObjectId, ref: 'Item' },
    gloves: { type: Schema.Types.ObjectId, ref: 'Item' },
    accessory1: { type: Schema.Types.ObjectId, ref: 'Item' },
    accessory2: { type: Schema.Types.ObjectId, ref: 'Item' },
  },
  
  // 背包
  inventory: {
    items: [{
      itemId: { type: Schema.Types.ObjectId, ref: 'Item', required: true },
      quantity: { type: Number, required: true, min: 1 },
      slot: { type: Number, required: true, min: 0 },
    }],
    maxSlots: {
      type: Number,
      default: 30,
      min: [10, '背包槽位不能少于10个'],
      max: [100, '背包槽位不能超过100个'],
    },
  },
  
  // 位置
  location: {
    mapId: { type: String, default: 'starter_area' },
    x: { type: Number, default: 0 },
    y: { type: Number, default: 0 },
    z: { type: Number, default: 0 },
  },
  
  // 战斗状态
  battleStatus: {
    isInBattle: { type: Boolean, default: false },
    battleId: { type: Schema.Types.ObjectId, ref: 'Battle' },
    statusEffects: [{
      effectId: { type: String, required: true },
      duration: { type: Number, required: true, min: 0 },
      startTime: { type: Date, required: true },
    }],
  },
  
  // 统计数据
  statistics: {
    totalPlayTime: { type: Number, default: 0, min: 0 },
    monstersKilled: { type: Number, default: 0, min: 0 },
    itemsCollected: { type: Number, default: 0, min: 0 },
    questsCompleted: { type: Number, default: 0, min: 0 },
    deathCount: { type: Number, default: 0, min: 0 },
    goldEarned: { type: Number, default: 0, min: 0 },
  },
  
  isActive: {
    type: Boolean,
    default: true,
    index: true,
  },
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true },
});

// 索引
CharacterSchema.index({ userId: 1, isActive: 1 });
CharacterSchema.index({ name: 1 }, { unique: true });
CharacterSchema.index({ class: 1 });
CharacterSchema.index({ level: -1 });
CharacterSchema.index({ 'location.mapId': 1 });
CharacterSchema.index({ 'battleStatus.isInBattle': 1 });

// 虚拟字段
CharacterSchema.virtual('hpPercentage').get(function() {
  return (this.attributes.currentHp / this.attributes.maxHp * 100).toFixed(1);
});

CharacterSchema.virtual('mpPercentage').get(function() {
  return (this.attributes.currentMp / this.attributes.maxMp * 100).toFixed(1);
});

CharacterSchema.virtual('experienceToNextLevel').get(function() {
  const expRequired = this.level * 100; // 简单的经验计算公式
  return Math.max(0, expRequired - this.experience);
});

// 中间件：保存前验证当前生命值和法力值
CharacterSchema.pre('save', function(next) {
  // 确保当前生命值不超过最大值
  if (this.attributes.currentHp > this.attributes.maxHp) {
    this.attributes.currentHp = this.attributes.maxHp;
  }
  
  // 确保当前法力值不超过最大值
  if (this.attributes.currentMp > this.attributes.maxMp) {
    this.attributes.currentMp = this.attributes.maxMp;
  }
  
  next();
});

// 实例方法：计算总属性（包括装备加成）
CharacterSchema.methods.calculateTotalAttributes = function(): ICharacterAttributes {
  // 这里应该计算装备加成，暂时返回基础属性
  return { ...this.attributes };
};

// 实例方法：检查是否可以升级
CharacterSchema.methods.canLevelUp = function(): boolean {
  const expRequired = this.level * 100; // 简单的经验计算公式
  return this.experience >= expRequired;
};

// 实例方法：升级
CharacterSchema.methods.levelUp = async function(): Promise<void> {
  if (!this.canLevelUp()) {
    throw new Error('经验不足，无法升级');
  }
  
  const expRequired = this.level * 100;
  this.experience -= expRequired;
  this.level += 1;
  this.skillPoints += 1;
  
  // 升级时恢复生命值和法力值
  this.attributes.currentHp = this.attributes.maxHp;
  this.attributes.currentMp = this.attributes.maxMp;
  
  await this.save();
};

// 实例方法：添加经验值
CharacterSchema.methods.addExperience = async function(amount: number): Promise<boolean> {
  this.experience += amount;
  
  let leveledUp = false;
  while (this.canLevelUp()) {
    await this.levelUp();
    leveledUp = true;
  }
  
  if (!leveledUp) {
    await this.save();
  }
  
  return leveledUp;
};

// 实例方法：治疗
CharacterSchema.methods.heal = function(amount: number): void {
  this.attributes.currentHp = Math.min(
    this.attributes.maxHp,
    this.attributes.currentHp + amount
  );
};

// 实例方法：恢复法力
CharacterSchema.methods.restoreMana = function(amount: number): void {
  this.attributes.currentMp = Math.min(
    this.attributes.maxMp,
    this.attributes.currentMp + amount
  );
};

// 实例方法：受到伤害
CharacterSchema.methods.takeDamage = function(amount: number): boolean {
  this.attributes.currentHp = Math.max(0, this.attributes.currentHp - amount);
  return this.attributes.currentHp <= 0;
};

// 实例方法：检查是否存活
CharacterSchema.methods.isAlive = function(): boolean {
  return this.attributes.currentHp > 0;
};

export const Character = mongoose.model<ICharacter>('Character', CharacterSchema);

/**
 * 根据职业获取默认属性
 */
export function getDefaultAttributesByClass(characterClass: CharacterClass): Partial<ICharacterAttributes> {
  const baseAttributes = {
    strength: 10,
    agility: 10,
    intelligence: 10,
    vitality: 10,
    spirit: 10,
  };

  switch (characterClass) {
    case CharacterClass.WARRIOR:
      return {
        ...baseAttributes,
        strength: 15,
        vitality: 15,
        agility: 8,
        intelligence: 5,
        spirit: 7,
        operationType: OperationType.MELEE,
        damageType: DamageType.PHYSICAL,
        damage: 60,
        maxHp: 200,
        maxMp: 50,
        currentHp: 200,
        currentMp: 50,
        def: 15,
        spellResistance: 5,
        meleeAccuracy: 0.1,
        rangedAccuracy: 0.05,
        magicAccuracy: 0.0,
        meleeDodge: 0.05,
        rangedDodge: 0.0,
        magicDodge: 0.0,
        penetrate: 0.1,
        criticalHit: 0.1,
        toughness: 0.2,
        enmity: 0.5,
        damageIncreasePhysical: 0.2,
      };

    case CharacterClass.MAGE:
      return {
        ...baseAttributes,
        intelligence: 15,
        spirit: 15,
        strength: 5,
        vitality: 8,
        agility: 7,
        operationType: OperationType.MAGIC,
        damageType: DamageType.MAGICAL,
        damage: 25,
        maxHp: 120,
        maxMp: 150,
        currentHp: 120,
        currentMp: 150,
        def: 5,
        spellResistance: 15,
        meleeAccuracy: 0.0,
        rangedAccuracy: 0.0,
        magicAccuracy: 0.15,
        meleeDodge: 0.0,
        rangedDodge: 0.05,
        magicDodge: 0.1,
        penetrate: 0.05,
        criticalHit: 0.15,
        toughness: 0.1,
        enmity: 0.3,
        damageIncreasePhysical: 0.0,
      };

    case CharacterClass.ARCHER:
      return {
        ...baseAttributes,
        agility: 15,
        strength: 12,
        intelligence: 8,
        vitality: 10,
        spirit: 5,
        operationType: OperationType.RANGED,
        damageType: DamageType.PHYSICAL,
        damage: 45,
        maxHp: 150,
        maxMp: 80,
        currentHp: 150,
        currentMp: 80,
        def: 8,
        spellResistance: 8,
        meleeAccuracy: 0.05,
        rangedAccuracy: 0.15,
        magicAccuracy: 0.0,
        meleeDodge: 0.1,
        rangedDodge: 0.1,
        magicDodge: 0.05,
        penetrate: 0.15,
        criticalHit: 0.2,
        toughness: 0.15,
        enmity: 0.4,
        damageIncreasePhysical: 0.15,
      };

    case CharacterClass.ASSASSIN:
      return {
        ...baseAttributes,
        agility: 18,
        strength: 12,
        intelligence: 8,
        vitality: 7,
        spirit: 5,
        operationType: OperationType.MELEE,
        damageType: DamageType.PHYSICAL,
        damage: 55,
        maxHp: 130,
        maxMp: 70,
        currentHp: 130,
        currentMp: 70,
        def: 6,
        spellResistance: 6,
        meleeAccuracy: 0.15,
        rangedAccuracy: 0.1,
        magicAccuracy: 0.0,
        meleeDodge: 0.15,
        rangedDodge: 0.1,
        magicDodge: 0.05,
        penetrate: 0.2,
        criticalHit: 0.25,
        toughness: 0.1,
        enmity: 0.6,
        damageIncreasePhysical: 0.25,
      };

    default:
      return baseAttributes;
  }
}
