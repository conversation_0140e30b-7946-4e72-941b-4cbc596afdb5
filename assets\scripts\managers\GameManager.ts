import { _decorator, Component, Node, director, game } from 'cc';
import { BaseManager } from './BaseManager';
import { GameState, GameConfig } from './types/ManagerTypes';

const { ccclass, property } = _decorator;

/**
 * 游戏主管理器
 * 负责游戏的整体状态管理和生命周期控制
 */
@ccclass('GameManager')
export class GameManager extends BaseManager {
    
    /**
     * 当前游戏状态
     */
    private _currentState: GameState = GameState.UNINITIALIZED;
    
    /**
     * 游戏配置
     */
    private _config: GameConfig = {
        version: '1.0.0',
        name: '江湖风放置游戏',
        defaultScene: 'Launch',
        enableAudio: true,
        enableNetwork: true,
        debug: true,
        autoInit: true,
        initTimeout: 10000
    };
    
    /**
     * 游戏启动时间
     */
    private _startTime: number = 0;
    
    /**
     * 游戏暂停时间
     */
    private _pauseTime: number = 0;
    
    /**
     * 游戏运行时间
     */
    private _runTime: number = 0;

    /**
     * 获取GameManager单例实例
     */
    public static getInstance(): GameManager {
        return super.getInstance.call(this) as GameManager;
    }

    /**
     * 初始化游戏管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('🎮 初始化游戏管理器...');
        
        try {
            // 设置游戏状态为初始化中
            this.setState(GameState.INITIALIZING);
            
            // 初始化游戏配置
            await this.initializeConfig();
            
            // 注册游戏事件监听器
            this.registerGameEvents();
            
            // 设置游戏状态为准备中
            this.setState(GameState.PREPARING);
            
            // 记录启动时间
            this._startTime = Date.now();
            
            console.log('✅ 游戏管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 游戏管理器初始化失败:', error);
            this.setState(GameState.ERROR);
            throw error;
        }
    }

    /**
     * 销毁游戏管理器
     */
    public destroyManager(): void {
        console.log('🗑️ 销毁游戏管理器...');
        
        // 移除游戏事件监听器
        this.unregisterGameEvents();
        
        // 重置状态
        this._currentState = GameState.UNINITIALIZED;
        this._startTime = 0;
        this._pauseTime = 0;
        this._runTime = 0;
        
        console.log('✅ 游戏管理器销毁完成');
    }

    /**
     * 初始化游戏配置
     */
    private async initializeConfig(): Promise<void> {
        console.log('⚙️ 初始化游戏配置...');
        
        // 这里可以从本地存储或服务器加载配置
        // 暂时使用默认配置
        
        console.log('📋 游戏配置:', this._config);
    }

    /**
     * 注册游戏事件监听器
     */
    private registerGameEvents(): void {
        console.log('📡 注册游戏事件监听器...');
        
        // 监听游戏暂停事件
        game.on(game.EVENT_PAUSE, this.onGamePause, this);
        
        // 监听游戏恢复事件
        game.on(game.EVENT_RESUME, this.onGameResume, this);
        
        // 监听游戏隐藏事件
        game.on(game.EVENT_HIDE, this.onGameHide, this);
        
        // 监听游戏显示事件
        game.on(game.EVENT_SHOW, this.onGameShow, this);
    }

    /**
     * 移除游戏事件监听器
     */
    private unregisterGameEvents(): void {
        console.log('📡 移除游戏事件监听器...');
        
        game.off(game.EVENT_PAUSE, this.onGamePause, this);
        game.off(game.EVENT_RESUME, this.onGameResume, this);
        game.off(game.EVENT_HIDE, this.onGameHide, this);
        game.off(game.EVENT_SHOW, this.onGameShow, this);
    }

    /**
     * 游戏暂停事件处理
     */
    private onGamePause(): void {
        console.log('⏸️ 游戏暂停');
        this._pauseTime = Date.now();
        this.setState(GameState.PAUSED);
    }

    /**
     * 游戏恢复事件处理
     */
    private onGameResume(): void {
        console.log('▶️ 游戏恢复');
        if (this._pauseTime > 0) {
            this._runTime += Date.now() - this._pauseTime;
            this._pauseTime = 0;
        }
        this.setState(GameState.RUNNING);
    }

    /**
     * 游戏隐藏事件处理
     */
    private onGameHide(): void {
        console.log('👁️ 游戏隐藏');
        // 可以在这里处理游戏隐藏时的逻辑
    }

    /**
     * 游戏显示事件处理
     */
    private onGameShow(): void {
        console.log('👁️ 游戏显示');
        // 可以在这里处理游戏显示时的逻辑
    }

    /**
     * 设置游戏状态
     */
    private setState(newState: GameState): void {
        const oldState = this._currentState;
        this._currentState = newState;
        
        console.log(`🔄 游戏状态变更: ${oldState} → ${newState}`);
        
        // 这里可以触发状态变更事件
        // EventManager.getInstance().emit('gameStateChanged', oldState, newState);
    }

    /**
     * 启动游戏
     */
    public async startGame(): Promise<void> {
        console.log('🚀 启动游戏...');
        
        if (this._currentState !== GameState.PREPARING) {
            console.warn('⚠️ 游戏状态不正确，无法启动');
            return;
        }
        
        try {
            // 设置游戏状态为运行中
            this.setState(GameState.RUNNING);
            
            // 加载默认场景
            await this.loadDefaultScene();
            
            console.log('✅ 游戏启动成功');
            
        } catch (error) {
            console.error('❌ 游戏启动失败:', error);
            this.setState(GameState.ERROR);
            throw error;
        }
    }

    /**
     * 加载默认场景
     */
    private async loadDefaultScene(): Promise<void> {
        console.log(`🎬 加载默认场景: ${this._config.defaultScene}`);
        
        return new Promise<void>((resolve, reject) => {
            director.loadScene(this._config.defaultScene, (error) => {
                if (error) {
                    console.error('❌ 默认场景加载失败:', error);
                    reject(error);
                } else {
                    console.log('✅ 默认场景加载成功');
                    resolve();
                }
            });
        });
    }

    /**
     * 暂停游戏
     */
    public pauseGame(): void {
        console.log('⏸️ 暂停游戏');
        if (this._currentState === GameState.RUNNING) {
            this.setState(GameState.PAUSED);
            game.pause();
        }
    }

    /**
     * 恢复游戏
     */
    public resumeGame(): void {
        console.log('▶️ 恢复游戏');
        if (this._currentState === GameState.PAUSED) {
            this.setState(GameState.RUNNING);
            game.resume();
        }
    }

    /**
     * 结束游戏
     */
    public endGame(): void {
        console.log('🏁 结束游戏');
        this.setState(GameState.ENDED);
        // 这里可以添加游戏结束的清理逻辑
    }

    /**
     * 重启游戏
     */
    public async restartGame(): Promise<void> {
        console.log('🔄 重启游戏');
        this.endGame();
        await this.reinitialize();
        await this.startGame();
    }

    /**
     * 获取当前游戏状态
     */
    public getCurrentState(): GameState {
        return this._currentState;
    }

    /**
     * 获取游戏配置
     */
    public getConfig(): GameConfig {
        return { ...this._config };
    }

    /**
     * 更新游戏配置
     */
    public updateConfig(config: Partial<GameConfig>): void {
        this._config = { ...this._config, ...config };
        console.log('⚙️ 游戏配置已更新:', config);
    }

    /**
     * 获取游戏运行时间(毫秒)
     */
    public getRunTime(): number {
        if (this._currentState === GameState.RUNNING) {
            return this._runTime + (Date.now() - this._startTime);
        }
        return this._runTime;
    }

    /**
     * 获取游戏状态信息
     */
    public getGameInfo(): {
        state: GameState;
        config: GameConfig;
        startTime: number;
        runTime: number;
        isRunning: boolean;
    } {
        return {
            state: this._currentState,
            config: this.getConfig(),
            startTime: this._startTime,
            runTime: this.getRunTime(),
            isRunning: this._currentState === GameState.RUNNING
        };
    }
}
