# AI驱动的自动化测试框架

> 🤖 专为武侠放置游戏Godot到Cocos Creator迁移项目设计的智能测试系统

## 🎯 项目概述

这是一个基于AI技术的自动化测试框架，专门为武侠放置游戏从Godot 4.4迁移到Cocos Creator 3.8.6项目设计。框架采用智能测试机器人架构，能够自动发现系统、生成测试用例、执行测试并验证算法一致性。

### 核心特性

- 🔍 **智能系统发现**: 自动识别项目中的各种游戏系统
- 🤖 **动态测试机器人**: 根据系统特征自动创建适配的测试机器人
- 📋 **模板化测试生成**: 基于预定义模板自动生成测试用例
- ⚖️ **算法一致性验证**: 对比Godot和Cocos Creator版本的算法实现
- 📊 **智能报告生成**: 生成详细的测试报告和分析

## 🏗️ 架构设计

```
AI测试框架
├── 🧠 控制层
│   ├── MasterTestBot (主测试机器人)
│   ├── TestBotFactory (测试机器人工厂)
│   └── TestReportGenerator (报告生成器)
├── 🤖 执行层
│   ├── GenericTestBot (通用测试机器人)
│   └── TestTemplateRegistry (测试模板注册表)
├── 🔍 分析层
│   ├── SystemDiscoveryAgent (系统发现代理)
│   ├── CodeAnalysisAgent (代码分析代理)
│   └── TestGenerationAgent (测试生成代理)
└── 📋 模板层
    ├── 业务逻辑测试模板
    ├── 算法一致性验证模板
    ├── API接口测试模板
    └── 武侠系统专用模板
```

## 🚀 快速开始

### 安装依赖

```bash
cd scripts/ai-testing
npm install
```

### 构建项目

```bash
npm run build
```

### 基础使用

```bash
# 1. 设置AI测试系统
npm run ai-test:setup

# 2. 发现系统并生成测试
npm run ai-test:discover-and-test

# 3. 验证算法一致性
npm run ai-test:validate-algorithms

# 4. 生成测试报告
npm run ai-test:report

# 5. 查看统计信息
npm run ai-test:stats
```

### 演示模式

```bash
# 运行完整演示
npm run demo:full

# 或分步执行
npm run demo:setup      # 设置演示环境
npm run demo:discover   # 发现系统
npm run demo:validate   # 验证算法
```

## 📋 命令行接口

### 主要命令

```bash
# 设置AI测试系统
ai-test setup [--project <path>]

# 发现系统并生成测试
ai-test discover [--project <path>]

# 验证算法一致性
ai-test validate [--system <path>]

# 生成测试报告
ai-test report

# 显示统计信息
ai-test stats

# 清理缓存
ai-test clean
```

### 使用示例

```bash
# 为特定项目设置测试
ai-test setup --project /path/to/your/project

# 发现并测试所有系统
ai-test discover --project /path/to/your/project

# 验证特定系统的算法
ai-test validate --system /path/to/wuxia/system

# 查看详细帮助
ai-test --help
```

## 🔧 配置说明

### 项目配置

框架会自动检测项目结构，但你也可以通过配置文件自定义行为：

```json
{
  "projectRoot": "./",
  "outputDir": "./test-results/ai-tests",
  "logLevel": "info",
  "testTemplates": {
    "enabled": ["business-logic", "algorithm-consistency", "api-testing"],
    "customTemplatesPath": "./custom-templates"
  },
  "systemDiscovery": {
    "ignorePatterns": ["node_modules", "dist", ".git"],
    "includePatterns": ["src/**/*.ts", "scripts/**/*.ts"]
  }
}
```

### 测试模板配置

你可以创建自定义测试模板：

```typescript
// custom-templates/my-template.ts
export const myCustomTemplate: TestTemplate = {
  id: 'my-custom-template',
  name: '我的自定义模板',
  systemTypes: ['my-system-type'],
  complexity: ['medium'],
  testTypes: ['unit', 'integration'],
  template: {
    testStructure: `
      describe('{{systemName}} Tests', () => {
        // 自定义测试结构
      });
    `,
    validationRules: ['custom-validation']
  }
};
```

## 🎮 武侠游戏专用功能

### 武侠系统测试

框架内置了专门针对武侠游戏系统的测试模板：

```typescript
// 门派系统测试
describe('门派系统', () => {
  test('should allow player to join sect', async () => {
    const result = await wuxiaSystem.joinSect(playerId, sectId);
    expect(result.success).toBe(true);
  });
});

// 修炼系统测试
describe('修炼系统', () => {
  test('should increase cultivation experience', async () => {
    await wuxiaSystem.practice(playerId, 'qi', 60);
    expect(player.cultivation.experience).toBeGreaterThan(initialExp);
  });
});
```

### 算法一致性验证

专门用于验证Godot和Cocos Creator版本的算法一致性：

```typescript
// 战斗伤害计算验证
test('damage calculation consistency', async () => {
  const testCases = generateDamageTestCases();
  
  for (const testCase of testCases) {
    const godotResult = await executeGodotDamageCalculation(testCase);
    const cocosResult = await executeCocosDamageCalculation(testCase);
    
    expect(cocosResult).toBeCloseTo(godotResult, 5); // 精度到小数点后5位
  }
});
```

## 📊 报告和分析

### 测试报告

框架会生成多种格式的测试报告：

- **JSON报告**: 机器可读的详细测试数据
- **HTML报告**: 人类友好的可视化报告
- **控制台输出**: 实时的测试进度和结果

### 算法验证报告

专门的算法一致性验证报告：

```json
{
  "timestamp": "2025-07-22T14:00:00.000Z",
  "summary": {
    "totalSystems": 5,
    "consistentSystems": 4,
    "inconsistentSystems": 1,
    "averageDeviation": 0.0023
  },
  "results": [
    {
      "system": "battle-system",
      "isConsistent": true,
      "deviationPercentage": 0.001,
      "testedAlgorithms": ["damageCalculation", "hitChance", "criticalHit"]
    }
  ]
}
```

## 🔍 系统发现机制

### 自动发现规则

框架使用多种策略自动发现项目中的系统：

1. **目录结构分析**: 基于常见的目录命名模式
2. **文件模式匹配**: 基于文件名和内容模式
3. **依赖关系分析**: 基于模块间的依赖关系
4. **语义分析**: 使用AI分析代码语义

### 支持的系统类型

- `user-management`: 用户管理系统
- `game-logic`: 游戏逻辑系统
- `battle-system`: 战斗系统
- `wuxia-system`: 武侠系统
- `ui-system`: UI界面系统
- `api-service`: API服务
- `data-management`: 数据管理系统
- `social-system`: 社交系统

## 🧪 测试模板系统

### 内置模板

1. **基础业务逻辑测试**: 适用于一般业务逻辑的单元测试
2. **算法一致性验证**: 专门用于迁移项目的算法对比
3. **API接口测试**: RESTful API的标准测试
4. **武侠系统测试**: 专门针对武侠游戏特性的测试

### 自定义模板

你可以创建和注册自己的测试模板：

```typescript
const customTemplate = await templateRegistry.createTemplateFromSystem(systemInfo);
templateRegistry.registerTemplate(customTemplate);
```

## 🚨 故障排除

### 常见问题

1. **系统发现失败**
   ```bash
   # 检查项目路径是否正确
   ai-test discover --project /correct/path/to/project
   ```

2. **测试生成失败**
   ```bash
   # 清理缓存后重试
   ai-test clean
   ai-test setup
   ```

3. **算法验证不一致**
   ```bash
   # 查看详细的验证报告
   ai-test validate --system /path/to/specific/system
   ```

### 调试模式

```bash
# 启用详细日志
DEBUG=ai-test:* npm run ai-test:discover

# 或使用开发模式
npm run dev discover --project ./
```

## 🤝 贡献指南

### 开发环境设置

```bash
git clone <repository>
cd scripts/ai-testing
npm install
npm run build
```

### 添加新的测试模板

1. 在 `templates/` 目录下创建新模板文件
2. 在 `TestTemplateRegistry.ts` 中注册模板
3. 添加相应的测试用例
4. 更新文档

### 添加新的系统类型支持

1. 在 `SystemDiscoveryAgent.ts` 中添加识别规则
2. 在 `GenericTestBot.ts` 中添加相应的能力
3. 创建专用的测试模板
4. 更新类型定义

## 📄 许可证

MIT License - 详见 [LICENSE](./LICENSE) 文件

## 🙏 致谢

感谢所有为武侠放置游戏迁移项目做出贡献的开发者们！

---

> 📖 **相关文档**: [迁移流程规范](../../rules/migration-process.md) | [项目开发计划](../../plans/README.md)
