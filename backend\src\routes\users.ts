import { Router } from 'express';
import { userController } from '../controllers/UserController';
import { validate, ValidationSchemas } from '../middleware/validation';
import { auth } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     UserRegister:
 *       type: object
 *       required:
 *         - username
 *         - email
 *         - password
 *         - confirmPassword
 *       properties:
 *         username:
 *           type: string
 *           minLength: 3
 *           maxLength: 20
 *           pattern: '^[a-zA-Z0-9_]+$'
 *           description: 用户名，只能包含字母、数字和下划线
 *           example: player123
 *         email:
 *           type: string
 *           format: email
 *           description: 邮箱地址
 *           example: <EMAIL>
 *         password:
 *           type: string
 *           minLength: 6
 *           description: 密码，至少6个字符
 *           example: password123
 *         confirmPassword:
 *           type: string
 *           description: 确认密码
 *           example: password123
 * 
 *     UserLogin:
 *       type: object
 *       required:
 *         - username
 *         - password
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名或邮箱
 *           example: player123
 *         password:
 *           type: string
 *           description: 密码
 *           example: password123
 * 
 *     UserProfile:
 *       type: object
 *       properties:
 *         nickname:
 *           type: string
 *           maxLength: 30
 *           description: 昵称
 *           example: 玩家昵称
 *         bio:
 *           type: string
 *           maxLength: 200
 *           description: 个人简介
 *           example: 这是我的个人简介
 *         preferences:
 *           type: object
 *           properties:
 *             language:
 *               type: string
 *               enum: [zh-CN, en-US]
 *               description: 语言设置
 *               example: zh-CN
 *             timezone:
 *               type: string
 *               description: 时区设置
 *               example: Asia/Shanghai
 *             notifications:
 *               type: object
 *               properties:
 *                 email:
 *                   type: boolean
 *                   description: 邮件通知
 *                 push:
 *                   type: boolean
 *                   description: 推送通知
 *                 inGame:
 *                   type: boolean
 *                   description: 游戏内通知
 *             privacy:
 *               type: object
 *               properties:
 *                 showOnlineStatus:
 *                   type: boolean
 *                   description: 显示在线状态
 *                 allowFriendRequests:
 *                   type: boolean
 *                   description: 允许好友请求
 *                 showProfile:
 *                   type: boolean
 *                   description: 显示个人资料
 * 
 *     ChangePassword:
 *       type: object
 *       required:
 *         - currentPassword
 *         - newPassword
 *         - confirmPassword
 *       properties:
 *         currentPassword:
 *           type: string
 *           description: 当前密码
 *           example: oldpassword123
 *         newPassword:
 *           type: string
 *           minLength: 6
 *           description: 新密码
 *           example: newpassword123
 *         confirmPassword:
 *           type: string
 *           description: 确认新密码
 *           example: newpassword123
 * 
 *     PasswordReset:
 *       type: object
 *       required:
 *         - email
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: 注册邮箱
 *           example: <EMAIL>
 * 
 *     ResetPassword:
 *       type: object
 *       required:
 *         - token
 *         - newPassword
 *         - confirmPassword
 *       properties:
 *         token:
 *           type: string
 *           description: 重置令牌
 *           example: abc123def456
 *         newPassword:
 *           type: string
 *           minLength: 6
 *           description: 新密码
 *           example: newpassword123
 *         confirmPassword:
 *           type: string
 *           description: 确认新密码
 *           example: newpassword123
 * 
 *     EmailVerification:
 *       type: object
 *       required:
 *         - token
 *       properties:
 *         token:
 *           type: string
 *           description: 验证令牌
 *           example: abc123def456
 */

/**
 * @swagger
 * /api/v1/users/register:
 *   post:
 *     summary: 用户注册
 *     tags: [Users]
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UserRegister'
 *     responses:
 *       201:
 *         description: 注册成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         user:
 *                           $ref: '#/components/schemas/User'
 *                         token:
 *                           type: string
 *                           description: JWT访问令牌
 *                         verificationToken:
 *                           type: string
 *                           description: 邮箱验证令牌
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       409:
 *         description: 用户名或邮箱已存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/register', 
  validate(ValidationSchemas.userRegister),
  asyncHandler(userController.register)
);

/**
 * @swagger
 * /api/v1/users/login:
 *   post:
 *     summary: 用户登录
 *     tags: [Users]
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UserLogin'
 *     responses:
 *       200:
 *         description: 登录成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         user:
 *                           $ref: '#/components/schemas/User'
 *                         token:
 *                           type: string
 *                           description: JWT访问令牌
 *       401:
 *         description: 用户名或密码错误
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       423:
 *         description: 账户已被锁定
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/login',
  validate(ValidationSchemas.userLogin),
  asyncHandler(userController.login)
);

/**
 * @swagger
 * /api/v1/users/profile:
 *   get:
 *     summary: 获取用户资料
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         user:
 *                           $ref: '#/components/schemas/User'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 用户不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/profile',
  auth.required,
  asyncHandler(userController.getProfile)
);

/**
 * @swagger
 * /api/v1/users/profile:
 *   put:
 *     summary: 更新用户资料
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UserProfile'
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         user:
 *                           $ref: '#/components/schemas/User'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.put('/profile',
  auth.required,
  validate(ValidationSchemas.userProfileUpdate),
  asyncHandler(userController.updateProfile)
);

/**
 * @swagger
 * /api/v1/users/change-password:
 *   post:
 *     summary: 修改密码
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ChangePassword'
 *     responses:
 *       200:
 *         description: 密码修改成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.post('/change-password',
  auth.required,
  validate(ValidationSchemas.changePassword),
  asyncHandler(userController.changePassword)
);

/**
 * @swagger
 * /api/v1/users/request-password-reset:
 *   post:
 *     summary: 请求密码重置
 *     tags: [Users]
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PasswordReset'
 *     responses:
 *       200:
 *         description: 重置链接已发送（如果邮箱存在）
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 */
router.post('/request-password-reset',
  validate(ValidationSchemas.passwordReset),
  asyncHandler(userController.requestPasswordReset)
);

/**
 * @swagger
 * /api/v1/users/reset-password:
 *   post:
 *     summary: 重置密码
 *     tags: [Users]
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ResetPassword'
 *     responses:
 *       200:
 *         description: 密码重置成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: 重置令牌无效或已过期
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/reset-password',
  validate(ValidationSchemas.resetPassword),
  asyncHandler(userController.resetPassword)
);

/**
 * @swagger
 * /api/v1/users/verify-email:
 *   post:
 *     summary: 验证邮箱
 *     tags: [Users]
 *     security: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/EmailVerification'
 *     responses:
 *       200:
 *         description: 邮箱验证成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: 验证令牌无效或已过期
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/verify-email',
  validate(ValidationSchemas.emailVerification),
  asyncHandler(userController.verifyEmail)
);

export default router;
