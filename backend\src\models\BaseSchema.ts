import mongoose = require('mongoose');

/**
 * 基础Schema配置
 */
export const baseSchemaOptions: mongoose.SchemaOptions = {
  timestamps: true, // 自动添加createdAt和updatedAt
  versionKey: 'version', // 版本控制字段
  toJSON: {
    virtuals: true,
    transform: function(doc, ret) {
      // 移除敏感字段
      delete ret.__v;
      delete ret.deletedAt;
      
      // 转换_id为id
      ret.id = ret._id;
      delete ret._id;
      
      return ret;
    },
  },
  toObject: {
    virtuals: true,
    transform: function(doc, ret) {
      // 转换_id为id
      ret.id = ret._id;
      delete ret._id;
      delete ret.__v;
      
      return ret;
    },
  },
};

/**
 * 基础Schema字段
 */
export const baseSchemaFields = {
  // 软删除标记
  isDeleted: {
    type: Boolean,
    default: false,
    index: true,
  },
  
  // 软删除时间
  deletedAt: {
    type: Date,
    default: null,
  },
  
  // 版本号（用于乐观锁）
  version: {
    type: Number,
    default: 0,
  },
};

/**
 * 创建基础Schema
 */
export function createBaseSchema(fields: mongoose.SchemaDefinition, options?: mongoose.SchemaOptions): mongoose.Schema {
  // 合并基础字段
  const schemaFields = {
    ...fields,
    ...baseSchemaFields,
  };

  // 合并选项
  const schemaOptions = {
    ...baseSchemaOptions,
    ...options,
  };

  const schema = new mongoose.Schema(schemaFields, schemaOptions);

  // 添加基础索引
  schema.index({ isDeleted: 1, createdAt: -1 });
  schema.index({ isDeleted: 1, updatedAt: -1 });

  // 添加基础中间件
  addBaseMiddleware(schema);

  // 添加基础方法
  addBaseMethods(schema);

  // 添加基础静态方法
  addBaseStatics(schema);

  return schema;
}

/**
 * 添加基础中间件
 */
function addBaseMiddleware(schema: mongoose.Schema): void {
  // 保存前中间件
  schema.pre('save', function(next) {
    // 设置默认值
    if (this.isNew) {
      this.isDeleted = false;
      this.version = 0;
    }
    next();
  });

  // 更新前中间件
  schema.pre(['updateOne', 'updateMany', 'findOneAndUpdate'], function() {
    // 自动更新updatedAt
    this.set({ updatedAt: new Date() });
  });

  // 查询中间件 - 自动过滤软删除的文档
  schema.pre(['find', 'findOne', 'findOneAndUpdate', 'count', 'countDocuments'], function() {
    // 如果没有明确包含已删除的文档，则过滤掉
    if (!this.getQuery().includeDeleted) {
      this.where({ isDeleted: { $ne: true } });
    }
  });
}

/**
 * 添加基础实例方法
 */
function addBaseMethods(schema: mongoose.Schema): void {
  // 软删除方法
  schema.methods.softDelete = function() {
    this.isDeleted = true;
    this.deletedAt = new Date();
    this.updatedAt = new Date();
    this.version += 1;
    return this.save();
  };

  // 恢复软删除方法
  schema.methods.restore = function() {
    this.isDeleted = false;
    this.deletedAt = null;
    this.updatedAt = new Date();
    this.version += 1;
    return this.save();
  };

  // 检查是否已删除
  schema.methods.isDeleted = function() {
    return this.isDeleted === true;
  };

  // 获取文档年龄（天数）
  schema.methods.getAge = function() {
    const now = new Date();
    const created = this.createdAt;
    return Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
  };

  // 获取最后更新时间差（分钟）
  schema.methods.getLastUpdateMinutes = function() {
    const now = new Date();
    const updated = this.updatedAt;
    return Math.floor((now.getTime() - updated.getTime()) / (1000 * 60));
  };
}

/**
 * 添加基础静态方法
 */
function addBaseStatics(schema: mongoose.Schema): void {
  // 查找未删除的文档
  schema.statics.findActive = function(filter = {}) {
    return this.find({ ...filter, isDeleted: { $ne: true } });
  };

  // 查找已删除的文档
  schema.statics.findDeleted = function(filter = {}) {
    return this.find({ ...filter, isDeleted: true });
  };

  // 统计未删除的文档
  schema.statics.countActive = function(filter = {}) {
    return this.countDocuments({ ...filter, isDeleted: { $ne: true } });
  };

  // 统计已删除的文档
  schema.statics.countDeleted = function(filter = {}) {
    return this.countDocuments({ ...filter, isDeleted: true });
  };

  // 批量软删除
  schema.statics.softDeleteMany = function(filter) {
    return this.updateMany(filter, {
      isDeleted: true,
      deletedAt: new Date(),
      updatedAt: new Date(),
      $inc: { version: 1 },
    });
  };

  // 批量恢复
  schema.statics.restoreMany = function(filter) {
    return this.updateMany(
      { ...filter, isDeleted: true },
      {
        isDeleted: false,
        deletedAt: null,
        updatedAt: new Date(),
        $inc: { version: 1 },
      }
    );
  };

  // 清理已删除的文档（硬删除）
  schema.statics.cleanupDeleted = function(olderThanDays = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    
    return this.deleteMany({
      isDeleted: true,
      deletedAt: { $lt: cutoffDate },
    });
  };
}

/**
 * 常用验证器
 */
export const validators = {
  // 邮箱验证
  email: {
    validator: function(email: string) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },
    message: '邮箱格式不正确',
  },

  // 手机号验证（中国）
  phone: {
    validator: function(phone: string) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      return phoneRegex.test(phone);
    },
    message: '手机号格式不正确',
  },

  // 用户名验证
  username: {
    validator: function(username: string) {
      const usernameRegex = /^[a-zA-Z0-9_-]{3,20}$/;
      return usernameRegex.test(username);
    },
    message: '用户名只能包含字母、数字、下划线和连字符，长度3-20位',
  },

  // 密码强度验证
  strongPassword: {
    validator: function(password: string) {
      // 至少8位，包含大小写字母、数字和特殊字符
      const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
      return passwordRegex.test(password);
    },
    message: '密码必须至少8位，包含大小写字母、数字和特殊字符',
  },

  // URL验证
  url: {
    validator: function(url: string) {
      try {
        new URL(url);
        return true;
      } catch {
        return false;
      }
    },
    message: 'URL格式不正确',
  },
};

/**
 * 常用Schema类型
 */
export const schemaTypes = {
  // ObjectId引用
  objectId: (ref: string, required = false) => ({
    type: mongoose.Schema.Types.ObjectId,
    ref,
    required,
    index: true,
  }),

  // 字符串字段
  string: (required = false, minLength?: number, maxLength?: number) => ({
    type: String,
    required,
    trim: true,
    minlength: minLength,
    maxlength: maxLength,
  }),

  // 数字字段
  number: (required = false, min?: number, max?: number) => ({
    type: Number,
    required,
    min,
    max,
  }),

  // 日期字段
  date: (required = false, defaultValue?: Date | (() => Date)) => ({
    type: Date,
    required,
    default: defaultValue,
  }),

  // 枚举字段
  enumField: (values: string[], required = false, defaultValue?: string) => ({
    type: String,
    enum: values,
    required,
    default: defaultValue,
  }),

  // 数组字段
  array: (itemType: any, required = false) => ({
    type: [itemType],
    required,
    default: [],
  }),
};
