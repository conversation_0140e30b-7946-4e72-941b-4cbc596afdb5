/**
 * 增强的UI面板基类
 * 提供完整的面板生命周期、动画系统、数据绑定等功能
 */

import { _decorator, Component, Node, tween, Vec3, UIOpacity, Animation } from 'cc';
import { UIPanelType, UIAnimationType } from '../types/UITypes';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 面板状态枚举
 */
export enum PanelState {
    Closed = 'closed',
    Opening = 'opening',
    Opened = 'opened',
    Closing = 'closing'
}

/**
 * 面板数据绑定接口
 */
export interface IPanelDataBinding {
    /** 数据键 */
    key: string;
    
    /** 目标节点路径 */
    nodePath: string;
    
    /** 绑定类型 */
    bindingType: 'text' | 'sprite' | 'active' | 'progress' | 'color';
    
    /** 格式化函数 */
    formatter?: (value: any) => any;
    
    /** 是否双向绑定 */
    twoWay?: boolean;
}

/**
 * 面板事件接口
 */
export interface IPanelEvent {
    /** 事件名称 */
    eventName: string;
    
    /** 事件数据 */
    eventData?: any;
    
    /** 时间戳 */
    timestamp: number;
}

@ccclass('EnhancedBasePanel')
export abstract class EnhancedBasePanel extends Component {
    
    @property({ tooltip: '面板类型' })
    public panelType: UIPanelType = UIPanelType.Debug;
    
    @property({ tooltip: '显示动画类型' })
    public showAnimationType: UIAnimationType = UIAnimationType.Scale;
    
    @property({ tooltip: '隐藏动画类型' })
    public hideAnimationType: UIAnimationType = UIAnimationType.Scale;
    
    @property({ tooltip: '动画持续时间' })
    public animationDuration: number = 0.3;
    
    @property({ tooltip: '是否模态面板' })
    public isModal: boolean = false;
    
    @property({ tooltip: '是否可以ESC关闭' })
    public escapeToClose: boolean = true;
    
    @property({ tooltip: '是否自动数据绑定' })
    public autoDataBinding: boolean = true;
    
    @property({ type: Node, tooltip: '内容根节点' })
    public contentRoot: Node | null = null;
    
    @property({ type: Node, tooltip: '背景遮罩节点' })
    public backgroundMask: Node | null = null;
    
    // 面板状态
    protected _panelState: PanelState = PanelState.Closed;
    
    // 面板数据
    protected _panelData: any = {};
    
    // 数据绑定配置
    protected _dataBindings: IPanelDataBinding[] = [];
    
    // 事件历史
    protected _eventHistory: IPanelEvent[] = [];
    
    // 动画组件
    protected _animationComponent: Animation | null = null;
    
    // 透明度组件
    protected _opacityComponent: UIOpacity | null = null;

    protected onLoad(): void {
        console.log(`📋 面板加载: ${this.panelType}`);
        
        // 查找组件
        this.findComponents();
        
        // 初始化数据绑定
        if (this.autoDataBinding) {
            this.initializeDataBinding();
        }
        
        // 调用子类初始化
        this.onPanelLoad();
    }

    protected onEnable(): void {
        // 绑定事件
        this.bindEvents();
        
        // 调用子类启用
        this.onPanelEnable();
    }

    protected onDisable(): void {
        // 解绑事件
        this.unbindEvents();
        
        // 调用子类禁用
        this.onPanelDisable();
    }

    protected onDestroy(): void {
        // 清理数据
        this.cleanup();
        
        // 调用子类销毁
        this.onPanelDestroy();
        
        console.log(`📋 面板销毁: ${this.panelType}`);
    }

    /**
     * 查找组件
     */
    private findComponents(): void {
        // 查找内容根节点
        if (!this.contentRoot) {
            this.contentRoot = this.node.getChildByName('Content') || this.node;
        }
        
        // 查找背景遮罩
        if (!this.backgroundMask) {
            this.backgroundMask = this.node.getChildByName('Background') || 
                                 this.node.getChildByName('Mask');
        }
        
        // 查找动画组件
        this._animationComponent = this.node.getComponent(Animation);
        
        // 查找或添加透明度组件
        this._opacityComponent = this.node.getComponent(UIOpacity);
        if (!this._opacityComponent) {
            this._opacityComponent = this.node.addComponent(UIOpacity);
        }
    }

    /**
     * 初始化数据绑定
     */
    private initializeDataBinding(): void {
        // 子类可以重写此方法来配置数据绑定
        this.setupDataBindings();
        
        console.log(`📊 数据绑定初始化完成: ${this._dataBindings.length} 个绑定`);
    }

    /**
     * 绑定事件
     */
    private bindEvents(): void {
        const eventManager = EventManager.getInstance();
        
        // 绑定面板相关事件
        eventManager.on(`panel_${this.panelType}_data_update`, this.onDataUpdate, this);
        eventManager.on(`panel_${this.panelType}_refresh`, this.onRefreshRequest, this);
        
        // 绑定全局事件
        eventManager.on('ui_language_change', this.onLanguageChange, this);
        eventManager.on('ui_theme_change', this.onThemeChange, this);
        
        // 调用子类事件绑定
        this.bindPanelEvents();
    }

    /**
     * 解绑事件
     */
    private unbindEvents(): void {
        const eventManager = EventManager.getInstance();
        
        // 解绑面板相关事件
        eventManager.off(`panel_${this.panelType}_data_update`, this.onDataUpdate, this);
        eventManager.off(`panel_${this.panelType}_refresh`, this.onRefreshRequest, this);
        
        // 解绑全局事件
        eventManager.off('ui_language_change', this.onLanguageChange, this);
        eventManager.off('ui_theme_change', this.onThemeChange, this);
        
        // 调用子类事件解绑
        this.unbindPanelEvents();
    }

    /**
     * 清理资源
     */
    private cleanup(): void {
        // 清理数据
        this._panelData = {};
        this._dataBindings = [];
        this._eventHistory = [];
        
        // 停止所有动画
        this.node.stopAllActions();
    }

    // ==================== 动画系统 ====================

    /**
     * 显示面板
     */
    public async show(data?: any): Promise<void> {
        if (this._panelState === PanelState.Opened || this._panelState === PanelState.Opening) {
            return;
        }

        console.log(`📋 显示面板: ${this.panelType}`);
        
        this._panelState = PanelState.Opening;
        
        // 设置面板数据
        if (data) {
            this.setPanelData(data);
        }
        
        // 调用显示前回调
        await this.onBeforeShow(data);
        
        // 激活节点
        this.node.active = true;
        
        // 执行显示动画
        await this.playShowAnimation();
        
        this._panelState = PanelState.Opened;
        
        // 调用显示后回调
        await this.onAfterShow(data);
        
        // 发送事件
        this.emitPanelEvent('panel_show', { panelType: this.panelType, data });
    }

    /**
     * 隐藏面板
     */
    public async hide(): Promise<void> {
        if (this._panelState === PanelState.Closed || this._panelState === PanelState.Closing) {
            return;
        }

        console.log(`📋 隐藏面板: ${this.panelType}`);
        
        this._panelState = PanelState.Closing;
        
        // 调用隐藏前回调
        await this.onBeforeHide();
        
        // 执行隐藏动画
        await this.playHideAnimation();
        
        // 隐藏节点
        this.node.active = false;
        
        this._panelState = PanelState.Closed;
        
        // 调用隐藏后回调
        await this.onAfterHide();
        
        // 发送事件
        this.emitPanelEvent('panel_hide', { panelType: this.panelType });
    }

    /**
     * 播放显示动画
     */
    private async playShowAnimation(): Promise<void> {
        return new Promise<void>((resolve) => {
            switch (this.showAnimationType) {
                case UIAnimationType.Scale:
                    this.playScaleInAnimation(resolve);
                    break;
                case UIAnimationType.Fade:
                    this.playFadeInAnimation(resolve);
                    break;
                case UIAnimationType.Slide:
                    this.playSlideInAnimation(resolve);
                    break;
                case UIAnimationType.None:
                default:
                    resolve();
                    break;
            }
        });
    }

    /**
     * 播放隐藏动画
     */
    private async playHideAnimation(): Promise<void> {
        return new Promise<void>((resolve) => {
            switch (this.hideAnimationType) {
                case UIAnimationType.Scale:
                    this.playScaleOutAnimation(resolve);
                    break;
                case UIAnimationType.Fade:
                    this.playFadeOutAnimation(resolve);
                    break;
                case UIAnimationType.Slide:
                    this.playSlideOutAnimation(resolve);
                    break;
                case UIAnimationType.None:
                default:
                    resolve();
                    break;
            }
        });
    }

    /**
     * 缩放进入动画
     */
    private playScaleInAnimation(callback: () => void): void {
        const targetNode = this.contentRoot || this.node;
        
        targetNode.setScale(0.5, 0.5, 1);
        
        tween(targetNode)
            .to(this.animationDuration, { scale: new Vec3(1, 1, 1) }, {
                easing: 'backOut'
            })
            .call(callback)
            .start();
    }

    /**
     * 缩放退出动画
     */
    private playScaleOutAnimation(callback: () => void): void {
        const targetNode = this.contentRoot || this.node;
        
        tween(targetNode)
            .to(this.animationDuration, { scale: new Vec3(0.5, 0.5, 1) }, {
                easing: 'backIn'
            })
            .call(callback)
            .start();
    }

    /**
     * 淡入动画
     */
    private playFadeInAnimation(callback: () => void): void {
        if (this._opacityComponent) {
            this._opacityComponent.opacity = 0;
            
            tween(this._opacityComponent)
                .to(this.animationDuration, { opacity: 255 })
                .call(callback)
                .start();
        } else {
            callback();
        }
    }

    /**
     * 淡出动画
     */
    private playFadeOutAnimation(callback: () => void): void {
        if (this._opacityComponent) {
            tween(this._opacityComponent)
                .to(this.animationDuration, { opacity: 0 })
                .call(callback)
                .start();
        } else {
            callback();
        }
    }

    /**
     * 滑入动画
     */
    private playSlideInAnimation(callback: () => void): void {
        const targetNode = this.contentRoot || this.node;
        const originalPos = targetNode.position.clone();
        
        targetNode.setPosition(originalPos.x, originalPos.y - 500, originalPos.z);
        
        tween(targetNode)
            .to(this.animationDuration, { position: originalPos }, {
                easing: 'quartOut'
            })
            .call(callback)
            .start();
    }

    /**
     * 滑出动画
     */
    private playSlideOutAnimation(callback: () => void): void {
        const targetNode = this.contentRoot || this.node;
        const currentPos = targetNode.position.clone();
        
        tween(targetNode)
            .to(this.animationDuration, { 
                position: new Vec3(currentPos.x, currentPos.y - 500, currentPos.z) 
            }, {
                easing: 'quartIn'
            })
            .call(callback)
            .start();
    }

    // ==================== 数据绑定系统 ====================

    /**
     * 设置面板数据
     */
    public setPanelData(data: any): void {
        this._panelData = { ...this._panelData, ...data };
        
        // 应用数据绑定
        this.applyDataBindings();
        
        // 调用子类数据更新
        this.onDataChanged(this._panelData);
    }

    /**
     * 获取面板数据
     */
    public getPanelData(): any {
        return { ...this._panelData };
    }

    /**
     * 应用数据绑定
     */
    private applyDataBindings(): void {
        for (const binding of this._dataBindings) {
            this.applyDataBinding(binding);
        }
    }

    /**
     * 应用单个数据绑定
     */
    private applyDataBinding(binding: IPanelDataBinding): void {
        const value = this._panelData[binding.key];
        if (value === undefined) {
            return;
        }
        
        const targetNode = this.findNodeByPath(binding.nodePath);
        if (!targetNode) {
            console.warn(`数据绑定目标节点未找到: ${binding.nodePath}`);
            return;
        }
        
        const finalValue = binding.formatter ? binding.formatter(value) : value;
        
        switch (binding.bindingType) {
            case 'text':
                this.applyTextBinding(targetNode, finalValue);
                break;
            case 'sprite':
                this.applySpriteBinding(targetNode, finalValue);
                break;
            case 'active':
                targetNode.active = !!finalValue;
                break;
            case 'progress':
                this.applyProgressBinding(targetNode, finalValue);
                break;
            case 'color':
                this.applyColorBinding(targetNode, finalValue);
                break;
        }
    }

    /**
     * 应用文本绑定
     */
    private applyTextBinding(node: Node, value: any): void {
        const label = node.getComponent('cc.Label');
        if (label) {
            label.string = String(value);
        }
    }

    /**
     * 应用精灵绑定
     */
    private applySpriteBinding(node: Node, value: any): void {
        const sprite = node.getComponent('cc.Sprite');
        if (sprite && value) {
            // 这里需要根据实际的资源加载方式来实现
            console.log(`设置精灵: ${value}`);
        }
    }

    /**
     * 应用进度条绑定
     */
    private applyProgressBinding(node: Node, value: any): void {
        const progressBar = node.getComponent('cc.ProgressBar');
        if (progressBar) {
            progressBar.progress = Number(value);
        }
    }

    /**
     * 应用颜色绑定
     */
    private applyColorBinding(node: Node, value: any): void {
        // 这里需要根据实际的颜色设置方式来实现
        console.log(`设置颜色: ${value}`);
    }

    /**
     * 根据路径查找节点
     */
    private findNodeByPath(path: string): Node | null {
        const parts = path.split('/');
        let currentNode = this.node;
        
        for (const part of parts) {
            if (part === '') continue;
            
            currentNode = currentNode.getChildByName(part);
            if (!currentNode) {
                return null;
            }
        }
        
        return currentNode;
    }

    // ==================== 事件系统 ====================

    /**
     * 发送面板事件
     */
    protected emitPanelEvent(eventName: string, eventData?: any): void {
        const event: IPanelEvent = {
            eventName,
            eventData,
            timestamp: Date.now()
        };
        
        this._eventHistory.push(event);
        
        // 限制事件历史大小
        if (this._eventHistory.length > 50) {
            this._eventHistory.shift();
        }
        
        // 发送事件
        const eventManager = EventManager.getInstance();
        eventManager.emit(eventName, eventData);
    }

    /**
     * 数据更新事件处理
     */
    private onDataUpdate(data: any): void {
        this.setPanelData(data);
    }

    /**
     * 刷新请求事件处理
     */
    private onRefreshRequest(): void {
        this.refresh();
    }

    /**
     * 语言变更事件处理
     */
    private onLanguageChange(): void {
        this.onLanguageChanged();
    }

    /**
     * 主题变更事件处理
     */
    private onThemeChange(): void {
        this.onThemeChanged();
    }

    // ==================== 公共API ====================

    /**
     * 刷新面板
     */
    public refresh(): void {
        this.applyDataBindings();
        this.onRefresh();
    }

    /**
     * 获取面板状态
     */
    public getPanelState(): PanelState {
        return this._panelState;
    }

    /**
     * 检查面板是否可见
     */
    public isVisible(): boolean {
        return this.node.active && this._panelState === PanelState.Opened;
    }

    /**
     * 检查面板是否在动画中
     */
    public isAnimating(): boolean {
        return this._panelState === PanelState.Opening || this._panelState === PanelState.Closing;
    }

    /**
     * 获取事件历史
     */
    public getEventHistory(): IPanelEvent[] {
        return [...this._eventHistory];
    }

    /**
     * 处理返回键
     */
    public onBackPressed(): boolean {
        if (this.escapeToClose && this.isVisible()) {
            this.hide();
            return true;
        }
        return false;
    }

    // ==================== 抽象方法（子类实现） ====================

    /**
     * 面板加载时调用
     */
    protected abstract onPanelLoad(): void;

    /**
     * 面板启用时调用
     */
    protected abstract onPanelEnable(): void;

    /**
     * 面板禁用时调用
     */
    protected abstract onPanelDisable(): void;

    /**
     * 面板销毁时调用
     */
    protected abstract onPanelDestroy(): void;

    /**
     * 设置数据绑定配置
     */
    protected abstract setupDataBindings(): void;

    /**
     * 绑定面板特定事件
     */
    protected abstract bindPanelEvents(): void;

    /**
     * 解绑面板特定事件
     */
    protected abstract unbindPanelEvents(): void;

    /**
     * 显示前回调
     */
    protected abstract onBeforeShow(data?: any): Promise<void>;

    /**
     * 显示后回调
     */
    protected abstract onAfterShow(data?: any): Promise<void>;

    /**
     * 隐藏前回调
     */
    protected abstract onBeforeHide(): Promise<void>;

    /**
     * 隐藏后回调
     */
    protected abstract onAfterHide(): Promise<void>;

    /**
     * 数据变更回调
     */
    protected abstract onDataChanged(data: any): void;

    /**
     * 刷新回调
     */
    protected abstract onRefresh(): void;

    /**
     * 语言变更回调
     */
    protected abstract onLanguageChanged(): void;

    /**
     * 主题变更回调
     */
    protected abstract onThemeChanged(): void;
}
