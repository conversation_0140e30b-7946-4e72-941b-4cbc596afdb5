// 手动测试脚本 - 使用JavaScript避免TypeScript编译问题
const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const jsonBody = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: jsonBody
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testAPI() {
  console.log('🧪 开始API手动测试...\n');

  const baseOptions = {
    hostname: 'localhost',
    port: 3000,
    headers: {
      'Content-Type': 'application/json'
    }
  };

  try {
    // 测试健康检查
    console.log('1. 测试健康检查...');
    const healthResponse = await makeRequest({
      ...baseOptions,
      path: '/health',
      method: 'GET'
    });
    console.log(`   状态码: ${healthResponse.statusCode}`);
    console.log(`   响应: ${JSON.stringify(healthResponse.body, null, 2)}\n`);

    // 测试根路由
    console.log('2. 测试根路由...');
    const rootResponse = await makeRequest({
      ...baseOptions,
      path: '/',
      method: 'GET'
    });
    console.log(`   状态码: ${rootResponse.statusCode}`);
    console.log(`   响应: ${JSON.stringify(rootResponse.body, null, 2)}\n`);

    // 测试API信息
    console.log('3. 测试API信息...');
    const apiInfoResponse = await makeRequest({
      ...baseOptions,
      path: '/api/info',
      method: 'GET'
    });
    console.log(`   状态码: ${apiInfoResponse.statusCode}`);
    console.log(`   响应: ${JSON.stringify(apiInfoResponse.body, null, 2)}\n`);

    // 测试API路由列表
    console.log('4. 测试API路由列表...');
    const apiRoutesResponse = await makeRequest({
      ...baseOptions,
      path: '/api/v1',
      method: 'GET'
    });
    console.log(`   状态码: ${apiRoutesResponse.statusCode}`);
    console.log(`   响应: ${JSON.stringify(apiRoutesResponse.body, null, 2)}\n`);

    // 测试用户注册
    console.log('5. 测试用户注册...');
    const registerResponse = await makeRequest({
      ...baseOptions,
      path: '/api/v1/auth/register',
      method: 'POST'
    }, {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123'
    });
    console.log(`   状态码: ${registerResponse.statusCode}`);
    console.log(`   响应: ${JSON.stringify(registerResponse.body, null, 2)}\n`);

    // 测试游戏状态
    console.log('6. 测试游戏状态...');
    const gameStatusResponse = await makeRequest({
      ...baseOptions,
      path: '/api/v1/game/status',
      method: 'GET'
    });
    console.log(`   状态码: ${gameStatusResponse.statusCode}`);
    console.log(`   响应: ${JSON.stringify(gameStatusResponse.body, null, 2)}\n`);

    // 测试404错误
    console.log('7. 测试404错误...');
    const notFoundResponse = await makeRequest({
      ...baseOptions,
      path: '/nonexistent',
      method: 'GET'
    });
    console.log(`   状态码: ${notFoundResponse.statusCode}`);
    console.log(`   响应: ${JSON.stringify(notFoundResponse.body, null, 2)}\n`);

    console.log('✅ API手动测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.code === 'ECONNREFUSED') {
      console.error('   服务器未启动，请先启动服务器');
    }
  }
}

// 运行测试
testAPI();
