{"version": "1.0.0", "lastUpdated": "2024-01-15T10:30:00.000Z", "rewardTables": [{"id": "bronze_chest_rewards", "name": "青铜宝箱奖励", "description": "青铜宝箱的随机奖励表", "type": "random", "allowDuplicates": false, "maxRewards": 3, "minRewards": 1, "rewards": [{"type": "gold", "rewardId": "gold", "quantity": 100, "minQuantity": 50, "maxQuantity": 150, "probability": 0.8, "weight": 40}, {"type": "item", "rewardId": "hp_potion_small", "quantity": 3, "minQuantity": 1, "maxQuantity": 5, "probability": 0.6, "weight": 30}, {"type": "item", "rewardId": "mp_potion_small", "quantity": 2, "minQuantity": 1, "maxQuantity": 3, "probability": 0.5, "weight": 25}, {"type": "experience", "rewardId": "experience", "quantity": 200, "minQuantity": 100, "maxQuantity": 300, "probability": 0.7, "weight": 35}, {"type": "item", "rewardId": "iron_sword", "quantity": 1, "probability": 0.1, "weight": 5, "levelRequirement": 3}]}, {"id": "silver_chest_rewards", "name": "白银宝箱奖励", "description": "白银宝箱的随机奖励表", "type": "random", "allowDuplicates": false, "maxRewards": 4, "minRewards": 2, "rewards": [{"type": "gold", "rewardId": "gold", "quantity": 300, "minQuantity": 200, "maxQuantity": 500, "probability": 0.9, "weight": 35}, {"type": "item", "rewardId": "hp_potion_small", "quantity": 5, "minQuantity": 3, "maxQuantity": 8, "probability": 0.7, "weight": 25}, {"type": "item", "rewardId": "mp_potion_small", "quantity": 4, "minQuantity": 2, "maxQuantity": 6, "probability": 0.6, "weight": 20}, {"type": "experience", "rewardId": "experience", "quantity": 500, "minQuantity": 300, "maxQuantity": 800, "probability": 0.8, "weight": 30}, {"type": "item", "rewardId": "fireball_scroll", "quantity": 1, "probability": 0.3, "weight": 15, "levelRequirement": 5}, {"type": "item", "rewardId": "iron_sword", "quantity": 1, "probability": 0.2, "weight": 10, "levelRequirement": 3}]}, {"id": "daily_login_rewards", "name": "每日登录奖励", "description": "每日登录获得的奖励", "type": "sequential", "allowDuplicates": true, "maxRewards": 1, "minRewards": 1, "rewards": [{"type": "gold", "rewardId": "gold", "quantity": 100, "probability": 1.0, "weight": 100}, {"type": "item", "rewardId": "hp_potion_small", "quantity": 2, "probability": 1.0, "weight": 100}, {"type": "experience", "rewardId": "experience", "quantity": 150, "probability": 1.0, "weight": 100}, {"type": "item", "rewardId": "mp_potion_small", "quantity": 2, "probability": 1.0, "weight": 100}, {"type": "gold", "rewardId": "gold", "quantity": 300, "probability": 1.0, "weight": 100}, {"type": "item", "rewardId": "fireball_scroll", "quantity": 1, "probability": 1.0, "weight": 100, "levelRequirement": 5}, {"type": "item", "rewardId": "treasure_chest_bronze", "quantity": 3, "probability": 1.0, "weight": 100}]}, {"id": "monster_drop_common", "name": "普通怪物掉落", "description": "普通怪物的掉落奖励表", "type": "random", "allowDuplicates": true, "maxRewards": 2, "minRewards": 0, "rewards": [{"type": "gold", "rewardId": "gold", "quantity": 20, "minQuantity": 10, "maxQuantity": 30, "probability": 0.7, "weight": 50}, {"type": "experience", "rewardId": "experience", "quantity": 50, "minQuantity": 30, "maxQuantity": 80, "probability": 1.0, "weight": 100}, {"type": "item", "rewardId": "hp_potion_small", "quantity": 1, "probability": 0.3, "weight": 20}, {"type": "item", "rewardId": "mp_potion_small", "quantity": 1, "probability": 0.2, "weight": 15}]}, {"id": "boss_drop_rare", "name": "稀有Boss掉落", "description": "稀有Boss的掉落奖励表", "type": "random", "allowDuplicates": false, "maxRewards": 5, "minRewards": 3, "rewards": [{"type": "gold", "rewardId": "gold", "quantity": 1000, "minQuantity": 800, "maxQuantity": 1500, "probability": 1.0, "weight": 100}, {"type": "experience", "rewardId": "experience", "quantity": 2000, "minQuantity": 1500, "maxQuantity": 3000, "probability": 1.0, "weight": 100}, {"type": "item", "rewardId": "iron_sword", "quantity": 1, "probability": 0.8, "weight": 40}, {"type": "item", "rewardId": "fireball_scroll", "quantity": 1, "probability": 0.6, "weight": 30}, {"type": "item", "rewardId": "treasure_chest_bronze", "quantity": 2, "minQuantity": 1, "maxQuantity": 3, "probability": 0.9, "weight": 45}, {"type": "item", "rewardId": "hp_potion_small", "quantity": 10, "minQuantity": 5, "maxQuantity": 15, "probability": 0.7, "weight": 35}]}]}