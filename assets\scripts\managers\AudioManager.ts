import { _decorator, Component, AudioClip, AudioSource, resources, Node } from 'cc';
import { BaseManager } from './BaseManager';

const { ccclass, property } = _decorator;

/**
 * 音频管理器
 * 提供BGM和SFX播放、音量控制、音频管理等功能
 */
@ccclass('AudioManager')
export class AudioManager extends BaseManager {
    private static _instance: AudioManager;
    
    private _bgmAudioSource: AudioSource | null = null;
    private _sfxAudioSources: AudioSource[] = [];
    private _audioClips: Map<string, AudioClip> = new Map();
    
    // 音量设置
    private _masterVolume: number = 1.0;
    private _bgmVolume: number = 0.8;
    private _sfxVolume: number = 1.0;
    
    // 音频状态
    private _isBGMEnabled: boolean = true;
    private _isSFXEnabled: boolean = true;
    private _currentBGM: string = '';
    
    // 音频节点
    private _audioNode: Node | null = null;

    /**
     * 获取单例实例
     */
    public static getInstance(): AudioManager {
        if (!AudioManager._instance) {
            AudioManager._instance = new AudioManager();
        }
        return AudioManager._instance;
    }

    /**
     * 初始化管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('🔊 音频管理器初始化开始...');
        
        try {
            // 创建音频节点
            this._createAudioNode();
            
            // 预加载常用音频资源
            await this._preloadAudioClips();
            
            console.log('✅ 音频管理器初始化完成');
        } catch (error) {
            console.error('❌ 音频管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁管理器
     */
    public destroyManager(): void {
        console.log('🗑️ 音频管理器销毁开始...');
        
        this.stopBGM();
        this.stopAllSFX();
        this._audioClips.clear();
        
        if (this._audioNode) {
            this._audioNode.destroy();
            this._audioNode = null;
        }
        
        console.log('✅ 音频管理器销毁完成');
    }

    /**
     * 播放背景音乐
     */
    public async playBGM(clipName: string, loop: boolean = true): Promise<void> {
        if (!this._isBGMEnabled) {
            console.log('🔇 BGM已禁用，跳过播放');
            return;
        }

        try {
            // 如果已经在播放相同的BGM，则不重复播放
            if (this._currentBGM === clipName && this._bgmAudioSource?.playing) {
                return;
            }

            // 停止当前BGM
            this.stopBGM();

            // 获取音频剪辑
            const audioClip = await this._getAudioClip(clipName);
            if (!audioClip) {
                console.error('❌ 找不到BGM音频剪辑:', clipName);
                return;
            }

            // 创建BGM音频源
            if (!this._bgmAudioSource) {
                this._bgmAudioSource = this._audioNode!.addComponent(AudioSource);
            }

            // 配置并播放BGM
            this._bgmAudioSource.clip = audioClip;
            this._bgmAudioSource.loop = loop;
            this._bgmAudioSource.volume = this._bgmVolume * this._masterVolume;
            this._bgmAudioSource.play();

            this._currentBGM = clipName;
            console.log('🎵 BGM播放:', clipName);

        } catch (error) {
            console.error('❌ BGM播放失败:', error);
        }
    }

    /**
     * 播放音效
     */
    public async playSFX(clipName: string, volume: number = 1.0): Promise<void> {
        if (!this._isSFXEnabled) {
            console.log('🔇 SFX已禁用，跳过播放');
            return;
        }

        try {
            // 获取音频剪辑
            const audioClip = await this._getAudioClip(clipName);
            if (!audioClip) {
                console.error('❌ 找不到SFX音频剪辑:', clipName);
                return;
            }

            // 获取可用的音频源
            const audioSource = this._getAvailableSFXAudioSource();
            
            // 配置并播放SFX
            audioSource.clip = audioClip;
            audioSource.loop = false;
            audioSource.volume = volume * this._sfxVolume * this._masterVolume;
            audioSource.play();

            console.log('🔊 SFX播放:', clipName);

        } catch (error) {
            console.error('❌ SFX播放失败:', error);
        }
    }

    /**
     * 设置BGM音量
     */
    public setBGMVolume(volume: number): void {
        this._bgmVolume = Math.max(0, Math.min(1, volume));
        
        if (this._bgmAudioSource) {
            this._bgmAudioSource.volume = this._bgmVolume * this._masterVolume;
        }
        
        console.log('🎵 BGM音量设置为:', this._bgmVolume);
    }

    /**
     * 设置SFX音量
     */
    public setSFXVolume(volume: number): void {
        this._sfxVolume = Math.max(0, Math.min(1, volume));
        console.log('🔊 SFX音量设置为:', this._sfxVolume);
    }

    /**
     * 设置主音量
     */
    public setMasterVolume(volume: number): void {
        this._masterVolume = Math.max(0, Math.min(1, volume));
        
        // 更新BGM音量
        if (this._bgmAudioSource) {
            this._bgmAudioSource.volume = this._bgmVolume * this._masterVolume;
        }
        
        console.log('🔊 主音量设置为:', this._masterVolume);
    }

    /**
     * 停止BGM
     */
    public stopBGM(): void {
        if (this._bgmAudioSource && this._bgmAudioSource.playing) {
            this._bgmAudioSource.stop();
            this._currentBGM = '';
            console.log('⏹️ BGM已停止');
        }
    }

    /**
     * 停止所有SFX
     */
    public stopAllSFX(): void {
        this._sfxAudioSources.forEach(audioSource => {
            if (audioSource.playing) {
                audioSource.stop();
            }
        });
        console.log('⏹️ 所有SFX已停止');
    }

    /**
     * 暂停所有音频
     */
    public pauseAll(): void {
        if (this._bgmAudioSource && this._bgmAudioSource.playing) {
            this._bgmAudioSource.pause();
        }
        
        this._sfxAudioSources.forEach(audioSource => {
            if (audioSource.playing) {
                audioSource.pause();
            }
        });
        
        console.log('⏸️ 所有音频已暂停');
    }

    /**
     * 恢复所有音频
     */
    public resumeAll(): void {
        if (this._bgmAudioSource && this._bgmAudioSource.state === AudioSource.State.PAUSED) {
            this._bgmAudioSource.play();
        }
        
        this._sfxAudioSources.forEach(audioSource => {
            if (audioSource.state === AudioSource.State.PAUSED) {
                audioSource.play();
            }
        });
        
        console.log('▶️ 所有音频已恢复');
    }

    /**
     * 启用/禁用BGM
     */
    public setBGMEnabled(enabled: boolean): void {
        this._isBGMEnabled = enabled;
        
        if (!enabled) {
            this.stopBGM();
        }
        
        console.log('🎵 BGM', enabled ? '已启用' : '已禁用');
    }

    /**
     * 启用/禁用SFX
     */
    public setSFXEnabled(enabled: boolean): void {
        this._isSFXEnabled = enabled;
        
        if (!enabled) {
            this.stopAllSFX();
        }
        
        console.log('🔊 SFX', enabled ? '已启用' : '已禁用');
    }

    /**
     * 获取音频状态
     */
    public getAudioStatus() {
        return {
            masterVolume: this._masterVolume,
            bgmVolume: this._bgmVolume,
            sfxVolume: this._sfxVolume,
            isBGMEnabled: this._isBGMEnabled,
            isSFXEnabled: this._isSFXEnabled,
            currentBGM: this._currentBGM,
            isBGMPlaying: this._bgmAudioSource?.playing || false
        };
    }

    /**
     * 创建音频节点
     */
    private _createAudioNode(): void {
        this._audioNode = new Node('AudioManager');
        this._audioNode.parent = this.node;
    }

    /**
     * 预加载音频剪辑
     */
    private async _preloadAudioClips(): Promise<void> {
        // 这里可以预加载常用的音频资源
        // 暂时留空，实际项目中可以根据需要添加
        console.log('🎵 音频资源预加载完成');
    }

    /**
     * 获取音频剪辑
     */
    private async _getAudioClip(clipName: string): Promise<AudioClip | null> {
        // 先从缓存中查找
        if (this._audioClips.has(clipName)) {
            return this._audioClips.get(clipName)!;
        }

        // 从资源中加载
        return new Promise((resolve) => {
            resources.load(`audio/${clipName}`, AudioClip, (err, audioClip) => {
                if (err) {
                    console.error('❌ 加载音频剪辑失败:', clipName, err);
                    resolve(null);
                } else {
                    this._audioClips.set(clipName, audioClip);
                    resolve(audioClip);
                }
            });
        });
    }

    /**
     * 获取可用的SFX音频源
     */
    private _getAvailableSFXAudioSource(): AudioSource {
        // 查找空闲的音频源
        for (const audioSource of this._sfxAudioSources) {
            if (!audioSource.playing) {
                return audioSource;
            }
        }

        // 如果没有空闲的，创建新的音频源
        const audioSource = this._audioNode!.addComponent(AudioSource);
        this._sfxAudioSources.push(audioSource);
        
        return audioSource;
    }
}
