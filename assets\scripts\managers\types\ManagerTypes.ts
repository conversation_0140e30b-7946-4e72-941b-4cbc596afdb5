/**
 * 游戏状态枚举
 */
export enum GameState {
    /** 未初始化 */
    UNINITIALIZED = 'uninitialized',
    /** 初始化中 */
    INITIALIZING = 'initializing',
    /** 准备中 */
    PREPARING = 'preparing',
    /** 运行中 */
    RUNNING = 'running',
    /** 暂停中 */
    PAUSED = 'paused',
    /** 结束 */
    ENDED = 'ended',
    /** 错误状态 */
    ERROR = 'error'
}

/**
 * 场景过渡类型
 */
export enum TransitionType {
    /** 无过渡 */
    NONE = 'none',
    /** 淡入淡出 */
    FADE = 'fade',
    /** 滑动 */
    SLIDE = 'slide',
    /** 缩放 */
    SCALE = 'scale',
    /** 自定义 */
    CUSTOM = 'custom'
}

/**
 * 事件回调函数类型
 */
export type EventCallback = (...args: any[]) => void;

/**
 * 异步事件回调函数类型
 */
export type AsyncEventCallback = (...args: any[]) => Promise<void>;

/**
 * 事件监听器信息
 */
export interface EventListener {
    /** 回调函数 */
    callback: EventCallback;
    /** 目标对象 */
    target?: any;
    /** 是否只执行一次 */
    once?: boolean;
}

/**
 * 场景数据接口
 */
export interface SceneData {
    /** 场景名称 */
    name: string;
    /** 场景参数 */
    params?: any;
    /** 上一个场景名称 */
    previousScene?: string;
    /** 场景加载时间 */
    loadTime?: number;
}

/**
 * 资源加载进度信息
 */
export interface LoadProgress {
    /** 已加载数量 */
    loaded: number;
    /** 总数量 */
    total: number;
    /** 进度百分比 */
    progress: number;
    /** 当前加载的资源路径 */
    currentPath?: string;
}

/**
 * 资源缓存信息
 */
export interface ResourceCacheInfo {
    /** 资源路径 */
    path: string;
    /** 资源对象 */
    resource: any;
    /** 资源类型 */
    type: string;
    /** 缓存时间 */
    cacheTime: number;
    /** 引用计数 */
    refCount: number;
}

/**
 * HTTP请求配置
 */
export interface HttpRequestConfig {
    /** 请求URL */
    url: string;
    /** 请求方法 */
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    /** 请求头 */
    headers?: Record<string, string>;
    /** 请求参数 */
    params?: any;
    /** 请求体数据 */
    data?: any;
    /** 超时时间(毫秒) */
    timeout?: number;
}

/**
 * HTTP响应接口
 */
export interface HttpResponse<T = any> {
    /** 响应数据 */
    data: T;
    /** 状态码 */
    status: number;
    /** 状态文本 */
    statusText: string;
    /** 响应头 */
    headers: Record<string, string>;
}

/**
 * WebSocket消息接口
 */
export interface WebSocketMessage {
    /** 消息类型 */
    type: string;
    /** 消息数据 */
    data: any;
    /** 消息ID */
    id?: string;
    /** 时间戳 */
    timestamp?: number;
}

/**
 * 管理器初始化配置
 */
export interface ManagerConfig {
    /** 是否启用调试模式 */
    debug?: boolean;
    /** 自动初始化 */
    autoInit?: boolean;
    /** 初始化超时时间 */
    initTimeout?: number;
}

/**
 * 游戏配置接口
 */
export interface GameConfig extends ManagerConfig {
    /** 游戏版本 */
    version: string;
    /** 游戏名称 */
    name: string;
    /** 默认场景 */
    defaultScene: string;
    /** 是否启用音频 */
    enableAudio?: boolean;
    /** 是否启用网络 */
    enableNetwork?: boolean;
}

/**
 * 场景管理器配置
 */
export interface SceneManagerConfig extends ManagerConfig {
    /** 预加载场景列表 */
    preloadScenes?: string[];
    /** 默认过渡类型 */
    defaultTransition?: TransitionType;
    /** 过渡持续时间 */
    transitionDuration?: number;
}

/**
 * 事件管理器配置
 */
export interface EventManagerConfig extends ManagerConfig {
    /** 最大监听器数量 */
    maxListeners?: number;
    /** 是否启用事件日志 */
    enableEventLog?: boolean;
}

/**
 * 资源管理器配置
 */
export interface ResourceManagerConfig extends ManagerConfig {
    /** 缓存大小限制(MB) */
    cacheLimit?: number;
    /** 预加载资源列表 */
    preloadResources?: string[];
    /** 是否启用资源压缩 */
    enableCompression?: boolean;
}
