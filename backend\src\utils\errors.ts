/**
 * 自定义错误类
 */

export class AppError extends Error {
  public statusCode: number;
  public errorCode: string;
  public isOperational: boolean;
  public timestamp: string;

  constructor(
    message: string,
    statusCode: number = 500,
    errorCode: string = 'INTERNAL_ERROR',
    isOperational: boolean = true
  ) {
    super(message);
    
    this.statusCode = statusCode;
    this.errorCode = errorCode;
    this.isOperational = isOperational;
    this.timestamp = new Date().toISOString();
    
    // 保持正确的堆栈跟踪
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 认证相关错误
 */
export class AuthenticationError extends AppError {
  constructor(message: string = '认证失败') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

export class TokenExpiredError extends AppError {
  constructor(message: string = '令牌已过期') {
    super(message, 401, 'TOKEN_EXPIRED');
  }
}

/**
 * 验证相关错误
 */
export class ValidationError extends AppError {
  public details: string[];

  constructor(message: string = '参数验证失败', details: string[] = []) {
    super(message, 400, 'VALIDATION_ERROR');
    this.details = details;
  }
}

/**
 * 资源相关错误
 */
export class NotFoundError extends AppError {
  constructor(resource: string = '资源') {
    super(`${resource}不存在`, 404, 'NOT_FOUND');
  }
}

export class ConflictError extends AppError {
  constructor(message: string = '资源冲突') {
    super(message, 409, 'CONFLICT');
  }
}

/**
 * 业务逻辑错误
 */
export class BusinessError extends AppError {
  constructor(message: string, errorCode: string = 'BUSINESS_ERROR') {
    super(message, 400, errorCode);
  }
}

/**
 * 游戏相关错误
 */
export class GameError extends AppError {
  constructor(message: string, errorCode: string = 'GAME_ERROR') {
    super(message, 400, errorCode);
  }
}

export class InsufficientResourcesError extends GameError {
  constructor(resource: string = '资源') {
    super(`${resource}不足`, 'INSUFFICIENT_RESOURCES');
  }
}

export class CharacterNotFoundError extends GameError {
  constructor() {
    super('角色不存在', 'CHARACTER_NOT_FOUND');
  }
}

export class BattleError extends GameError {
  constructor(message: string) {
    super(message, 'BATTLE_ERROR');
  }
}

/**
 * 数据库相关错误
 */
export class DatabaseError extends AppError {
  constructor(message: string = '数据库操作失败') {
    super(message, 500, 'DATABASE_ERROR');
  }
}

/**
 * 外部服务错误
 */
export class ExternalServiceError extends AppError {
  constructor(service: string, message: string = '外部服务错误') {
    super(`${service}: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR');
  }
}

/**
 * 速率限制错误
 */
export class RateLimitError extends AppError {
  public retryAfter: number;

  constructor(retryAfter: number = 60) {
    super('请求过于频繁，请稍后再试', 429, 'RATE_LIMIT_EXCEEDED');
    this.retryAfter = retryAfter;
  }
}

/**
 * 维护模式错误
 */
export class MaintenanceError extends AppError {
  constructor() {
    super('系统正在维护中，请稍后再试', 503, 'MAINTENANCE_MODE');
  }
}

/**
 * 错误代码常量
 */
export const ErrorCodes = {
  // 通用错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  
  // 认证错误
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  MISSING_TOKEN: 'MISSING_TOKEN',
  
  // 用户错误
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS: 'USER_ALREADY_EXISTS',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  USERNAME_TAKEN: 'USERNAME_TAKEN',
  EMAIL_TAKEN: 'EMAIL_TAKEN',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  ACCOUNT_DISABLED: 'ACCOUNT_DISABLED',
  
  // 游戏错误
  CHARACTER_NOT_FOUND: 'CHARACTER_NOT_FOUND',
  CHARACTER_NAME_TAKEN: 'CHARACTER_NAME_TAKEN',
  INSUFFICIENT_RESOURCES: 'INSUFFICIENT_RESOURCES',
  BATTLE_ERROR: 'BATTLE_ERROR',
  INVALID_ACTION: 'INVALID_ACTION',
  
  // 系统错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  MAINTENANCE_MODE: 'MAINTENANCE_MODE',
} as const;

/**
 * 错误工厂函数
 */
export const createError = {
  // 认证错误
  authentication: (message?: string) => new AuthenticationError(message),
  authorization: (message?: string) => new AuthorizationError(message),
  tokenExpired: (message?: string) => new TokenExpiredError(message),
  
  // 验证错误
  validation: (message?: string, details?: string[]) => new ValidationError(message, details),
  
  // 资源错误
  notFound: (resource?: string) => new NotFoundError(resource),
  conflict: (message?: string) => new ConflictError(message),
  
  // 业务错误
  business: (message: string, code?: string) => new BusinessError(message, code),
  
  // 游戏错误
  game: (message: string, code?: string) => new GameError(message, code),
  insufficientResources: (resource?: string) => new InsufficientResourcesError(resource),
  characterNotFound: () => new CharacterNotFoundError(),
  battle: (message: string) => new BattleError(message),
  
  // 系统错误
  database: (message?: string) => new DatabaseError(message),
  externalService: (service: string, message?: string) => new ExternalServiceError(service, message),
  rateLimit: (retryAfter?: number) => new RateLimitError(retryAfter),
  maintenance: () => new MaintenanceError(),
};
