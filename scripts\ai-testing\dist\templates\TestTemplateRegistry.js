"use strict";
/**
 * 测试模板注册表 - 管理和提供各种测试模板
 * 支持动态模板选择和自定义模板注册
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestTemplateRegistry = void 0;
class TestTemplateRegistry {
    constructor() {
        this.templates = new Map();
        this.templatesByType = new Map();
        this.templatesByComplexity = new Map();
        this.initializeDefaultTemplates();
        this.buildIndices();
    }
    /**
     * 为系统选择适用的测试模板
     */
    selectTemplatesForSystem(systemInfo) {
        console.log(`📋 Selecting templates for ${systemInfo.name} (${systemInfo.type})`);
        const applicableTemplates = [];
        // 1. 基于系统类型筛选
        const typeTemplates = this.templatesByType.get(systemInfo.type) || [];
        applicableTemplates.push(...typeTemplates);
        // 2. 基于复杂度筛选
        const complexityTemplates = this.templatesByComplexity.get(systemInfo.complexity) || [];
        for (const template of complexityTemplates) {
            if (!applicableTemplates.find(t => t.id === template.id)) {
                applicableTemplates.push(template);
            }
        }
        // 3. 基于系统特征进一步筛选
        const filteredTemplates = applicableTemplates.filter(template => this.isTemplateApplicable(template, systemInfo));
        // 4. 按优先级排序
        const prioritizedTemplates = this.prioritizeTemplates(filteredTemplates, systemInfo);
        console.log(`✅ Selected ${prioritizedTemplates.length} templates`);
        return prioritizedTemplates;
    }
    /**
     * 注册新的测试模板
     */
    registerTemplate(template) {
        console.log(`📝 Registering template: ${template.name}`);
        this.templates.set(template.id, template);
        this.updateIndices(template);
        console.log(`✅ Template registered: ${template.id}`);
    }
    /**
     * 获取模板
     */
    getTemplate(templateId) {
        return this.templates.get(templateId);
    }
    /**
     * 获取所有模板
     */
    getAllTemplates() {
        return Array.from(this.templates.values());
    }
    /**
     * 根据类型获取模板
     */
    getTemplatesByType(systemType) {
        return this.templatesByType.get(systemType) || [];
    }
    /**
     * 搜索模板
     */
    searchTemplates(query) {
        const lowerQuery = query.toLowerCase();
        return Array.from(this.templates.values()).filter(template => template.name.toLowerCase().includes(lowerQuery) ||
            template.description.toLowerCase().includes(lowerQuery) ||
            template.metadata.tags.some(tag => tag.toLowerCase().includes(lowerQuery)));
    }
    /**
     * 动态创建模板
     */
    async createTemplateFromSystem(systemInfo) {
        console.log(`🏗️ Creating custom template for ${systemInfo.name}`);
        const templateId = `custom-${systemInfo.name}-${Date.now()}`;
        const template = {
            id: templateId,
            name: `${systemInfo.name} 专用测试模板`,
            description: `为 ${systemInfo.name} 系统自动生成的测试模板`,
            systemTypes: [systemInfo.type],
            complexity: [systemInfo.complexity],
            testTypes: this.inferTestTypes(systemInfo),
            template: await this.generateTemplateDefinition(systemInfo),
            metadata: {
                author: 'AI Generator',
                version: '1.0.0',
                createdAt: new Date(),
                updatedAt: new Date(),
                tags: ['auto-generated', systemInfo.type],
                usage: 0,
                rating: 0
            }
        };
        this.registerTemplate(template);
        return template;
    }
    // 私有方法
    /**
     * 初始化默认模板
     */
    initializeDefaultTemplates() {
        // 通用业务逻辑测试模板
        this.registerTemplate({
            id: 'business-logic-basic',
            name: '基础业务逻辑测试',
            description: '适用于一般业务逻辑系统的基础测试模板',
            systemTypes: ['business-logic', 'game-logic'],
            complexity: ['simple', 'medium'],
            testTypes: ['unit', 'integration'],
            template: {
                testStructure: `
describe('{{systemName}} System Tests', () => {
    let {{systemName.toLowerCase()}}System: {{systemName}}System;
    let testData: {{systemName}}TestData;
    
    beforeEach(async () => {
        {{systemName.toLowerCase()}}System = new {{systemName}}System();
        testData = await generateTestData();
    });
    
    afterEach(async () => {
        await cleanup();
    });
    
    {{#each functions}}
    describe('{{name}} function', () => {
        {{#each testCases}}
        test('{{description}}', async () => {
            // Arrange
            {{arrange}}
            
            // Act
            const result = await {{systemName.toLowerCase()}}System.{{../name}}({{parameters}});
            
            // Assert
            {{assert}}
        });
        {{/each}}
        
        test('should handle invalid input', async () => {
            await expect({{systemName.toLowerCase()}}System.{{name}}(null))
                .rejects.toThrow();
        });
    });
    {{/each}}
});`,
                dataGeneration: `
async function generateTestData(): Promise<{{systemName}}TestData> {
    return {
        {{#each dataTypes}}
        {{name}}: {{generator}},
        {{/each}}
    };
}

async function cleanup(): Promise<void> {
    // 清理测试数据
}`,
                validationRules: [
                    'input validation',
                    'output verification',
                    'state consistency',
                    'error handling'
                ],
                imports: [
                    "import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';",
                    "import { {{systemName}}System } from '../src/{{systemPath}}';",
                    "import { {{systemName}}TestData } from './fixtures/{{systemName}}TestData';"
                ]
            },
            metadata: {
                author: 'AI Testing Framework',
                version: '1.0.0',
                createdAt: new Date(),
                updatedAt: new Date(),
                tags: ['business-logic', 'basic', 'unit-test'],
                usage: 0,
                rating: 5
            }
        });
        // 算法一致性验证模板
        this.registerTemplate({
            id: 'algorithm-consistency',
            name: '算法一致性验证',
            description: '用于验证Godot和Cocos Creator版本算法一致性的测试模板',
            systemTypes: ['game-logic', 'battle-system', 'wuxia-system'],
            complexity: ['medium', 'complex'],
            testTypes: ['consistency', 'regression'],
            template: {
                testStructure: `
describe('{{systemName}} Algorithm Consistency Tests', () => {
    {{#each algorithms}}
    describe('{{name}} algorithm', () => {
        test('should produce consistent results with original implementation', async () => {
            const testCases = await generateAlgorithmTestCases('{{name}}');
            
            for (const testCase of testCases) {
                // 执行原版算法（Godot）
                const originalResult = await executeOriginalAlgorithm('{{name}}', testCase);
                
                // 执行新版算法（Cocos Creator）
                const newResult = await executeNewAlgorithm('{{name}}', testCase);
                
                // 验证结果一致性
                expect(newResult).toBeCloseTo(originalResult, {{precision}});
                
                // 记录差异（如果有）
                if (Math.abs(newResult - originalResult) > {{tolerance}}) {
                    console.warn('Algorithm deviation detected:', {
                        algorithm: '{{name}}',
                        testCase,
                        original: originalResult,
                        new: newResult,
                        deviation: Math.abs(newResult - originalResult)
                    });
                }
            }
        });
        
        test('should handle edge cases consistently', async () => {
            const edgeCases = await generateEdgeCases('{{name}}');
            
            for (const edgeCase of edgeCases) {
                const originalResult = await executeOriginalAlgorithm('{{name}}', edgeCase);
                const newResult = await executeNewAlgorithm('{{name}}', edgeCase);
                
                // 对于边界情况，允许更大的容差
                expect(newResult).toBeCloseTo(originalResult, {{edgePrecision}});
            }
        });
    });
    {{/each}}
});`,
                dataGeneration: `
async function generateAlgorithmTestCases(algorithmName: string): Promise<any[]> {
    const generators = {
        'damageCalculation': generateDamageTestCases,
        'experienceCalculation': generateExperienceTestCases,
        'skillEffect': generateSkillEffectTestCases
    };
    
    const generator = generators[algorithmName];
    return generator ? await generator() : [];
}

async function generateEdgeCases(algorithmName: string): Promise<any[]> {
    // 生成边界情况测试用例
    return [];
}`,
                validationRules: [
                    'numerical precision',
                    'edge case handling',
                    'performance consistency',
                    'random seed consistency'
                ],
                imports: [
                    "import { describe, test, expect } from '@jest/globals';",
                    "import { executeOriginalAlgorithm, executeNewAlgorithm } from '../utils/AlgorithmComparator';"
                ]
            },
            metadata: {
                author: 'Migration Team',
                version: '1.0.0',
                createdAt: new Date(),
                updatedAt: new Date(),
                tags: ['algorithm', 'consistency', 'migration'],
                usage: 0,
                rating: 5
            }
        });
        // API测试模板
        this.registerTemplate({
            id: 'api-testing',
            name: 'API接口测试',
            description: '用于测试RESTful API接口的标准模板',
            systemTypes: ['api-service', 'user-management'],
            complexity: ['simple', 'medium', 'complex'],
            testTypes: ['api', 'integration'],
            template: {
                testStructure: `
describe('{{systemName}} API Tests', () => {
    let app: Application;
    let server: Server;
    
    beforeAll(async () => {
        app = await createTestApp();
        server = app.listen(0);
    });
    
    afterAll(async () => {
        await server.close();
    });
    
    {{#each endpoints}}
    describe('{{method}} {{path}}', () => {
        test('should return success response with valid data', async () => {
            const response = await request(app)
                .{{method.toLowerCase()}}('{{path}}')
                {{#if payload}}.send({{payload}}){{/if}}
                .expect({{successStatus}});
            
            expect(response.body).toMatchSchema({{responseSchema}});
            {{#each assertions}}
            {{this}}
            {{/each}}
        });
        
        test('should handle invalid input gracefully', async () => {
            const response = await request(app)
                .{{method.toLowerCase()}}('{{path}}')
                .send({{invalidPayload}})
                .expect({{errorStatus}});
            
            expect(response.body.error).toBeDefined();
            expect(response.body.message).toBeTruthy();
        });
        
        {{#if requiresAuth}}
        test('should require authentication', async () => {
            await request(app)
                .{{method.toLowerCase()}}('{{path}}')
                .expect(401);
        });
        {{/if}}
    });
    {{/each}}
});`,
                validationRules: [
                    'status code validation',
                    'response schema validation',
                    'authentication check',
                    'error handling'
                ],
                imports: [
                    "import { describe, test, expect, beforeAll, afterAll } from '@jest/globals';",
                    "import request from 'supertest';",
                    "import { createTestApp } from '../utils/TestApp';"
                ]
            },
            metadata: {
                author: 'API Team',
                version: '1.0.0',
                createdAt: new Date(),
                updatedAt: new Date(),
                tags: ['api', 'integration', 'http'],
                usage: 0,
                rating: 4
            }
        });
        // 武侠系统专用模板
        this.registerTemplate({
            id: 'wuxia-system',
            name: '武侠系统测试',
            description: '专门用于武侠游戏系统的测试模板',
            systemTypes: ['wuxia-system', 'game-logic'],
            complexity: ['medium', 'complex'],
            testTypes: ['unit', 'integration', 'game-logic'],
            template: {
                testStructure: `
describe('{{systemName}} Wuxia System Tests', () => {
    let wuxiaSystem: WuxiaSystem;
    let testPlayer: Player;
    
    beforeEach(async () => {
        wuxiaSystem = new WuxiaSystem();
        testPlayer = await createTestPlayer();
    });
    
    describe('门派系统', () => {
        test('should allow player to join sect', async () => {
            const sect = await wuxiaSystem.getSect('少林');
            const result = await wuxiaSystem.joinSect(testPlayer.id, sect.id);
            
            expect(result.success).toBe(true);
            expect(testPlayer.sectId).toBe(sect.id);
        });
        
        test('should calculate sect contribution correctly', async () => {
            const contribution = await wuxiaSystem.calculateContribution(testPlayer.id, 'resource', 100);
            expect(contribution).toBeGreaterThan(0);
        });
    });
    
    describe('修炼系统', () => {
        test('should increase cultivation experience', async () => {
            const initialExp = testPlayer.cultivation.experience;
            await wuxiaSystem.practice(testPlayer.id, 'qi', 60); // 修炼1小时
            
            expect(testPlayer.cultivation.experience).toBeGreaterThan(initialExp);
        });
        
        test('should handle realm breakthrough', async () => {
            testPlayer.cultivation.experience = 10000; // 足够突破的经验
            const result = await wuxiaSystem.breakthrough(testPlayer.id);
            
            expect(result.success).toBe(true);
            expect(testPlayer.cultivation.realm).toBe('筑基');
        });
    });
    
    describe('技能系统', () => {
        test('should learn new skill', async () => {
            const skill = await wuxiaSystem.getSkill('基础剑法');
            const result = await wuxiaSystem.learnSkill(testPlayer.id, skill.id);
            
            expect(result.success).toBe(true);
            expect(testPlayer.skills).toContain(skill.id);
        });
    });
});`,
                validationRules: [
                    'game balance validation',
                    'progression logic',
                    'resource management',
                    'state consistency'
                ],
                imports: [
                    "import { WuxiaSystem } from '../src/systems/WuxiaSystem';",
                    "import { Player } from '../src/models/Player';",
                    "import { createTestPlayer } from '../utils/TestDataGenerator';"
                ]
            },
            metadata: {
                author: 'Game Team',
                version: '1.0.0',
                createdAt: new Date(),
                updatedAt: new Date(),
                tags: ['wuxia', 'game-logic', 'rpg'],
                usage: 0,
                rating: 5
            }
        });
    }
    /**
     * 构建索引
     */
    buildIndices() {
        for (const template of this.templates.values()) {
            this.updateIndices(template);
        }
    }
    /**
     * 更新索引
     */
    updateIndices(template) {
        // 按系统类型索引
        for (const systemType of template.systemTypes) {
            if (!this.templatesByType.has(systemType)) {
                this.templatesByType.set(systemType, []);
            }
            this.templatesByType.get(systemType).push(template);
        }
        // 按复杂度索引
        for (const complexity of template.complexity) {
            if (!this.templatesByComplexity.has(complexity)) {
                this.templatesByComplexity.set(complexity, []);
            }
            this.templatesByComplexity.get(complexity).push(template);
        }
    }
    /**
     * 检查模板是否适用
     */
    isTemplateApplicable(template, systemInfo) {
        // 检查系统类型匹配
        if (!template.systemTypes.includes(systemInfo.type)) {
            return false;
        }
        // 检查复杂度匹配
        if (!template.complexity.includes(systemInfo.complexity)) {
            return false;
        }
        // 可以添加更多的适用性检查逻辑
        return true;
    }
    /**
     * 按优先级排序模板
     */
    prioritizeTemplates(templates, systemInfo) {
        return templates.sort((a, b) => {
            // 按评分排序
            if (a.metadata.rating !== b.metadata.rating) {
                return b.metadata.rating - a.metadata.rating;
            }
            // 按使用次数排序
            if (a.metadata.usage !== b.metadata.usage) {
                return b.metadata.usage - a.metadata.usage;
            }
            // 按更新时间排序
            return b.metadata.updatedAt.getTime() - a.metadata.updatedAt.getTime();
        });
    }
    /**
     * 推断测试类型
     */
    inferTestTypes(systemInfo) {
        const testTypes = ['unit'];
        if (systemInfo.type.includes('api')) {
            testTypes.push('api', 'integration');
        }
        if (systemInfo.type.includes('ui')) {
            testTypes.push('ui', 'visual');
        }
        if (systemInfo.type.includes('game') || systemInfo.type.includes('wuxia') || systemInfo.type.includes('battle')) {
            testTypes.push('algorithm-consistency', 'performance');
        }
        if (systemInfo.complexity === 'complex') {
            testTypes.push('integration', 'stress');
        }
        return testTypes;
    }
    /**
     * 生成模板定义
     */
    async generateTemplateDefinition(systemInfo) {
        // 这里可以使用AI来生成更智能的模板定义
        // 简化实现
        return {
            testStructure: `
describe('${systemInfo.name} Tests', () => {
    // 自动生成的测试结构
    test('should work correctly', () => {
        expect(true).toBe(true);
    });
});`,
            validationRules: ['basic validation'],
            imports: ["import { describe, test, expect } from '@jest/globals';"]
        };
    }
}
exports.TestTemplateRegistry = TestTemplateRegistry;
//# sourceMappingURL=TestTemplateRegistry.js.map