// 简单的编译测试文件
import { MasterTestBot } from './core/MasterTestBot';
import { TestBotFactory } from './core/TestBotFactory';
import { SystemDiscoveryAgent } from './agents/SystemDiscoveryAgent';

console.log('Testing imports...');

try {
    const masterBot = new MasterTestBot();
    const factory = new TestBotFactory();
    const discovery = new SystemDiscoveryAgent();
    
    console.log('✅ All imports successful');
} catch (error) {
    console.error('❌ Import error:', error);
}
