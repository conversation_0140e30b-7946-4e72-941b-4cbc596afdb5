import { _decorator, Component } from 'cc';

const { ccclass } = _decorator;

/**
 * 基础测试组件
 * 不继承任何自定义基类，直接继承Cocos Creator的Component
 */
@ccclass('BasicTest')
export class BasicTest extends Component {

    protected onLoad(): void {
        console.log('🧪 基础测试组件加载');
    }

    protected start(): void {
        console.log('🧪 基础测试组件开始');
        this.runBasicTest();
    }

    /**
     * 运行基础测试
     */
    private runBasicTest(): void {
        console.log('🎯 运行基础测试...');
        
        try {
            // 测试基本功能
            console.log('✅ TypeScript编译正常');
            console.log('✅ Cocos Creator组件系统正常');
            console.log('✅ 装饰器系统正常');
            
            // 测试基本数据类型
            const testData = {
                name: '武侠放置游戏',
                version: '1.0.0',
                engine: 'Cocos Creator 3.8.6'
            };
            
            console.log('✅ 数据结构正常:', testData);
            
            console.log('🎉 基础测试全部通过！');
            
        } catch (error) {
            console.error('❌ 基础测试失败:', error);
        }
    }

    protected onDestroy(): void {
        console.log('🧪 基础测试组件销毁');
    }
}
