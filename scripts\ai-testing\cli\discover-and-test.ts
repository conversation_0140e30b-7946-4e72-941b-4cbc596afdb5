#!/usr/bin/env ts-node

/**
 * AI测试框架 - 发现并测试命令
 * 自动发现项目中的系统并生成测试
 */

import { Command } from 'commander';
import chalk from 'chalk';
import * as path from 'path';
import { MasterTestBot } from '../core/MasterTestBot';
import { TestBotFactory } from '../core/TestBotFactory';
import { SystemDiscoveryAgent } from '../agents/SystemDiscoveryAgent';

const program = new Command();

interface DiscoverAndTestOptions {
    projectPath: string;
    outputDir?: string;
    verbose?: boolean;
    dryRun?: boolean;
    systemType?: string;
    maxSystems?: number;
    parallel?: boolean;
    generateReport?: boolean;
}

program
    .name('ai-test-discover-and-test')
    .description('AI-driven system discovery and test generation')
    .version('1.0.0')
    .option('-p, --project-path <path>', 'Project path to analyze', process.cwd())
    .option('-o, --output-dir <dir>', 'Output directory for generated tests', './ai-tests')
    .option('-v, --verbose', 'Enable verbose logging', false)
    .option('-d, --dry-run', 'Dry run mode (no files written)', false)
    .option('-t, --system-type <type>', 'Filter by system type')
    .option('-m, --max-systems <number>', 'Maximum number of systems to process', '10')
    .option('--parallel', 'Enable parallel processing', false)
    .option('--generate-report', 'Generate detailed analysis report', true)
    .action(async (options: DiscoverAndTestOptions) => {
        await discoverAndTest(options);
    });

async function discoverAndTest(options: DiscoverAndTestOptions): Promise<void> {
    console.log(chalk.blue('🚀 AI Testing Framework - Discover and Test'));
    console.log(chalk.gray(`Project: ${options.projectPath}`));
    console.log(chalk.gray(`Output: ${options.outputDir}`));
    
    if (options.dryRun) {
        console.log(chalk.yellow('🔍 DRY RUN MODE - No files will be written'));
    }

    try {
        // 1. 初始化AI测试框架
        console.log(chalk.blue('\n📋 Step 1: Initializing AI Testing Framework'));
        const masterBot = new MasterTestBot();
        const testBotFactory = new TestBotFactory();
        const discoveryAgent = new SystemDiscoveryAgent();

        // 2. 发现项目中的系统
        console.log(chalk.blue('\n🔍 Step 2: Discovering Systems'));
        const systems = await discoveryAgent.discoverSystems(options.projectPath);
        
        if (systems.length === 0) {
            console.log(chalk.yellow('⚠️ No systems found in the project'));
            return;
        }

        console.log(chalk.green(`✅ Found ${systems.length} systems:`));
        systems.forEach((system, index) => {
            console.log(chalk.gray(`  ${index + 1}. ${system.name} (${system.type}) - ${system.complexity}`));
        });

        // 3. 过滤系统（如果指定了类型）
        let filteredSystems = systems;
        if (options.systemType) {
            filteredSystems = systems.filter(s => s.type === options.systemType);
            console.log(chalk.blue(`\n🔍 Filtered to ${filteredSystems.length} systems of type '${options.systemType}'`));
        }

        // 4. 限制处理的系统数量
        const maxSystems = parseInt(options.maxSystems || '10');
        if (filteredSystems.length > maxSystems) {
            filteredSystems = filteredSystems.slice(0, maxSystems);
            console.log(chalk.yellow(`⚠️ Limited to first ${maxSystems} systems`));
        }

        // 5. 为每个系统创建测试机器人并生成测试
        console.log(chalk.blue('\n🤖 Step 3: Creating Test Bots and Generating Tests'));
        
        const results = [];
        
        if (options.parallel) {
            // 并行处理
            console.log(chalk.blue('⚡ Processing systems in parallel'));
            const promises = filteredSystems.map(async (system) => {
                return await processSystem(system, testBotFactory, options);
            });
            
            const parallelResults = await Promise.allSettled(promises);
            parallelResults.forEach((result, index) => {
                if (result.status === 'fulfilled') {
                    results.push(result.value);
                } else {
                    console.error(chalk.red(`❌ Failed to process ${filteredSystems[index].name}: ${result.reason}`));
                }
            });
        } else {
            // 串行处理
            console.log(chalk.blue('🔄 Processing systems sequentially'));
            for (const system of filteredSystems) {
                try {
                    const result = await processSystem(system, testBotFactory, options);
                    results.push(result);
                } catch (error) {
                    console.error(chalk.red(`❌ Failed to process ${system.name}: ${error}`));
                }
            }
        }

        // 6. 生成总结报告
        console.log(chalk.blue('\n📊 Step 4: Generating Summary Report'));
        await generateSummaryReport(results, options);

        // 7. 显示完成信息
        console.log(chalk.green('\n🎉 AI Testing Framework - Discovery and Test Generation Complete!'));
        console.log(chalk.gray(`✅ Processed ${results.length} systems`));
        console.log(chalk.gray(`📁 Output directory: ${options.outputDir}`));
        
        if (!options.dryRun) {
            console.log(chalk.blue('\n🚀 Next steps:'));
            console.log(chalk.gray('  1. Review generated tests in the output directory'));
            console.log(chalk.gray('  2. Run tests using your preferred test runner'));
            console.log(chalk.gray('  3. Integrate tests into your CI/CD pipeline'));
        }

    } catch (error) {
        console.error(chalk.red('\n❌ AI Testing Framework failed:'), error);
        process.exit(1);
    }
}

async function processSystem(system: any, testBotFactory: TestBotFactory, options: DiscoverAndTestOptions): Promise<any> {
    console.log(chalk.blue(`\n🔄 Processing: ${system.name}`));
    
    try {
        // 1. 创建测试机器人
        const testBot = await testBotFactory.createTestBotForSystem(system.path);
        
        // 2. 生成测试用例
        const testCases = await testBot.generateTestsForSystem();
        
        // 3. 写入测试文件（如果不是dry run）
        if (!options.dryRun) {
            await writeTestFiles(system, testCases, options);
        }
        
        console.log(chalk.green(`✅ ${system.name}: Generated ${testCases.length} test cases`));
        
        return {
            system,
            testCases,
            success: true
        };
        
    } catch (error) {
        console.error(chalk.red(`❌ ${system.name}: ${error}`));
        return {
            system,
            testCases: [],
            success: false,
            error: error.message
        };
    }
}

async function writeTestFiles(system: any, testCases: any[], options: DiscoverAndTestOptions): Promise<void> {
    // 实现测试文件写入逻辑
    console.log(chalk.gray(`📝 Writing ${testCases.length} test files for ${system.name}`));
}

async function generateSummaryReport(results: any[], options: DiscoverAndTestOptions): Promise<void> {
    const successCount = results.filter(r => r.success).length;
    const totalTestCases = results.reduce((sum, r) => sum + r.testCases.length, 0);
    
    console.log(chalk.green(`\n📊 Summary Report:`));
    console.log(chalk.gray(`  Systems processed: ${results.length}`));
    console.log(chalk.gray(`  Successful: ${successCount}`));
    console.log(chalk.gray(`  Failed: ${results.length - successCount}`));
    console.log(chalk.gray(`  Total test cases generated: ${totalTestCases}`));
    
    if (options.generateReport && !options.dryRun) {
        // 生成详细报告文件
        console.log(chalk.blue('📄 Generating detailed report...'));
    }
}

// 如果直接运行此文件
if (require.main === module) {
    program.parse();
}
