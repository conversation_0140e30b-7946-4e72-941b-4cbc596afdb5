import Redis = require('ioredis');
import { Logger } from './logger';
import { RedisConfig, RealRedisConfig } from '../config/redis';

/**
 * 缓存操作选项接口
 */
export interface CacheOptions {
  ttl?: number; // 过期时间（秒）
  prefix?: string; // 键前缀
  serialize?: boolean; // 是否序列化
  compress?: boolean; // 是否压缩
}

/**
 * 缓存统计信息接口
 */
export interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
  hitRate: number;
  totalOperations: number;
}

/**
 * 缓存管理器类
 */
export class CacheManager {
  private static instance: CacheManager;
  private redisClient: any; // Redis客户端
  private useRealRedis: boolean;
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
    hitRate: 0,
    totalOperations: 0,
  };

  private constructor() {
    this.useRealRedis = process.env['USE_REAL_REDIS'] === 'true';
    this.initializeClient();
  }

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  /**
   * 初始化Redis客户端
   */
  private initializeClient(): void {
    if (this.useRealRedis) {
      const realRedisConfig = RealRedisConfig.getInstance();
      this.redisClient = realRedisConfig.getClient();
    } else {
      const redisConfig = RedisConfig.getInstance();
      this.redisClient = redisConfig.getClient();
    }
  }

  /**
   * 确保Redis客户端已连接
   */
  private ensureConnection(): void {
    if (!this.redisClient) {
      throw new Error('Redis客户端未初始化');
    }
  }

  /**
   * 生成缓存键
   */
  private generateKey(key: string, prefix?: string): string {
    const finalPrefix = prefix || process.env['CACHE_PREFIX'] || 'cache:';
    return `${finalPrefix}${key}`;
  }

  /**
   * 序列化值
   */
  private serializeValue(value: any, serialize: boolean = true): string {
    if (!serialize) {
      return value;
    }
    
    try {
      return JSON.stringify(value);
    } catch (error) {
      Logger.error('缓存值序列化失败', { value, error });
      throw error;
    }
  }

  /**
   * 反序列化值
   */
  private deserializeValue<T>(value: string, serialize: boolean = true): T {
    if (!serialize) {
      return value as T;
    }
    
    try {
      return JSON.parse(value);
    } catch (error) {
      Logger.error('缓存值反序列化失败', { value, error });
      throw error;
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(operation: 'hit' | 'miss' | 'set' | 'delete' | 'error'): void {
    this.stats[operation === 'hit' ? 'hits' : operation === 'miss' ? 'misses' : operation === 'set' ? 'sets' : operation === 'delete' ? 'deletes' : 'errors']++;
    this.stats.totalOperations = this.stats.hits + this.stats.misses + this.stats.sets + this.stats.deletes;
    this.stats.hitRate = this.stats.totalOperations > 0 ? (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100 : 0;
  }

  /**
   * 设置缓存值
   */
  public async set(key: string, value: any, options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();
      
      const finalKey = this.generateKey(key, options.prefix);
      const serializedValue = this.serializeValue(value, options.serialize !== false);
      
      const startTime = Date.now();
      
      if (options.ttl) {
        await this.redisClient.setex(finalKey, options.ttl, serializedValue);
      } else {
        await this.redisClient.set(finalKey, serializedValue);
      }
      
      const duration = Date.now() - startTime;
      this.updateStats('set');
      
      Logger.debug('缓存设置成功', {
        key: finalKey,
        ttl: options.ttl,
        duration: `${duration}ms`,
      });
      
    } catch (error) {
      this.updateStats('error');
      Logger.error('缓存设置失败', { key, error });
      throw error;
    }
  }

  /**
   * 批量设置缓存 - 性能优化
   */
  public async mset(keyValuePairs: Array<{key: string, value: any, ttl?: number, prefix?: string}>): Promise<void> {
    try {
      this.ensureConnection();

      const pipeline = this.redisClient.pipeline();

      for (const {key, value, ttl, prefix} of keyValuePairs) {
        const finalKey = this.generateKey(key, prefix);
        const serializedValue = this.serializeValue(value, true);

        if (ttl) {
          pipeline.setex(finalKey, ttl, serializedValue);
        } else {
          pipeline.set(finalKey, serializedValue);
        }
      }

      const startTime = Date.now();
      await pipeline.exec();
      const duration = Date.now() - startTime;

      // 更新统计
      for (let i = 0; i < keyValuePairs.length; i++) {
        this.updateStats('set');
      }

      Logger.debug('批量缓存设置成功', {
        count: keyValuePairs.length,
        duration: `${duration}ms`
      });
    } catch (error) {
      this.updateStats('error');
      Logger.error('批量缓存设置失败', { error });
      throw error;
    }
  }

  /**
   * 批量获取缓存 - 性能优化
   */
  public async mget<T>(keys: Array<{key: string, prefix?: string}>): Promise<Array<T | null>> {
    try {
      this.ensureConnection();

      const finalKeys = keys.map(({key, prefix}) => this.generateKey(key, prefix));

      const startTime = Date.now();
      const values = await this.redisClient.mget(...finalKeys);
      const duration = Date.now() - startTime;

      const results: Array<T | null> = [];

      for (let i = 0; i < values.length; i++) {
        if (values[i] === null) {
          this.updateStats('miss');
          results.push(null);
        } else {
          this.updateStats('hit');
          results.push(this.deserializeValue(values[i]!, true));
        }
      }

      Logger.debug('批量缓存获取完成', {
        count: keys.length,
        hits: results.filter(r => r !== null).length,
        duration: `${duration}ms`
      });

      return results;
    } catch (error) {
      this.updateStats('error');
      Logger.error('批量缓存获取失败', { error });
      throw error;
    }
  }

  /**
   * 获取缓存值
   */
  public async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    try {
      this.ensureConnection();
      
      const finalKey = this.generateKey(key, options.prefix);
      const startTime = Date.now();
      
      const value = await this.redisClient.get(finalKey);
      const duration = Date.now() - startTime;
      
      if (value === null) {
        this.updateStats('miss');
        Logger.debug('缓存未命中', { key: finalKey, duration: `${duration}ms` });
        return null;
      }
      
      this.updateStats('hit');
      const deserializedValue = this.deserializeValue<T>(value, options.serialize !== false);
      
      Logger.debug('缓存命中', {
        key: finalKey,
        duration: `${duration}ms`,
      });
      
      return deserializedValue;
      
    } catch (error) {
      this.updateStats('error');
      Logger.error('缓存获取失败', { key, error });
      return null;
    }
  }

  /**
   * 删除缓存值
   */
  public async del(key: string, options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();
      
      const finalKey = this.generateKey(key, options.prefix);
      const startTime = Date.now();
      
      await this.redisClient.del(finalKey);
      const duration = Date.now() - startTime;
      
      this.updateStats('delete');
      
      Logger.debug('缓存删除成功', {
        key: finalKey,
        duration: `${duration}ms`,
      });
      
    } catch (error) {
      this.updateStats('error');
      Logger.error('缓存删除失败', { key, error });
      throw error;
    }
  }

  /**
   * 检查缓存是否存在
   */
  public async exists(key: string, options: CacheOptions = {}): Promise<boolean> {
    try {
      this.ensureConnection();
      
      const finalKey = this.generateKey(key, options.prefix);
      const result = await this.redisClient.exists(finalKey);
      
      return result === 1;
      
    } catch (error) {
      this.updateStats('error');
      Logger.error('缓存存在检查失败', { key, error });
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   */
  public async expire(key: string, ttl: number, options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();
      
      const finalKey = this.generateKey(key, options.prefix);
      await this.redisClient.expire(finalKey, ttl);
      
      Logger.debug('缓存过期时间设置成功', { key: finalKey, ttl });
      
    } catch (error) {
      this.updateStats('error');
      Logger.error('缓存过期时间设置失败', { key, ttl, error });
      throw error;
    }
  }

  /**
   * 获取缓存剩余过期时间
   */
  public async ttl(key: string, options: CacheOptions = {}): Promise<number> {
    try {
      this.ensureConnection();
      
      const finalKey = this.generateKey(key, options.prefix);
      const result = await this.redisClient.ttl(finalKey);
      
      return result;
      
    } catch (error) {
      this.updateStats('error');
      Logger.error('获取缓存过期时间失败', { key, error });
      return -1;
    }
  }

  /**
   * 获取或设置缓存（缓存穿透保护）
   */
  public async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    try {
      // 先尝试从缓存获取
      const cachedValue = await this.get<T>(key, options);
      if (cachedValue !== null) {
        return cachedValue;
      }

      // 缓存未命中，执行获取函数
      Logger.debug('缓存未命中，执行获取函数', { key });
      const startTime = Date.now();

      const value = await fetcher();
      const duration = Date.now() - startTime;

      // 将结果存入缓存
      await this.set(key, value, options);

      Logger.debug('获取函数执行完成并缓存', {
        key,
        duration: `${duration}ms`,
      });

      return value;

    } catch (error) {
      this.updateStats('error');
      Logger.error('获取或设置缓存失败', { key, error });
      throw error;
    }
  }

  /**
   * 批量获取缓存
   */
  public async mget<T>(keys: string[], options: CacheOptions = {}): Promise<(T | null)[]> {
    try {
      this.ensureConnection();

      const finalKeys = keys.map(key => this.generateKey(key, options.prefix));
      const values = await this.redisClient.mget(...finalKeys);

      return values.map((value: string | null) => {
        if (value === null) {
          this.updateStats('miss');
          return null;
        }
        this.updateStats('hit');
        return this.deserializeValue<T>(value, options.serialize !== false);
      });

    } catch (error) {
      this.updateStats('error');
      Logger.error('批量获取缓存失败', { keys, error });
      throw error;
    }
  }

  /**
   * 批量设置缓存
   */
  public async mset(keyValues: Record<string, any>, options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();

      const pipeline = this.redisClient.pipeline();

      for (const [key, value] of Object.entries(keyValues)) {
        const finalKey = this.generateKey(key, options.prefix);
        const serializedValue = this.serializeValue(value, options.serialize !== false);

        if (options.ttl) {
          pipeline.setex(finalKey, options.ttl, serializedValue);
        } else {
          pipeline.set(finalKey, serializedValue);
        }
      }

      await pipeline.exec();
      this.updateStats('set');

      Logger.debug('批量设置缓存成功', {
        count: Object.keys(keyValues).length,
        ttl: options.ttl,
      });

    } catch (error) {
      this.updateStats('error');
      Logger.error('批量设置缓存失败', { keyValues, error });
      throw error;
    }
  }

  /**
   * 批量删除缓存
   */
  public async mdel(keys: string[], options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();

      const finalKeys = keys.map(key => this.generateKey(key, options.prefix));
      await this.redisClient.del(...finalKeys);

      this.updateStats('delete');

      Logger.debug('批量删除缓存成功', { count: keys.length });

    } catch (error) {
      this.updateStats('error');
      Logger.error('批量删除缓存失败', { keys, error });
      throw error;
    }
  }

  /**
   * 设置哈希字段
   */
  public async hset(key: string, field: string, value: any, options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const serializedValue = this.serializeValue(value, options.serialize !== false);

      await this.redisClient.hset(finalKey, field, serializedValue);

      if (options.ttl) {
        await this.redisClient.expire(finalKey, options.ttl);
      }

      this.updateStats('set');

      Logger.debug('哈希字段设置成功', { key: finalKey, field });

    } catch (error) {
      this.updateStats('error');
      Logger.error('哈希字段设置失败', { key, field, error });
      throw error;
    }
  }

  /**
   * 获取哈希字段
   */
  public async hget<T>(key: string, field: string, options: CacheOptions = {}): Promise<T | null> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const value = await this.redisClient.hget(finalKey, field);

      if (value === null) {
        this.updateStats('miss');
        return null;
      }

      this.updateStats('hit');
      return this.deserializeValue<T>(value, options.serialize !== false);

    } catch (error) {
      this.updateStats('error');
      Logger.error('哈希字段获取失败', { key, field, error });
      return null;
    }
  }

  /**
   * 获取哈希所有字段
   */
  public async hgetall<T>(key: string, options: CacheOptions = {}): Promise<Record<string, T> | null> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const hash = await this.redisClient.hgetall(finalKey);

      if (!hash || Object.keys(hash).length === 0) {
        this.updateStats('miss');
        return null;
      }

      this.updateStats('hit');

      const result: Record<string, T> = {};
      for (const [field, value] of Object.entries(hash)) {
        result[field] = this.deserializeValue<T>(value, options.serialize !== false);
      }

      return result;

    } catch (error) {
      this.updateStats('error');
      Logger.error('哈希获取失败', { key, error });
      return null;
    }
  }

  /**
   * 删除哈希字段
   */
  public async hdel(key: string, fields: string | string[], options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const fieldsArray = Array.isArray(fields) ? fields : [fields];

      await this.redisClient.hdel(finalKey, ...fieldsArray);
      this.updateStats('delete');

      Logger.debug('哈希字段删除成功', { key: finalKey, fields: fieldsArray });

    } catch (error) {
      this.updateStats('error');
      Logger.error('哈希字段删除失败', { key, fields, error });
      throw error;
    }
  }

  /**
   * 向列表左侧推入元素
   */
  public async lpush(key: string, values: any[], options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const serializedValues = values.map(value => this.serializeValue(value, options.serialize !== false));

      await this.redisClient.lpush(finalKey, ...serializedValues);

      if (options.ttl) {
        await this.redisClient.expire(finalKey, options.ttl);
      }

      this.updateStats('set');

      Logger.debug('列表左侧推入成功', { key: finalKey, count: values.length });

    } catch (error) {
      this.updateStats('error');
      Logger.error('列表左侧推入失败', { key, values, error });
      throw error;
    }
  }

  /**
   * 向列表右侧推入元素
   */
  public async rpush(key: string, values: any[], options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const serializedValues = values.map(value => this.serializeValue(value, options.serialize !== false));

      await this.redisClient.rpush(finalKey, ...serializedValues);

      if (options.ttl) {
        await this.redisClient.expire(finalKey, options.ttl);
      }

      this.updateStats('set');

      Logger.debug('列表右侧推入成功', { key: finalKey, count: values.length });

    } catch (error) {
      this.updateStats('error');
      Logger.error('列表右侧推入失败', { key, values, error });
      throw error;
    }
  }

  /**
   * 从列表左侧弹出元素
   */
  public async lpop<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const value = await this.redisClient.lpop(finalKey);

      if (value === null) {
        this.updateStats('miss');
        return null;
      }

      this.updateStats('hit');
      return this.deserializeValue<T>(value, options.serialize !== false);

    } catch (error) {
      this.updateStats('error');
      Logger.error('列表左侧弹出失败', { key, error });
      return null;
    }
  }

  /**
   * 从列表右侧弹出元素
   */
  public async rpop<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const value = await this.redisClient.rpop(finalKey);

      if (value === null) {
        this.updateStats('miss');
        return null;
      }

      this.updateStats('hit');
      return this.deserializeValue<T>(value, options.serialize !== false);

    } catch (error) {
      this.updateStats('error');
      Logger.error('列表右侧弹出失败', { key, error });
      return null;
    }
  }

  /**
   * 获取列表范围内的元素
   */
  public async lrange<T>(key: string, start: number, stop: number, options: CacheOptions = {}): Promise<T[]> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const values = await this.redisClient.lrange(finalKey, start, stop);

      if (values.length === 0) {
        this.updateStats('miss');
        return [];
      }

      this.updateStats('hit');
      return values.map((value: string) => this.deserializeValue<T>(value, options.serialize !== false));

    } catch (error) {
      this.updateStats('error');
      Logger.error('列表范围获取失败', { key, start, stop, error });
      return [];
    }
  }

  /**
   * 获取列表长度
   */
  public async llen(key: string, options: CacheOptions = {}): Promise<number> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const length = await this.redisClient.llen(finalKey);

      return length;

    } catch (error) {
      this.updateStats('error');
      Logger.error('列表长度获取失败', { key, error });
      return 0;
    }
  }

  /**
   * 向集合添加元素
   */
  public async sadd(key: string, members: any[], options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const serializedMembers = members.map(member => this.serializeValue(member, options.serialize !== false));

      await this.redisClient.sadd(finalKey, ...serializedMembers);

      if (options.ttl) {
        await this.redisClient.expire(finalKey, options.ttl);
      }

      this.updateStats('set');

      Logger.debug('集合添加成功', { key: finalKey, count: members.length });

    } catch (error) {
      this.updateStats('error');
      Logger.error('集合添加失败', { key, members, error });
      throw error;
    }
  }

  /**
   * 获取集合所有成员
   */
  public async smembers<T>(key: string, options: CacheOptions = {}): Promise<T[]> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const members = await this.redisClient.smembers(finalKey);

      if (members.length === 0) {
        this.updateStats('miss');
        return [];
      }

      this.updateStats('hit');
      return members.map((member: string) => this.deserializeValue<T>(member, options.serialize !== false));

    } catch (error) {
      this.updateStats('error');
      Logger.error('集合成员获取失败', { key, error });
      return [];
    }
  }

  /**
   * 检查集合成员是否存在
   */
  public async sismember(key: string, member: any, options: CacheOptions = {}): Promise<boolean> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const serializedMember = this.serializeValue(member, options.serialize !== false);

      const result = await this.redisClient.sismember(finalKey, serializedMember);
      return result === 1;

    } catch (error) {
      this.updateStats('error');
      Logger.error('集合成员检查失败', { key, member, error });
      return false;
    }
  }

  /**
   * 从集合移除元素
   */
  public async srem(key: string, members: any[], options: CacheOptions = {}): Promise<void> {
    try {
      this.ensureConnection();

      const finalKey = this.generateKey(key, options.prefix);
      const serializedMembers = members.map(member => this.serializeValue(member, options.serialize !== false));

      await this.redisClient.srem(finalKey, ...serializedMembers);
      this.updateStats('delete');

      Logger.debug('集合移除成功', { key: finalKey, count: members.length });

    } catch (error) {
      this.updateStats('error');
      Logger.error('集合移除失败', { key, members, error });
      throw error;
    }
  }

  /**
   * 获取匹配模式的所有键
   */
  public async keys(pattern: string, options: CacheOptions = {}): Promise<string[]> {
    try {
      this.ensureConnection();

      const finalPattern = this.generateKey(pattern, options.prefix);
      const keys = await this.redisClient.keys(finalPattern);

      // 移除前缀
      const prefix = options.prefix || process.env['CACHE_PREFIX'] || 'cache:';
      return keys.map((key: string) => key.replace(prefix, ''));

    } catch (error) {
      this.updateStats('error');
      Logger.error('键模式匹配失败', { pattern, error });
      return [];
    }
  }

  /**
   * 清空所有缓存
   */
  public async flushall(): Promise<void> {
    try {
      this.ensureConnection();

      await this.redisClient.flushall();

      // 重置统计信息
      this.stats = {
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        errors: 0,
        hitRate: 0,
        totalOperations: 0,
      };

      Logger.info('所有缓存已清空');

    } catch (error) {
      this.updateStats('error');
      Logger.error('清空缓存失败', error);
      throw error;
    }
  }

  /**
   * 获取缓存统计信息
   */
  public getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  public resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      hitRate: 0,
      totalOperations: 0,
    };

    Logger.info('缓存统计信息已重置');
  }

  /**
   * 获取缓存信息
   */
  public async getInfo(): Promise<any> {
    try {
      this.ensureConnection();

      const info = {
        stats: this.getStats(),
        useRealRedis: this.useRealRedis,
        connected: !!this.redisClient,
      };

      if (this.useRealRedis && this.redisClient) {
        const realRedisConfig = RealRedisConfig.getInstance();
        info['redisInfo'] = await realRedisConfig.getRedisInfo();
      }

      return info;

    } catch (error) {
      Logger.error('获取缓存信息失败', error);
      throw error;
    }
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<boolean> {
    try {
      this.ensureConnection();

      const testKey = 'health_check';
      const testValue = 'ok';

      await this.set(testKey, testValue, { ttl: 1 });
      const result = await this.get(testKey);
      await this.del(testKey);

      return result === testValue;

    } catch (error) {
      Logger.error('缓存健康检查失败', error);
      return false;
    }
  }

  /**
   * 缓存预热
   */
  public async warmup(data: Record<string, any>, options: CacheOptions = {}): Promise<void> {
    try {
      Logger.info('开始缓存预热', { count: Object.keys(data).length });

      await this.mset(data, options);

      Logger.info('缓存预热完成', { count: Object.keys(data).length });

    } catch (error) {
      Logger.error('缓存预热失败', { data, error });
      throw error;
    }
  }

  /**
   * 缓存穿透保护（空值缓存）
   */
  public async getWithNullCache<T>(
    key: string,
    fetcher: () => Promise<T | null>,
    options: CacheOptions = {}
  ): Promise<T | null> {
    try {
      // 先检查缓存
      const cached = await this.get<T | string>(key, options);

      if (cached === 'NULL_VALUE') {
        // 空值缓存命中
        return null;
      }

      if (cached !== null) {
        return cached as T;
      }

      // 执行获取函数
      const value = await fetcher();

      if (value === null) {
        // 缓存空值，防止缓存穿透
        await this.set(key, 'NULL_VALUE', { ...options, ttl: options.ttl || 300 }); // 5分钟
      } else {
        await this.set(key, value, options);
      }

      return value;

    } catch (error) {
      Logger.error('缓存穿透保护失败', { key, error });
      throw error;
    }
  }

  /**
   * 分布式锁
   */
  public async lock(key: string, ttl: number = 30): Promise<string | null> {
    try {
      this.ensureConnection();

      const lockKey = this.generateKey(`lock:${key}`);
      const lockValue = `${Date.now()}-${Math.random()}`;

      const result = await this.redisClient.set(lockKey, lockValue, 'PX', ttl * 1000, 'NX');

      if (result === 'OK') {
        Logger.debug('分布式锁获取成功', { key: lockKey, ttl });
        return lockValue;
      }

      return null;

    } catch (error) {
      Logger.error('分布式锁获取失败', { key, error });
      return null;
    }
  }

  /**
   * 释放分布式锁
   */
  public async unlock(key: string, lockValue: string): Promise<boolean> {
    try {
      this.ensureConnection();

      const lockKey = this.generateKey(`lock:${key}`);

      // Lua脚本确保原子性
      const luaScript = `
        if redis.call("get", KEYS[1]) == ARGV[1] then
          return redis.call("del", KEYS[1])
        else
          return 0
        end
      `;

      const result = await this.redisClient.eval(luaScript, 1, lockKey, lockValue);

      const unlocked = result === 1;
      Logger.debug('分布式锁释放', { key: lockKey, unlocked });

      return unlocked;

    } catch (error) {
      Logger.error('分布式锁释放失败', { key, error });
      return false;
    }
  }
}
