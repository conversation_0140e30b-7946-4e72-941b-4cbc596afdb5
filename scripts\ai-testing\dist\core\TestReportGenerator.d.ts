/**
 * 测试报告生成器 - 智能测试报告生成和分析
 * 生成详细的测试报告、趋势分析和改进建议
 */
import { TestReport, TestSummary } from '../types';
export interface CoverageInfo {
    lines: number;
    functions: number;
    branches: number;
    statements: number;
}
export interface PerformanceSummary {
    averageExecutionTime: number;
    slowestTest: TestPerformance;
    fastestTest: TestPerformance;
    memoryUsage: MemoryUsage;
}
export interface TestPerformance {
    testName: string;
    executionTime: number;
    memoryUsed: number;
}
export interface MemoryUsage {
    peak: number;
    average: number;
    leaks: MemoryLeak[];
}
export interface MemoryLeak {
    testName: string;
    leakSize: number;
    description: string;
}
export interface TestDetails {
    testResults: TestResult[];
    failureAnalysis: FailureAnalysis[];
    performanceBreakdown: PerformanceBreakdown[];
}
export interface TestResult {
    id: string;
    name: string;
    status: 'passed' | 'failed' | 'skipped';
    executionTime: number;
    errorMessage?: string;
    stackTrace?: string;
    assertions: AssertionResult[];
}
export interface AssertionResult {
    description: string;
    passed: boolean;
    expected: any;
    actual: any;
}
export interface FailureAnalysis {
    testName: string;
    failureType: 'assertion' | 'timeout' | 'error' | 'exception';
    rootCause: string;
    suggestedFix: string;
    relatedTests: string[];
}
export interface PerformanceBreakdown {
    category: string;
    tests: TestPerformance[];
    averageTime: number;
    bottlenecks: string[];
}
export interface TestAnalysis {
    qualityScore: number;
    riskAreas: RiskArea[];
    patterns: TestPattern[];
    insights: string[];
}
export interface RiskArea {
    area: string;
    riskLevel: 'low' | 'medium' | 'high';
    description: string;
    affectedTests: string[];
    mitigation: string;
}
export interface TestPattern {
    pattern: string;
    frequency: number;
    impact: 'positive' | 'negative' | 'neutral';
    description: string;
}
export interface TrendAnalysis {
    historicalData: HistoricalDataPoint[];
    trends: Trend[];
    predictions: Prediction[];
}
export interface HistoricalDataPoint {
    date: string;
    successRate: number;
    executionTime: number;
    testCount: number;
}
export interface Trend {
    metric: string;
    direction: 'improving' | 'declining' | 'stable';
    changeRate: number;
    significance: 'low' | 'medium' | 'high';
}
export interface Prediction {
    metric: string;
    predictedValue: number;
    confidence: number;
    timeframe: string;
}
export declare class TestReportGenerator {
    private reportHistory;
    private templates;
    constructor();
    /**
     * 生成完整的测试报告
     */
    generateReport(testResults: TestResult[], options?: ReportGenerationOptions): Promise<TestReport>;
    /**
     * 生成HTML格式报告
     */
    generateHTMLReport(report: TestReport, outputPath: string): Promise<void>;
    /**
     * 生成JSON格式报告
     */
    generateJSONReport(report: TestReport, outputPath: string): Promise<void>;
    /**
     * 生成摘要报告
     */
    generateSummary(testResults: TestResult[]): TestSummary;
    /**
     * 计算测试摘要
     */
    private calculateTestSummary;
    /**
     * 分析测试详情
     */
    private analyzeTestDetails;
    /**
     * 分析测试失败
     */
    private analyzeFailures;
    /**
     * 分析性能
     */
    private analyzePerformance;
    /**
     * 执行深度分析
     */
    private performTestAnalysis;
    /**
     * 生成改进建议
     */
    private generateRecommendations;
    /**
     * 分析趋势
     */
    private analyzeTrends;
    /**
     * 生成HTML内容
     */
    private generateHTMLContent;
    private generateMetadata;
    private categorizeFailure;
    private identifyRootCause;
    private suggestFix;
    private findRelatedTests;
    private categorizeTestsByPerformance;
    private identifyBottlenecks;
    private calculateQualityScore;
    private identifyRiskAreas;
    private identifyTestPatterns;
    private generateInsights;
    private cacheReport;
    private initializeTemplates;
}
export interface ReportGenerationOptions {
    projectName?: string;
    projectPath?: string;
    testFramework?: string;
    includeDetails?: boolean;
    includeTrends?: boolean;
}
//# sourceMappingURL=TestReportGenerator.d.ts.map