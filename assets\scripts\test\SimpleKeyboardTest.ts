import { _decorator, Component, input, Input, EventKeyboard, KeyCode } from 'cc';

const { ccclass } = _decorator;

@ccclass('SimpleKeyboardTest')
export class SimpleKeyboardTest extends Component {
    onLoad() {
        console.log('🧪 简化键盘测试开始');
        console.log('💡 请确保游戏窗口有焦点，然后按T键测试');
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
    }

    private onKeyDown(event: EventKeyboard) {
        console.log(`⌨️ 按键检测成功: ${event.keyCode}`);
        
        switch (event.keyCode) {
            case KeyCode.KEY_T:
                console.log('🎉 T键测试成功！键盘输入正常工作！');
                break;
            case KeyCode.DIGIT_1:
                console.log('🎉 数字1键测试成功！');
                break;
            case KeyCode.KEY_H:
                console.log('📖 帮助: 按T键测试，按1键测试数字键');
                break;
            default:
                console.log(`⌨️ 其他按键测试: ${event.keyCode}`);
        }
    }

    onDestroy() {
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🧪 简化键盘测试结束');
    }
}