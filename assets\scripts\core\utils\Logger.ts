/**
 * 前端日志工具类
 * 提供统一的日志记录接口
 */
export enum LogLevel {
    DEBUG = 'debug',
    INFO = 'info',
    WARN = 'warn',
    ERROR = 'error'
}

export class Logger {
    private static logLevel: LogLevel = LogLevel.INFO;
    private static enableConsole: boolean = true;

    /**
     * 设置日志级别
     */
    public static setLogLevel(level: LogLevel): void {
        Logger.logLevel = level;
    }

    /**
     * 启用/禁用控制台输出
     */
    public static setConsoleEnabled(enabled: boolean): void {
        Logger.enableConsole = enabled;
    }

    /**
     * 检查是否应该输出指定级别的日志
     */
    private static shouldLog(level: LogLevel): boolean {
        if (!Logger.enableConsole) return false;

        const levels = [LogLevel.DEBUG, LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR];
        const currentLevelIndex = levels.indexOf(Logger.logLevel);
        const targetLevelIndex = levels.indexOf(level);
        
        return targetLevelIndex >= currentLevelIndex;
    }

    /**
     * 格式化日志消息
     */
    private static formatMessage(level: LogLevel, message: string, data?: any): string {
        const timestamp = new Date().toISOString();
        const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
        
        if (data) {
            return `${prefix} ${message} ${JSON.stringify(data)}`;
        }
        return `${prefix} ${message}`;
    }

    /**
     * 调试日志
     */
    public static debug(message: string, data?: any): void {
        if (Logger.shouldLog(LogLevel.DEBUG)) {
            console.debug(Logger.formatMessage(LogLevel.DEBUG, message, data));
        }
    }

    /**
     * 信息日志
     */
    public static info(message: string, data?: any): void {
        if (Logger.shouldLog(LogLevel.INFO)) {
            console.info(Logger.formatMessage(LogLevel.INFO, message, data));
        }
    }

    /**
     * 警告日志
     */
    public static warn(message: string, data?: any): void {
        if (Logger.shouldLog(LogLevel.WARN)) {
            console.warn(Logger.formatMessage(LogLevel.WARN, message, data));
        }
    }

    /**
     * 错误日志
     */
    public static error(message: string, error?: any): void {
        if (Logger.shouldLog(LogLevel.ERROR)) {
            let errorData: any = {};
            
            if (error instanceof Error) {
                errorData = {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                };
            } else if (error) {
                errorData = error;
            }
            
            console.error(Logger.formatMessage(LogLevel.ERROR, message, errorData));
        }
    }

    /**
     * 性能日志
     */
    public static performance(operation: string, duration: number, data?: any): void {
        const level = duration > 1000 ? LogLevel.WARN : LogLevel.INFO;
        const message = `性能监控: ${operation} (${duration}ms)`;
        
        if (level === LogLevel.WARN) {
            Logger.warn(message, data);
        } else {
            Logger.info(message, data);
        }
    }

    /**
     * 网络请求日志
     */
    public static network(method: string, url: string, status?: number, duration?: number, data?: any): void {
        const message = `网络请求: ${method} ${url}`;
        const logData = {
            method,
            url,
            status,
            duration,
            ...data
        };

        if (status && status >= 400) {
            Logger.error(message, logData);
        } else if (duration && duration > 2000) {
            Logger.warn(message, logData);
        } else {
            Logger.debug(message, logData);
        }
    }
}

// 在开发环境下启用调试日志
if (CC_DEV) {
    Logger.setLogLevel(LogLevel.DEBUG);
} else {
    Logger.setLogLevel(LogLevel.INFO);
}
