import { Request, Response, NextFunction } from 'express';
import { Character, ICharacter, CharacterClass, EntityType } from '../models/Character';
import { User } from '../models/User';
import { AppError, ErrorCodes } from '../utils/errors';
import { Logger } from '../utils/logger';
import { CacheManager } from '../utils/cache';
import { sendResponse, parsePaginationParams, calculatePagination, sendPaginatedResponse } from '../utils/response';

/**
 * 角色控制器
 */
export class CharacterController {
  private cacheManager: CacheManager;

  constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  /**
   * 创建角色
   */
  public createCharacter = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { name, characterClass, gender } = req.body;

      // 检查用户是否存在
      const user = await User.findById(userId);
      if (!user) {
        throw new AppError('用户不存在', 404, ErrorCodes.USER_NOT_FOUND);
      }

      // 检查角色名是否已存在
      const existingCharacter = await Character.findOne({ name });
      if (existingCharacter) {
        throw new AppError('角色名已存在', 409, ErrorCodes.CHARACTER_NAME_TAKEN);
      }

      // 检查用户角色数量限制
      const userCharacterCount = await Character.countDocuments({ 
        userId, 
        isActive: true 
      });
      
      const maxCharacters = 3; // 最多3个角色
      if (userCharacterCount >= maxCharacters) {
        throw new AppError(`最多只能创建${maxCharacters}个角色`, 400, ErrorCodes.INVALID_ACTION);
      }

      // 获取职业默认属性
      const defaultAttributes = this.getDefaultAttributesByClass(characterClass);

      // 创建角色
      const character = new Character({
        userId,
        name,
        class: characterClass,
        type: EntityType.PLAYER,
        level: 1,
        experience: 0,
        attributes: {
          ...defaultAttributes,
          currentHp: defaultAttributes.maxHp,
          currentMp: defaultAttributes.maxMp,
        },
        skills: [],
        skillPoints: 1,
        equipment: {},
        inventory: {
          items: [],
          maxSlots: 30,
        },
        location: {
          mapId: 'starter_area',
          x: 0,
          y: 0,
          z: 0,
        },
        battleStatus: {
          isInBattle: false,
          statusEffects: [],
        },
        statistics: {
          totalPlayTime: 0,
          monstersKilled: 0,
          itemsCollected: 0,
          questsCompleted: 0,
          deathCount: 0,
          goldEarned: 0,
        },
      });

      await character.save();

      // 更新用户游戏数据
      user.gameData.charactersCreated += 1;
      if (!user.gameData.currentCharacterId) {
        user.gameData.currentCharacterId = character._id;
      }
      await user.save();

      // 缓存角色信息
      await this.cacheManager.set(
        `character:${character._id}`,
        JSON.stringify(character.toObject()),
        3600 // 1小时
      );

      Logger.info('角色创建成功', {
        userId,
        characterId: character._id,
        characterName: character.name,
        characterClass: character.class,
      });

      sendResponse(res, 201, '角色创建成功', {
        character: character.toObject(),
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取用户角色列表
   */
  public getCharacters = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { page, limit } = parsePaginationParams(req.query);

      // 查询角色列表
      const characters = await Character.find({ 
        userId, 
        isActive: true 
      })
        .sort({ createdAt: -1 })
        .skip((page - 1) * limit)
        .limit(limit);

      // 获取总数
      const total = await Character.countDocuments({ 
        userId, 
        isActive: true 
      });

      const pagination = calculatePagination(page, limit, total);

      sendPaginatedResponse(res, '获取角色列表成功', characters, pagination);
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取角色详情
   */
  public getCharacter = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId } = req.params;

      // 先从缓存获取
      const cachedCharacter = await this.cacheManager.get(`character:${characterId}`);
      if (cachedCharacter) {
        const character = JSON.parse(cachedCharacter);
        if (character.userId === userId) {
          return sendResponse(res, 200, '获取角色详情成功', { character });
        }
      }

      // 从数据库获取
      const character = await Character.findOne({
        _id: characterId,
        userId,
        isActive: true,
      });

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 更新缓存
      await this.cacheManager.set(
        `character:${characterId}`,
        JSON.stringify(character.toObject()),
        3600
      );

      sendResponse(res, 200, '获取角色详情成功', {
        character: character.toObject(),
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 更新角色信息
   */
  public updateCharacter = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId } = req.params;
      const updates = req.body;

      // 过滤允许更新的字段
      const allowedUpdates = ['name'];
      const filteredUpdates: any = {};
      
      Object.keys(updates).forEach(key => {
        if (allowedUpdates.includes(key)) {
          filteredUpdates[key] = updates[key];
        }
      });

      // 如果更新角色名，检查是否重复
      if (filteredUpdates.name) {
        const existingCharacter = await Character.findOne({
          name: filteredUpdates.name,
          _id: { $ne: characterId },
        });
        
        if (existingCharacter) {
          throw new AppError('角色名已存在', 409, ErrorCodes.CHARACTER_NAME_TAKEN);
        }
      }

      const character = await Character.findOneAndUpdate(
        { _id: characterId, userId, isActive: true },
        { $set: filteredUpdates },
        { new: true, runValidators: true }
      );

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 更新缓存
      await this.cacheManager.set(
        `character:${characterId}`,
        JSON.stringify(character.toObject()),
        3600
      );

      Logger.info('角色信息更新成功', {
        userId,
        characterId,
        updates: Object.keys(filteredUpdates),
      });

      sendResponse(res, 200, '角色信息更新成功', {
        character: character.toObject(),
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 删除角色
   */
  public deleteCharacter = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId } = req.params;

      const character = await Character.findOneAndUpdate(
        { _id: characterId, userId, isActive: true },
        { $set: { isActive: false } },
        { new: true }
      );

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 如果删除的是当前角色，需要更新用户的当前角色
      const user = await User.findById(userId);
      if (user && user.gameData.currentCharacterId?.toString() === characterId) {
        // 查找其他活跃角色
        const otherCharacter = await Character.findOne({
          userId,
          isActive: true,
          _id: { $ne: characterId },
        });
        
        user.gameData.currentCharacterId = otherCharacter?._id || undefined;
        await user.save();
      }

      // 删除缓存
      await this.cacheManager.del(`character:${characterId}`);

      Logger.info('角色删除成功', {
        userId,
        characterId,
        characterName: character.name,
      });

      sendResponse(res, 200, '角色删除成功');
    } catch (error) {
      next(error);
    }
  };

  /**
   * 设置当前角色
   */
  public setCurrentCharacter = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId } = req.params;

      // 验证角色是否属于用户
      const character = await Character.findOne({
        _id: characterId,
        userId,
        isActive: true,
      });

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 更新用户当前角色
      const user = await User.findByIdAndUpdate(
        userId,
        { $set: { 'gameData.currentCharacterId': characterId } },
        { new: true }
      );

      if (!user) {
        throw new AppError('用户不存在', 404, ErrorCodes.USER_NOT_FOUND);
      }

      Logger.info('设置当前角色成功', {
        userId,
        characterId,
        characterName: character.name,
      });

      sendResponse(res, 200, '设置当前角色成功', {
        currentCharacterId: characterId,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 根据职业获取默认属性
   */
  private getDefaultAttributesByClass(characterClass: CharacterClass): any {
    const baseAttributes = {
      strength: 10,
      agility: 10,
      intelligence: 10,
      vitality: 10,
      spirit: 10,
      damage: 10,
      maxHp: 100,
      maxMp: 50,
      recoverHp: 0.02,
      recoverMp: 0.02,
      recoveryInterval: 1.0,
      atkInterval: 1.5,
      atkSpeed: 1.0,
      castSpeed: 1.0,
      def: 5,
      spellResistance: 5,
      meleeAccuracy: 0.1,
      rangedAccuracy: 0.05,
      magicAccuracy: 0.0,
      meleeDodge: 0.05,
      rangedDodge: 0.0,
      magicDodge: 0.0,
      penetrate: 0.1,
      criticalHit: 0.1,
      toughness: 0.2,
      enmity: 0.5,
      damageIncreasePhysical: 0.2,
    };

    switch (characterClass) {
      case CharacterClass.WARRIOR:
        return {
          ...baseAttributes,
          strength: 15,
          vitality: 15,
          agility: 8,
          intelligence: 5,
          spirit: 7,
          damage: 60,
          maxHp: 200,
          maxMp: 50,
          def: 15,
          spellResistance: 5,
        };
        
      case CharacterClass.MAGE:
        return {
          ...baseAttributes,
          intelligence: 15,
          spirit: 15,
          strength: 5,
          vitality: 8,
          agility: 7,
          damage: 25,
          maxHp: 120,
          maxMp: 150,
          def: 5,
          spellResistance: 15,
          magicAccuracy: 0.15,
        };
        
      case CharacterClass.ARCHER:
        return {
          ...baseAttributes,
          agility: 15,
          strength: 12,
          intelligence: 8,
          vitality: 10,
          spirit: 5,
          damage: 45,
          maxHp: 150,
          maxMp: 80,
          def: 8,
          spellResistance: 8,
          rangedAccuracy: 0.15,
        };
        
      case CharacterClass.ASSASSIN:
        return {
          ...baseAttributes,
          agility: 18,
          strength: 12,
          intelligence: 8,
          vitality: 7,
          spirit: 5,
          damage: 55,
          maxHp: 130,
          maxMp: 70,
          def: 6,
          spellResistance: 6,
          criticalHit: 0.25,
        };
        
      default:
        return baseAttributes;
    }
  }
}

export const characterController = new CharacterController();
