"use strict";
/**
 * 测试报告生成器 - 智能测试报告生成和分析
 * 生成详细的测试报告、趋势分析和改进建议
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestReportGenerator = void 0;
const fs = __importStar(require("fs"));
const path = __importStar(require("path"));
class TestReportGenerator {
    constructor() {
        this.reportHistory = new Map();
        this.templates = new Map();
        this.initializeTemplates();
    }
    /**
     * 生成完整的测试报告
     */
    async generateReport(testResults, options = {}) {
        console.log('📊 TestReportGenerator: Generating comprehensive test report');
        try {
            const startTime = Date.now();
            // 1. 生成报告元数据
            const metadata = this.generateMetadata(options);
            // 2. 计算测试摘要
            const summary = this.calculateTestSummary(testResults);
            // 3. 分析测试详情
            const details = this.analyzeTestDetails(testResults);
            // 4. 执行深度分析
            const analysis = await this.performTestAnalysis(testResults, summary);
            // 5. 生成改进建议
            const recommendations = this.generateRecommendations(analysis, summary);
            // 6. 分析趋势
            const trends = await this.analyzeTrends(summary);
            const report = {
                metadata: {
                    ...metadata,
                    totalExecutionTime: Date.now() - startTime
                },
                summary,
                details,
                analysis,
                recommendations,
                trends
            };
            // 7. 缓存报告用于趋势分析
            this.cacheReport(report);
            console.log(`✅ Test report generated in ${report.metadata.totalExecutionTime}ms`);
            return report;
        }
        catch (error) {
            console.error('❌ Failed to generate test report:', error);
            throw error;
        }
    }
    /**
     * 生成HTML格式报告
     */
    async generateHTMLReport(report, outputPath) {
        console.log(`📄 Generating HTML report: ${outputPath}`);
        const htmlContent = this.generateHTMLContent(report);
        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        fs.writeFileSync(outputPath, htmlContent);
        console.log(`✅ HTML report saved: ${outputPath}`);
    }
    /**
     * 生成JSON格式报告
     */
    async generateJSONReport(report, outputPath) {
        console.log(`📄 Generating JSON report: ${outputPath}`);
        const jsonContent = JSON.stringify(report, null, 2);
        // 确保输出目录存在
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        fs.writeFileSync(outputPath, jsonContent);
        console.log(`✅ JSON report saved: ${outputPath}`);
    }
    /**
     * 生成摘要报告
     */
    generateSummary(testResults) {
        return this.calculateTestSummary(testResults);
    }
    /**
     * 计算测试摘要
     */
    calculateTestSummary(testResults) {
        const totalTests = testResults.length;
        const passedTests = testResults.filter(t => t.status === 'passed').length;
        const failedTests = testResults.filter(t => t.status === 'failed').length;
        const skippedTests = testResults.filter(t => t.status === 'skipped').length;
        const successRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
        // 计算性能摘要
        const executionTimes = testResults.map(t => t.executionTime);
        const averageExecutionTime = executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length;
        const slowestTest = testResults.reduce((slowest, current) => current.executionTime > slowest.executionTime ? current : slowest);
        const fastestTest = testResults.reduce((fastest, current) => current.executionTime < fastest.executionTime ? current : fastest);
        return {
            totalTests,
            passedTests,
            failedTests,
            skippedTests,
            successRate: Math.round(successRate * 100) / 100,
            coverage: {
                lines: 85, // 模拟数据，实际需要从覆盖率工具获取
                functions: 90,
                branches: 80,
                statements: 88
            },
            performance: {
                averageExecutionTime: Math.round(averageExecutionTime),
                slowestTest: {
                    testName: slowestTest.name,
                    executionTime: slowestTest.executionTime,
                    memoryUsed: 0 // 模拟数据
                },
                fastestTest: {
                    testName: fastestTest.name,
                    executionTime: fastestTest.executionTime,
                    memoryUsed: 0 // 模拟数据
                },
                memoryUsage: {
                    peak: 50 * 1024 * 1024, // 50MB
                    average: 30 * 1024 * 1024, // 30MB
                    leaks: []
                }
            }
        };
    }
    /**
     * 分析测试详情
     */
    analyzeTestDetails(testResults) {
        const failureAnalysis = this.analyzeFailures(testResults);
        const performanceBreakdown = this.analyzePerformance(testResults);
        return {
            testResults,
            failureAnalysis,
            performanceBreakdown
        };
    }
    /**
     * 分析测试失败
     */
    analyzeFailures(testResults) {
        const failedTests = testResults.filter(t => t.status === 'failed');
        return failedTests.map(test => ({
            testName: test.name,
            failureType: this.categorizeFailure(test),
            rootCause: this.identifyRootCause(test),
            suggestedFix: this.suggestFix(test),
            relatedTests: this.findRelatedTests(test, testResults)
        }));
    }
    /**
     * 分析性能
     */
    analyzePerformance(testResults) {
        const categories = this.categorizeTestsByPerformance(testResults);
        return Object.entries(categories).map(([category, tests]) => ({
            category,
            tests: tests.map(t => ({
                testName: t.name,
                executionTime: t.executionTime,
                memoryUsed: 0 // 模拟数据
            })),
            averageTime: tests.reduce((sum, t) => sum + t.executionTime, 0) / tests.length,
            bottlenecks: this.identifyBottlenecks(tests)
        }));
    }
    /**
     * 执行深度分析
     */
    async performTestAnalysis(testResults, summary) {
        const qualityScore = this.calculateQualityScore(summary);
        const riskAreas = this.identifyRiskAreas(testResults, summary);
        const patterns = this.identifyTestPatterns(testResults);
        const insights = this.generateInsights(testResults, summary);
        return {
            qualityScore,
            riskAreas,
            patterns,
            insights
        };
    }
    /**
     * 生成改进建议
     */
    generateRecommendations(analysis, summary) {
        const recommendations = [];
        if (summary.successRate < 90) {
            recommendations.push('Improve test success rate by addressing failing tests');
        }
        if (summary.performance.averageExecutionTime > 1000) {
            recommendations.push('Optimize test execution time - consider parallel execution');
        }
        if (summary.coverage.lines < 80) {
            recommendations.push('Increase code coverage by adding more comprehensive tests');
        }
        for (const riskArea of analysis.riskAreas) {
            if (riskArea.riskLevel === 'high') {
                recommendations.push(`Address high-risk area: ${riskArea.area} - ${riskArea.mitigation}`);
            }
        }
        return recommendations;
    }
    /**
     * 分析趋势
     */
    async analyzeTrends(summary) {
        // 模拟历史数据
        const historicalData = [
            { date: '2024-01-01', successRate: 85, executionTime: 1200, testCount: 50 },
            { date: '2024-01-02', successRate: 88, executionTime: 1100, testCount: 55 },
            { date: '2024-01-03', successRate: summary.successRate, executionTime: summary.performance.averageExecutionTime, testCount: summary.totalTests }
        ];
        const trends = [
            {
                metric: 'Success Rate',
                direction: 'improving',
                changeRate: 3.5,
                significance: 'medium'
            },
            {
                metric: 'Execution Time',
                direction: 'improving',
                changeRate: -8.3,
                significance: 'high'
            }
        ];
        const predictions = [
            {
                metric: 'Success Rate',
                predictedValue: 92,
                confidence: 0.85,
                timeframe: '1 week'
            }
        ];
        return {
            historicalData,
            trends,
            predictions
        };
    }
    /**
     * 生成HTML内容
     */
    generateHTMLContent(report) {
        return `
<!DOCTYPE html>
<html>
<head>
    <title>AI Testing Framework Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .summary { display: flex; gap: 20px; margin: 20px 0; }
        .metric { background: #e8f4f8; padding: 15px; border-radius: 5px; flex: 1; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI Testing Framework Report</h1>
        <p>Generated: ${report.metadata.generatedAt}</p>
        <p>Project: ${report.metadata.projectName}</p>
    </div>
    
    <div class="summary">
        <div class="metric">
            <h3>Total Tests</h3>
            <p>${report.summary.totalTests}</p>
        </div>
        <div class="metric">
            <h3>Success Rate</h3>
            <p class="${report.summary.successRate >= 90 ? 'success' : report.summary.successRate >= 70 ? 'warning' : 'danger'}">${report.summary.successRate}%</p>
        </div>
        <div class="metric">
            <h3>Avg Execution Time</h3>
            <p>${report.summary.performance.averageExecutionTime}ms</p>
        </div>
        <div class="metric">
            <h3>Quality Score</h3>
            <p>${report.analysis.qualityScore}/100</p>
        </div>
    </div>
    
    <h2>Recommendations</h2>
    <ul>
        ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
    </ul>
    
    <h2>Test Results</h2>
    <table>
        <tr>
            <th>Test Name</th>
            <th>Status</th>
            <th>Execution Time</th>
        </tr>
        ${report.details.testResults.map(test => `
            <tr>
                <td>${test.name}</td>
                <td class="${test.status === 'passed' ? 'success' : test.status === 'failed' ? 'danger' : 'warning'}">${test.status}</td>
                <td>${test.executionTime}ms</td>
            </tr>
        `).join('')}
    </table>
</body>
</html>`;
    }
    // 辅助方法
    generateMetadata(options) {
        return {
            generatedAt: new Date().toISOString(),
            projectName: options.projectName || 'Unknown Project',
            projectPath: options.projectPath || process.cwd(),
            reportVersion: '1.0.0',
            testFramework: options.testFramework || 'jest',
            totalExecutionTime: 0
        };
    }
    categorizeFailure(test) {
        if (test.errorMessage?.includes('timeout'))
            return 'timeout';
        if (test.errorMessage?.includes('AssertionError'))
            return 'assertion';
        if (test.errorMessage?.includes('Error'))
            return 'error';
        return 'exception';
    }
    identifyRootCause(test) {
        return test.errorMessage || 'Unknown error';
    }
    suggestFix(test) {
        return 'Review test implementation and fix the identified issue';
    }
    findRelatedTests(test, allTests) {
        return allTests
            .filter(t => t.name.includes(test.name.split(' ')[0]) && t.id !== test.id)
            .map(t => t.name);
    }
    categorizeTestsByPerformance(testResults) {
        return {
            'Fast Tests': testResults.filter(t => t.executionTime < 100),
            'Medium Tests': testResults.filter(t => t.executionTime >= 100 && t.executionTime < 1000),
            'Slow Tests': testResults.filter(t => t.executionTime >= 1000)
        };
    }
    identifyBottlenecks(tests) {
        return tests
            .filter(t => t.executionTime > 1000)
            .map(t => `${t.name} (${t.executionTime}ms)`);
    }
    calculateQualityScore(summary) {
        let score = 0;
        // 成功率权重 40%
        score += (summary.successRate / 100) * 40;
        // 覆盖率权重 30%
        const avgCoverage = (summary.coverage.lines + summary.coverage.functions + summary.coverage.branches + summary.coverage.statements) / 4;
        score += (avgCoverage / 100) * 30;
        // 性能权重 20%
        const performanceScore = summary.performance.averageExecutionTime < 500 ? 100 : Math.max(0, 100 - (summary.performance.averageExecutionTime - 500) / 10);
        score += (performanceScore / 100) * 20;
        // 测试数量权重 10%
        const testCountScore = Math.min(100, summary.totalTests * 2);
        score += (testCountScore / 100) * 10;
        return Math.round(score);
    }
    identifyRiskAreas(testResults, summary) {
        const riskAreas = [];
        if (summary.successRate < 70) {
            riskAreas.push({
                area: 'Test Reliability',
                riskLevel: 'high',
                description: 'Low test success rate indicates reliability issues',
                affectedTests: testResults.filter(t => t.status === 'failed').map(t => t.name),
                mitigation: 'Review and fix failing tests'
            });
        }
        return riskAreas;
    }
    identifyTestPatterns(testResults) {
        return [
            {
                pattern: 'Long execution times',
                frequency: testResults.filter(t => t.executionTime > 1000).length,
                impact: 'negative',
                description: 'Tests taking longer than 1 second'
            }
        ];
    }
    generateInsights(testResults, summary) {
        const insights = [];
        if (summary.successRate > 95) {
            insights.push('Excellent test reliability - maintain current quality standards');
        }
        if (summary.performance.averageExecutionTime < 200) {
            insights.push('Fast test execution enables rapid development feedback');
        }
        return insights;
    }
    cacheReport(report) {
        const key = report.metadata.generatedAt;
        this.reportHistory.set(key, report);
        // 保持最近10个报告
        if (this.reportHistory.size > 10) {
            const oldestKey = this.reportHistory.keys().next().value;
            this.reportHistory.delete(oldestKey);
        }
    }
    initializeTemplates() {
        // 初始化报告模板
        console.log('🔧 Initializing report templates');
    }
}
exports.TestReportGenerator = TestReportGenerator;
//# sourceMappingURL=TestReportGenerator.js.map