/**
 * 游戏核心类型定义
 * 武侠放置游戏 - Cocos Creator版本
 */

// 游戏状态枚举
export enum GameState {
    LOADING = 'loading',
    MENU = 'menu',
    PLAYING = 'playing',
    PAUSED = 'paused',
    BATTLE = 'battle',
    SETTINGS = 'settings'
}

// 场景类型枚举
export enum SceneType {
    LAUNCH = 'Launch',
    MAIN = 'Main',
    BATTLE = 'Battle',
    SECT_HALL = 'SectHall',
    MARKET = 'Market'
}

// 过渡效果类型
export enum TransitionType {
    FADE = 'fade',
    SLIDE_LEFT = 'slideLeft',
    SLIDE_RIGHT = 'slideRight',
    SLIDE_UP = 'slideUp',
    SLIDE_DOWN = 'slideDown'
}

// 网络状态枚举
export enum NetworkType {
    WIFI = 'wifi',
    CELLULAR = '2g/3g/4g/5g',
    NONE = 'none',
    UNKNOWN = 'unknown'
}

// 连接状态枚举
export enum ConnectionState {
    DISCONNECTED = 'disconnected',
    CONNECTING = 'connecting',
    CONNECTED = 'connected',
    RECONNECTING = 'reconnecting',
    ERROR = 'error'
}

// 滑动方向枚举
export enum SwipeDirection {
    UP = 'up',
    DOWN = 'down',
    LEFT = 'left',
    RIGHT = 'right'
}

// 武侠相关类型
export enum SectType {
    SHAOLIN = 'shaolin',      // 少林派
    WUDANG = 'wudang',        // 武当派
    EMEI = 'emei',            // 峨眉派
    HUASHAN = 'huashan',      // 华山派
    BEGGAR = 'beggar'         // 丐帮
}

export enum SkillType {
    ATTACK = 'attack',        // 攻击技能
    DEFENSE = 'defense',      // 防御技能
    HEAL = 'heal',           // 治疗技能
    BUFF = 'buff',           // 增益技能
    DEBUFF = 'debuff'        // 减益技能
}

export enum ItemType {
    WEAPON = 'weapon',        // 武器
    ARMOR = 'armor',          // 防具
    ACCESSORY = 'accessory',  // 饰品
    CONSUMABLE = 'consumable', // 消耗品
    MATERIAL = 'material'     // 材料
}

// 基础接口定义
export interface IGameConfig {
    version: string;
    debug: boolean;
    maxLevel: number;
    autoSaveInterval: number;
}

export interface ISceneData {
    sceneName: string;
    sceneType: SceneType;
    data?: any;
}

export interface INetworkRequest {
    id: string;
    url: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE';
    data?: any;
    headers?: Record<string, string>;
    timeout?: number;
}

export interface INetworkError {
    code: number;
    message: string;
    details?: any;
}

// 事件相关接口
export interface IEventData {
    type: string;
    data?: any;
    timestamp: number;
}

// 武侠游戏相关接口
export interface IPlayerData {
    id: string;
    name: string;
    level: number;
    experience: number;
    sect: SectType;
    skills: ISkillData[];
    items: IItemData[];
    stats: IPlayerStats;
}

export interface ISkillData {
    id: string;
    name: string;
    type: SkillType;
    level: number;
    damage?: number;
    cooldown: number;
    description: string;
}

export interface IItemData {
    id: string;
    name: string;
    type: ItemType;
    quality: number;
    quantity: number;
    description: string;
}

export interface IPlayerStats {
    health: number;
    maxHealth: number;
    attack: number;
    defense: number;
    speed: number;
    criticalRate: number;
}

// 回调函数类型
export type EventCallback = (...args: any[]) => void;
export type AsyncEventCallback = (...args: any[]) => Promise<void>;
export type RequestInterceptor = (request: INetworkRequest) => INetworkRequest;
export type ResponseInterceptor = (response: any) => any;
