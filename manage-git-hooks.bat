@echo off
setlocal

set HOOKS_DIR=.git\hooks

if "%1"=="enable" (
    echo 🔧 启用Git hooks...
    if exist "%HOOKS_DIR%\pre-commit.backup" (
        copy "%HOOKS_DIR%\pre-commit.backup" "%HOOKS_DIR%\pre-commit" >nul
        echo ✅ Git hooks已启用
    ) else (
        echo ❌ 没有找到备份的hook文件
    )
    goto :end
)

if "%1"=="disable" (
    echo 🔧 禁用Git hooks...
    if exist "%HOOKS_DIR%\pre-commit" (
        copy "%HOOKS_DIR%\pre-commit" "%HOOKS_DIR%\pre-commit.backup" >nul
        copy "%HOOKS_DIR%\pre-commit-disabled" "%HOOKS_DIR%\pre-commit" >nul
        echo ✅ Git hooks已禁用
    ) else (
        echo ℹ️ Git hooks已经是禁用状态
    )
    goto :end
)

if "%1"=="status" (
    echo 📋 Git hooks状态:
    if exist "%HOOKS_DIR%\pre-commit" (
        echo   • pre-commit hook: 存在
    ) else (
        echo   • pre-commit hook: 不存在
    )
    if exist "%HOOKS_DIR%\pre-commit.backup" (
        echo   • 备份文件: 存在
    ) else (
        echo   • 备份文件: 不存在
    )
    goto :end
)

echo 用法: %0 {enable^|disable^|status}
echo.
echo 命令说明:
echo   enable   - 启用Git hooks
echo   disable  - 禁用Git hooks
echo   status   - 查看Git hooks状态
echo.
echo 示例:
echo   %0 disable    # 禁用Git hooks
echo   %0 enable     # 启用Git hooks
echo   %0 status     # 查看状态

:end
