import { _decorator, Component, input, Input, EventTouch, EventKeyboard, KeyCode, Touch, Vec2 } from 'cc';
import { BaseManager } from './BaseManager';

const { ccclass } = _decorator;

/**
 * 滑动方向枚举
 */
export enum SwipeDirection {
    UP = 'up',
    DOWN = 'down',
    LEFT = 'left',
    RIGHT = 'right'
}

/**
 * 触摸事件数据接口
 */
export interface ITouchEventData {
    touch: Touch;
    position: Vec2;
    deltaPosition: Vec2;
    timestamp: number;
}

/**
 * 手势事件数据接口
 */
export interface IGestureEventData {
    direction?: SwipeDirection;
    scale?: number;
    distance?: number;
    velocity?: number;
    timestamp: number;
}

/**
 * 输入管理器
 * 提供触摸输入、键盘输入、手势识别等功能
 */
@ccclass('InputManager')
export class InputManager extends BaseManager {
    private static _instance: InputManager;
    
    // 触摸事件回调
    private _touchStartCallbacks: ((data: ITouchEventData) => void)[] = [];
    private _touchMoveCallbacks: ((data: ITouchEventData) => void)[] = [];
    private _touchEndCallbacks: ((data: ITouchEventData) => void)[] = [];
    
    // 键盘事件回调
    private _keyDownCallbacks: Map<string, (() => void)[]> = new Map();
    private _keyUpCallbacks: Map<string, (() => void)[]> = new Map();
    
    // 手势事件回调
    private _swipeCallbacks: ((data: IGestureEventData) => void)[] = [];
    private _pinchCallbacks: ((data: IGestureEventData) => void)[] = [];
    
    // 触摸状态
    private _touchStartTime: number = 0;
    private _touchStartPosition: Vec2 = new Vec2();
    private _lastTouchPosition: Vec2 = new Vec2();
    private _touchCount: number = 0;
    private _lastTouchDistance: number = 0;
    
    // 手势识别参数
    private _swipeThreshold: number = 50; // 滑动最小距离
    private _swipeTimeThreshold: number = 500; // 滑动最大时间
    private _pinchThreshold: number = 10; // 缩放最小距离变化

    /**
     * 获取单例实例
     */
    public static getInstance(): InputManager {
        if (!InputManager._instance) {
            InputManager._instance = new InputManager();
        }
        return InputManager._instance;
    }

    /**
     * 初始化管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('🎮 输入管理器初始化开始...');
        
        try {
            this._setupTouchEvents();
            this._setupKeyboardEvents();
            
            console.log('✅ 输入管理器初始化完成');
        } catch (error) {
            console.error('❌ 输入管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁管理器
     */
    public destroyManager(): void {
        console.log('🗑️ 输入管理器销毁开始...');
        
        this._removeTouchEvents();
        this._removeKeyboardEvents();
        this._clearAllCallbacks();
        
        console.log('✅ 输入管理器销毁完成');
    }

    /**
     * 注册触摸开始事件
     */
    public onTouchStart(callback: (data: ITouchEventData) => void): void {
        this._touchStartCallbacks.push(callback);
    }

    /**
     * 注册触摸移动事件
     */
    public onTouchMove(callback: (data: ITouchEventData) => void): void {
        this._touchMoveCallbacks.push(callback);
    }

    /**
     * 注册触摸结束事件
     */
    public onTouchEnd(callback: (data: ITouchEventData) => void): void {
        this._touchEndCallbacks.push(callback);
    }

    /**
     * 注册键盘按下事件
     */
    public onKeyDown(key: string, callback: () => void): void {
        if (!this._keyDownCallbacks.has(key)) {
            this._keyDownCallbacks.set(key, []);
        }
        this._keyDownCallbacks.get(key)!.push(callback);
    }

    /**
     * 注册键盘抬起事件
     */
    public onKeyUp(key: string, callback: () => void): void {
        if (!this._keyUpCallbacks.has(key)) {
            this._keyUpCallbacks.set(key, []);
        }
        this._keyUpCallbacks.get(key)!.push(callback);
    }

    /**
     * 注册滑动手势事件
     */
    public onSwipe(callback: (data: IGestureEventData) => void): void {
        this._swipeCallbacks.push(callback);
    }

    /**
     * 注册缩放手势事件
     */
    public onPinch(callback: (data: IGestureEventData) => void): void {
        this._pinchCallbacks.push(callback);
    }

    /**
     * 移除触摸事件回调
     */
    public offTouchStart(callback: (data: ITouchEventData) => void): void {
        const index = this._touchStartCallbacks.indexOf(callback);
        if (index > -1) {
            this._touchStartCallbacks.splice(index, 1);
        }
    }

    /**
     * 移除键盘事件回调
     */
    public offKeyDown(key: string, callback: () => void): void {
        const callbacks = this._keyDownCallbacks.get(key);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * 设置手势识别参数
     */
    public setGestureThresholds(swipeThreshold?: number, swipeTimeThreshold?: number, pinchThreshold?: number): void {
        if (swipeThreshold !== undefined) this._swipeThreshold = swipeThreshold;
        if (swipeTimeThreshold !== undefined) this._swipeTimeThreshold = swipeTimeThreshold;
        if (pinchThreshold !== undefined) this._pinchThreshold = pinchThreshold;
        
        console.log('🎮 手势识别参数已更新:', {
            swipeThreshold: this._swipeThreshold,
            swipeTimeThreshold: this._swipeTimeThreshold,
            pinchThreshold: this._pinchThreshold
        });
    }

    /**
     * 获取输入状态
     */
    public getInputStatus() {
        return {
            touchCount: this._touchCount,
            hasActiveTouch: this._touchCount > 0,
            registeredCallbacks: {
                touchStart: this._touchStartCallbacks.length,
                touchMove: this._touchMoveCallbacks.length,
                touchEnd: this._touchEndCallbacks.length,
                keyDown: this._keyDownCallbacks.size,
                keyUp: this._keyUpCallbacks.size,
                swipe: this._swipeCallbacks.length,
                pinch: this._pinchCallbacks.length
            }
        };
    }

    /**
     * 设置触摸事件监听
     */
    private _setupTouchEvents(): void {
        input.on(Input.EventType.TOUCH_START, this._onTouchStart, this);
        input.on(Input.EventType.TOUCH_MOVE, this._onTouchMove, this);
        input.on(Input.EventType.TOUCH_END, this._onTouchEnd, this);
        input.on(Input.EventType.TOUCH_CANCEL, this._onTouchEnd, this);
    }

    /**
     * 设置键盘事件监听
     */
    private _setupKeyboardEvents(): void {
        input.on(Input.EventType.KEY_DOWN, this._onKeyDown, this);
        input.on(Input.EventType.KEY_UP, this._onKeyUp, this);
    }

    /**
     * 移除触摸事件监听
     */
    private _removeTouchEvents(): void {
        input.off(Input.EventType.TOUCH_START, this._onTouchStart, this);
        input.off(Input.EventType.TOUCH_MOVE, this._onTouchMove, this);
        input.off(Input.EventType.TOUCH_END, this._onTouchEnd, this);
        input.off(Input.EventType.TOUCH_CANCEL, this._onTouchEnd, this);
    }

    /**
     * 移除键盘事件监听
     */
    private _removeKeyboardEvents(): void {
        input.off(Input.EventType.KEY_DOWN, this._onKeyDown, this);
        input.off(Input.EventType.KEY_UP, this._onKeyUp, this);
    }

    /**
     * 清理所有回调
     */
    private _clearAllCallbacks(): void {
        this._touchStartCallbacks = [];
        this._touchMoveCallbacks = [];
        this._touchEndCallbacks = [];
        this._keyDownCallbacks.clear();
        this._keyUpCallbacks.clear();
        this._swipeCallbacks = [];
        this._pinchCallbacks = [];
    }

    /**
     * 触摸开始事件处理
     */
    private _onTouchStart(event: EventTouch): void {
        const touch = event.touch!;
        const position = touch.getLocation();
        
        this._touchCount++;
        this._touchStartTime = Date.now();
        this._touchStartPosition.set(position);
        this._lastTouchPosition.set(position);

        const eventData: ITouchEventData = {
            touch,
            position: position.clone(),
            deltaPosition: new Vec2(0, 0),
            timestamp: this._touchStartTime
        };

        this._touchStartCallbacks.forEach(callback => {
            try {
                callback(eventData);
            } catch (error) {
                console.error('❌ 触摸开始回调执行失败:', error);
            }
        });
    }

    /**
     * 触摸移动事件处理
     */
    private _onTouchMove(event: EventTouch): void {
        const touch = event.touch!;
        const position = touch.getLocation();
        const deltaPosition = position.clone().subtract(this._lastTouchPosition);
        
        this._lastTouchPosition.set(position);

        const eventData: ITouchEventData = {
            touch,
            position: position.clone(),
            deltaPosition,
            timestamp: Date.now()
        };

        this._touchMoveCallbacks.forEach(callback => {
            try {
                callback(eventData);
            } catch (error) {
                console.error('❌ 触摸移动回调执行失败:', error);
            }
        });

        // 检测多点触摸缩放手势
        if (this._touchCount >= 2) {
            this._detectPinchGesture(event);
        }
    }

    /**
     * 触摸结束事件处理
     */
    private _onTouchEnd(event: EventTouch): void {
        const touch = event.touch!;
        const position = touch.getLocation();
        const deltaPosition = position.clone().subtract(this._lastTouchPosition);
        
        this._touchCount = Math.max(0, this._touchCount - 1);

        const eventData: ITouchEventData = {
            touch,
            position: position.clone(),
            deltaPosition,
            timestamp: Date.now()
        };

        this._touchEndCallbacks.forEach(callback => {
            try {
                callback(eventData);
            } catch (error) {
                console.error('❌ 触摸结束回调执行失败:', error);
            }
        });

        // 检测滑动手势
        if (this._touchCount === 0) {
            this._detectSwipeGesture(position);
        }
    }

    /**
     * 键盘按下事件处理
     */
    private _onKeyDown(event: EventKeyboard): void {
        const keyCode = event.keyCode.toString();
        const callbacks = this._keyDownCallbacks.get(keyCode);
        
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback();
                } catch (error) {
                    console.error('❌ 键盘按下回调执行失败:', error);
                }
            });
        }
    }

    /**
     * 键盘抬起事件处理
     */
    private _onKeyUp(event: EventKeyboard): void {
        const keyCode = event.keyCode.toString();
        const callbacks = this._keyUpCallbacks.get(keyCode);
        
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback();
                } catch (error) {
                    console.error('❌ 键盘抬起回调执行失败:', error);
                }
            });
        }
    }

    /**
     * 检测滑动手势
     */
    private _detectSwipeGesture(endPosition: Vec2): void {
        const currentTime = Date.now();
        const timeDelta = currentTime - this._touchStartTime;
        
        if (timeDelta > this._swipeTimeThreshold) {
            return; // 超时，不是滑动手势
        }

        const distance = endPosition.clone().subtract(this._touchStartPosition);
        const swipeDistance = distance.length();
        
        if (swipeDistance < this._swipeThreshold) {
            return; // 距离太短，不是滑动手势
        }

        // 确定滑动方向
        let direction: SwipeDirection;
        if (Math.abs(distance.x) > Math.abs(distance.y)) {
            direction = distance.x > 0 ? SwipeDirection.RIGHT : SwipeDirection.LEFT;
        } else {
            direction = distance.y > 0 ? SwipeDirection.UP : SwipeDirection.DOWN;
        }

        const gestureData: IGestureEventData = {
            direction,
            distance: swipeDistance,
            velocity: swipeDistance / timeDelta,
            timestamp: currentTime
        };

        this._swipeCallbacks.forEach(callback => {
            try {
                callback(gestureData);
            } catch (error) {
                console.error('❌ 滑动手势回调执行失败:', error);
            }
        });
    }

    /**
     * 检测缩放手势
     */
    private _detectPinchGesture(event: EventTouch): void {
        const touches = event.getAllTouches();
        if (touches.length < 2) return;

        const touch1 = touches[0].getLocation();
        const touch2 = touches[1].getLocation();
        const currentDistance = touch1.clone().subtract(touch2).length();

        if (this._lastTouchDistance > 0) {
            const distanceDelta = currentDistance - this._lastTouchDistance;
            
            if (Math.abs(distanceDelta) > this._pinchThreshold) {
                const scale = currentDistance / this._lastTouchDistance;
                
                const gestureData: IGestureEventData = {
                    scale,
                    distance: currentDistance,
                    timestamp: Date.now()
                };

                this._pinchCallbacks.forEach(callback => {
                    try {
                        callback(gestureData);
                    } catch (error) {
                        console.error('❌ 缩放手势回调执行失败:', error);
                    }
                });
            }
        }

        this._lastTouchDistance = currentDistance;
    }
}
