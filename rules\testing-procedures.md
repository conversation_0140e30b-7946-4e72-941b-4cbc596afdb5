# 测试规范

> 📖 **导航**: [返回主页](./README.md) | [开发工作流](./development-workflow.md) | [部署发布](./deployment-process.md)

## 🧪 测试策略

### 测试金字塔
```
    /\
   /  \     E2E Tests (端到端测试)
  /____\    - 用户场景测试
 /      \   - 平台兼容性测试
/________\  
           Integration Tests (集成测试)
          - 系统间交互测试
          - API接口测试
          
          Unit Tests (单元测试)
         - 函数级别测试
         - 组件级别测试
```

### 测试覆盖率要求
```typescript
// 测试覆盖率标准
export const TestCoverageStandards = {
    // 最低覆盖率要求
    minimum: {
        statements: 80,    // 语句覆盖率
        branches: 75,      // 分支覆盖率
        functions: 85,     // 函数覆盖率
        lines: 80         // 行覆盖率
    },
    
    // 核心模块要求更高覆盖率
    coreModules: {
        statements: 90,
        branches: 85,
        functions: 95,
        lines: 90
    },
    
    // 核心模块列表
    coreModulePatterns: [
        'assets/scripts/core/**',
        'assets/scripts/systems/battle/**',
        'assets/scripts/systems/character/**',
        'assets/scripts/managers/**'
    ]
};
```

## 🔬 单元测试规范

### Jest配置
```javascript
// jest.config.js
module.exports = {
    preset: 'ts-jest',
    testEnvironment: 'jsdom',
    roots: ['<rootDir>/assets/scripts', '<rootDir>/tests'],
    testMatch: [
        '**/__tests__/**/*.ts',
        '**/?(*.)+(spec|test).ts'
    ],
    transform: {
        '^.+\\.ts$': 'ts-jest'
    },
    collectCoverageFrom: [
        'assets/scripts/**/*.ts',
        '!assets/scripts/**/*.d.ts',
        '!assets/scripts/**/index.ts'
    ],
    coverageDirectory: 'coverage',
    coverageReporters: ['text', 'lcov', 'html'],
    setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
    moduleNameMapping: {
        '^cc$': '<rootDir>/tests/mocks/cocos-creator.ts',
        '^@/(.*)$': '<rootDir>/assets/scripts/$1'
    }
};
```

### 测试工具类
```typescript
// tests/utils/TestUtils.ts
export class TestUtils {
    // 创建模拟玩家数据
    static createMockPlayer(overrides: Partial<IPlayerData> = {}): IPlayerData {
        return {
            id: 'test_player_001',
            name: '测试玩家',
            level: 1,
            sect: '',
            attributes: {
                health: { current: 100, max: 100 },
                mana: { current: 50, max: 50 },
                attack: 10,
                defense: 5,
                speed: 8,
                luck: 5,
                internalPower: 0,
                martialTalent: 50,
                comprehension: 50,
                morality: 0
            },
            skills: {
                learned: [],
                equipped: []
            },
            inventory: {
                items: [],
                capacity: 40
            },
            cultivation: {
                level: 1,
                experience: 0,
                realm: 'qi_refining',
                techniques: [],
                breakthroughProgress: 0
            },
            reputation: {
                jianghu: 0,
                righteous: 0,
                evil: 0,
                sects: {}
            },
            ...overrides
        };
    }
    
    // 创建模拟技能数据
    static createMockSkill(overrides: Partial<ISkillData> = {}): ISkillData {
        return {
            id: 'test_skill_001',
            name: '测试技能',
            description: '用于测试的技能',
            sect: '',
            level: 1,
            maxLevel: 10,
            manaCost: 10,
            castTime: 1000,
            cooldown: 3000,
            damageType: 'physical',
            targetType: 'enemy',
            effects: [],
            requirements: {
                level: 1,
                cultivationLevel: 1,
                prerequisiteSkills: []
            },
            ...overrides
        };
    }
    
    // 等待异步操作完成
    static async waitFor(condition: () => boolean, timeout: number = 5000): Promise<void> {
        const startTime = Date.now();
        
        while (!condition() && Date.now() - startTime < timeout) {
            await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        if (!condition()) {
            throw new Error(`Condition not met within ${timeout}ms`);
        }
    }
    
    // 模拟时间流逝
    static advanceTime(milliseconds: number): void {
        jest.advanceTimersByTime(milliseconds);
    }
}
```

### 武侠系统测试示例
```typescript
// tests/systems/SectSystem.test.ts
import { SectSystem } from '@/systems/wuxia/SectSystem';
import { TestUtils } from '../utils/TestUtils';

describe('SectSystem', () => {
    let sectSystem: SectSystem;
    let mockPlayer: IPlayerData;
    
    beforeEach(() => {
        sectSystem = new SectSystem();
        mockPlayer = TestUtils.createMockPlayer();
        
        // 模拟数据管理器
        jest.spyOn(DataManager, 'getPlayer').mockReturnValue(mockPlayer);
        jest.spyOn(DataManager, 'getSect').mockReturnValue({
            id: 'sect_shaolin',
            name: '少林派',
            description: '天下武功出少林',
            location: 'location_sect_shaolin_temple',
            specialty: 'external',
            masterNpc: 'npc_shaolin_master',
            entryRequirement: { level: 1 },
            skills: ['skill_shaolin_luohan_quan'],
            bonuses: { attackBonus: 0.1 },
            reputation: 0,
            rank: SectRank.DISCIPLE
        });
    });
    
    afterEach(() => {
        jest.restoreAllMocks();
    });
    
    describe('joinSect', () => {
        it('应该允许符合条件的玩家加入门派', async () => {
            // Arrange
            const playerId = 'test_player_001';
            const sectId = 'sect_shaolin';
            
            // Act
            const result = await sectSystem.joinSect(playerId, sectId);
            
            // Assert
            expect(result).toBe(true);
            expect(mockPlayer.sect).toBe(sectId);
        });
        
        it('应该拒绝已加入门派的玩家重复加入', async () => {
            // Arrange
            const playerId = 'test_player_001';
            const sectId = 'sect_shaolin';
            mockPlayer.sect = 'sect_wudang'; // 已加入武当派
            
            // Act
            const result = await sectSystem.joinSect(playerId, sectId);
            
            // Assert
            expect(result).toBe(false);
            expect(mockPlayer.sect).toBe('sect_wudang'); // 门派未改变
        });
        
        it('应该在加入门派时触发相应事件', async () => {
            // Arrange
            const playerId = 'test_player_001';
            const sectId = 'sect_shaolin';
            const eventSpy = jest.spyOn(EventManager, 'emit');
            
            // Act
            await sectSystem.joinSect(playerId, sectId);
            
            // Assert
            expect(eventSpy).toHaveBeenCalledWith('player_joined_sect', {
                playerId,
                sectId
            });
        });
    });
    
    describe('calculateSectBonus', () => {
        it('应该根据门派等级正确计算加成', () => {
            // Arrange
            const sectLevel = 5;
            const baseBonus = 0.1;
            
            // Act
            const bonus = sectSystem.calculateSectBonus(baseBonus, sectLevel);
            
            // Assert
            expect(bonus).toBeCloseTo(0.15); // 0.1 + (5-1) * 0.01
        });
        
        it('应该限制最大加成不超过50%', () => {
            // Arrange
            const sectLevel = 100;
            const baseBonus = 0.1;
            
            // Act
            const bonus = sectSystem.calculateSectBonus(baseBonus, sectLevel);
            
            // Assert
            expect(bonus).toBeLessThanOrEqual(0.5);
        });
    });
});
```

## 🔗 集成测试规范

### API测试
```typescript
// tests/integration/NetworkAPI.test.ts
import { NetworkManager } from '@/network/NetworkManager';
import { CloudService } from '@/platform/CloudService';

describe('Network API Integration', () => {
    let networkManager: NetworkManager;
    
    beforeEach(() => {
        networkManager = NetworkManager.getInstance();
    });
    
    describe('Player Data Sync', () => {
        it('应该能够同步玩家数据到云端', async () => {
            // Arrange
            const playerData = TestUtils.createMockPlayer();
            
            // Act
            const result = await networkManager.syncPlayerData(playerData);
            
            // Assert
            expect(result.success).toBe(true);
            expect(result.data.id).toBe(playerData.id);
        });
        
        it('应该能够处理网络错误', async () => {
            // Arrange
            const playerData = TestUtils.createMockPlayer();
            jest.spyOn(CloudService, 'savePlayerData').mockRejectedValue(new Error('Network Error'));
            
            // Act & Assert
            await expect(networkManager.syncPlayerData(playerData))
                .rejects.toThrow('Network Error');
        });
    });
    
    describe('Real-time Battle', () => {
        it('应该能够处理实时战斗消息', async () => {
            // Arrange
            const battleData = {
                battleId: 'battle_001',
                participants: ['player_001', 'player_002'],
                actions: []
            };
            
            // Act
            const connection = await networkManager.joinBattle(battleData.battleId);
            
            // Assert
            expect(connection.isConnected()).toBe(true);
            expect(connection.getBattleId()).toBe(battleData.battleId);
        });
    });
});
```

## 🎮 端到端测试规范

### 用户场景测试
```typescript
// tests/e2e/WuxiaGameplay.test.ts
describe('武侠游戏核心玩法', () => {
    beforeEach(async () => {
        // 启动游戏
        await GameTestRunner.startGame();
        await GameTestRunner.createTestPlayer();
    });
    
    afterEach(async () => {
        await GameTestRunner.cleanup();
    });
    
    it('玩家应该能够完成完整的门派加入流程', async () => {
        // 1. 打开门派界面
        await GameTestRunner.clickButton('sect-button');
        await GameTestRunner.waitForPanel('sect-selection-panel');
        
        // 2. 选择少林派
        await GameTestRunner.clickButton('sect-shaolin');
        await GameTestRunner.waitForDialog('sect-join-confirmation');
        
        // 3. 确认加入
        await GameTestRunner.clickButton('confirm-join');
        await GameTestRunner.waitForMessage('加入少林派成功');
        
        // 4. 验证结果
        const playerData = await GameTestRunner.getPlayerData();
        expect(playerData.sect).toBe('sect_shaolin');
        
        // 5. 验证UI更新
        const sectBadge = await GameTestRunner.findElement('sect-badge');
        expect(sectBadge.text).toContain('少林派');
    });
    
    it('玩家应该能够学习和使用技能', async () => {
        // 前置条件：加入门派
        await GameTestRunner.joinSect('sect_shaolin');
        
        // 1. 打开技能界面
        await GameTestRunner.clickButton('skill-button');
        await GameTestRunner.waitForPanel('skill-panel');
        
        // 2. 学习技能
        await GameTestRunner.clickButton('skill-luohan-quan');
        await GameTestRunner.clickButton('learn-skill');
        await GameTestRunner.waitForMessage('学会了罗汉拳');
        
        // 3. 装备技能
        await GameTestRunner.dragSkillToSlot('skill-luohan-quan', 0);
        
        // 4. 进入战斗
        await GameTestRunner.startBattle();
        
        // 5. 使用技能
        await GameTestRunner.clickSkillSlot(0);
        await GameTestRunner.waitForSkillAnimation();
        
        // 6. 验证技能效果
        const battleLog = await GameTestRunner.getBattleLog();
        expect(battleLog).toContain('使用了罗汉拳');
    });
});
```

### 平台兼容性测试
```typescript
// tests/e2e/PlatformCompatibility.test.ts
describe('平台兼容性测试', () => {
    const platforms = ['wechat', 'douyin'];
    
    platforms.forEach(platform => {
        describe(`${platform} 平台`, () => {
            beforeEach(async () => {
                await PlatformTestRunner.switchToPlatform(platform);
                await PlatformTestRunner.startGame();
            });
            
            it('应该能够正常启动游戏', async () => {
                const gameState = await PlatformTestRunner.getGameState();
                expect(gameState.isLoaded).toBe(true);
                expect(gameState.platform).toBe(platform);
            });
            
            it('应该能够正常登录', async () => {
                const loginResult = await PlatformTestRunner.performLogin();
                expect(loginResult.success).toBe(true);
                expect(loginResult.userId).toBeDefined();
            });
            
            it('应该能够保存和加载数据', async () => {
                const testData = { level: 5, sect: 'sect_shaolin' };
                
                await PlatformTestRunner.saveData(testData);
                const loadedData = await PlatformTestRunner.loadData();
                
                expect(loadedData.level).toBe(testData.level);
                expect(loadedData.sect).toBe(testData.sect);
            });
        });
    });
});
```

---

> 📖 **相关文档**: [部署发布](./deployment-process.md) | [开发工作流](./development-workflow.md) | [代码标准](./code-standards.md)
