"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PrefabToolsTest = void 0;
const prefab_tools_1 = require("../tools/prefab-tools");
// 预制体工具测试
class PrefabToolsTest {
    constructor() {
        this.prefabTools = new prefab_tools_1.PrefabTools();
    }
    async runAllTests() {
        console.log('开始预制体工具测试...');
        try {
            // 测试1: 获取工具列表
            await this.testGetTools();
            // 测试2: 获取预制体列表
            await this.testGetPrefabList();
            // 测试3: 测试预制体创建（模拟）
            await this.testCreatePrefab();
            // 测试3.5: 测试预制体实例化（模拟）
            await this.testInstantiatePrefab();
            // 测试4: 测试预制体验证
            await this.testValidatePrefab();
            console.log('所有测试完成！');
        }
        catch (error) {
            console.error('测试过程中发生错误:', error);
        }
    }
    async testGetTools() {
        console.log('测试1: 获取工具列表');
        const tools = this.prefabTools.getTools();
        console.log(`找到 ${tools.length} 个工具:`);
        tools.forEach(tool => {
            console.log(`  - ${tool.name}: ${tool.description}`);
        });
        console.log('测试1完成\n');
    }
    async testGetPrefabList() {
        var _a;
        console.log('测试2: 获取预制体列表');
        try {
            const result = await this.prefabTools.execute('get_prefab_list', { folder: 'db://assets' });
            if (result.success) {
                console.log(`找到 ${((_a = result.data) === null || _a === void 0 ? void 0 : _a.length) || 0} 个预制体`);
                if (result.data && result.data.length > 0) {
                    result.data.slice(0, 3).forEach((prefab) => {
                        console.log(`  - ${prefab.name}: ${prefab.path}`);
                    });
                }
            }
            else {
                console.log('获取预制体列表失败:', result.error);
            }
        }
        catch (error) {
            console.log('获取预制体列表时发生错误:', error);
        }
        console.log('测试2完成\n');
    }
    async testCreatePrefab() {
        console.log('测试3: 测试预制体创建（模拟）');
        try {
            // 模拟创建预制体
            const mockArgs = {
                nodeUuid: 'mock-node-uuid',
                savePath: 'db://assets/test',
                prefabName: 'TestPrefab'
            };
            const result = await this.prefabTools.execute('create_prefab', mockArgs);
            console.log('创建预制体结果:', result);
        }
        catch (error) {
            console.log('创建预制体时发生错误:', error);
        }
        console.log('测试3完成\n');
    }
    async testInstantiatePrefab() {
        console.log('测试3.5: 测试预制体实例化（模拟）');
        try {
            // 模拟实例化预制体
            const mockArgs = {
                prefabPath: 'db://assets/prefabs/TestPrefab.prefab',
                parentUuid: 'canvas-uuid',
                position: { x: 100, y: 200, z: 0 }
            };
            const result = await this.prefabTools.execute('instantiate_prefab', mockArgs);
            console.log('实例化预制体结果:', result);
            // 测试API参数构建
            this.testCreateNodeAPIParams();
        }
        catch (error) {
            console.log('实例化预制体时发生错误:', error);
        }
        console.log('测试3.5完成\n');
    }
    testCreateNodeAPIParams() {
        console.log('测试 create-node API 参数构建...');
        // 模拟 assetUuid
        const assetUuid = 'mock-prefab-uuid';
        // 测试基本参数
        const basicOptions = {
            assetUuid: assetUuid,
            name: 'TestPrefabInstance'
        };
        console.log('基本参数:', basicOptions);
        // 测试带父节点的参数
        const withParentOptions = Object.assign(Object.assign({}, basicOptions), { parent: 'parent-node-uuid' });
        console.log('带父节点参数:', withParentOptions);
        // 测试带位置的参数
        const withPositionOptions = Object.assign(Object.assign({}, basicOptions), { dump: {
                position: { x: 100, y: 200, z: 0 }
            } });
        console.log('带位置参数:', withPositionOptions);
        // 测试完整参数
        const fullOptions = {
            assetUuid: assetUuid,
            name: 'TestPrefabInstance',
            parent: 'parent-node-uuid',
            dump: {
                position: { x: 100, y: 200, z: 0 }
            },
            keepWorldTransform: false,
            unlinkPrefab: false
        };
        console.log('完整参数:', fullOptions);
    }
    async testValidatePrefab() {
        console.log('测试4: 测试预制体验证');
        try {
            // 测试验证一个不存在的预制体
            const result = await this.prefabTools.execute('validate_prefab', {
                prefabPath: 'db://assets/nonexistent.prefab'
            });
            console.log('验证预制体结果:', result);
        }
        catch (error) {
            console.log('验证预制体时发生错误:', error);
        }
        console.log('测试4完成\n');
    }
    // 测试预制体数据结构生成
    testPrefabDataGeneration() {
        console.log('测试预制体数据结构生成...');
        const mockNodeData = {
            name: 'TestNode',
            position: { x: 0, y: 0, z: 0 },
            scale: { x: 1, y: 1, z: 1 },
            active: true,
            children: [],
            components: [
                {
                    type: 'cc.UITransform',
                    enabled: true,
                    properties: {
                        _contentSize: { width: 100, height: 100 },
                        _anchorPoint: { x: 0.5, y: 0.5 }
                    }
                }
            ]
        };
        const prefabUuid = this.prefabTools['generateUUID']();
        const prefabData = this.prefabTools['createPrefabData'](mockNodeData, 'TestPrefab', prefabUuid);
        console.log('生成的预制体数据结构:');
        console.log(JSON.stringify(prefabData, null, 2));
        // 验证数据结构
        const validationResult = this.prefabTools['validatePrefabFormat'](prefabData);
        console.log('验证结果:', validationResult);
        console.log('预制体数据结构生成测试完成\n');
    }
    // 测试UUID生成
    testUUIDGeneration() {
        console.log('测试UUID生成...');
        const uuids = [];
        for (let i = 0; i < 5; i++) {
            const uuid = this.prefabTools['generateUUID']();
            uuids.push(uuid);
            console.log(`UUID ${i + 1}: ${uuid}`);
        }
        // 检查UUID格式
        const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
        const validUuids = uuids.filter(uuid => uuidPattern.test(uuid));
        console.log(`UUID格式验证: ${validUuids.length}/${uuids.length} 个有效`);
        console.log('UUID生成测试完成\n');
    }
}
exports.PrefabToolsTest = PrefabToolsTest;
// 如果直接运行此文件
if (typeof module !== 'undefined' && module.exports) {
    const test = new PrefabToolsTest();
    test.runAllTests();
    test.testPrefabDataGeneration();
    test.testUUIDGeneration();
}
//# sourceMappingURL=data:application/json;base64,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