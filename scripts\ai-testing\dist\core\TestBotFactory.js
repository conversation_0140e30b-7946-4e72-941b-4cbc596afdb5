"use strict";
/**
 * 测试机器人工厂 - 动态创建和管理测试机器人
 * 根据系统特征自动创建适配的测试机器人
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestBotFactory = void 0;
const GenericTestBot_1 = require("./GenericTestBot");
const SystemDiscoveryAgent_1 = require("../agents/SystemDiscoveryAgent");
const TestTemplateRegistry_1 = require("../templates/TestTemplateRegistry");
class TestBotFactory {
    constructor() {
        this.botCache = new Map();
        this.systemContextCache = new Map();
        this.systemDiscovery = new SystemDiscoveryAgent_1.SystemDiscoveryAgent();
        this.templateRegistry = new TestTemplateRegistry_1.TestTemplateRegistry();
    }
    /**
     * 动态创建测试机器人
     * 根据系统路径分析系统特征并创建适配的测试机器人
     */
    async createTestBotForSystem(systemPath) {
        console.log(`🏭 TestBotFactory: Creating test bot for system at ${systemPath}`);
        // 检查缓存
        if (this.botCache.has(systemPath)) {
            console.log('📦 Using cached test bot');
            return this.botCache.get(systemPath);
        }
        try {
            // 1. 发现和分析系统
            const systemContext = await this.analyzeSystem(systemPath);
            // 2. 创建通用测试机器人
            const testBot = new GenericTestBot_1.GenericTestBot(systemContext);
            // 3. 根据系统类型适配能力
            await testBot.adaptToSystemType(systemContext.systemType);
            // 4. 加载适用的测试模板
            await this.loadTestTemplatesForBot(testBot, systemContext);
            // 5. 缓存机器人实例
            this.botCache.set(systemPath, testBot);
            this.systemContextCache.set(systemPath, systemContext);
            console.log(`✅ Test bot created for ${systemContext.name} (${systemContext.systemType})`);
            return testBot;
        }
        catch (error) {
            console.error(`❌ Failed to create test bot for ${systemPath}:`, error);
            throw error;
        }
    }
    /**
     * 批量创建测试机器人
     * 为整个项目的所有系统创建测试机器人
     */
    async createTestBotsForProject(projectPath) {
        console.log(`🏭 TestBotFactory: Creating test bots for entire project at ${projectPath}`);
        const testBots = new Map();
        try {
            // 1. 发现项目中的所有系统
            const systems = await this.systemDiscovery.discoverSystems(projectPath);
            console.log(`📦 Discovered ${systems.length} systems in project`);
            // 2. 为每个系统创建测试机器人
            for (const system of systems) {
                try {
                    const testBot = await this.createTestBotForSystem(system.path);
                    testBots.set(system.name, testBot);
                    console.log(`✅ Created test bot for ${system.name}`);
                }
                catch (error) {
                    console.error(`❌ Failed to create test bot for ${system.name}:`, error);
                    // 继续处理其他系统，不中断整个流程
                }
            }
            console.log(`🎉 Successfully created ${testBots.size} test bots for project`);
            return testBots;
        }
        catch (error) {
            console.error(`❌ Failed to create test bots for project:`, error);
            throw error;
        }
    }
    /**
     * 动态更新测试机器人
     * 根据代码变更更新现有的测试机器人
     */
    async updateTestBot(systemPath, changes) {
        console.log(`🔄 TestBotFactory: Updating test bot for ${systemPath}`);
        const testBot = this.botCache.get(systemPath);
        if (!testBot) {
            console.log('⚠️ Test bot not found in cache, creating new one');
            await this.createTestBotForSystem(systemPath);
            return;
        }
        try {
            // 1. 分析变更影响
            const impactAnalysis = await this.analyzeChangeImpact(systemPath, changes);
            // 2. 如果系统类型发生变化，重新适配
            if (impactAnalysis.systemTypeChanged) {
                console.log(`🔄 System type changed to ${impactAnalysis.newSystemType}, re-adapting bot`);
                await testBot.adaptToSystemType(impactAnalysis.newSystemType);
                // 更新系统上下文缓存
                const systemContext = this.systemContextCache.get(systemPath);
                if (systemContext) {
                    systemContext.systemType = impactAnalysis.newSystemType;
                }
            }
            // 3. 更新测试模板
            if (impactAnalysis.requiresNewTemplates) {
                console.log('📋 Loading new test templates');
                await this.updateTestTemplates(testBot, impactAnalysis.newRequirements);
            }
            // 4. 刷新测试用例
            if (impactAnalysis.requiresTestRefresh) {
                console.log('🔄 Refreshing test cases');
                await testBot.refreshTestCases();
            }
            console.log('✅ Test bot updated successfully');
        }
        catch (error) {
            console.error(`❌ Failed to update test bot:`, error);
            // 如果更新失败，重新创建测试机器人
            console.log('🔄 Recreating test bot due to update failure');
            this.botCache.delete(systemPath);
            this.systemContextCache.delete(systemPath);
            await this.createTestBotForSystem(systemPath);
        }
    }
    /**
     * 获取系统统计信息
     */
    getSystemStatistics() {
        const stats = {
            totalSystems: this.botCache.size,
            systemTypes: new Map(),
            complexityDistribution: new Map(),
            averageTestCoverage: 0
        };
        // 统计系统类型分布
        for (const context of this.systemContextCache.values()) {
            const currentCount = stats.systemTypes.get(context.systemType) || 0;
            stats.systemTypes.set(context.systemType, currentCount + 1);
            const complexityCount = stats.complexityDistribution.get(context.complexity) || 0;
            stats.complexityDistribution.set(context.complexity, complexityCount + 1);
        }
        return stats;
    }
    /**
     * 清理缓存
     */
    clearCache() {
        console.log('🧹 Clearing test bot cache');
        this.botCache.clear();
        this.systemContextCache.clear();
    }
    // 私有辅助方法
    /**
     * 分析系统特征
     */
    async analyzeSystem(systemPath) {
        console.log(`🔍 Analyzing system at ${systemPath}`);
        // 使用系统发现代理分析系统
        const systemInfo = await this.systemDiscovery.analyzeSystemAtPath(systemPath);
        // 提取系统特征
        const features = await this.extractSystemFeatures(systemPath);
        // 分析系统依赖
        const dependencies = await this.analyzeDependencies(systemPath);
        const systemContext = {
            name: systemInfo.name,
            path: systemPath,
            systemType: systemInfo.type,
            complexity: systemInfo.complexity,
            features,
            dependencies
        };
        console.log(`✅ System analysis complete:`, systemContext);
        return systemContext;
    }
    /**
     * 提取系统特征
     */
    async extractSystemFeatures(systemPath) {
        // 这里可以通过静态代码分析、文件扫描等方式提取系统特征
        // 简化实现，实际项目中需要更复杂的分析逻辑
        return {
            hasAlgorithms: await this.detectAlgorithms(systemPath),
            hasAPIs: await this.detectAPIs(systemPath),
            hasUI: await this.detectUI(systemPath),
            hasDatabase: await this.detectDatabase(systemPath),
            isGameLogic: await this.detectGameLogic(systemPath),
            isRealtime: await this.detectRealtime(systemPath)
        };
    }
    /**
     * 分析系统依赖
     */
    async analyzeDependencies(systemPath) {
        // 简化实现，实际项目中需要分析import/require语句
        return [];
    }
    /**
     * 为测试机器人加载测试模板
     */
    async loadTestTemplatesForBot(testBot, systemContext) {
        const applicableTemplates = this.templateRegistry.selectTemplatesForSystem({
            name: systemContext.name,
            type: systemContext.systemType,
            complexity: systemContext.complexity,
            features: systemContext.features
        });
        await testBot.loadTestTemplates(applicableTemplates);
    }
    /**
     * 分析代码变更影响
     */
    async analyzeChangeImpact(systemPath, changes) {
        // 简化实现，实际项目中需要更复杂的影响分析
        return {
            systemTypeChanged: false,
            newSystemType: 'game-logic',
            requiresNewTemplates: false,
            requiresTestRefresh: true,
            newRequirements: []
        };
    }
    /**
     * 更新测试模板
     */
    async updateTestTemplates(testBot, requirements) {
        // 根据新需求更新测试模板
        console.log('Updating test templates with new requirements:', requirements);
    }
    // 特征检测方法（简化实现）
    async detectAlgorithms(systemPath) {
        // 检测是否包含算法逻辑
        return true;
    }
    async detectAPIs(systemPath) {
        // 检测是否包含API接口
        return false;
    }
    async detectUI(systemPath) {
        // 检测是否包含UI组件
        return false;
    }
    async detectDatabase(systemPath) {
        // 检测是否包含数据库操作
        return false;
    }
    async detectGameLogic(systemPath) {
        // 检测是否包含游戏逻辑
        return true;
    }
    async detectRealtime(systemPath) {
        // 检测是否包含实时功能
        return false;
    }
}
exports.TestBotFactory = TestBotFactory;
//# sourceMappingURL=TestBotFactory.js.map