{"description": "武侠放置游戏 - 微信小程序版本", "packOptions": {"ignore": [{"value": ".eslintrc.js", "type": "file"}, {"value": ".prettier<PERSON>", "type": "file"}, {"value": "tsconfig.json", "type": "file"}, {"value": "node_modules", "type": "folder"}, {"value": "temp", "type": "folder"}, {"value": "library", "type": "folder"}], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "minified": true, "newFeature": true, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "enableEngineNative": false, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "compileWorklet": false, "localPlugins": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "compileType": "game", "libVersion": "3.8.12", "appid": "wxc99c5459cd75a4ed", "projectname": "武侠放置游戏", "simulatorPluginLibVersion": {}, "isGameTourist": false, "editorSetting": {}, "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}