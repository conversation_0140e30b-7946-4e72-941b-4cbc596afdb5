{"containerPort": 3000, "minNum": 0, "maxNum": 1, "cpu": 0.25, "mem": 0.5, "policyType": "cpu", "policyThreshold": 70, "envParams": {"NODE_ENV": "production", "WECHAT_CLOUD": "true", "LOG_LEVEL": "warn", "FREE_TIER": "true"}, "customLogs": [{"name": "error", "logPath": "/app/logs/error.log"}], "dataBaseName": "idlegame", "serviceName": "idlegame-backend-free", "uploadType": "package", "flowRatio": 100, "description": "IdleGame后端服务 - 免费版配置", "scaleDownDelay": 300, "scaleUpCooldown": 60}