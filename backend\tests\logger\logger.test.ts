import { Logger, LogLevel, LogType } from '../../src/utils/logger';
import { LogAnalyzer } from '../../src/utils/logAnalyzer';
import { LogVisualizer } from '../../src/utils/logVisualizer';
import { LoggerOutputConfig } from '../../src/config/loggerConfig';
import fs = require('fs');
import path = require('path');

describe('日志系统测试', () => {
  const testLogDir = path.join(process.cwd(), 'test-logs');

  beforeAll(() => {
    // 设置测试环境
    process.env['LOG_DIR'] = testLogDir;
    process.env['LOG_LEVEL'] = 'debug';
    process.env['LOG_ENABLE_CONSOLE'] = 'false';
    process.env['LOG_ENABLE_FILE'] = 'true';
    process.env['LOG_ENABLE_ROTATION'] = 'false';

    // 确保测试日志目录存在
    if (!fs.existsSync(testLogDir)) {
      fs.mkdirSync(testLogDir, { recursive: true });
    }
  });

  afterAll(() => {
    // 清理测试日志文件
    try {
      if (fs.existsSync(testLogDir)) {
        const files = fs.readdirSync(testLogDir);
        files.forEach(file => {
          fs.unlinkSync(path.join(testLogDir, file));
        });
        fs.rmdirSync(testLogDir);
      }
    } catch (error) {
      console.warn('清理测试日志文件失败');
    }
  });

  beforeEach(() => {
    // 清除日志上下文
    Logger.clearContext();
  });

  describe('Logger基础功能测试', () => {
    it('应该记录不同级别的日志', () => {
      expect(() => {
        Logger.debug('调试信息', { test: true });
        Logger.info('信息日志', { test: true });
        Logger.warn('警告日志', { test: true });
        Logger.error('错误日志', { test: true });
      }).not.toThrow();
    });

    it('应该设置和获取日志上下文', () => {
      const context = {
        userId: 'test-user-123',
        requestId: 'req-456',
        operation: 'test-operation',
      };

      Logger.setContext(context);
      const retrievedContext = Logger.getContext();

      expect(retrievedContext).toMatchObject(context);
    });

    it('应该清除日志上下文', () => {
      Logger.setContext({ userId: 'test-user' });
      Logger.clearContext();
      
      const context = Logger.getContext();
      expect(Object.keys(context)).toHaveLength(0);
    });

    it('应该记录用户行为日志', () => {
      expect(() => {
        Logger.userAction('user123', 'login', { ip: '127.0.0.1' });
        Logger.userAction('user456', 'logout', { duration: 3600 });
      }).not.toThrow();
    });

    it('应该记录游戏事件日志', () => {
      expect(() => {
        Logger.gameEvent('level_up', { userId: 'user123', level: 5 });
        Logger.gameEvent('item_acquired', { userId: 'user123', itemId: 'sword001' });
      }).not.toThrow();
    });

    it('应该记录安全事件日志', () => {
      expect(() => {
        Logger.securityEvent('failed_login', { ip: '*************', attempts: 3 });
        Logger.securityEvent('suspicious_activity', { userId: 'user123', action: 'multiple_requests' });
      }).not.toThrow();
    });

    it('应该记录性能日志', () => {
      expect(() => {
        Logger.performance('database_query', 150, { query: 'SELECT * FROM users' });
        Logger.performance('api_request', 250, { endpoint: '/api/users' });
      }).not.toThrow();
    });

    it('应该记录业务日志', () => {
      expect(() => {
        Logger.business('order_created', { orderId: 'order123', amount: 99.99 });
        Logger.business('payment_processed', { paymentId: 'pay456' }, LogLevel.INFO);
      }).not.toThrow();
    });

    it('应该记录审计日志', () => {
      expect(() => {
        Logger.audit('admin123', 'delete_user', 'user456', 'success', { reason: 'violation' });
        Logger.audit('admin123', 'update_config', 'system_config', 'failure', { error: 'permission_denied' });
      }).not.toThrow();
    });
  });

  describe('性能监控功能测试', () => {
    it('应该启动和结束性能计时', () => {
      const operation = 'test_operation';
      
      Logger.startTimer(operation);
      
      // 模拟一些操作
      const start = Date.now();
      while (Date.now() - start < 10) {
        // 等待10ms
      }
      
      const duration = Logger.endTimer(operation, { test: true });
      
      expect(duration).toBeGreaterThan(0);
      expect(duration).toBeLessThan(100); // 应该小于100ms
    });

    it('应该处理不存在的计时器', () => {
      const duration = Logger.endTimer('nonexistent_timer');
      expect(duration).toBe(0);
    });

    it('应该生成请求ID和跟踪ID', () => {
      const requestId = Logger.generateRequestId();
      const traceId = Logger.generateTraceId();

      expect(requestId).toMatch(/^req_\d+_[a-z0-9]+$/);
      expect(traceId).toMatch(/^trace_\d+_[a-z0-9]+$/);
      expect(requestId).not.toBe(traceId);
    });
  });

  describe('结构化日志功能测试', () => {
    it('应该记录结构化日志', () => {
      expect(() => {
        Logger.structured(LogLevel.INFO, '结构化日志测试', {
          module: 'test',
          action: 'structured_log',
          data: { key: 'value' },
        }, LogType.BUSINESS);
      }).not.toThrow();
    });

    it('应该批量记录日志', () => {
      const logs = [
        { level: LogLevel.INFO, message: '批量日志1', meta: { id: 1 }, type: LogType.SYSTEM },
        { level: LogLevel.WARN, message: '批量日志2', meta: { id: 2 }, type: LogType.BUSINESS },
        { level: LogLevel.ERROR, message: '批量日志3', meta: { id: 3 }, type: LogType.SECURITY_EVENT },
      ];

      expect(() => {
        Logger.batch(logs);
      }).not.toThrow();
    });

    it('应该条件记录日志', () => {
      expect(() => {
        Logger.conditional(true, LogLevel.INFO, '条件为真的日志', { condition: true });
        Logger.conditional(false, LogLevel.ERROR, '条件为假的日志', { condition: false });
      }).not.toThrow();
    });
  });

  describe('日志配置功能测试', () => {
    it('应该获取日志统计信息', () => {
      const stats = Logger.getStats();
      
      expect(stats).toHaveProperty('activeTimers');
      expect(stats).toHaveProperty('context');
      expect(stats).toHaveProperty('config');
      expect(stats).toHaveProperty('transports');
      expect(typeof stats.activeTimers).toBe('number');
    });

    it('应该通过健康检查', async () => {
      const isHealthy = await Logger.healthCheck();
      expect(isHealthy).toBe(true);
    });

    it('应该检查日志级别', () => {
      const isDebugEnabled = Logger.isLevelEnabled(LogLevel.DEBUG);
      const isErrorEnabled = Logger.isLevelEnabled(LogLevel.ERROR);
      
      expect(typeof isDebugEnabled).toBe('boolean');
      expect(isErrorEnabled).toBe(true); // ERROR级别应该总是启用
    });

    it('应该格式化错误对象', () => {
      const error = new Error('测试错误');
      error.name = 'TestError';
      
      const formatted = Logger.formatError(error);
      
      expect(formatted).toHaveProperty('name', 'TestError');
      expect(formatted).toHaveProperty('message', '测试错误');
      expect(formatted).toHaveProperty('stack');
    });
  });

  describe('LoggerOutputConfig测试', () => {
    it('应该获取配置实例', () => {
      const config = LoggerOutputConfig.getInstance();
      expect(config).toBeDefined();
      
      const config2 = LoggerOutputConfig.getInstance();
      expect(config).toBe(config2); // 应该是单例
    });

    it('应该创建控制台传输器', () => {
      const config = LoggerOutputConfig.getInstance();
      const transport = config.createConsoleTransport({
        level: LogLevel.INFO,
        colorize: false,
      });
      
      expect(transport).toBeDefined();
    });

    it('应该创建文件传输器', () => {
      const config = LoggerOutputConfig.getInstance();
      const transport = config.createFileTransport({
        filename: 'test.log',
        level: LogLevel.DEBUG,
      });
      
      expect(transport).toBeDefined();
    });

    it('应该获取默认传输器', () => {
      const config = LoggerOutputConfig.getInstance();
      const transports = config.getDefaultTransports();
      
      expect(Array.isArray(transports)).toBe(true);
      expect(transports.length).toBeGreaterThan(0);
    });
  });

  describe('LogAnalyzer测试', () => {
    let logAnalyzer: LogAnalyzer;

    beforeAll(() => {
      logAnalyzer = LogAnalyzer.getInstance();
    });

    it('应该获取分析器实例', () => {
      expect(logAnalyzer).toBeDefined();
      
      const analyzer2 = LogAnalyzer.getInstance();
      expect(logAnalyzer).toBe(analyzer2); // 应该是单例
    });

    it('应该添加和删除告警规则', () => {
      const rule = {
        id: 'test-rule-1',
        name: '测试告警规则',
        condition: { level: LogLevel.ERROR },
        threshold: 5,
        timeWindow: 10,
        enabled: true,
        actions: ['log'],
      };

      logAnalyzer.addAlertRule(rule);
      
      const rules = logAnalyzer.getAlertRules();
      expect(rules).toContainEqual(rule);

      logAnalyzer.removeAlertRule('test-rule-1');
      
      const rulesAfterRemoval = logAnalyzer.getAlertRules();
      expect(rulesAfterRemoval).not.toContainEqual(rule);
    });

    it('应该生成日志报告', async () => {
      const startTime = new Date(Date.now() - 60 * 60 * 1000); // 1小时前
      const endTime = new Date();

      const report = await logAnalyzer.generateReport(startTime, endTime);
      
      expect(report).toHaveProperty('period');
      expect(report).toHaveProperty('summary');
      expect(report).toHaveProperty('statistics');
      expect(report).toHaveProperty('recentErrors');
      expect(report).toHaveProperty('recommendations');
    });
  });

  describe('LogVisualizer测试', () => {
    let logVisualizer: LogVisualizer;

    beforeAll(() => {
      logVisualizer = LogVisualizer.getInstance();
    });

    it('应该获取可视化器实例', () => {
      expect(logVisualizer).toBeDefined();
      
      const visualizer2 = LogVisualizer.getInstance();
      expect(logVisualizer).toBe(visualizer2); // 应该是单例
    });

    it('应该生成实时监控数据', async () => {
      const realTimeData = await logVisualizer.generateRealTimeData();
      
      expect(realTimeData).toHaveProperty('timestamp');
      expect(realTimeData).toHaveProperty('metrics');
      expect(realTimeData).toHaveProperty('recentActivity');
      expect(realTimeData).toHaveProperty('systemHealth');
    });

    it('应该生成性能报告', async () => {
      const timeRange = {
        start: new Date(Date.now() - 60 * 60 * 1000), // 1小时前
        end: new Date(),
      };

      const performanceReport = await logVisualizer.generatePerformanceReport(timeRange);
      
      expect(performanceReport).toHaveProperty('period');
      expect(performanceReport).toHaveProperty('performance');
      expect(performanceReport).toHaveProperty('recommendations');
    });
  });

  describe('集成测试', () => {
    it('应该完整的日志流程工作正常', async () => {
      // 设置上下文
      Logger.setContext({
        userId: 'integration-test-user',
        requestId: 'integration-test-req',
      });

      // 记录各种类型的日志
      Logger.info('集成测试开始');
      Logger.userAction('integration-test-user', 'test_action', { test: true });
      Logger.performance('integration_test', 100, { success: true });
      Logger.business('integration_test_business', { result: 'success' });

      // 获取统计信息
      const stats = Logger.getStats();
      expect(stats.context.userId).toBe('integration-test-user');

      // 健康检查
      const isHealthy = await Logger.healthCheck();
      expect(isHealthy).toBe(true);

      // 清理
      Logger.clearContext();
      Logger.info('集成测试完成');
    });
  });
});
