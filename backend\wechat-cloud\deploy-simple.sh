#!/bin/bash

# 简化的微信云托管部署脚本
# 跳过TypeScript编译，直接部署源码

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认参数
ENVIRONMENT=${1:-prod}
VERSION=${2:-v1.0.0}
CONFIG_TYPE=${3:-free}
SERVICE_NAME="idlegame-backend"

# 根据配置类型选择配置文件
if [ "$CONFIG_TYPE" = "free" ]; then
    CONFIG_FILE="wechat-cloud/container.config.free.json"
    SERVICE_NAME="idlegame-backend-free"
    echo -e "${GREEN}🆓 使用免费版配置${NC}"
else
    CONFIG_FILE="wechat-cloud/container.config.json"
    echo -e "${GREEN}💼 使用标准版配置${NC}"
fi

echo -e "${GREEN}🚀 开始部署微信云托管服务${NC}"
echo -e "${YELLOW}环境: $ENVIRONMENT${NC}"
echo -e "${YELLOW}版本: $VERSION${NC}"
echo -e "${YELLOW}配置: $CONFIG_TYPE${NC}"
echo -e "${YELLOW}服务名: $SERVICE_NAME${NC}"

# 检查必要工具
check_tools() {
    echo -e "${YELLOW}📋 检查部署工具...${NC}"
    
    if ! command -v tcb &> /dev/null; then
        echo -e "${RED}❌ 腾讯云CLI工具未安装${NC}"
        echo -e "${YELLOW}请安装: npm install -g @cloudbase/cli${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 工具检查完成${NC}"
}

# 检查登录状态
check_login() {
    echo -e "${YELLOW}🔐 检查登录状态...${NC}"
    
    if ! tcb env list &> /dev/null; then
        echo -e "${RED}❌ 未登录腾讯云${NC}"
        echo -e "${YELLOW}请先执行: tcb login${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 已登录腾讯云${NC}"
}

# 检查环境变量
check_env() {
    echo -e "${YELLOW}🔍 检查环境变量...${NC}"
    
    if [ ! -f ".env.wechat" ]; then
        echo -e "${RED}❌ 环境变量文件不存在${NC}"
        echo -e "${YELLOW}请先创建 .env.wechat 文件${NC}"
        exit 1
    fi
    
    # 检查关键环境变量
    if ! grep -q "TCB_ENV=cloudbase-" .env.wechat; then
        echo -e "${RED}❌ 请在 .env.wechat 中配置正确的 TCB_ENV${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境变量检查完成${NC}"
}

# 创建简化的Dockerfile
create_simple_dockerfile() {
    echo -e "${YELLOW}📝 创建简化Dockerfile...${NC}"
    
    cat > wechat-cloud/Dockerfile.simple << 'EOF'
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 创建日志目录
RUN mkdir -p /app/logs

# 暴露端口
EXPOSE 3000

# 启动应用（直接运行TypeScript）
CMD ["npx", "ts-node", "src/server.ts"]
EOF
    
    echo -e "${GREEN}✅ 简化Dockerfile创建完成${NC}"
}

# 部署到云托管
deploy_to_cloudrun() {
    echo -e "${YELLOW}🚀 部署到云托管...${NC}"
    
    # 使用简化的Dockerfile
    tcb run deploy \
        --name $SERVICE_NAME \
        --dockerfile wechat-cloud/Dockerfile.simple \
        --config $CONFIG_FILE \
        --env-file .env.wechat
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✅ 部署成功${NC}"
    else
        echo -e "${RED}❌ 部署失败${NC}"
        exit 1
    fi
}

# 检查服务状态
check_service() {
    echo -e "${YELLOW}🔍 检查服务状态...${NC}"
    
    # 等待服务启动
    sleep 30
    
    # 获取服务信息
    tcb run describe --name $SERVICE_NAME
    
    echo -e "${GREEN}✅ 服务状态检查完成${NC}"
}

# 主流程
main() {
    echo -e "${GREEN}开始简化部署流程...${NC}"
    
    check_tools
    check_login
    check_env
    create_simple_dockerfile
    deploy_to_cloudrun
    check_service
    
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo -e "${YELLOW}请在微信开发者工具中查看服务状态${NC}"
}

# 执行主流程
main
