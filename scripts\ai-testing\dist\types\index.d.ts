/**
 * 统一类型定义文件
 * 解决循环依赖和类型导入问题
 */
export type SystemType = 'user-management' | 'game-logic' | 'battle-system' | 'ui-system' | 'data-management' | 'api-service' | 'wuxia-system' | 'social-system';
export type ComplexityLevel = 'simple' | 'medium' | 'complex';
export type TestType = 'unit' | 'integration' | 'performance' | 'edge-case' | 'regression' | 'algorithm-consistency' | 'ui-interaction' | 'api-endpoint';
export type TestCaseType = 'unit' | 'integration' | 'algorithm-consistency' | 'performance' | 'api' | 'ui' | 'edge-case' | 'regression';
export type Priority = 'critical' | 'high' | 'medium' | 'low';
export interface SystemInfo {
    name: string;
    path: string;
    type: string;
    complexity: ComplexityLevel;
    features: string[];
    dependencies: string[];
    testingPriority: Priority;
    recommendedStrategies: string[];
}
export interface SystemContext {
    name: string;
    path: string;
    systemType: SystemType;
    complexity: ComplexityLevel;
    features: SystemFeatures;
    dependencies: string[];
}
export interface SystemFeatures {
    hasAlgorithms: boolean;
    hasAPIs: boolean;
    hasUI: boolean;
    hasDatabase: boolean;
    isGameLogic: boolean;
    isRealtime: boolean;
}
export interface TestCase {
    id: string;
    name: string;
    description: string;
    type: TestCaseType;
    priority: number;
    estimatedTime: number;
    testCode: string;
    expectedResult: any;
    dependencies: string[];
}
export interface TestTask {
    id: string;
    name: string;
    priority: number;
    estimatedTime: number;
    dependencies: string[];
    testCases?: TestCase[];
}
export interface TestResult {
    taskId: string;
    name?: string;
    status: 'passed' | 'failed' | 'skipped';
    duration: number;
    executionTime?: number;
    details: any;
    error?: string;
}
export interface TestStrategy {
    testPriority: TestPriority[];
    testScope: TestScope;
    testSequence: TestSequence[];
    resourceAllocation: ResourceAllocation;
}
export interface TestPriority {
    systemName: string;
    priority: Priority;
    reason: string;
    estimatedTime: number;
}
export interface TestScope {
    modules: SystemModule[];
    testTypes: TestType[];
    coverage: CoverageRequirement;
}
export interface TestSequence {
    order: number;
    tasks: string[];
}
export interface ResourceAllocation {
    estimatedTime: number;
    requiredBots: number;
    priority: Priority;
}
export interface SystemModule {
    name: string;
    path: string;
    type: string;
}
export interface CoverageRequirement {
    minimum: number;
    target: number;
}
export interface ValidationResult {
    isConsistent: boolean;
    deviationPercentage: number;
    riskPoints: string[];
    recommendations: string[];
}
export interface CodeAnalysisResult {
    complexity: ComplexityLevel;
    features: CodeFeatures;
    dependencies: string[];
    metrics: CodeMetrics;
    suggestions: string[];
}
export interface CodeFeatures {
    hasClasses: boolean;
    hasInterfaces: boolean;
    hasAsyncCode: boolean;
    hasEventHandlers: boolean;
    hasAlgorithms: boolean;
    hasDataStructures: boolean;
    hasNetworking: boolean;
    hasUI: boolean;
}
export interface CodeMetrics {
    linesOfCode: number;
    cyclomaticComplexity: number;
    maintainabilityIndex: number;
    testCoverage: number;
    duplicateCodePercentage: number;
}
export interface TestTemplate {
    id: string;
    name: string;
    description: string;
    systemTypes: string[];
    complexity: string[];
    testTypes: string[];
    template: TestTemplateDefinition;
    metadata: TemplateMetadata;
}
export interface TestTemplateDefinition {
    testStructure: string;
    dataGeneration?: string;
    validationRules: string[];
    setupCode?: string;
    teardownCode?: string;
    imports?: string[];
    utilities?: string[];
}
export interface TemplateMetadata {
    author: string;
    version: string;
    createdAt: Date;
    updatedAt: Date;
    tags: string[];
    usage: number;
    rating: number;
}
export interface TestReport {
    metadata: ReportMetadata;
    summary: TestSummary;
    details: TestDetails;
    analysis: TestAnalysis;
    recommendations: string[];
    trends: TrendAnalysis;
}
export interface ReportMetadata {
    generatedAt: string;
    projectName: string;
    projectPath: string;
    reportVersion: string;
    testFramework: string;
    totalExecutionTime: number;
}
export interface TestSummary {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    skippedTests: number;
    successRate: number;
    totalExecutionTime: number;
    insights: TestInsights;
    recommendations: string[];
    nextActions: string[];
    coverage?: CoverageReport;
    performance?: {
        averageExecutionTime: number;
        slowestTests?: TestResult[];
        memoryUsage?: number;
        cpuUsage?: number;
    };
}
export interface TestDetails {
    testResults: TestResult[];
    systemResults: SystemTestResult[];
    performanceMetrics: PerformanceMetrics;
    coverageReport: CoverageReport;
}
export interface TestAnalysis {
    patterns: string[];
    trends: string[];
    anomalies: string[];
    riskAreas: string[];
    improvements: string[];
    qualityScore?: number;
}
export interface TrendAnalysis {
    performanceTrends: PerformanceTrend[];
    qualityTrends: QualityTrend[];
    coverageTrends: CoverageTrend[];
    historicalData?: any[];
}
export interface TestInsights {
    patterns: string[];
    trends: string[];
    anomalies: string[];
}
export interface SystemTestResult {
    systemName: string;
    totalTests: number;
    passedTests: number;
    failedTests: number;
    executionTime: number;
    coverage: number;
}
export interface PerformanceMetrics {
    averageExecutionTime: number;
    slowestTests: TestResult[];
    memoryUsage: number;
    cpuUsage: number;
}
export interface CoverageReport {
    overallCoverage: number;
    lineCoverage: number;
    branchCoverage: number;
    functionCoverage: number;
    uncoveredLines: string[];
    lines?: number;
    functions?: number;
    branches?: number;
    statements?: number;
}
export interface PerformanceTrend {
    metric: string;
    values: number[];
    timestamps: string[];
    trend: 'improving' | 'declining' | 'stable';
}
export interface QualityTrend {
    metric: string;
    values: number[];
    timestamps: string[];
    trend: 'improving' | 'declining' | 'stable';
}
export interface CoverageTrend {
    metric: string;
    values: number[];
    timestamps: string[];
    trend: 'improving' | 'declining' | 'stable';
}
//# sourceMappingURL=index.d.ts.map