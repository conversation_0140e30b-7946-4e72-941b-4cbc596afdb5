"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolTester = void 0;
class ToolTester {
    constructor() {
        this.results = [];
    }
    async runTest(tool, method, params) {
        const startTime = Date.now();
        const result = {
            tool,
            method,
            success: false,
            time: 0
        };
        try {
            const response = await Editor.Message.request(tool, method, params);
            result.success = true;
            result.result = response;
        }
        catch (error) {
            result.success = false;
            result.error = error instanceof Error ? error.message : String(error);
        }
        result.time = Date.now() - startTime;
        this.results.push(result);
        return result;
    }
    async testSceneOperations() {
        console.log('Testing Scene Operations...');
        // Test node creation (this is the main scene operation that works)
        const createResult = await this.runTest('scene', 'create-node', {
            name: 'TestNode',
            type: 'cc.Node'
        });
        if (createResult.success && createResult.result) {
            const nodeUuid = createResult.result;
            // Test query node info
            await this.runTest('scene', 'query-node-info', nodeUuid);
            // Test remove node
            await this.runTest('scene', 'remove-node', nodeUuid);
        }
        // Test execute scene script
        await this.runTest('scene', 'execute-scene-script', {
            name: 'cocos-mcp-server',
            method: 'test-method',
            args: []
        });
    }
    async testNodeOperations() {
        console.log('Testing Node Operations...');
        // Create a test node first
        const createResult = await this.runTest('scene', 'create-node', {
            name: 'TestNodeForOps',
            type: 'cc.Node'
        });
        if (createResult.success && createResult.result) {
            const nodeUuid = createResult.result;
            // Test set property
            await this.runTest('scene', 'set-property', {
                uuid: nodeUuid,
                path: 'position',
                dump: {
                    type: 'cc.Vec3',
                    value: { x: 100, y: 200, z: 0 }
                }
            });
            // Test add component
            await this.runTest('scene', 'add-component', {
                uuid: nodeUuid,
                component: 'cc.Sprite'
            });
            // Clean up
            await this.runTest('scene', 'remove-node', nodeUuid);
        }
    }
    async testAssetOperations() {
        console.log('Testing Asset Operations...');
        // Test asset list
        await this.runTest('asset-db', 'query-assets', {
            pattern: '**/*.png',
            ccType: 'cc.ImageAsset'
        });
        // Test query asset by path
        await this.runTest('asset-db', 'query-path', 'db://assets');
        // Test query asset by uuid (using a valid uuid format)
        await this.runTest('asset-db', 'query-uuid', 'db://assets');
    }
    async testProjectOperations() {
        console.log('Testing Project Operations...');
        // Test open project settings
        await this.runTest('project', 'open-settings', {});
        // Test query project settings
        const projectName = await this.runTest('project', 'query-setting', 'name');
        if (projectName.success) {
            console.log('Project name:', projectName.result);
        }
    }
    async runAllTests() {
        this.results = [];
        await this.testSceneOperations();
        await this.testNodeOperations();
        await this.testAssetOperations();
        await this.testProjectOperations();
        return this.getTestReport();
    }
    getTestReport() {
        const total = this.results.length;
        const passed = this.results.filter(r => r.success).length;
        const failed = total - passed;
        return {
            summary: {
                total,
                passed,
                failed,
                passRate: total > 0 ? (passed / total * 100).toFixed(2) + '%' : '0%'
            },
            results: this.results,
            grouped: this.groupResultsByTool()
        };
    }
    groupResultsByTool() {
        const grouped = {};
        for (const result of this.results) {
            if (!grouped[result.tool]) {
                grouped[result.tool] = [];
            }
            grouped[result.tool].push(result);
        }
        return grouped;
    }
}
exports.ToolTester = ToolTester;
//# sourceMappingURL=data:application/json;base64,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