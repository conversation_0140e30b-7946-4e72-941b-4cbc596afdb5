#!/usr/bin/env node

/**
 * 代码质量自动修复工具
 * 处理AI测试框架发现的代码质量问题
 */

const fs = require('fs');
const path = require('path');

class CodeQualityFixer {
    constructor(projectRoot = process.cwd()) {
        this.projectRoot = path.resolve(projectRoot, '../..');
        this.fixedFiles = [];
        this.issues = {
            consoleLogs: 0,
            todos: 0,
            longLines: 0,
            trailingSpaces: 0,
            emptyLines: 0
        };
    }

    async fixCodeQuality() {
        console.log('🔧 启动代码质量自动修复工具...\n');
        console.log(`📁 项目根目录: ${this.projectRoot}\n`);

        const scriptsDir = path.join(this.projectRoot, 'assets', 'scripts');
        
        if (!fs.existsSync(scriptsDir)) {
            console.log('❌ 脚本目录不存在');
            return;
        }

        const scriptFiles = this.findFiles(scriptsDir, ['.ts', '.js']);
        console.log(`🔍 发现 ${scriptFiles.length} 个脚本文件\n`);

        for (const file of scriptFiles) {
            await this.fixFile(file);
        }

        this.generateReport();
    }

    async fixFile(filePath) {
        try {
            const originalContent = fs.readFileSync(filePath, 'utf8');
            let content = originalContent;
            let hasChanges = false;

            console.log(`🔧 处理文件: ${path.relative(this.projectRoot, filePath)}`);

            // 1. 移除或注释console.log
            const consoleLogRegex = /console\.log\([^)]*\);?\s*/g;
            const consoleMatches = content.match(consoleLogRegex);
            if (consoleMatches) {
                content = content.replace(consoleLogRegex, (match) => {
                    this.issues.consoleLogs++;
                    // 如果在开发环境或调试代码中，保留但添加条件
                    if (match.includes('debug') || match.includes('error')) {
                        return `// DEBUG: ${match.trim()}\n`;
                    }
                    return ''; // 移除普通的console.log
                });
                hasChanges = true;
                console.log(`  ✅ 处理了 ${consoleMatches.length} 个console.log`);
            }

            // 2. 处理TODO和FIXME注释
            const todoRegex = /(\/\/\s*(TODO|FIXME|XXX):?\s*)(.*)/gi;
            const todoMatches = content.match(todoRegex);
            if (todoMatches) {
                content = content.replace(todoRegex, (match, prefix, type, comment) => {
                    this.issues.todos++;
                    const timestamp = new Date().toISOString().split('T')[0];
                    return `${prefix}[${timestamp}] ${comment.trim()}`;
                });
                hasChanges = true;
                console.log(`  ✅ 标准化了 ${todoMatches.length} 个TODO/FIXME注释`);
            }

            // 3. 修复长行代码（超过120字符）
            const lines = content.split('\n');
            const fixedLines = lines.map((line, index) => {
                if (line.length > 120 && !line.trim().startsWith('//') && !line.includes('http')) {
                    this.issues.longLines++;
                    
                    // 尝试在合适的位置断行
                    if (line.includes('&&') || line.includes('||')) {
                        return line.replace(/(\s+&&\s+|\s+\|\|\s+)/g, '\n            $1');
                    } else if (line.includes(',') && line.includes('(')) {
                        return line.replace(/,\s+/g, ',\n            ');
                    } else if (line.includes('.') && line.includes('(')) {
                        return line.replace(/\)\./g, ')\n            .');
                    }
                    
                    // 如果无法智能断行，添加注释提醒
                    return line + ' // TODO: 考虑重构此长行';
                }
                return line;
            });

            if (fixedLines.join('\n') !== content) {
                content = fixedLines.join('\n');
                hasChanges = true;
                console.log(`  ✅ 处理了长行代码`);
            }

            // 4. 移除行尾空格
            const trimmedContent = content.replace(/[ \t]+$/gm, '');
            if (trimmedContent !== content) {
                content = trimmedContent;
                hasChanges = true;
                this.issues.trailingSpaces++;
                console.log(`  ✅ 移除了行尾空格`);
            }

            // 5. 规范化空行（移除多余的连续空行）
            const normalizedContent = content.replace(/\n\s*\n\s*\n/g, '\n\n');
            if (normalizedContent !== content) {
                content = normalizedContent;
                hasChanges = true;
                this.issues.emptyLines++;
                console.log(`  ✅ 规范化了空行`);
            }

            // 6. 添加文件头注释（如果没有）
            if (!content.trim().startsWith('/**') && !content.trim().startsWith('//')) {
                const fileName = path.basename(filePath, path.extname(filePath));
                const header = `/**\n * ${fileName} - Cocos Creator组件\n * 武侠放置游戏项目\n */\n\n`;
                content = header + content;
                hasChanges = true;
                console.log(`  ✅ 添加了文件头注释`);
            }

            // 保存修改后的文件
            if (hasChanges) {
                fs.writeFileSync(filePath, content, 'utf8');
                this.fixedFiles.push(path.relative(this.projectRoot, filePath));
                console.log(`  💾 文件已保存\n`);
            } else {
                console.log(`  ✨ 文件无需修改\n`);
            }

        } catch (error) {
            console.log(`  ❌ 处理文件时出错: ${error.message}\n`);
        }
    }

    findFiles(dir, extensions) {
        const files = [];
        
        if (!fs.existsSync(dir)) return files;

        try {
            const items = fs.readdirSync(dir);
            for (const item of items) {
                const fullPath = path.join(dir, item);
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    files.push(...this.findFiles(fullPath, extensions));
                } else if (extensions.some(ext => item.endsWith(ext))) {
                    files.push(fullPath);
                }
            }
        } catch (error) {
            // 忽略权限错误
        }
        
        return files;
    }

    generateReport() {
        console.log('\n' + '='.repeat(60));
        console.log('🔧 代码质量修复报告');
        console.log('='.repeat(60));
        
        console.log(`📊 修复统计:`);
        console.log(`  • 修改的文件: ${this.fixedFiles.length}`);
        console.log(`  • Console.log处理: ${this.issues.consoleLogs}`);
        console.log(`  • TODO/FIXME标准化: ${this.issues.todos}`);
        console.log(`  • 长行代码处理: ${this.issues.longLines}`);
        console.log(`  • 行尾空格清理: ${this.issues.trailingSpaces}`);
        console.log(`  • 空行规范化: ${this.issues.emptyLines}`);
        
        if (this.fixedFiles.length > 0) {
            console.log(`\n📝 修改的文件列表:`);
            for (const file of this.fixedFiles) {
                console.log(`  • ${file}`);
            }
        }
        
        const totalIssues = Object.values(this.issues).reduce((a, b) => a + b, 0);
        console.log(`\n🎉 总共修复了 ${totalIssues} 个代码质量问题！`);
        
        // 保存修复报告
        const report = {
            timestamp: new Date().toISOString(),
            fixedFiles: this.fixedFiles,
            issues: this.issues,
            totalIssues
        };
        
        const reportPath = path.join(__dirname, 'code-quality-fix-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📄 详细报告已保存到: ${reportPath}`);
        
        // 生成建议
        console.log(`\n💡 后续建议:`);
        console.log(`  • 配置ESLint和Prettier进行自动化代码格式化`);
        console.log(`  • 设置Git pre-commit hooks防止代码质量问题`);
        console.log(`  • 定期运行此工具维护代码质量`);
        console.log(`  • 考虑使用TypeScript严格模式提高代码质量`);
    }
}

// 主函数
async function main() {
    const fixer = new CodeQualityFixer();
    
    try {
        await fixer.fixCodeQuality();
    } catch (error) {
        console.error('❌ 代码质量修复失败:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { CodeQualityFixer };
