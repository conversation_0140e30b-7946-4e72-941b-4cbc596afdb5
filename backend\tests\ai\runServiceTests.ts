import { serviceTestRunner, ITestSuiteResult } from './ServiceTestRunner';
import { databaseManager } from '../../src/config/database';
import { Logger } from '../../src/utils/logger';
import fs from 'fs';
import path from 'path';

/**
 * AI测试报告生成器
 */
class TestReportGenerator {
  /**
   * 生成测试报告
   */
  public generateReport(suiteResult: ITestSuiteResult): string {
    const timestamp = new Date().toISOString();
    
    let report = `# Day10-11 AI测试验收报告

> 📅 **测试日期**: ${timestamp}  
> 🤖 **测试框架**: AI测试框架  
> ⏱️ **测试时长**: ${suiteResult.duration}ms  
> 📊 **测试覆盖率**: ${suiteResult.coverage.toFixed(2)}%

## 📋 测试摘要

- **测试套件**: ${suiteResult.suiteName}
- **总测试数**: ${suiteResult.totalTests}
- **通过测试**: ${suiteResult.passedTests}
- **失败测试**: ${suiteResult.failedTests}
- **成功率**: ${((suiteResult.passedTests / suiteResult.totalTests) * 100).toFixed(2)}%

## 🎯 测试结果详情

`;

    // 按类别分组测试结果
    const categories = this.categorizeTests(suiteResult.results);
    
    for (const [category, tests] of Object.entries(categories)) {
      report += `### ${category}\n\n`;
      
      for (const test of tests) {
        const status = test.success ? '✅' : '❌';
        report += `${status} **${test.testName}** (${test.duration}ms)\n`;
        
        if (test.success) {
          report += `   - 测试通过\n`;
          if (test.details) {
            report += `   - 详情: ${JSON.stringify(test.details, null, 2)}\n`;
          }
        } else {
          report += `   - 测试失败\n`;
          if (test.errors) {
            report += `   - 错误: ${test.errors.join(', ')}\n`;
          }
        }
        report += '\n';
      }
    }

    // 性能分析
    report += this.generatePerformanceAnalysis(suiteResult);

    // 算法验证结果
    report += this.generateAlgorithmValidation(suiteResult);

    // 问题和建议
    report += this.generateIssuesAndRecommendations(suiteResult);

    return report;
  }

  /**
   * 按类别分组测试
   */
  private categorizeTests(results: any[]): Record<string, any[]> {
    const categories: Record<string, any[]> = {
      '技能服务测试': [],
      '用户服务测试': [],
      '战斗服务测试': [],
      '算法验证测试': [],
      '性能测试': [],
    };

    for (const result of results) {
      if (result.testName.includes('技能')) {
        categories['技能服务测试'].push(result);
      } else if (result.testName.includes('用户')) {
        categories['用户服务测试'].push(result);
      } else if (result.testName.includes('战斗')) {
        categories['战斗服务测试'].push(result);
      } else if (result.testName.includes('算法') || result.testName.includes('计算')) {
        categories['算法验证测试'].push(result);
      } else if (result.testName.includes('性能')) {
        categories['性能测试'].push(result);
      }
    }

    return categories;
  }

  /**
   * 生成性能分析
   */
  private generatePerformanceAnalysis(suiteResult: ITestSuiteResult): string {
    const performanceTests = suiteResult.results.filter(r => r.testName.includes('性能'));
    
    let analysis = `## 📊 性能分析

`;

    if (performanceTests.length === 0) {
      analysis += '未执行性能测试。\n\n';
      return analysis;
    }

    for (const test of performanceTests) {
      analysis += `### ${test.testName}\n\n`;
      
      if (test.success && test.details) {
        const details = test.details;
        analysis += `- **平均响应时间**: ${details.averageTime}ms\n`;
        analysis += `- **总执行时间**: ${details.totalTime}ms\n`;
        analysis += `- **迭代次数**: ${details.iterations}\n`;
        analysis += `- **性能评级**: ${details.performanceGood ? '✅ 良好' : '⚠️ 需优化'}\n`;
        
        if (details.averageTime > 200) {
          analysis += `- **建议**: 响应时间超过200ms，建议优化\n`;
        }
      }
      analysis += '\n';
    }

    return analysis;
  }

  /**
   * 生成算法验证结果
   */
  private generateAlgorithmValidation(suiteResult: ITestSuiteResult): string {
    const algorithmTests = suiteResult.results.filter(r => 
      r.testName.includes('算法') || r.testName.includes('计算')
    );
    
    let validation = `## 🔍 算法验证结果

`;

    if (algorithmTests.length === 0) {
      validation += '未执行算法验证测试。\n\n';
      return validation;
    }

    for (const test of algorithmTests) {
      validation += `### ${test.testName}\n\n`;
      
      if (test.success && test.details) {
        const details = test.details;
        
        if (test.testName.includes('伤害计算')) {
          validation += `- **预期基础伤害**: ${details.expectedBaseDamage}\n`;
          validation += `- **实际伤害**: ${details.actualDamage}\n`;
          validation += `- **伤害范围正确**: ${details.damageInRange ? '✅' : '❌'}\n`;
          validation += `- **计算正确性**: ${details.calculationCorrect ? '✅' : '❌'}\n`;
        }
        
        if (test.testName.includes('经验等级')) {
          validation += `- **公式验证**: ${details.formulaCorrect ? '✅ 正确' : '❌ 错误'}\n`;
          if (details.testCases) {
            validation += `- **测试用例**:\n`;
            for (const testCase of details.testCases) {
              validation += `  - 等级${testCase.level}: 期望${testCase.expectedExp}，实际${testCase.actualExp} ${testCase.tolerance ? '✅' : '❌'}\n`;
            }
          }
        }
      }
      validation += '\n';
    }

    return validation;
  }

  /**
   * 生成问题和建议
   */
  private generateIssuesAndRecommendations(suiteResult: ITestSuiteResult): string {
    const failedTests = suiteResult.results.filter(r => !r.success);
    const slowTests = suiteResult.results.filter(r => r.duration > 1000);
    
    let issues = `## ⚠️ 发现的问题和建议

`;

    if (failedTests.length === 0 && slowTests.length === 0) {
      issues += '🎉 未发现重大问题，所有测试均通过且性能良好！\n\n';
    } else {
      if (failedTests.length > 0) {
        issues += `### 失败的测试 (${failedTests.length}个)\n\n`;
        for (const test of failedTests) {
          issues += `- **${test.testName}**: ${test.errors?.join(', ') || '未知错误'}\n`;
        }
        issues += '\n';
      }

      if (slowTests.length > 0) {
        issues += `### 性能问题 (${slowTests.length}个)\n\n`;
        for (const test of slowTests) {
          issues += `- **${test.testName}**: 执行时间${test.duration}ms，建议优化\n`;
        }
        issues += '\n';
      }
    }

    // 通用建议
    issues += `### 优化建议\n\n`;
    issues += `1. **缓存优化**: 对频繁访问的数据增加缓存机制\n`;
    issues += `2. **数据库优化**: 优化查询语句和索引设计\n`;
    issues += `3. **算法优化**: 对计算密集型操作进行优化\n`;
    issues += `4. **错误处理**: 完善边界条件和异常处理\n`;
    issues += `5. **监控告警**: 增加性能监控和告警机制\n\n`;

    return issues;
  }

  /**
   * 保存报告到文件
   */
  public async saveReport(report: string, filename: string): Promise<void> {
    const reportsDir = path.join(process.cwd(), '../../Reports');
    
    // 确保Reports目录存在
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }

    const filePath = path.join(reportsDir, filename);
    fs.writeFileSync(filePath, report, 'utf8');
    
    Logger.info('测试报告已保存', { filePath });
  }
}

/**
 * 主测试执行函数
 */
async function runServiceTests(): Promise<void> {
  const reportGenerator = new TestReportGenerator();
  
  try {
    Logger.info('开始Day10-11 AI测试验收');

    // 连接数据库
    await databaseManager.connect();
    Logger.info('数据库连接成功');

    // 运行测试套件
    const suiteResult = await serviceTestRunner.runAllTests();

    // 生成测试报告
    const report = reportGenerator.generateReport(suiteResult);
    
    // 保存报告
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `Day10-11-AI-Test-Report-${timestamp}.md`;
    await reportGenerator.saveReport(report, filename);

    // 输出测试摘要
    console.log('\n=== AI测试验收完成 ===');
    console.log(`测试套件: ${suiteResult.suiteName}`);
    console.log(`总测试数: ${suiteResult.totalTests}`);
    console.log(`通过测试: ${suiteResult.passedTests}`);
    console.log(`失败测试: ${suiteResult.failedTests}`);
    console.log(`成功率: ${((suiteResult.passedTests / suiteResult.totalTests) * 100).toFixed(2)}%`);
    console.log(`测试时长: ${suiteResult.duration}ms`);
    console.log(`测试覆盖率: ${suiteResult.coverage.toFixed(2)}%`);
    console.log(`报告文件: ${filename}`);

    if (suiteResult.failedTests > 0) {
      console.log('\n⚠️ 存在失败的测试，请查看详细报告');
      process.exit(1);
    } else {
      console.log('\n🎉 所有测试通过！');
      process.exit(0);
    }

  } catch (error) {
    Logger.error('AI测试执行失败', error);
    console.error('测试执行失败:', error.message);
    process.exit(1);
  } finally {
    // 断开数据库连接
    await databaseManager.disconnect();
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  runServiceTests();
}

export { runServiceTests, TestReportGenerator };
