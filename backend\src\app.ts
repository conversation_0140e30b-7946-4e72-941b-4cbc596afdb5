import express = require('express');
import cors = require('cors');
import helmet = require('helmet');
import compression = require('compression');
import morgan = require('morgan');
import dotenv = require('dotenv');
import { DatabaseConfig } from './config/database';
import { RedisConfig } from './config/redis';
import { Logger } from './utils/logger';
import { apiRoutes } from './routes';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import { setupSwagger } from './config/swagger';

// 加载环境变量
dotenv.config();

export class App {
  public app: express.Application;
  private dbConfig: DatabaseConfig;
  private redisConfig: RedisConfig;

  constructor() {
    this.app = express();
    this.dbConfig = DatabaseConfig.getInstance();
    this.redisConfig = RedisConfig.getInstance();
    
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeSwagger();
    this.initializeErrorHandling();
  }

  /**
   * 初始化中间件
   */
  private initializeMiddlewares(): void {
    // 安全中间件
    this.app.use(helmet.default());
    
    // CORS配置
    this.app.use(cors({
      origin: process.env['CORS_ORIGIN'] || '*',
      credentials: true,
    }));
    
    // 压缩响应
    this.app.use(compression());
    
    // 请求日志
    this.app.use(morgan('combined', {
      stream: {
        write: (message: string) => {
          Logger.info(message.trim());
        },
      },
    }));
    
    // 解析JSON请求体
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  }

  /**
   * 初始化路由
   */
  private initializeRoutes(): void {
    // 健康检查路由
    this.app.get('/health', async (_req, res) => {
      try {
        const dbHealth = await this.dbConfig.healthCheck();
        const redisHealth = await this.redisConfig.healthCheck();
        
        const health = {
          status: 'ok',
          timestamp: new Date().toISOString(),
          services: {
            database: dbHealth ? 'healthy' : 'unhealthy',
            redis: redisHealth ? 'healthy' : 'unhealthy',
          },
          version: process.env['npm_package_version'] || '1.0.0',
          environment: process.env['NODE_ENV'] || 'development',
        };
        
        const statusCode = dbHealth && redisHealth ? 200 : 503;
        res.status(statusCode).json(health);
      } catch (error) {
        Logger.error('健康检查失败', error);
        res.status(503).json({
          status: 'error',
          message: 'Health check failed',
          timestamp: new Date().toISOString(),
        });
      }
    });

    // API信息路由
    this.app.get('/api/info', (_req, res) => {
      res.json({
        name: 'IdleGame Backend API',
        version: process.env['npm_package_version'] || '1.0.0',
        description: '江湖风放置游戏后端服务',
        environment: process.env['NODE_ENV'] || 'development',
        timestamp: new Date().toISOString(),
      });
    });

    // API路由
    this.app.use('/api/v1', apiRoutes);

    // 根路由
    this.app.get('/', (_req, res) => {
      res.json({
        message: '欢迎使用江湖风放置游戏API',
        version: process.env['npm_package_version'] || '1.0.0',
        docs: '/api/docs',
        health: '/health',
        api: '/api/v1',
      });
    });

    // 404处理
    this.app.use('*', notFoundHandler);
  }

  /**
   * 初始化Swagger文档
   */
  private initializeSwagger(): void {
    setupSwagger(this.app);
  }

  /**
   * 初始化错误处理
   */
  private initializeErrorHandling(): void {
    // 全局错误处理中间件
    this.app.use(errorHandler);
  }

  /**
   * 连接数据库和缓存
   */
  public async connectDatabases(): Promise<void> {
    try {
      Logger.info('正在连接数据库和缓存服务...');
      
      await this.dbConfig.connect();
      await this.redisConfig.connect();
      
      Logger.info('所有数据库服务连接成功');
    } catch (error) {
      Logger.error('数据库连接失败', error);
      throw error;
    }
  }

  /**
   * 断开数据库连接
   */
  public async disconnectDatabases(): Promise<void> {
    try {
      Logger.info('正在断开数据库连接...');
      
      await this.dbConfig.disconnect();
      await this.redisConfig.disconnect();
      
      Logger.info('所有数据库连接已断开');
    } catch (error) {
      Logger.error('断开数据库连接失败', error);
      throw error;
    }
  }

  /**
   * 获取Express应用实例
   */
  public getApp(): express.Application {
    return this.app;
  }
}
