/**
 * 网络通信模块统一导出
 * 提供HTTP客户端、WebSocket客户端、网络管理器和相关类型的导入入口
 */

// 导入核心网络类用于内部使用
import { HttpClient } from './HttpClient';
import { WebSocketClient } from './WebSocketClient';
import { NetworkManager } from './NetworkManager';

// 重新导出核心网络类
export { HttpClient } from './HttpClient';
export { WebSocketClient } from './WebSocketClient';
export { NetworkManager } from './NetworkManager';

// 类型定义
export * from './types/NetworkTypes';

// 便捷访问类
export class Network {
    private static _httpClient?: HttpClient;
    private static _websocketClient?: WebSocketClient;
    private static _networkManager?: NetworkManager;

    /**
     * 获取HTTP客户端实例
     */
    public static getHttpClient(): HttpClient {
        if (!Network._httpClient) {
            Network._httpClient = new HttpClient();
        }
        return Network._httpClient;
    }

    /**
     * 获取WebSocket客户端实例
     */
    public static getWebSocketClient(): WebSocketClient {
        if (!Network._websocketClient) {
            Network._websocketClient = new WebSocketClient();
        }
        return Network._websocketClient;
    }

    /**
     * 获取网络管理器实例
     */
    public static getNetworkManager(): NetworkManager {
        if (!Network._networkManager) {
            Network._networkManager = NetworkManager.getInstance();
        }
        return Network._networkManager;
    }

    /**
     * 快速HTTP GET请求
     */
    public static async get<T = any>(url: string, params?: any): Promise<T> {
        return await Network.getHttpClient().get<T>(url, params);
    }

    /**
     * 快速HTTP POST请求
     */
    public static async post<T = any>(url: string, data?: any): Promise<T> {
        return await Network.getHttpClient().post<T>(url, data);
    }

    /**
     * 快速HTTP PUT请求
     */
    public static async put<T = any>(url: string, data?: any): Promise<T> {
        return await Network.getHttpClient().put<T>(url, data);
    }

    /**
     * 快速HTTP DELETE请求
     */
    public static async delete<T = any>(url: string): Promise<T> {
        return await Network.getHttpClient().delete<T>(url);
    }

    /**
     * 连接WebSocket
     */
    public static async connectWebSocket(url: string): Promise<void> {
        return await Network.getWebSocketClient().connect(url);
    }

    /**
     * 发送WebSocket消息
     */
    public static sendWebSocketMessage(message: any): void {
        Network.getWebSocketClient().send(message);
    }

    /**
     * 监听WebSocket事件
     */
    public static onWebSocketEvent(event: string, callback: Function): void {
        Network.getWebSocketClient().on(event, callback);
    }

    /**
     * 检查网络状态
     */
    public static isOnline(): boolean {
        return Network.getNetworkManager().isOnline();
    }

    /**
     * 获取网络状态
     */
    public static getNetworkStatus() {
        return Network.getNetworkManager().getNetworkStatus();
    }
}
