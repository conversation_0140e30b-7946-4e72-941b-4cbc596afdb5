import { _decorator, Component, Node, director, Scene } from 'cc';
import { BaseManager } from './BaseManager';
import { SceneData, TransitionType, SceneManagerConfig } from './types/ManagerTypes';

const { ccclass, property } = _decorator;

/**
 * 场景管理器
 * 负责场景的加载、切换和状态管理
 */
@ccclass('SceneManager')
export class SceneManager extends BaseManager {
    
    /**
     * 当前场景数据
     */
    private _currentScene: SceneData | null = null;
    
    /**
     * 场景历史记录
     */
    private _sceneHistory: SceneData[] = [];
    
    /**
     * 场景管理器配置
     */
    private _config: SceneManagerConfig = {
        preloadScenes: ['Launch', 'Main', 'Battle'],
        defaultTransition: TransitionType.FADE,
        transitionDuration: 1000,
        debug: true,
        autoInit: true
    };
    
    /**
     * 是否正在切换场景
     */
    private _isTransitioning: boolean = false;
    
    /**
     * 预加载的场景缓存
     */
    private _preloadedScenes: Set<string> = new Set();

    /**
     * 获取SceneManager单例实例
     */
    public static getInstance(): SceneManager {
        return super.getInstance.call(this) as SceneManager;
    }

    /**
     * 初始化场景管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('🎬 初始化场景管理器...');
        
        try {
            // 注册场景事件监听器
            this.registerSceneEvents();
            
            // 获取当前场景信息
            this.updateCurrentSceneInfo();
            
            // 预加载场景
            await this.preloadScenes();
            
            console.log('✅ 场景管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 场景管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁场景管理器
     */
    public destroyManager(): void {
        console.log('🗑️ 销毁场景管理器...');
        
        // 移除场景事件监听器
        this.unregisterSceneEvents();
        
        // 清理数据
        this._currentScene = null;
        this._sceneHistory = [];
        this._isTransitioning = false;
        this._preloadedScenes.clear();
        
        console.log('✅ 场景管理器销毁完成');
    }

    /**
     * 注册场景事件监听器
     */
    private registerSceneEvents(): void {
        console.log('📡 注册场景事件监听器...');
        
        // 监听场景启动前事件
        director.on(director.EVENT_BEFORE_SCENE_LAUNCH, this.onBeforeSceneLaunch, this);
        
        // 监听场景启动后事件
        director.on(director.EVENT_AFTER_SCENE_LAUNCH, this.onAfterSceneLaunch, this);
    }

    /**
     * 移除场景事件监听器
     */
    private unregisterSceneEvents(): void {
        console.log('📡 移除场景事件监听器...');
        
        director.off(director.EVENT_BEFORE_SCENE_LAUNCH, this.onBeforeSceneLaunch, this);
        director.off(director.EVENT_AFTER_SCENE_LAUNCH, this.onAfterSceneLaunch, this);
    }

    /**
     * 场景启动前事件处理
     */
    private onBeforeSceneLaunch(scene: Scene): void {
        console.log(`🎬 场景启动前: ${scene.name}`);
        this._isTransitioning = true;
    }

    /**
     * 场景启动后事件处理
     */
    private onAfterSceneLaunch(scene: Scene): void {
        console.log(`🎬 场景启动后: ${scene.name}`);
        this._isTransitioning = false;
        this.updateCurrentSceneInfo();
    }

    /**
     * 更新当前场景信息
     */
    private updateCurrentSceneInfo(): void {
        const currentScene = director.getScene();
        if (currentScene) {
            const sceneData: SceneData = {
                name: currentScene.name,
                loadTime: Date.now(),
                previousScene: this._currentScene?.name
            };
            
            // 添加到历史记录
            if (this._currentScene) {
                this._sceneHistory.push(this._currentScene);
                
                // 限制历史记录长度
                if (this._sceneHistory.length > 10) {
                    this._sceneHistory.shift();
                }
            }
            
            this._currentScene = sceneData;
            console.log(`📍 当前场景更新: ${sceneData.name}`);
        }
    }

    /**
     * 预加载场景
     */
    private async preloadScenes(): Promise<void> {
        console.log('📦 预加载场景...');
        
        const preloadPromises = this._config.preloadScenes?.map(sceneName => 
            this.preloadScene(sceneName)
        ) || [];
        
        try {
            await Promise.all(preloadPromises);
            console.log('✅ 场景预加载完成');
        } catch (error) {
            console.warn('⚠️ 部分场景预加载失败:', error);
        }
    }

    /**
     * 预加载单个场景
     */
    private async preloadScene(sceneName: string): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            director.preloadScene(sceneName, (error) => {
                if (error) {
                    console.warn(`⚠️ 场景预加载失败: ${sceneName}`, error);
                    reject(error);
                } else {
                    this._preloadedScenes.add(sceneName);
                    console.log(`✅ 场景预加载成功: ${sceneName}`);
                    resolve();
                }
            });
        });
    }

    /**
     * 切换场景
     */
    public async switchScene(
        sceneName: string, 
        params?: any, 
        transition: TransitionType = this._config.defaultTransition || TransitionType.FADE
    ): Promise<void> {
        console.log(`🔄 切换场景: ${this._currentScene?.name} → ${sceneName}`);
        
        if (this._isTransitioning) {
            console.warn('⚠️ 正在切换场景中，请稍后再试');
            return;
        }
        
        if (this._currentScene?.name === sceneName) {
            console.warn('⚠️ 目标场景与当前场景相同');
            return;
        }
        
        try {
            this._isTransitioning = true;
            
            // 执行场景切换前的处理
            await this.beforeSceneSwitch(sceneName, params);
            
            // 执行场景切换
            await this.performSceneSwitch(sceneName, transition);
            
            // 执行场景切换后的处理
            await this.afterSceneSwitch(sceneName, params);
            
            console.log(`✅ 场景切换成功: ${sceneName}`);
            
        } catch (error) {
            console.error(`❌ 场景切换失败: ${sceneName}`, error);
            this._isTransitioning = false;
            throw error;
        }
    }

    /**
     * 场景切换前处理
     */
    private async beforeSceneSwitch(sceneName: string, params?: any): Promise<void> {
        console.log(`🔄 场景切换前处理: ${sceneName}`);
        
        // 这里可以添加场景切换前的逻辑
        // 例如：保存当前场景状态、显示加载界面等
        
        // 如果场景未预加载，则进行预加载
        if (!this._preloadedScenes.has(sceneName)) {
            await this.preloadScene(sceneName);
        }
    }

    /**
     * 执行场景切换
     */
    private async performSceneSwitch(sceneName: string, transition: TransitionType): Promise<void> {
        console.log(`🎬 执行场景切换: ${sceneName} (${transition})`);
        
        return new Promise<void>((resolve, reject) => {
            director.loadScene(sceneName, (error) => {
                if (error) {
                    console.error(`❌ 场景加载失败: ${sceneName}`, error);
                    reject(error);
                } else {
                    console.log(`✅ 场景加载成功: ${sceneName}`);
                    resolve();
                }
            });
        });
    }

    /**
     * 场景切换后处理
     */
    private async afterSceneSwitch(sceneName: string, params?: any): Promise<void> {
        console.log(`🔄 场景切换后处理: ${sceneName}`);
        
        // 这里可以添加场景切换后的逻辑
        // 例如：隐藏加载界面、初始化场景数据等
        
        // 触发场景切换完成事件
        // EventManager.getInstance().emit('sceneChanged', sceneName, params);
    }

    /**
     * 返回上一个场景
     */
    public async goBack(): Promise<void> {
        if (this._sceneHistory.length === 0) {
            console.warn('⚠️ 没有历史场景可以返回');
            return;
        }
        
        const previousScene = this._sceneHistory.pop();
        if (previousScene) {
            await this.switchScene(previousScene.name, previousScene.params);
        }
    }

    /**
     * 重新加载当前场景
     */
    public async reloadCurrentScene(): Promise<void> {
        if (!this._currentScene) {
            console.warn('⚠️ 没有当前场景可以重新加载');
            return;
        }
        
        const sceneName = this._currentScene.name;
        const params = this._currentScene.params;
        
        console.log(`🔄 重新加载当前场景: ${sceneName}`);
        await this.switchScene(sceneName, params);
    }

    /**
     * 获取当前场景信息
     */
    public getCurrentScene(): SceneData | null {
        return this._currentScene ? { ...this._currentScene } : null;
    }

    /**
     * 获取场景历史记录
     */
    public getSceneHistory(): SceneData[] {
        return [...this._sceneHistory];
    }

    /**
     * 检查场景是否已预加载
     */
    public isScenePreloaded(sceneName: string): boolean {
        return this._preloadedScenes.has(sceneName);
    }

    /**
     * 检查是否正在切换场景
     */
    public isTransitioning(): boolean {
        return this._isTransitioning;
    }

    /**
     * 获取场景管理器配置
     */
    public getConfig(): SceneManagerConfig {
        return { ...this._config };
    }

    /**
     * 更新场景管理器配置
     */
    public updateConfig(config: Partial<SceneManagerConfig>): void {
        this._config = { ...this._config, ...config };
        console.log('⚙️ 场景管理器配置已更新:', config);
    }

    /**
     * 清理场景历史记录
     */
    public clearHistory(): void {
        this._sceneHistory = [];
        console.log('🗑️ 场景历史记录已清理');
    }
}
