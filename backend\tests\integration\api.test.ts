import request from 'supertest';
import { app } from '../../src/app';
import { Logger } from '../../src/utils/logger';

describe('API集成测试', () => {
  let authToken: string;
  let testUserId: string;
  let testCharacterId: string;

  beforeAll(async () => {
    // 设置测试环境
    process.env['NODE_ENV'] = 'test';
    process.env['LOG_LEVEL'] = 'error';
    
    Logger.info('开始API集成测试');
  });

  afterAll(async () => {
    Logger.info('API集成测试完成');
  });

  describe('健康检查测试', () => {
    it('应该返回基础健康状态', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'healthy');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
      expect(response.body).toHaveProperty('version');
    });

    it('应该返回详细健康状态', async () => {
      const response = await request(app)
        .get('/health/detailed')
        .expect('Content-Type', /json/);

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('dependencies');
      expect(response.body).toHaveProperty('memory');
      expect(response.body).toHaveProperty('performance');
    });

    it('应该返回就绪状态', async () => {
      const response = await request(app)
        .get('/health/readiness')
        .expect('Content-Type', /json/);

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('checks');
    });

    it('应该返回存活状态', async () => {
      const response = await request(app)
        .get('/health/liveness')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'alive');
      expect(response.body).toHaveProperty('uptime');
    });
  });

  describe('认证系统测试', () => {
    const testUser = {
      username: 'testuser_' + Date.now(),
      email: 'test_' + Date.now() + '@example.com',
      password: 'TestPassword123!',
      confirmPassword: 'TestPassword123!',
    };

    it('应该成功注册新用户', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(testUser)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      
      testUserId = response.body.data.user.id;
      authToken = response.body.data.token;
    });

    it('应该拒绝重复的用户名', async () => {
      const response = await request(app)
        .post('/api/v1/auth/register')
        .send(testUser)
        .expect(409);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code');
    });

    it('应该成功登录', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: testUser.password,
        })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
    });

    it('应该拒绝错误的凭据', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .send({
          username: testUser.username,
          password: 'wrongpassword',
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    it('应该验证JWT令牌', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('user');
    });

    it('应该拒绝无效的令牌', async () => {
      const response = await request(app)
        .get('/api/v1/auth/profile')
        .set('Authorization', 'Bearer invalid_token')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('用户管理测试', () => {
    it('应该获取用户列表', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('items');
      expect(response.body.data).toHaveProperty('pagination');
    });

    it('应该获取用户详情', async () => {
      const response = await request(app)
        .get(`/api/v1/users/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user).toHaveProperty('id', testUserId);
    });

    it('应该更新用户信息', async () => {
      const updateData = {
        email: 'updated_' + Date.now() + '@example.com',
      };

      const response = await request(app)
        .put(`/api/v1/users/${testUserId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data.user).toHaveProperty('email', updateData.email);
    });

    it('应该处理不存在的用户', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await request(app)
        .get(`/api/v1/users/${fakeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 'USER_NOT_FOUND');
    });
  });

  describe('角色系统测试', () => {
    const testCharacter = {
      name: 'TestCharacter_' + Date.now(),
      class: 'warrior',
      gender: 'male',
    };

    it('应该创建新角色', async () => {
      const response = await request(app)
        .post('/api/v1/character')
        .set('Authorization', `Bearer ${authToken}`)
        .send(testCharacter)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('character');
      expect(response.body.data.character).toHaveProperty('name', testCharacter.name);
      
      testCharacterId = response.body.data.character.id;
    });

    it('应该获取角色列表', async () => {
      const response = await request(app)
        .get('/api/v1/character')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('characters');
      expect(Array.isArray(response.body.data.characters)).toBe(true);
    });

    it('应该获取角色详情', async () => {
      const response = await request(app)
        .get(`/api/v1/character/${testCharacterId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('character');
      expect(response.body.data.character).toHaveProperty('id', testCharacterId);
    });

    it('应该更新角色信息', async () => {
      const updateData = {
        name: 'UpdatedCharacter_' + Date.now(),
      };

      const response = await request(app)
        .put(`/api/v1/character/${testCharacterId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data.character).toHaveProperty('name', updateData.name);
    });

    it('应该删除角色', async () => {
      const response = await request(app)
        .delete(`/api/v1/character/${testCharacterId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
    });

    it('应该验证角色创建参数', async () => {
      const invalidCharacter = {
        name: '', // 无效的名称
        class: 'invalid_class', // 无效的职业
      };

      const response = await request(app)
        .post('/api/v1/character')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidCharacter)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('errors');
      expect(Array.isArray(response.body.errors)).toBe(true);
    });
  });

  describe('错误处理测试', () => {
    it('应该处理404错误', async () => {
      const response = await request(app)
        .get('/api/v1/nonexistent')
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('code');
    });

    it('应该处理无效的JSON', async () => {
      const response = await request(app)
        .post('/api/v1/auth/login')
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });

    it('应该处理缺少认证头', async () => {
      const response = await request(app)
        .get('/api/v1/users')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('code', 'MISSING_TOKEN');
    });

    it('应该处理无效的ID格式', async () => {
      const response = await request(app)
        .get('/api/v1/users/invalid_id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('errors');
    });
  });

  describe('分页测试', () => {
    it('应该支持分页参数', async () => {
      const response = await request(app)
        .get('/api/v1/users?page=1&limit=5')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toHaveProperty('pagination');
      expect(response.body.data.pagination).toHaveProperty('page', 1);
      expect(response.body.data.pagination).toHaveProperty('limit', 5);
    });

    it('应该验证分页参数', async () => {
      const response = await request(app)
        .get('/api/v1/users?page=0&limit=1000')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('errors');
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内响应', async () => {
      const startTime = Date.now();
      
      await request(app)
        .get('/health')
        .expect(200);
      
      const responseTime = Date.now() - startTime;
      expect(responseTime).toBeLessThan(1000); // 1秒内响应
    });

    it('应该处理并发请求', async () => {
      const promises = Array.from({ length: 10 }, () =>
        request(app)
          .get('/health')
          .expect(200)
      );

      const responses = await Promise.all(promises);
      expect(responses).toHaveLength(10);
      responses.forEach(response => {
        expect(response.body).toHaveProperty('status', 'healthy');
      });
    });
  });

  describe('API文档测试', () => {
    it('应该提供Swagger文档', async () => {
      const response = await request(app)
        .get('/api/docs.json')
        .expect(200);

      expect(response.body).toHaveProperty('openapi');
      expect(response.body).toHaveProperty('info');
      expect(response.body).toHaveProperty('paths');
    });

    it('应该提供文档状态', async () => {
      const response = await request(app)
        .get('/api/docs/status')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('spec');
      expect(response.body).toHaveProperty('urls');
    });
  });
});
