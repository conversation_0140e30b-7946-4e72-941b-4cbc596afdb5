import { _decorator, Component } from 'cc';
import { BaseManager } from '../managers/BaseManager';
import { HttpClient } from './HttpClient';
import { WebSocketClient } from './WebSocketClient';
import { 
    NetworkType, 
    INetworkStatus, 
    INetworkRequest, 
    INetworkError,
    INetworkManagerConfig,
    NetworkEventType
} from './types/NetworkTypes';
import { EventManager } from '../managers/EventManager';

const { ccclass } = _decorator;

/**
 * 网络管理器
 * 统一管理HTTP和WebSocket连接，提供网络状态监控、请求队列等功能
 */
@ccclass('NetworkManager')
export class NetworkManager extends BaseManager {
    private static _instance: NetworkManager;
    
    private _httpClient: HttpClient;
    private _websocketClient: WebSocketClient;
    private _networkStatus: INetworkStatus;
    private _requestQueue: INetworkRequest[] = [];
    private _isProcessingQueue: boolean = false;
    private _config: INetworkManagerConfig;
    private _maxQueueSize: number = 100;
    private _enableQueue: boolean = true;
    private _enableRetry: boolean = true;
    private _enableHeartbeat: boolean = true;

    constructor() {
        super();
        
        this._config = {
            enableQueue: true,
            maxQueueSize: 100,
            enableRetry: true,
            enableHeartbeat: true
        };

        this._httpClient = new HttpClient(this._config.httpOptions);
        this._websocketClient = new WebSocketClient(this._config.websocketOptions);
        
        this._networkStatus = {
            isOnline: navigator.onLine,
            networkType: this._detectNetworkType(),
            connectionQuality: 'good',
            latency: 0,
            lastCheck: Date.now()
        };

        this._initializeNetworkMonitoring();
    }

    /**
     * 获取单例实例
     */
    public static getInstance(): NetworkManager {
        if (!NetworkManager._instance) {
            NetworkManager._instance = new NetworkManager();
        }
        return NetworkManager._instance;
    }

    /**
     * 初始化管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('🌐 网络管理器初始化开始...');
        
        try {
            // 配置HTTP客户端拦截器
            this._setupHttpInterceptors();
            
            // 配置WebSocket事件监听
            this._setupWebSocketListeners();
            
            // 开始网络状态监控
            this._startNetworkMonitoring();
            
            console.log('✅ 网络管理器初始化完成');
        } catch (error) {
            console.error('❌ 网络管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁管理器
     */
    public destroyManager(): void {
        console.log('🗑️ 网络管理器销毁开始...');
        
        this._stopNetworkMonitoring();
        this._websocketClient.destroy();
        this._requestQueue = [];
        
        console.log('✅ 网络管理器销毁完成');
    }

    /**
     * 配置网络管理器
     */
    public configure(config: INetworkManagerConfig): void {
        this._config = { ...this._config, ...config };
        
        if (config.httpOptions) {
            this._httpClient.configure(config.httpOptions);
        }
        
        if (config.websocketOptions) {
            this._websocketClient.configure(config.websocketOptions);
        }
        
        if (config.maxQueueSize !== undefined) {
            this._maxQueueSize = config.maxQueueSize;
        }
        
        if (config.enableQueue !== undefined) {
            this._enableQueue = config.enableQueue;
        }
        
        if (config.enableRetry !== undefined) {
            this._enableRetry = config.enableRetry;
        }
        
        if (config.enableHeartbeat !== undefined) {
            this._enableHeartbeat = config.enableHeartbeat;
        }
    }

    /**
     * 获取HTTP客户端
     */
    public getHttpClient(): HttpClient {
        return this._httpClient;
    }

    /**
     * 获取WebSocket客户端
     */
    public getWebSocketClient(): WebSocketClient {
        return this._websocketClient;
    }

    /**
     * 发送HTTP请求（为API客户端提供）
     */
    public async request(url: string, options?: RequestInit): Promise<any> {
        if (!this._networkStatus.isOnline) {
            throw new Error('网络连接不可用');
        }

        try {
            // 使用内置的HttpClient
            const method = options?.method || 'GET';
            const headers = options?.headers as Record<string, string> || {};
            const body = options?.body;

            let response: any;
            switch (method.toUpperCase()) {
                case 'GET':
                    response = await this._httpClient.get(url, { headers });
                    break;
                case 'POST':
                    response = await this._httpClient.post(url, body, { headers });
                    break;
                case 'PUT':
                    response = await this._httpClient.put(url, body, { headers });
                    break;
                case 'DELETE':
                    response = await this._httpClient.delete(url, { headers });
                    break;
                default:
                    throw new Error(`不支持的HTTP方法: ${method}`);
            }

            this._updateNetworkStatus({ isOnline: true });
            return response;
        } catch (error) {
            this._updateNetworkStatus({ isOnline: false });
            this.handleNetworkError(error as INetworkError);
            throw error;
        }
    }

    /**
     * 检查是否在线
     */
    public isOnline(): boolean {
        return this._networkStatus.isOnline;
    }

    /**
     * 获取网络类型
     */
    public getNetworkType(): NetworkType {
        return this._networkStatus.networkType;
    }

    /**
     * 获取网络状态
     */
    public getNetworkStatus(): INetworkStatus {
        return { ...this._networkStatus };
    }

    /**
     * 添加请求到队列
     */
    public addRequest(request: INetworkRequest): void {
        if (!this._enableQueue) {
            console.warn('Request queue is disabled');
            return;
        }

        if (this._requestQueue.length >= this._maxQueueSize) {
            console.warn('Request queue is full, removing oldest request');
            this._requestQueue.shift();
        }

        this._requestQueue.push(request);
        
        if (!this._isProcessingQueue) {
            this.processQueue();
        }
    }

    /**
     * 处理请求队列
     */
    public async processQueue(): Promise<void> {
        if (this._isProcessingQueue || this._requestQueue.length === 0) {
            return;
        }

        this._isProcessingQueue = true;

        try {
            while (this._requestQueue.length > 0) {
                const request = this._requestQueue.shift()!;
                
                try {
                    await this._processRequest(request);
                } catch (error) {
                    this.handleNetworkError(error as INetworkError);
                    
                    // 如果启用重试且未达到最大重试次数
                    if (this._enableRetry && request.retries < request.maxRetries) {
                        request.retries++;
                        this._requestQueue.unshift(request); // 重新加入队列头部
                        
                        // 等待一段时间后重试
                        await this._delay(1000 * request.retries);
                    }
                }
            }
        } finally {
            this._isProcessingQueue = false;
        }
    }

    /**
     * 处理网络错误
     */
    public handleNetworkError(error: INetworkError): void {
        console.error('🚨 网络错误:', error);
        
        // 发送错误事件
        EventManager.getInstance().emit(NetworkEventType.REQUEST_ERROR, { error });
        
        // 根据错误类型进行处理
        switch (error.code) {
            case 'NETWORK_ERROR':
                this._updateNetworkStatus({ isOnline: false });
                break;
            case 'TIMEOUT':
                this._updateConnectionQuality('poor');
                break;
            default:
                break;
        }
    }

    /**
     * 测试网络连接
     */
    public async testConnection(): Promise<boolean> {
        try {
            const startTime = Date.now();
            
            // 尝试发送一个简单的HTTP请求
            await this._httpClient.get('/api/ping');
            
            const latency = Date.now() - startTime;
            this._updateNetworkStatus({ 
                isOnline: true, 
                latency,
                lastCheck: Date.now()
            });
            
            // 根据延迟判断连接质量
            if (latency < 100) {
                this._updateConnectionQuality('excellent');
            } else if (latency < 500) {
                this._updateConnectionQuality('good');
            } else {
                this._updateConnectionQuality('poor');
            }
            
            return true;
        } catch (error) {
            this._updateNetworkStatus({ 
                isOnline: false, 
                lastCheck: Date.now()
            });
            return false;
        }
    }

    /**
     * 初始化网络监控
     */
    private _initializeNetworkMonitoring(): void {
        // 监听浏览器网络状态变化
        window.addEventListener('online', () => {
            this._updateNetworkStatus({ isOnline: true });
            EventManager.getInstance().emit(NetworkEventType.ONLINE);
        });

        window.addEventListener('offline', () => {
            this._updateNetworkStatus({ isOnline: false });
            EventManager.getInstance().emit(NetworkEventType.OFFLINE);
        });
    }

    /**
     * 开始网络状态监控
     */
    private _startNetworkMonitoring(): void {
        // 定期检查网络状态
        setInterval(() => {
            this.testConnection();
        }, 30000); // 每30秒检查一次
    }

    /**
     * 停止网络状态监控
     */
    private _stopNetworkMonitoring(): void {
        // 移除事件监听器
        window.removeEventListener('online', () => {});
        window.removeEventListener('offline', () => {});
    }

    /**
     * 设置HTTP拦截器
     */
    private _setupHttpInterceptors(): void {
        this._httpClient.setRequestInterceptor({
            onRequest: (config) => {
                // 添加通用请求头
                config.headers = {
                    ...config.headers,
                    'X-Timestamp': Date.now().toString(),
                    'X-Client-Version': '1.0.0'
                };
                return config;
            }
        });

        this._httpClient.setResponseInterceptor({
            onResponse: (response) => {
                // 记录响应时间
                console.log(`HTTP ${response.config.method} ${response.config.url} - ${response.status}`);
                return response;
            }
        });
    }

    /**
     * 设置WebSocket监听器
     */
    private _setupWebSocketListeners(): void {
        this._websocketClient.on('connect', () => {
            console.log('🔗 WebSocket连接已建立');
        });

        this._websocketClient.on('disconnect', () => {
            console.log('🔌 WebSocket连接已断开');
        });

        this._websocketClient.on('error', (error: any) => {
            console.error('🚨 WebSocket错误:', error);
        });
    }

    /**
     * 处理单个请求
     */
    private async _processRequest(request: INetworkRequest): Promise<any> {
        if (request.type === 'http') {
            // 处理HTTP请求
            const config = request.config as any;
            switch (config.method) {
                case 'GET':
                    return await this._httpClient.get(config.url, config.params);
                case 'POST':
                    return await this._httpClient.post(config.url, config.data);
                case 'PUT':
                    return await this._httpClient.put(config.url, config.data);
                case 'DELETE':
                    return await this._httpClient.delete(config.url);
                default:
                    throw new Error(`Unsupported HTTP method: ${config.method}`);
            }
        } else if (request.type === 'websocket') {
            // 处理WebSocket请求
            const config = request.config as any;
            this._websocketClient.send(config.data);
        }
    }

    /**
     * 更新网络状态
     */
    private _updateNetworkStatus(updates: Partial<INetworkStatus>): void {
        const oldStatus = { ...this._networkStatus };
        this._networkStatus = { ...this._networkStatus, ...updates };
        
        // 如果网络类型发生变化，发送事件
        if (oldStatus.networkType !== this._networkStatus.networkType || 
            oldStatus.isOnline !== this._networkStatus.isOnline) {
            EventManager.getInstance().emit(NetworkEventType.CONNECTION_CHANGE, {
                oldStatus,
                newStatus: this._networkStatus
            });
        }
    }

    /**
     * 更新连接质量
     */
    private _updateConnectionQuality(quality: 'poor' | 'good' | 'excellent'): void {
        this._networkStatus.connectionQuality = quality;
    }

    /**
     * 检测网络类型
     */
    private _detectNetworkType(): NetworkType {
        // 在浏览器环境中，网络类型检测有限
        if (!navigator.onLine) {
            return NetworkType.OFFLINE;
        }
        
        // 尝试使用Network Information API
        const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
        if (connection) {
            switch (connection.type) {
                case 'wifi':
                    return NetworkType.WIFI;
                case 'cellular':
                    return NetworkType.CELLULAR;
                case 'ethernet':
                    return NetworkType.ETHERNET;
                default:
                    return NetworkType.UNKNOWN;
            }
        }
        
        return NetworkType.UNKNOWN;
    }

    /**
     * 延迟函数
     */
    private _delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
