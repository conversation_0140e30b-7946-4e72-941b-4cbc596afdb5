import { Express } from 'express';
import request from 'supertest';
import { app } from '../../src/app';
import { databaseManager } from '../../src/config/database';
import { Logger } from '../../src/utils/logger';

/**
 * API端点信息
 */
export interface IApiEndpoint {
    path: string;
    method: string;
    description?: string;
    parameters?: any[];
    responses?: any[];
    requiresAuth?: boolean;
    tags?: string[];
}

/**
 * API测试结果
 */
export interface IApiTestResult {
    endpoint: IApiEndpoint;
    success: boolean;
    statusCode: number;
    responseTime: number;
    responseData?: any;
    error?: string;
    validationErrors?: string[];
}

/**
 * API发现和测试结果
 */
export interface IApiDiscoveryResult {
    totalEndpoints: number;
    testedEndpoints: number;
    successfulTests: number;
    failedTests: number;
    averageResponseTime: number;
    endpoints: IApiEndpoint[];
    testResults: IApiTestResult[];
    coverage: number;
}

/**
 * AI驱动的API发现和测试框架
 */
export class ApiDiscoveryTest {
    private app: Express;
    private discoveredEndpoints: IApiEndpoint[] = [];
    private testResults: IApiTestResult[] = [];
    private authToken: string | null = null;

    constructor() {
        this.app = app;
    }

    /**
     * 运行完整的API发现和测试
     */
    public async runFullDiscovery(): Promise<IApiDiscoveryResult> {
        try {
            Logger.info('开始API自动发现和测试');

            // 连接数据库
            await databaseManager.connect();

            // 1. 发现API端点
            await this.discoverApiEndpoints();

            // 2. 获取认证令牌
            await this.obtainAuthToken();

            // 3. 测试所有端点
            await this.testAllEndpoints();

            // 4. 生成测试报告
            const result = this.generateTestReport();

            Logger.info('API发现和测试完成', {
                totalEndpoints: result.totalEndpoints,
                successfulTests: result.successfulTests,
                failedTests: result.failedTests,
                coverage: result.coverage,
            });

            return result;

        } catch (error) {
            Logger.error('API发现和测试失败', error);
            throw error;
        } finally {
            await databaseManager.disconnect();
        }
    }

    /**
     * 发现API端点
     */
    private async discoverApiEndpoints(): Promise<void> {
        Logger.info('开始发现API端点...');

        // 从Swagger文档获取端点信息
        try {
            const swaggerResponse = await request(this.app).get('/api/docs.json');
            if (swaggerResponse.status === 200) {
                this.parseSwaggerDoc(swaggerResponse.body);
            }
        } catch (error) {
            Logger.warn('无法获取Swagger文档，使用预定义端点');
            this.loadPredefinedEndpoints();
        }

        Logger.info(`发现 ${this.discoveredEndpoints.length} 个API端点`);
    }

    /**
     * 解析Swagger文档
     */
    private parseSwaggerDoc(swaggerDoc: any): void {
        const paths = swaggerDoc.paths || {};
        
        for (const [path, pathItem] of Object.entries(paths)) {
            for (const [method, operation] of Object.entries(pathItem as any)) {
                if (typeof operation === 'object' && operation.summary) {
                    this.discoveredEndpoints.push({
                        path,
                        method: method.toUpperCase(),
                        description: operation.summary,
                        parameters: operation.parameters || [],
                        responses: operation.responses || [],
                        requiresAuth: this.checkAuthRequirement(operation),
                        tags: operation.tags || [],
                    });
                }
            }
        }
    }

    /**
     * 加载预定义端点
     */
    private loadPredefinedEndpoints(): void {
        this.discoveredEndpoints = [
            // 健康检查
            { path: '/api/health', method: 'GET', description: '健康检查', requiresAuth: false },
            
            // 用户相关
            { path: '/api/v1/users/register', method: 'POST', description: '用户注册', requiresAuth: false },
            { path: '/api/v1/users/login', method: 'POST', description: '用户登录', requiresAuth: false },
            { path: '/api/v1/users/profile', method: 'GET', description: '获取用户资料', requiresAuth: true },
            { path: '/api/v1/users/profile', method: 'PUT', description: '更新用户资料', requiresAuth: true },
            { path: '/api/v1/users/change-password', method: 'POST', description: '修改密码', requiresAuth: true },
            
            // 角色相关
            { path: '/api/v1/characters', method: 'GET', description: '获取角色列表', requiresAuth: true },
            { path: '/api/v1/characters', method: 'POST', description: '创建角色', requiresAuth: true },
            { path: '/api/v1/characters/:characterId', method: 'GET', description: '获取角色详情', requiresAuth: true },
            { path: '/api/v1/characters/:characterId', method: 'PUT', description: '更新角色', requiresAuth: true },
            { path: '/api/v1/characters/:characterId/set-current', method: 'POST', description: '设置当前角色', requiresAuth: true },
            
            // 技能相关
            { path: '/api/v1/skills', method: 'GET', description: '获取技能列表', requiresAuth: false },
            { path: '/api/v1/skills/:skillId', method: 'GET', description: '获取技能详情', requiresAuth: false },
            { path: '/api/v1/skills/characters/:characterId', method: 'GET', description: '获取角色技能', requiresAuth: true },
            { path: '/api/v1/skills/characters/:characterId/learn', method: 'POST', description: '学习技能', requiresAuth: true },
            
            // 物品相关
            { path: '/api/v1/items', method: 'GET', description: '获取物品列表', requiresAuth: false },
            { path: '/api/v1/items/:itemId', method: 'GET', description: '获取物品详情', requiresAuth: false },
            { path: '/api/v1/items/characters/:characterId/inventory', method: 'GET', description: '获取背包', requiresAuth: true },
            { path: '/api/v1/items/characters/:characterId/add', method: 'POST', description: '添加物品', requiresAuth: true },
        ];
    }

    /**
     * 检查是否需要认证
     */
    private checkAuthRequirement(operation: any): boolean {
        return !!(operation.security && operation.security.length > 0);
    }

    /**
     * 获取认证令牌
     */
    private async obtainAuthToken(): Promise<void> {
        try {
            // 创建测试用户
            const registerData = {
                username: 'api_test_user_' + Date.now(),
                email: 'apitest_' + Date.now() + '@test.com',
                password: 'ApiTest123!',
            };

            const registerResponse = await request(this.app)
                .post('/api/v1/users/register')
                .send(registerData);

            if (registerResponse.status === 201 && registerResponse.body.data?.token) {
                this.authToken = registerResponse.body.data.token;
                Logger.info('获取认证令牌成功');
            } else {
                // 尝试登录
                const loginResponse = await request(this.app)
                    .post('/api/v1/users/login')
                    .send({
                        username: registerData.username,
                        password: registerData.password,
                    });

                if (loginResponse.status === 200 && loginResponse.body.data?.token) {
                    this.authToken = loginResponse.body.data.token;
                    Logger.info('通过登录获取认证令牌成功');
                }
            }
        } catch (error) {
            Logger.warn('获取认证令牌失败', error);
        }
    }

    /**
     * 测试所有端点
     */
    private async testAllEndpoints(): Promise<void> {
        Logger.info('开始测试所有API端点...');

        for (const endpoint of this.discoveredEndpoints) {
            try {
                const result = await this.testSingleEndpoint(endpoint);
                this.testResults.push(result);
            } catch (error) {
                this.testResults.push({
                    endpoint,
                    success: false,
                    statusCode: 0,
                    responseTime: 0,
                    error: error.message,
                });
            }
        }
    }

    /**
     * 测试单个端点
     */
    private async testSingleEndpoint(endpoint: IApiEndpoint): Promise<IApiTestResult> {
        const startTime = Date.now();
        
        try {
            // 准备请求
            const testPath = this.prepareTestPath(endpoint.path);
            const testData = this.generateTestData(endpoint);
            
            let requestBuilder = request(this.app)[endpoint.method.toLowerCase()](testPath);

            // 添加认证头
            if (endpoint.requiresAuth && this.authToken) {
                requestBuilder = requestBuilder.set('Authorization', `Bearer ${this.authToken}`);
            }

            // 添加请求体
            if (['POST', 'PUT', 'PATCH'].includes(endpoint.method) && testData) {
                requestBuilder = requestBuilder.send(testData);
            }

            // 发送请求
            const response = await requestBuilder;
            const responseTime = Date.now() - startTime;

            // 验证响应
            const validationErrors = this.validateResponse(endpoint, response);

            return {
                endpoint,
                success: response.status < 400 && validationErrors.length === 0,
                statusCode: response.status,
                responseTime,
                responseData: response.body,
                validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
            };

        } catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                endpoint,
                success: false,
                statusCode: error.status || 0,
                responseTime,
                error: error.message,
            };
        }
    }

    /**
     * 准备测试路径
     */
    private prepareTestPath(path: string): string {
        // 替换路径参数
        return path
            .replace(':characterId', 'test-character-id')
            .replace(':skillId', 'test-skill-id')
            .replace(':itemId', 'test-item-id')
            .replace(':instanceId', 'test-instance-id')
            .replace(':equipSlot', 'weapon');
    }

    /**
     * 生成测试数据
     */
    private generateTestData(endpoint: IApiEndpoint): any {
        const { path, method } = endpoint;

        if (method === 'GET') return null;

        // 根据端点生成测试数据
        if (path.includes('/register')) {
            return {
                username: 'testuser_' + Date.now(),
                email: 'test_' + Date.now() + '@example.com',
                password: 'TestPassword123!',
            };
        }

        if (path.includes('/login')) {
            return {
                username: 'testuser',
                password: 'TestPassword123!',
            };
        }

        if (path.includes('/characters') && method === 'POST') {
            return {
                name: 'TestCharacter_' + Date.now(),
                class: 'warrior',
            };
        }

        if (path.includes('/learn')) {
            return {
                skillId: 'test-skill-001',
            };
        }

        if (path.includes('/add')) {
            return {
                itemId: 'test-item-001',
                quantity: 1,
            };
        }

        return {};
    }

    /**
     * 验证响应
     */
    private validateResponse(endpoint: IApiEndpoint, response: any): string[] {
        const errors: string[] = [];

        // 检查响应格式
        if (response.body && typeof response.body === 'object') {
            if (!response.body.hasOwnProperty('success')) {
                errors.push('响应缺少success字段');
            }
            if (!response.body.hasOwnProperty('message')) {
                errors.push('响应缺少message字段');
            }
        }

        // 检查状态码
        if (endpoint.requiresAuth && !this.authToken && response.status !== 401) {
            errors.push('需要认证的端点应该返回401状态码');
        }

        return errors;
    }

    /**
     * 生成测试报告
     */
    private generateTestReport(): IApiDiscoveryResult {
        const successfulTests = this.testResults.filter(r => r.success).length;
        const failedTests = this.testResults.length - successfulTests;
        const totalResponseTime = this.testResults.reduce((sum, r) => sum + r.responseTime, 0);
        const averageResponseTime = this.testResults.length > 0 ? totalResponseTime / this.testResults.length : 0;
        const coverage = this.discoveredEndpoints.length > 0 ? (this.testResults.length / this.discoveredEndpoints.length) * 100 : 0;

        return {
            totalEndpoints: this.discoveredEndpoints.length,
            testedEndpoints: this.testResults.length,
            successfulTests,
            failedTests,
            averageResponseTime,
            endpoints: this.discoveredEndpoints,
            testResults: this.testResults,
            coverage,
        };
    }

    /**
     * 获取测试结果
     */
    public getTestResults(): IApiTestResult[] {
        return this.testResults;
    }

    /**
     * 获取发现的端点
     */
    public getDiscoveredEndpoints(): IApiEndpoint[] {
        return this.discoveredEndpoints;
    }
}

export const apiDiscoveryTest = new ApiDiscoveryTest();
