# Cocos Creator MCP服务器诊断脚本
Write-Host "=== Cocos Creator MCP服务器诊断 ===" -ForegroundColor Cyan

# 获取项目根目录
$projectRoot = Split-Path -Parent $PSScriptRoot
Write-Host "项目目录: $projectRoot" -ForegroundColor Yellow

# 1. 检查MCP扩展目录
Write-Host "`n1. 检查MCP扩展目录..." -ForegroundColor Green
$mcpExtensionPath = Join-Path $projectRoot "extensions\cocos-mcp-server"
if (Test-Path $mcpExtensionPath) {
    Write-Host "✅ MCP扩展目录存在: $mcpExtensionPath" -ForegroundColor Green
} else {
    Write-Host "❌ MCP扩展目录不存在: $mcpExtensionPath" -ForegroundColor Red
    exit 1
}

# 2. 检查关键文件
Write-Host "`n2. 检查关键文件..." -ForegroundColor Green
$criticalFiles = @(
    "package.json",
    "dist\main.js",
    "dist\mcp-server.js"
)

foreach ($file in $criticalFiles) {
    $filePath = Join-Path $mcpExtensionPath $file
    if (Test-Path $filePath) {
        Write-Host "✅ $file 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $file 不存在" -ForegroundColor Red
    }
}

# 3. 检查设置文件
Write-Host "`n3. 检查设置文件..." -ForegroundColor Green
$settingsPath = Join-Path $projectRoot "settings\mcp-server.json"
if (Test-Path $settingsPath) {
    Write-Host "✅ 设置文件存在: $settingsPath" -ForegroundColor Green
    try {
        $settings = Get-Content $settingsPath | ConvertFrom-Json
        Write-Host "   端口: $($settings.port)" -ForegroundColor Cyan
        Write-Host "   自动启动: $($settings.autoStart)" -ForegroundColor Cyan
        Write-Host "   调试日志: $($settings.enableDebugLog)" -ForegroundColor Cyan
        
        if ($settings.port -eq 0) {
            Write-Host "⚠️ 端口设置为0，这可能导致问题" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "❌ 设置文件格式错误: $_" -ForegroundColor Red
    }
} else {
    Write-Host "❌ 设置文件不存在: $settingsPath" -ForegroundColor Red
}

# 4. 检查端口占用
Write-Host "`n4. 检查端口占用..." -ForegroundColor Green
try {
    $portCheck = netstat -ano | findstr ":3000"
    if ($portCheck) {
        Write-Host "⚠️ 端口3000被占用:" -ForegroundColor Yellow
        Write-Host $portCheck -ForegroundColor Gray
    } else {
        Write-Host "✅ 端口3000未被占用" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠️ 无法检查端口占用状态" -ForegroundColor Yellow
}

# 5. 测试MCP服务器连接
Write-Host "`n5. 测试MCP服务器连接..." -ForegroundColor Green
try {
    $response = Invoke-WebRequest -Uri "http://127.0.0.1:3000/health" -Method GET -TimeoutSec 5
    Write-Host "✅ MCP服务器正在运行" -ForegroundColor Green
    Write-Host "   状态码: $($response.StatusCode)" -ForegroundColor Cyan
    Write-Host "   响应: $($response.Content)" -ForegroundColor Cyan
} catch {
    Write-Host "❌ MCP服务器未响应" -ForegroundColor Red
    Write-Host "   错误: $($_.Exception.Message)" -ForegroundColor Gray
}

# 6. 检查Cocos Creator进程
Write-Host "`n6. 检查Cocos Creator进程..." -ForegroundColor Green
try {
    $cocosProcesses = Get-Process | Where-Object { $_.ProcessName -like "*cocos*" -or $_.ProcessName -like "*CocosCreator*" }
    if ($cocosProcesses) {
        Write-Host "✅ 发现Cocos Creator进程:" -ForegroundColor Green
        foreach ($proc in $cocosProcesses) {
            Write-Host "   $($proc.ProcessName) (PID: $($proc.Id))" -ForegroundColor Cyan
        }
    } else {
        Write-Host "⚠️ 未发现Cocos Creator进程" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ 无法检查进程状态" -ForegroundColor Yellow
}

Write-Host "`n=== 诊断完成 ===" -ForegroundColor Cyan
Write-Host "`n建议的修复步骤:" -ForegroundColor Yellow
Write-Host "1. 确保Cocos Creator已启动并打开项目" -ForegroundColor White
Write-Host "2. 在扩展管理器中启用MCP服务器扩展" -ForegroundColor White
Write-Host "3. 检查扩展的控制台输出是否有错误信息" -ForegroundColor White
Write-Host "4. 如果问题持续，尝试重启Cocos Creator" -ForegroundColor White
