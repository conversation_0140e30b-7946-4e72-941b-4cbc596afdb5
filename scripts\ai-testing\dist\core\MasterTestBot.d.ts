/**
 * 主测试机器人 - AI驱动的测试策略制定和任务分发
 * 用于武侠放置游戏Godot到Cocos Creator迁移项目
 */
import { TestStrategy, ValidationResult, TestTask, TestResult, TestSummary } from '../types';
export declare class MasterTestBot {
    private testBotFactory;
    private systemDiscovery;
    private reportGenerator;
    private activeTestBots;
    constructor();
    /**
     * 核心能力：测试策略制定
     * 基于代码变更分析制定智能测试策略
     */
    generateTestStrategy(codeChanges: CodeChange[]): Promise<TestStrategy>;
    /**
     * 核心能力：任务分解和分发
     * 将测试策略分解为具体任务并分发给测试机器人
     */
    distributeTestTasks(strategy: TestStrategy): Promise<TestTask[]>;
    /**
     * 核心能力：结果汇总和分析
     * 汇总所有测试结果并生成智能分析报告
     */
    aggregateTestResults(results: TestResult[]): Promise<TestSummary>;
    /**
     * 算法一致性验证
     * 对比Godot和Cocos Creator版本的算法实现
     */
    validateAlgorithmConsistency(godotCode: string, cocosCode: string, algorithmName: string): Promise<ValidationResult>;
    /**
     * 为整个项目设置AI测试
     */
    setupProjectTesting(projectPath: string): Promise<void>;
    /**
     * 处理代码变更的智能测试
     */
    handleCodeChanges(changes: CodeChange[]): Promise<TestResult[]>;
    private analyzeCodeChanges;
    private assessMigrationRisks;
    private calculateTestPriority;
    private determineTestScope;
    private planTestSequence;
    private allocateTestResources;
    private optimizeTaskSequence;
    private extractTestInsights;
    private generateRecommendations;
    private planNextActions;
    private getValidationAgent;
    private generateProjectTestStrategy;
    private executeInitialTests;
    private identifyAffectedSystems;
    private executeTargetedTests;
}
export interface CodeChange {
    file: string;
    type: 'added' | 'modified' | 'deleted';
    content: string;
}
export interface ChangeAnalysis {
    impactedModules: string[];
    riskLevel: 'low' | 'medium' | 'high';
    testRequirements: string[];
    migrationComplexity: 'simple' | 'medium' | 'complex';
}
export interface RiskAssessment {
    overallRisk: 'low' | 'medium' | 'high';
    riskFactors: string[];
    mitigationStrategies: string[];
}
export interface TestTask {
    id: string;
    name: string;
    priority: number;
    estimatedTime: number;
    dependencies: string[];
}
export interface TestResult {
    taskId: string;
    status: 'passed' | 'failed' | 'skipped';
    duration: number;
    details: any;
}
export interface TestSummary {
    summary: any;
    insights: TestInsights;
    recommendations: string[];
    nextActions: string[];
}
export interface TestInsights {
    patterns: string[];
    trends: string[];
    anomalies: string[];
}
export interface SystemModule {
    name: string;
    path: string;
    type: string;
}
export interface TestType {
    name: string;
    description: string;
}
export interface CoverageRequirement {
    minimum: number;
    target: number;
}
export interface AffectedSystem {
    name: string;
    path: string;
    isNewSystem: boolean;
}
//# sourceMappingURL=MasterTestBot.d.ts.map