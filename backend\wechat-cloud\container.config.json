{"containerPort": 3000, "minNum": 1, "maxNum": 10, "cpu": 1, "mem": 2, "policyType": "cpu", "policyThreshold": 60, "envParams": {"NODE_ENV": "production", "WECHAT_CLOUD": "true", "LOG_LEVEL": "info"}, "customLogs": [{"name": "access", "logPath": "/app/logs/access.log"}, {"name": "error", "logPath": "/app/logs/error.log"}], "dataBaseName": "idlegame", "serviceName": "idlegame-backend", "uploadType": "package", "flowRatio": 100}