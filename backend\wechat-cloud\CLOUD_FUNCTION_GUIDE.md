# 🎉 云函数部署成功指南

恭喜！您的IdleGame后端已成功部署为微信云开发的云函数！

## 📋 部署状态

✅ **云函数**: `idlegame-api` 部署成功  
✅ **运行时**: Node.js 12.16  
✅ **内存**: 512MB  
✅ **超时**: 30秒  
✅ **环境**: cloudbase-7gzvsi422b6fddd6  

## 🔗 访问方式

### 在小程序中调用
```javascript
// 初始化云开发
wx.cloud.init({
  env: 'cloudbase-7gzvsi422b6fddd6'
});

// 调用云函数
const result = await wx.cloud.callFunction({
  name: 'idlegame-api',
  data: {
    path: '/api/health',
    httpMethod: 'GET',
    headers: {},
    body: {}
  }
});
```

### 支持的API接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/health` | GET | 健康检查 |
| `/api/auth/login` | POST | 用户登录 |
| `/api/game/sync` | POST | 游戏数据同步 |
| `/api/player/:userId` | GET | 获取玩家数据 |
| `/api/leaderboard` | GET | 获取排行榜 |

## 🗄️ 数据库配置

您需要在微信开发者工具中手动创建以下数据库集合：

1. **打开微信开发者工具**
2. **进入云开发控制台**
3. **点击"数据库"**
4. **创建以下集合**：

### 必需的数据库集合

```
users - 用户信息表
├── id (string) - 用户ID
├── openid (string) - 微信openid
├── nickname (string) - 用户昵称
├── avatar (string) - 头像URL
└── createdAt (date) - 创建时间

game_sessions - 游戏会话数据表
├── userId (string) - 用户ID
├── gameData (object) - 游戏数据
├── timestamp (date) - 时间戳
└── syncType (string) - 同步类型

skills - 技能数据表
├── id (string) - 技能ID
├── name (string) - 技能名称
├── description (string) - 技能描述
├── level (number) - 技能等级
└── effects (array) - 技能效果

items - 物品数据表
├── id (string) - 物品ID
├── name (string) - 物品名称
├── type (string) - 物品类型
├── rarity (string) - 稀有度
└── attributes (object) - 物品属性
```

## 🧪 测试云函数

### 方法1：在微信开发者工具中测试

1. 打开微信开发者工具
2. 进入云开发控制台
3. 点击"云函数"
4. 找到 `idlegame-api` 函数
5. 点击"测试"按钮
6. 输入测试数据：

```json
{
  "path": "/api/health",
  "httpMethod": "GET",
  "headers": {},
  "body": {}
}
```

### 方法2：在小程序中测试

使用提供的 `miniprogram-test.js` 文件中的代码进行测试。

## 🎮 集成到游戏中

### 1. 在小程序项目中初始化

```javascript
// app.js
App({
  onLaunch: function () {
    wx.cloud.init({
      env: 'cloudbase-7gzvsi422b6fddd6',
      traceUser: true,
    });
  }
});
```

### 2. 创建API服务文件

```javascript
// utils/gameAPI.js
const gameAPI = {
  async callAPI(path, method = 'GET', data = {}) {
    return await wx.cloud.callFunction({
      name: 'idlegame-api',
      data: {
        path,
        httpMethod: method,
        headers: { 'Content-Type': 'application/json' },
        body: data
      }
    });
  },
  
  async login() {
    const { code } = await wx.login();
    return this.callAPI('/api/auth/login', 'POST', { code });
  },
  
  async syncGameData(gameData) {
    const userInfo = wx.getStorageSync('userInfo');
    return this.callAPI('/api/game/sync', 'POST', {
      userId: userInfo.id,
      gameData
    });
  }
};

module.exports = gameAPI;
```

### 3. 在游戏页面中使用

```javascript
// pages/game/game.js
const gameAPI = require('../../utils/gameAPI');

Page({
  async onLoad() {
    try {
      // 用户登录
      const loginResult = await gameAPI.login();
      console.log('登录成功:', loginResult);
      
      // 同步游戏数据
      const gameData = { level: 1, exp: 0, coins: 100 };
      await gameAPI.syncGameData(gameData);
      
    } catch (error) {
      console.error('游戏初始化失败:', error);
    }
  }
});
```

## 📊 监控和调试

### 查看云函数日志
1. 打开微信开发者工具
2. 进入云开发控制台
3. 点击"云函数" → "idlegame-api"
4. 查看"调用日志"

### 查看数据库数据
1. 进入云开发控制台
2. 点击"数据库"
3. 选择对应的集合查看数据

## 🔧 常见问题

### Q: 云函数调用失败
A: 检查以下项目：
- 环境ID是否正确
- 云函数是否部署成功
- 传入的参数格式是否正确

### Q: 数据库操作失败
A: 确保：
- 数据库集合已创建
- 数据格式符合要求
- 没有超出免费额度限制

### Q: 如何查看错误日志
A: 在云开发控制台的云函数页面查看调用日志

## 🚀 下一步

1. **完善API功能**: 根据游戏需求添加更多API接口
2. **优化性能**: 添加缓存和批量操作
3. **增加安全性**: 添加用户认证和权限控制
4. **监控告警**: 设置云函数监控和告警
5. **数据分析**: 收集游戏数据进行分析

## 💡 优化建议

### 性能优化
- 使用数据库索引提高查询速度
- 实现本地缓存减少云函数调用
- 批量处理数据减少网络请求

### 成本优化
- 合理设置云函数超时时间
- 优化代码减少执行时间
- 定期清理无用数据

---

**🎉 恭喜！您的IdleGame现在可以使用微信云开发的免费服务了！**

所有核心功能都已部署完成，您可以开始在小程序中集成和测试游戏功能。
