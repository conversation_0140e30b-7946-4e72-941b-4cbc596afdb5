"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const fs_extra_1 = require("fs-extra");
const path_1 = require("path");
/**
 * @zh 如果希望兼容 3.3 之前的版本可以使用下方的代码
 * @en You can add the code below if you want compatibility with versions prior to 3.3
 */
// Editor.Panel.define = Editor.Panel.define || function(options: any) { return options }
module.exports = Editor.Panel.define({
    listeners: {
        show() { console.log('MCP Server panel shown'); },
        hide() { console.log('MCP Server panel hidden'); }
    },
    template: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/template/default/index.html'), 'utf-8'),
    style: (0, fs_extra_1.readFileSync)((0, path_1.join)(__dirname, '../../../static/style/default/index.css'), 'utf-8'),
    $: {
        panelTitle: '#panelTitle',
        serverStatusLabel: '#serverStatusLabel',
        serverStatusLabelProp: '#serverStatusLabelProp',
        serverStatusValue: '#serverStatusValue',
        connectedLabel: '#connectedLabel',
        connectedClients: '#connectedClients',
        toggleServerBtn: '#toggleServerBtn',
        settingsLabel: '#settingsLabel',
        portLabel: '#portLabel',
        autoStartLabel: '#autoStartLabel',
        debugLogLabel: '#debugLogLabel',
        maxConnectionsLabel: '#maxConnectionsLabel',
        connectionInfoLabel: '#connectionInfoLabel',
        httpUrlLabel: '#httpUrlLabel',
        httpUrlInput: '#httpUrlInput',
        copyBtn: '#copyBtn',
        saveSettingsBtn: '#saveSettingsBtn',
        // 新增输入控件id
        portInput: '#portInput',
        maxConnInput: '#maxConnInput',
        autoStartInput: '#autoStartInput',
        debugLogInput: '#debugLogInput',
    },
    methods: {
        async updateServerStatus() {
            try {
                const status = await Editor.Message.request('cocos-mcp-server', 'get-server-status');
                this.serverRunning = status.running;
                this.connectedClients = status.clients || 0;
                this.serverStatus = this.serverRunning ?
                    Editor.I18n.t('cocos-mcp-server.connected') :
                    Editor.I18n.t('cocos-mcp-server.disconnected');
                this.statusClass = this.serverRunning ? 'running' : 'stopped';
                this.buttonText = this.serverRunning ?
                    Editor.I18n.t('cocos-mcp-server.stop_server') :
                    Editor.I18n.t('cocos-mcp-server.start_server');
                // 刷新UI
                this.$.serverStatusValue.innerText = this.serverStatus;
                this.$.connectedClients.innerText = this.connectedClients;
                this.$.toggleServerBtn.innerText = this.buttonText;
                if (this.serverRunning) {
                    this.httpUrl = `http://localhost:${this.settings.port}/mcp`;
                    this.$.httpUrlInput.value = this.httpUrl;
                }
                else {
                    this.httpUrl = '';
                    this.$.httpUrlInput.value = '';
                }
            }
            catch (err) {
                console.error('Failed to update server status:', err);
            }
        },
        async toggleServer() {
            this.isProcessing = true;
            try {
                if (this.serverRunning) {
                    await this.stopServer();
                }
                else {
                    await this.startServer();
                }
            }
            finally {
                this.isProcessing = false;
            }
        },
        async startServer() {
            try {
                await Editor.Message.request('cocos-mcp-server', 'start-server');
                Editor.Dialog.info(Editor.I18n.t('cocos-mcp-server.server_started'), {
                    detail: Editor.I18n.t('cocos-mcp-server.server_running').replace('{0}', this.settings.port.toString())
                });
                await this.updateServerStatus();
            }
            catch (err) {
                Editor.Dialog.error(Editor.I18n.t('cocos-mcp-server.failed_to_start'), err.message);
            }
        },
        async stopServer() {
            try {
                await Editor.Message.request('cocos-mcp-server', 'stop-server');
                Editor.Dialog.info(Editor.I18n.t('cocos-mcp-server.server_stopped_msg'), {
                    detail: Editor.I18n.t('cocos-mcp-server.server_stopped')
                });
                await this.updateServerStatus();
            }
            catch (err) {
                Editor.Dialog.error(Editor.I18n.t('cocos-mcp-server.failed_to_stop'), err.message);
            }
        },
        async saveSettings() {
            try {
                // 直接用 this.$ 获取所有输入控件的当前值
                const port = this.$.portInput ? Number(this.$.portInput.value) : 3000;
                const maxConnections = this.$.maxConnInput ? Number(this.$.maxConnInput.value) : 10;
                const autoStart = this.$.autoStartInput ? !!this.$.autoStartInput.checked : false;
                const enableDebugLog = this.$.debugLogInput ? !!this.$.debugLogInput.checked : false;
                // 组装 settings
                const settings = Object.assign(Object.assign({}, this.settings), { port,
                    maxConnections,
                    autoStart,
                    enableDebugLog });
                await Editor.Message.request('cocos-mcp-server', 'update-settings', settings);
                // 重新拉取设置
                const newSettings = await Editor.Message.request('cocos-mcp-server', 'get-server-settings');
                this.settings = newSettings;
                this.originalSettings = JSON.stringify(newSettings);
                Editor.Dialog.info(Editor.I18n.t('cocos-mcp-server.settings_saved'));
            }
            catch (err) {
                Editor.Dialog.error(Editor.I18n.t('cocos-mcp-server.failed_to_save'), err.message);
            }
        },
        copyUrl() {
            Editor.Clipboard.write('text', this.httpUrl);
            Editor.Dialog.info(Editor.I18n.t('cocos-mcp-server.url_copied'));
        },
        settingsChanged() {
            return JSON.stringify(this.settings) !== this.originalSettings;
        },
        bindSettingsEvents() {
            // 端口输入框
            const portInput = document.querySelectorAll('ui-num-input[slot="content"]')[0];
            if (portInput) {
                portInput.addEventListener('change', (e) => {
                    this.settings.port = Number(e.detail.value);
                });
            }
            // 最大连接数
            const maxConnInput = document.querySelectorAll('ui-num-input[slot="content"]')[1];
            if (maxConnInput) {
                maxConnInput.addEventListener('change', (e) => {
                    this.settings.maxConnections = Number(e.detail.value);
                });
            }
            // 复选框
            const checkboxes = document.querySelectorAll('ui-checkbox[slot="content"]');
            if (checkboxes && checkboxes.length >= 2) {
                checkboxes[0].addEventListener('change', (e) => {
                    this.settings.autoStart = !!e.detail.value;
                });
                checkboxes[1].addEventListener('change', (e) => {
                    this.settings.enableDebugLog = !!e.detail.value;
                });
            }
        },
    },
    ready() {
        Editor.Message.request('cocos-mcp-server', 'get-server-settings').then((settings) => {
            this.settings = settings;
            this.originalSettings = JSON.stringify(settings);
            // 本地化label赋值
            this.$.panelTitle.innerText = Editor.I18n.t('cocos-mcp-server.panel_title');
            this.$.serverStatusLabel.innerText = Editor.I18n.t('cocos-mcp-server.server_status');
            this.$.serverStatusLabelProp.innerText = Editor.I18n.t('cocos-mcp-server.server_status');
            this.$.connectedLabel.innerText = Editor.I18n.t('cocos-mcp-server.connected');
            this.$.settingsLabel.innerText = Editor.I18n.t('cocos-mcp-server.settings');
            this.$.portLabel.innerText = Editor.I18n.t('cocos-mcp-server.port');
            this.$.autoStartLabel.innerText = Editor.I18n.t('cocos-mcp-server.auto_start');
            this.$.debugLogLabel.innerText = Editor.I18n.t('cocos-mcp-server.debug_log');
            this.$.maxConnectionsLabel.innerText = Editor.I18n.t('cocos-mcp-server.max_connections');
            this.$.connectionInfoLabel.innerText = Editor.I18n.t('cocos-mcp-server.connection_info');
            this.$.httpUrlLabel.innerText = Editor.I18n.t('cocos-mcp-server.http_url');
            this.$.copyBtn.innerText = Editor.I18n.t('cocos-mcp-server.copy');
            this.$.saveSettingsBtn.innerText = Editor.I18n.t('cocos-mcp-server.save_settings');
            // 动态内容初始化
            this.$.serverStatusValue.innerText = '';
            this.$.connectedClients.innerText = '';
            this.$.toggleServerBtn.innerText = '';
            this.$.httpUrlInput.value = '';
            // 绑定按钮事件
            this.$.toggleServerBtn.addEventListener('confirm', this.toggleServer.bind(this));
            this.$.saveSettingsBtn.addEventListener('confirm', this.saveSettings.bind(this));
            this.$.copyBtn.addEventListener('confirm', this.copyUrl.bind(this));
            // 延迟绑定事件，确保 UI 组件已渲染
            setTimeout(() => {
                this.bindSettingsEvents();
            }, 100);
            // Set up periodic status updates
            this.statusInterval = setInterval(() => {
                this.updateServerStatus();
            }, 2000);
            // 不再自动启动服务器，用户点击才启动
            this.updateServerStatus();
        });
    },
    beforeClose() {
        if (this.statusInterval) {
            clearInterval(this.statusInterval);
        }
    },
    close() {
        // Panel close cleanup
    },
    // Direct properties for data access
    serverRunning: false,
    connectedClients: 0,
    serverStatus: '',
    statusClass: 'stopped',
    buttonText: '',
    isProcessing: false,
    settings: {},
    httpUrl: '',
    statusInterval: null,
    originalSettings: ''
});
//# sourceMappingURL=data:application/json;base64,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