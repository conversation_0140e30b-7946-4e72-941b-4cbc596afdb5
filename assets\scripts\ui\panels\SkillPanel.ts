/**
 * 技能面板
 * 显示和管理玩家的技能系统
 */

import { _decorator, Node, ScrollView, Prefab, instantiate, Label, ProgressBar } from 'cc';
import { UIPanel } from '../components/UIPanel';
import { UIPanelType } from '../types/UITypes';
import { UIButton } from '../components/UIButton';
import { ConfigManager } from '../../managers/ConfigManager';
import { ISkillData } from '../../config/interfaces/ISkillData';

const { ccclass, property } = _decorator;

/**
 * 玩家技能数据接口
 */
export interface IPlayerSkill {
    /** 技能配置数据 */
    skillData: ISkillData;
    
    /** 技能等级 */
    level: number;
    
    /** 当前经验 */
    experience: number;
    
    /** 升级所需经验 */
    requiredExperience: number;
    
    /** 是否已解锁 */
    unlocked: boolean;
    
    /** 学会时间 */
    learnTime: number;
    
    /** 使用次数 */
    useCount: number;
    
    /** 最后使用时间 */
    lastUseTime: number;
    
    /** 是否在技能栏中 */
    inSkillBar: boolean;
    
    /** 技能栏位置 */
    skillBarSlot?: number;
}

/**
 * 技能分类枚举
 */
export enum SkillCategory {
    All = 'all',
    Combat = 'combat',
    Magic = 'magic',
    Passive = 'passive',
    Special = 'special'
}

@ccclass('SkillPanel')
export class SkillPanel extends UIPanel {
    
    @property({ type: ScrollView, tooltip: '技能滚动视图' })
    public skillScrollView: ScrollView | null = null;
    
    @property({ type: Node, tooltip: '技能容器' })
    public skillContainer: Node | null = null;
    
    @property({ type: Prefab, tooltip: '技能槽预制体' })
    public skillSlotPrefab: Prefab | null = null;
    
    @property({ type: Node, tooltip: '技能分类按钮容器' })
    public categoryButtonContainer: Node | null = null;
    
    @property({ type: Node, tooltip: '技能详情面板' })
    public skillDetailPanel: Node | null = null;
    
    @property({ type: Label, tooltip: '技能点数标签' })
    public skillPointsLabel: Label | null = null;
    
    @property({ type: UIButton, tooltip: '重置技能按钮' })
    public resetSkillsButton: UIButton | null = null;
    
    // 私有属性
    private _playerSkills: IPlayerSkill[] = [];
    private _skillSlots: Node[] = [];
    private _currentCategory: SkillCategory = SkillCategory.All;
    private _selectedSkill: IPlayerSkill | null = null;
    private _categoryButtons: Map<SkillCategory, UIButton> = new Map();
    private _skillPoints: number = 10; // 可用技能点

    protected onPanelLoad(): void {
        super.onPanelLoad();
        
        // 设置面板类型
        this._panelType = UIPanelType.Skills;
        
        // 设置标题
        this.setTitle('技能');
        
        // 查找组件
        this.findSkillComponents();
    }

    protected async onPanelInitialize(data?: any): Promise<void> {
        // 创建分类按钮
        this.createCategoryButtons();
        
        // 加载技能数据
        await this.loadSkillData();
        
        // 创建技能槽
        this.createSkillSlots();
        
        // 更新技能点显示
        this.updateSkillPointsDisplay();
    }

    protected bindEvents(): void {
        super.bindEvents();
        
        // 绑定重置技能按钮
        if (this.resetSkillsButton) {
            this.resetSkillsButton.setClickCallback(() => {
                this.showResetSkillsConfirm();
            });
        }
    }

    protected onPanelRefresh(data?: any): void {
        // 刷新技能显示
        this.refreshSkillDisplay();
        this.updateSkillPointsDisplay();
    }

    /**
     * 查找技能组件
     */
    private findSkillComponents(): void {
        if (!this.skillScrollView) {
            this.skillScrollView = this.node.getComponentInChildren(ScrollView);
        }
        
        if (!this.skillContainer && this.skillScrollView) {
            this.skillContainer = this.skillScrollView.content;
        }
        
        if (!this.categoryButtonContainer) {
            this.categoryButtonContainer = this.node.getChildByName('CategoryButtons');
        }
        
        if (!this.skillDetailPanel) {
            this.skillDetailPanel = this.node.getChildByName('SkillDetail');
        }
        
        if (!this.skillPointsLabel) {
            const pointsNode = this.node.getChildByName('SkillPoints');
            if (pointsNode) {
                this.skillPointsLabel = pointsNode.getComponent(Label);
            }
        }
        
        if (!this.resetSkillsButton) {
            const resetNode = this.node.getChildByName('ResetButton');
            if (resetNode) {
                this.resetSkillsButton = resetNode.getComponent(UIButton);
            }
        }
    }

    /**
     * 创建分类按钮
     */
    private createCategoryButtons(): void {
        if (!this.categoryButtonContainer) {
            return;
        }
        
        const categories = [
            { type: SkillCategory.All, text: '全部' },
            { type: SkillCategory.Combat, text: '战斗' },
            { type: SkillCategory.Magic, text: '魔法' },
            { type: SkillCategory.Passive, text: '被动' },
            { type: SkillCategory.Special, text: '特殊' }
        ];
        
        for (const category of categories) {
            const buttonNode = new Node(`Category_${category.type}`);
            const button = buttonNode.addComponent(UIButton);
            
            button.setText(category.text);
            button.setClickCallback(() => {
                this.setCategory(category.type);
            });
            
            buttonNode.setParent(this.categoryButtonContainer);
            this._categoryButtons.set(category.type, button);
        }
        
        // 设置默认选中
        this.setCategory(SkillCategory.All);
    }

    /**
     * 加载技能数据
     */
    private async loadSkillData(): Promise<void> {
        try {
            // 这里应该从游戏数据管理器加载实际的技能数据
            // 现在使用模拟数据
            this._playerSkills = await this.generateMockSkillData();
            
            console.log(`加载了 ${this._playerSkills.length} 个技能`);
            
        } catch (error) {
            console.error('加载技能数据失败:', error);
            this._playerSkills = [];
        }
    }

    /**
     * 生成模拟技能数据
     */
    private async generateMockSkillData(): Promise<IPlayerSkill[]> {
        try {
            const configManager = ConfigManager.getInstance();
            const allSkills = configManager.getAllSkillData();
            const playerSkills: IPlayerSkill[] = [];
            
            for (const skillData of allSkills) {
                const playerSkill: IPlayerSkill = {
                    skillData,
                    level: Math.floor(Math.random() * 5) + 1,
                    experience: Math.floor(Math.random() * 100),
                    requiredExperience: 100,
                    unlocked: Math.random() > 0.3, // 70%概率解锁
                    learnTime: Date.now() - Math.random() * 86400000,
                    useCount: Math.floor(Math.random() * 50),
                    lastUseTime: Date.now() - Math.random() * 3600000,
                    inSkillBar: Math.random() > 0.8, // 20%概率在技能栏
                    skillBarSlot: Math.random() > 0.8 ? Math.floor(Math.random() * 8) : undefined
                };
                
                playerSkills.push(playerSkill);
            }
            
            return playerSkills;
        } catch (error) {
            console.warn('生成模拟技能数据失败，返回空数组:', error);
            return [];
        }
    }

    /**
     * 创建技能槽
     */
    private createSkillSlots(): void {
        if (!this.skillContainer || !this.skillSlotPrefab) {
            console.warn('缺少技能容器或技能槽预制体');
            return;
        }
        
        // 清除现有槽位
        this.skillContainer.removeAllChildren();
        this._skillSlots = [];
        
        const filteredSkills = this.getFilteredSkills();
        
        // 创建技能槽
        for (let i = 0; i < filteredSkills.length; i++) {
            const slotNode = instantiate(this.skillSlotPrefab);
            slotNode.name = `SkillSlot_${i}`;
            slotNode.setParent(this.skillContainer);
            
            // 绑定槽位事件
            this.bindSkillSlotEvents(slotNode, i);
            
            this._skillSlots.push(slotNode);
        }
        
        // 刷新显示
        this.refreshSkillDisplay();
        
        console.log(`创建了 ${filteredSkills.length} 个技能槽`);
    }

    /**
     * 绑定技能槽事件
     */
    private bindSkillSlotEvents(slotNode: Node, index: number): void {
        slotNode.on(Node.EventType.TOUCH_END, () => {
            this.onSkillSlotClick(index);
        });
        
        // 查找升级按钮
        const upgradeButton = slotNode.getChildByName('UpgradeButton');
        if (upgradeButton) {
            const button = upgradeButton.getComponent(UIButton);
            if (button) {
                button.setClickCallback(() => {
                    this.upgradeSkill(index);
                });
            }
        }
        
        // 查找添加到技能栏按钮
        const addToBarButton = slotNode.getChildByName('AddToBarButton');
        if (addToBarButton) {
            const button = addToBarButton.getComponent(UIButton);
            if (button) {
                button.setClickCallback(() => {
                    this.addToSkillBar(index);
                });
            }
        }
    }

    /**
     * 获取过滤后的技能
     */
    private getFilteredSkills(): IPlayerSkill[] {
        if (this._currentCategory === SkillCategory.All) {
            return this._playerSkills;
        }
        
        return this._playerSkills.filter(skill => {
            switch (this._currentCategory) {
                case SkillCategory.Combat:
                    return skill.skillData.damageType === 'physical';
                case SkillCategory.Magic:
                    return skill.skillData.damageType === 'magical';
                case SkillCategory.Passive:
                    return skill.skillData.type === 'passive';
                case SkillCategory.Special:
                    return skill.skillData.type === 'special';
                default:
                    return true;
            }
        });
    }

    /**
     * 刷新技能显示
     */
    private refreshSkillDisplay(): void {
        const filteredSkills = this.getFilteredSkills();
        
        for (let i = 0; i < this._skillSlots.length; i++) {
            const slot = this._skillSlots[i];
            const skill = filteredSkills[i];
            
            if (skill) {
                this.updateSkillSlotDisplay(slot, skill);
            }
        }
        
        console.log(`显示了 ${filteredSkills.length} 个过滤后的技能`);
    }

    /**
     * 更新技能槽显示
     */
    private updateSkillSlotDisplay(slot: Node, skill: IPlayerSkill): void {
        // 更新技能图标
        // 更新技能名称
        const nameLabel = slot.getComponentInChildren(Label);
        if (nameLabel) {
            nameLabel.string = skill.skillData.name;
        }
        
        // 更新技能等级
        const levelLabel = slot.getChildByName('Level')?.getComponent(Label);
        if (levelLabel) {
            levelLabel.string = `Lv.${skill.level}`;
        }
        
        // 更新经验进度条
        const expBar = slot.getComponentInChildren(ProgressBar);
        if (expBar) {
            expBar.progress = skill.experience / skill.requiredExperience;
        }
        
        // 设置解锁状态
        slot.getComponent('Button')?.setEnabled(skill.unlocked);
        
        console.log(`更新技能槽: ${skill.skillData.name} Lv.${skill.level}`);
    }

    /**
     * 技能槽点击事件
     */
    private onSkillSlotClick(index: number): void {
        const filteredSkills = this.getFilteredSkills();
        const skill = filteredSkills[index];
        
        if (skill && skill.unlocked) {
            this.selectSkill(skill);
        }
    }

    /**
     * 选择技能
     */
    private selectSkill(skill: IPlayerSkill): void {
        this._selectedSkill = skill;
        
        // 显示技能详情
        this.showSkillDetail(skill);
        
        console.log(`选择技能: ${skill.skillData.name}`);
    }

    /**
     * 显示技能详情
     */
    private showSkillDetail(skill: IPlayerSkill): void {
        if (!this.skillDetailPanel) {
            return;
        }
        
        this.skillDetailPanel.active = true;
        
        // 更新详情面板内容
        const nameLabel = this.skillDetailPanel.getChildByName('Name')?.getComponent(Label);
        if (nameLabel) {
            nameLabel.string = skill.skillData.name;
        }
        
        const descLabel = this.skillDetailPanel.getChildByName('Description')?.getComponent(Label);
        if (descLabel) {
            descLabel.string = skill.skillData.description;
        }
        
        const levelLabel = this.skillDetailPanel.getChildByName('Level')?.getComponent(Label);
        if (levelLabel) {
            levelLabel.string = `等级: ${skill.level}`;
        }
        
        console.log(`显示技能详情: ${skill.skillData.name}`);
    }

    /**
     * 设置分类
     */
    private setCategory(category: SkillCategory): void {
        this._currentCategory = category;
        
        // 更新按钮状态
        this._categoryButtons.forEach((button, type) => {
            // 这里需要设置按钮的选中状态
            // button.setSelected(type === category);
        });
        
        // 重新创建技能槽
        this.createSkillSlots();
        
        console.log(`设置技能分类: ${category}`);
    }

    /**
     * 升级技能
     */
    private upgradeSkill(index: number): void {
        const filteredSkills = this.getFilteredSkills();
        const skill = filteredSkills[index];
        
        if (!skill || !skill.unlocked) {
            return;
        }
        
        // 检查技能点
        if (this._skillPoints <= 0) {
            console.warn('技能点不足');
            return;
        }
        
        // 升级技能
        skill.level++;
        skill.experience = 0;
        skill.requiredExperience = Math.floor(skill.requiredExperience * 1.5);
        this._skillPoints--;
        
        // 刷新显示
        this.refreshSkillDisplay();
        this.updateSkillPointsDisplay();
        
        console.log(`升级技能: ${skill.skillData.name} 到 Lv.${skill.level}`);
    }

    /**
     * 添加到技能栏
     */
    private addToSkillBar(index: number): void {
        const filteredSkills = this.getFilteredSkills();
        const skill = filteredSkills[index];
        
        if (!skill || !skill.unlocked || skill.inSkillBar) {
            return;
        }
        
        // 查找空闲的技能栏位置
        const usedSlots = this._playerSkills
            .filter(s => s.inSkillBar)
            .map(s => s.skillBarSlot)
            .filter(slot => slot !== undefined);
        
        let freeSlot = -1;
        for (let i = 0; i < 8; i++) {
            if (!usedSlots.includes(i)) {
                freeSlot = i;
                break;
            }
        }
        
        if (freeSlot === -1) {
            console.warn('技能栏已满');
            return;
        }
        
        // 添加到技能栏
        skill.inSkillBar = true;
        skill.skillBarSlot = freeSlot;
        
        console.log(`添加技能到技能栏: ${skill.skillData.name} 位置 ${freeSlot}`);
    }

    /**
     * 更新技能点显示
     */
    private updateSkillPointsDisplay(): void {
        if (this.skillPointsLabel) {
            this.skillPointsLabel.string = `技能点: ${this._skillPoints}`;
        }
    }

    /**
     * 显示重置技能确认
     */
    private showResetSkillsConfirm(): void {
        // 这里应该显示确认对话框
        console.log('显示重置技能确认对话框');
        
        // 模拟确认
        this.resetAllSkills();
    }

    /**
     * 重置所有技能
     */
    private resetAllSkills(): void {
        let totalRefund = 0;
        
        for (const skill of this._playerSkills) {
            if (skill.level > 1) {
                totalRefund += skill.level - 1;
                skill.level = 1;
                skill.experience = 0;
                skill.requiredExperience = 100;
            }
            
            skill.inSkillBar = false;
            skill.skillBarSlot = undefined;
        }
        
        this._skillPoints += totalRefund;
        
        // 刷新显示
        this.refreshSkillDisplay();
        this.updateSkillPointsDisplay();
        
        console.log(`重置所有技能，返还 ${totalRefund} 技能点`);
    }

    // ==================== 公共API ====================

    /**
     * 学习技能
     */
    public learnSkill(skillId: string): boolean {
        const skill = this._playerSkills.find(s => s.skillData.id === skillId);
        if (!skill) {
            console.warn(`未找到技能: ${skillId}`);
            return false;
        }
        
        if (skill.unlocked) {
            console.warn(`技能已学会: ${skillId}`);
            return false;
        }
        
        skill.unlocked = true;
        skill.learnTime = Date.now();
        
        this.refreshSkillDisplay();
        console.log(`学会技能: ${skill.skillData.name}`);
        return true;
    }

    /**
     * 获取技能栏技能
     */
    public getSkillBarSkills(): IPlayerSkill[] {
        return this._playerSkills
            .filter(skill => skill.inSkillBar)
            .sort((a, b) => (a.skillBarSlot || 0) - (b.skillBarSlot || 0));
    }

    /**
     * 添加技能点
     */
    public addSkillPoints(points: number): void {
        this._skillPoints += points;
        this.updateSkillPointsDisplay();
        console.log(`获得 ${points} 技能点`);
    }

    /**
     * 获取技能信息
     */
    public getSkillInfo(skillId: string): IPlayerSkill | null {
        return this._playerSkills.find(s => s.skillData.id === skillId) || null;
    }
}
