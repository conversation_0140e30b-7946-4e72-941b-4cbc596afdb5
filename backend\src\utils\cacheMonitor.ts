import { <PERSON>ache<PERSON>anager } from './cache';
import { CacheStrategyManager } from '../config/cacheStrategies';
import { Logger } from './logger';

/**
 * 缓存监控指标接口
 */
export interface CacheMetrics {
  timestamp: Date;
  hitRate: number;
  missRate: number;
  totalOperations: number;
  averageResponseTime: number;
  errorRate: number;
  memoryUsage: number;
  keyCount: number;
  expiredKeys: number;
}

/**
 * 缓存告警配置接口
 */
export interface CacheAlertConfig {
  hitRateThreshold: number; // 命中率阈值
  errorRateThreshold: number; // 错误率阈值
  responseTimeThreshold: number; // 响应时间阈值（毫秒）
  memoryUsageThreshold: number; // 内存使用阈值（百分比）
  keyCountThreshold: number; // 键数量阈值
  enabled: boolean;
}

/**
 * 缓存告警事件接口
 */
export interface CacheAlert {
  type: 'hit_rate' | 'error_rate' | 'response_time' | 'memory_usage' | 'key_count';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  value: number;
  threshold: number;
  timestamp: Date;
}

/**
 * 缓存监控器类
 */
export class CacheMonitor {
  private static instance: CacheMonitor;
  private cacheManager: CacheManager;
  private strategyManager: CacheStrategyManager;
  private metrics: CacheMetrics[] = [];
  private alerts: CacheAlert[] = [];
  private alertConfig: CacheAlertConfig;
  private monitoringInterval?: NodeJS.Timeout | null;
  private alertInterval?: NodeJS.Timeout | null;
  private isMonitoring: boolean = false;

  private constructor() {
    this.cacheManager = CacheManager.getInstance();
    this.strategyManager = CacheStrategyManager.getInstance();
    this.alertConfig = this.getDefaultAlertConfig();
  }

  public static getInstance(): CacheMonitor {
    if (!CacheMonitor.instance) {
      CacheMonitor.instance = new CacheMonitor();
    }
    return CacheMonitor.instance;
  }

  /**
   * 获取默认告警配置
   */
  private getDefaultAlertConfig(): CacheAlertConfig {
    return {
      hitRateThreshold: parseFloat(process.env['CACHE_HIT_RATE_THRESHOLD'] || '80'), // 80%
      errorRateThreshold: parseFloat(process.env['CACHE_ERROR_RATE_THRESHOLD'] || '5'), // 5%
      responseTimeThreshold: parseInt(process.env['CACHE_RESPONSE_TIME_THRESHOLD'] || '100'), // 100ms
      memoryUsageThreshold: parseFloat(process.env['CACHE_MEMORY_USAGE_THRESHOLD'] || '80'), // 80%
      keyCountThreshold: parseInt(process.env['CACHE_KEY_COUNT_THRESHOLD'] || '100000'), // 10万个键
      enabled: process.env['CACHE_ALERTS_ENABLED'] !== 'false',
    };
  }

  /**
   * 开始监控
   */
  public startMonitoring(): void {
    if (this.isMonitoring) {
      Logger.warn('缓存监控已经在运行');
      return;
    }

    this.isMonitoring = true;
    
    // 指标收集间隔
    const metricsInterval = parseInt(process.env['CACHE_METRICS_INTERVAL'] || '60000'); // 1分钟
    this.monitoringInterval = setInterval(() => {
      this.collectMetrics();
    }, metricsInterval);

    // 告警检查间隔
    const alertInterval = parseInt(process.env['CACHE_ALERT_INTERVAL'] || '30000'); // 30秒
    this.alertInterval = setInterval(() => {
      this.checkAlerts();
    }, alertInterval);

    Logger.info('缓存监控已启动', {
      metricsInterval: `${metricsInterval}ms`,
      alertInterval: `${alertInterval}ms`,
      alertsEnabled: this.alertConfig.enabled,
    });
  }

  /**
   * 停止监控
   */
  public stopMonitoring(): void {
    if (!this.isMonitoring) {
      Logger.warn('缓存监控未在运行');
      return;
    }

    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.alertInterval) {
      clearInterval(this.alertInterval);
      this.alertInterval = null;
    }

    Logger.info('缓存监控已停止');
  }

  /**
   * 收集缓存指标
   */
  private async collectMetrics(): Promise<void> {
    try {
      const startTime = Date.now();
      
      // 获取基础统计信息
      const stats = this.cacheManager.getStats();
      
      // 获取策略统计信息
      const strategyStats = await this.strategyManager.getStats();
      
      // 获取缓存信息（用于未来扩展）
      // const cacheInfo = await this.cacheManager.getInfo();
      
      const collectTime = Date.now() - startTime;

      const metrics: CacheMetrics = {
        timestamp: new Date(),
        hitRate: stats.hitRate,
        missRate: stats.totalOperations > 0 ? (stats.misses / stats.totalOperations) * 100 : 0,
        totalOperations: stats.totalOperations,
        averageResponseTime: collectTime, // 简化处理，实际应该是平均响应时间
        errorRate: stats.totalOperations > 0 ? (stats.errors / stats.totalOperations) * 100 : 0,
        memoryUsage: 0, // 需要从Redis获取
        keyCount: Object.values(strategyStats.strategies).reduce((sum: number, strategy: any) => sum + strategy.keyCount, 0),
        expiredKeys: 0, // 需要计算过期键数量
      };

      // 保存指标
      this.metrics.push(metrics);
      
      // 保持最近1000条记录
      if (this.metrics.length > 1000) {
        this.metrics = this.metrics.slice(-1000);
      }

      Logger.debug('缓存指标收集完成', {
        hitRate: `${metrics.hitRate.toFixed(2)}%`,
        totalOperations: metrics.totalOperations,
        keyCount: metrics.keyCount,
        collectTime: `${collectTime}ms`,
      });

    } catch (error) {
      Logger.error('缓存指标收集失败', error);
    }
  }

  /**
   * 检查告警
   */
  private async checkAlerts(): Promise<void> {
    if (!this.alertConfig.enabled || this.metrics.length === 0) {
      return;
    }

    try {
      const latestMetrics = this.metrics[this.metrics.length - 1];
      if (!latestMetrics) {
        return;
      }

      const alerts: CacheAlert[] = [];

      // 检查命中率
      if (latestMetrics.hitRate < this.alertConfig.hitRateThreshold) {
        alerts.push({
          type: 'hit_rate',
          severity: this.getSeverity(latestMetrics.hitRate, this.alertConfig.hitRateThreshold, 'below'),
          message: `缓存命中率过低: ${latestMetrics.hitRate.toFixed(2)}%`,
          value: latestMetrics.hitRate,
          threshold: this.alertConfig.hitRateThreshold,
          timestamp: new Date(),
        });
      }

      // 检查错误率
      if (latestMetrics.errorRate > this.alertConfig.errorRateThreshold) {
        alerts.push({
          type: 'error_rate',
          severity: this.getSeverity(latestMetrics.errorRate, this.alertConfig.errorRateThreshold, 'above'),
          message: `缓存错误率过高: ${latestMetrics.errorRate.toFixed(2)}%`,
          value: latestMetrics.errorRate,
          threshold: this.alertConfig.errorRateThreshold,
          timestamp: new Date(),
        });
      }

      // 检查响应时间
      if (latestMetrics.averageResponseTime > this.alertConfig.responseTimeThreshold) {
        alerts.push({
          type: 'response_time',
          severity: this.getSeverity(latestMetrics.averageResponseTime, this.alertConfig.responseTimeThreshold, 'above'),
          message: `缓存响应时间过长: ${latestMetrics.averageResponseTime}ms`,
          value: latestMetrics.averageResponseTime,
          threshold: this.alertConfig.responseTimeThreshold,
          timestamp: new Date(),
        });
      }

      // 检查键数量
      if (latestMetrics.keyCount > this.alertConfig.keyCountThreshold) {
        alerts.push({
          type: 'key_count',
          severity: this.getSeverity(latestMetrics.keyCount, this.alertConfig.keyCountThreshold, 'above'),
          message: `缓存键数量过多: ${latestMetrics.keyCount}`,
          value: latestMetrics.keyCount,
          threshold: this.alertConfig.keyCountThreshold,
          timestamp: new Date(),
        });
      }

      // 处理告警
      for (const alert of alerts) {
        this.handleAlert(alert);
      }

    } catch (error) {
      Logger.error('缓存告警检查失败', error);
    }
  }

  /**
   * 获取告警严重程度
   */
  private getSeverity(value: number, threshold: number, direction: 'above' | 'below'): 'low' | 'medium' | 'high' | 'critical' {
    const ratio = direction === 'above' ? value / threshold : threshold / value;
    
    if (ratio >= 2) return 'critical';
    if (ratio >= 1.5) return 'high';
    if (ratio >= 1.2) return 'medium';
    return 'low';
  }

  /**
   * 处理告警
   */
  private handleAlert(alert: CacheAlert): void {
    // 检查是否是重复告警（5分钟内相同类型的告警）
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const recentAlert = this.alerts.find(a => 
      a.type === alert.type && 
      a.timestamp > fiveMinutesAgo
    );

    if (recentAlert) {
      return; // 跳过重复告警
    }

    // 保存告警
    this.alerts.push(alert);
    
    // 保持最近100条告警记录
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }

    // 记录告警日志
    const logLevel = alert.severity === 'critical' ? 'error' : 
                    alert.severity === 'high' ? 'warn' : 'info';
    
    Logger[logLevel]('缓存告警', {
      type: alert.type,
      severity: alert.severity,
      message: alert.message,
      value: alert.value,
      threshold: alert.threshold,
    });

    // 可以在这里添加其他告警处理逻辑，如发送邮件、短信等
    this.sendAlert(alert);
  }

  /**
   * 发送告警
   */
  private sendAlert(alert: CacheAlert): void {
    // 这里可以实现具体的告警发送逻辑
    // 例如：发送邮件、短信、Webhook等
    Logger.info('发送缓存告警', {
      type: alert.type,
      severity: alert.severity,
      message: alert.message,
    });
  }

  /**
   * 获取最新指标
   */
  public getLatestMetrics(): CacheMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] || null : null;
  }

  /**
   * 获取历史指标
   */
  public getHistoricalMetrics(hours: number = 24): CacheMetrics[] {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.metrics.filter(metric => metric.timestamp > cutoffTime);
  }

  /**
   * 获取告警历史
   */
  public getAlerts(hours: number = 24): CacheAlert[] {
    const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.alerts.filter(alert => alert.timestamp > cutoffTime);
  }

  /**
   * 获取监控状态
   */
  public getMonitoringStatus(): any {
    const latestMetrics = this.getLatestMetrics();
    const recentAlerts = this.getAlerts(1); // 最近1小时的告警

    return {
      isMonitoring: this.isMonitoring,
      latestMetrics,
      totalMetrics: this.metrics.length,
      recentAlerts: recentAlerts.length,
      alertConfig: this.alertConfig,
      uptime: this.isMonitoring ? 'running' : 'stopped',
    };
  }

  /**
   * 更新告警配置
   */
  public updateAlertConfig(config: Partial<CacheAlertConfig>): void {
    this.alertConfig = { ...this.alertConfig, ...config };
    Logger.info('缓存告警配置已更新', this.alertConfig);
  }

  /**
   * 生成监控报告
   */
  public generateReport(hours: number = 24): any {
    const metrics = this.getHistoricalMetrics(hours);
    const alerts = this.getAlerts(hours);

    if (metrics.length === 0) {
      return {
        period: `${hours}小时`,
        message: '暂无监控数据',
      };
    }

    // 计算统计信息
    const hitRates = metrics.map(m => m.hitRate);
    const responseTimes = metrics.map(m => m.averageResponseTime);
    const errorRates = metrics.map(m => m.errorRate);
    const keyCounts = metrics.map(m => m.keyCount);

    const report = {
      period: `${hours}小时`,
      summary: {
        totalMetrics: metrics.length,
        totalAlerts: alerts.length,
        avgHitRate: this.average(hitRates),
        minHitRate: Math.min(...hitRates),
        maxHitRate: Math.max(...hitRates),
        avgResponseTime: this.average(responseTimes),
        minResponseTime: Math.min(...responseTimes),
        maxResponseTime: Math.max(...responseTimes),
        avgErrorRate: this.average(errorRates),
        maxErrorRate: Math.max(...errorRates),
        avgKeyCount: this.average(keyCounts),
        maxKeyCount: Math.max(...keyCounts),
      },
      trends: {
        hitRateTrend: this.calculateTrend(hitRates),
        responseTimeTrend: this.calculateTrend(responseTimes),
        errorRateTrend: this.calculateTrend(errorRates),
        keyCountTrend: this.calculateTrend(keyCounts),
      },
      alerts: {
        total: alerts.length,
        bySeverity: this.groupBy(alerts, 'severity'),
        byType: this.groupBy(alerts, 'type'),
      },
      recommendations: this.generateRecommendations(metrics, alerts),
    };

    return report;
  }

  /**
   * 计算平均值
   */
  private average(numbers: number[]): number {
    return numbers.length > 0 ? numbers.reduce((sum, num) => sum + num, 0) / numbers.length : 0;
  }

  /**
   * 计算趋势
   */
  private calculateTrend(values: number[]): 'improving' | 'stable' | 'declining' {
    if (values.length < 2) return 'stable';

    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));

    const firstAvg = this.average(firstHalf);
    const secondAvg = this.average(secondHalf);

    if (firstAvg === 0) return 'stable';

    const changePercent = ((secondAvg - firstAvg) / firstAvg) * 100;

    if (changePercent > 5) return 'improving';
    if (changePercent < -5) return 'declining';
    return 'stable';
  }

  /**
   * 分组统计
   */
  private groupBy<T>(array: T[], key: keyof T): Record<string, number> {
    return array.reduce((groups, item) => {
      const value = String(item[key]);
      groups[value] = (groups[value] || 0) + 1;
      return groups;
    }, {} as Record<string, number>);
  }

  /**
   * 生成优化建议
   */
  private generateRecommendations(metrics: CacheMetrics[], alerts: CacheAlert[]): string[] {
    const recommendations: string[] = [];

    if (metrics.length === 0) return recommendations;

    const latestMetrics = metrics[metrics.length - 1];
    const avgHitRate = this.average(metrics.map(m => m.hitRate));
    const avgErrorRate = this.average(metrics.map(m => m.errorRate));
    const avgResponseTime = this.average(metrics.map(m => m.averageResponseTime));

    // 命中率建议
    if (avgHitRate < 70) {
      recommendations.push('缓存命中率较低，建议检查缓存策略和TTL设置');
    }

    // 错误率建议
    if (avgErrorRate > 2) {
      recommendations.push('缓存错误率较高，建议检查Redis连接和网络状况');
    }

    // 响应时间建议
    if (avgResponseTime > 50) {
      recommendations.push('缓存响应时间较长，建议优化网络或增加Redis实例');
    }

    // 键数量建议
    if (latestMetrics && latestMetrics.keyCount > 50000) {
      recommendations.push('缓存键数量较多，建议定期清理过期键或调整TTL');
    }

    // 告警建议
    const criticalAlerts = alerts.filter(a => a.severity === 'critical');
    if (criticalAlerts.length > 0) {
      recommendations.push('存在严重告警，建议立即检查缓存系统状态');
    }

    // 趋势建议
    const hitRateTrend = this.calculateTrend(metrics.map(m => m.hitRate));
    if (hitRateTrend === 'declining') {
      recommendations.push('缓存命中率呈下降趋势，建议分析缓存失效原因');
    }

    const errorRateTrend = this.calculateTrend(metrics.map(m => m.errorRate));
    if (errorRateTrend === 'improving') {
      recommendations.push('缓存错误率呈上升趋势，建议检查系统稳定性');
    }

    return recommendations;
  }

  /**
   * 清理历史数据
   */
  public cleanup(retentionHours: number = 168): void { // 默认保留7天
    const cutoffTime = new Date(Date.now() - retentionHours * 60 * 60 * 1000);

    const originalMetricsCount = this.metrics.length;
    const originalAlertsCount = this.alerts.length;

    this.metrics = this.metrics.filter(metric => metric.timestamp > cutoffTime);
    this.alerts = this.alerts.filter(alert => alert.timestamp > cutoffTime);

    const cleanedMetrics = originalMetricsCount - this.metrics.length;
    const cleanedAlerts = originalAlertsCount - this.alerts.length;

    Logger.info('缓存监控数据清理完成', {
      retentionHours,
      cleanedMetrics,
      cleanedAlerts,
      remainingMetrics: this.metrics.length,
      remainingAlerts: this.alerts.length,
    });
  }

  /**
   * 导出监控数据
   */
  public exportData(hours: number = 24): any {
    return {
      exportTime: new Date().toISOString(),
      period: `${hours}小时`,
      metrics: this.getHistoricalMetrics(hours),
      alerts: this.getAlerts(hours),
      config: this.alertConfig,
      status: this.getMonitoringStatus(),
      report: this.generateReport(hours),
    };
  }

  /**
   * 获取性能分析
   */
  public getPerformanceAnalysis(hours: number = 24): any {
    const metrics = this.getHistoricalMetrics(hours);

    if (metrics.length === 0) {
      return { message: '暂无性能数据' };
    }

    const hitRates = metrics.map(m => m.hitRate);
    const responseTimes = metrics.map(m => m.averageResponseTime);
    const errorRates = metrics.map(m => m.errorRate);
    const operations = metrics.map(m => m.totalOperations);

    // 计算性能指标
    const analysis = {
      hitRateAnalysis: {
        current: hitRates[hitRates.length - 1],
        average: this.average(hitRates),
        min: Math.min(...hitRates),
        max: Math.max(...hitRates),
        trend: this.calculateTrend(hitRates),
        stability: this.calculateStability(hitRates),
      },
      responseTimeAnalysis: {
        current: responseTimes[responseTimes.length - 1],
        average: this.average(responseTimes),
        min: Math.min(...responseTimes),
        max: Math.max(...responseTimes),
        trend: this.calculateTrend(responseTimes),
        p95: this.calculatePercentile(responseTimes, 95),
        p99: this.calculatePercentile(responseTimes, 99),
      },
      errorRateAnalysis: {
        current: errorRates[errorRates.length - 1],
        average: this.average(errorRates),
        max: Math.max(...errorRates),
        trend: this.calculateTrend(errorRates),
        errorSpikes: this.detectSpikes(errorRates),
      },
      throughputAnalysis: {
        current: operations[operations.length - 1],
        average: this.average(operations),
        peak: Math.max(...operations),
        trend: this.calculateTrend(operations),
      },
    };

    return analysis;
  }

  /**
   * 计算稳定性指标
   */
  private calculateStability(values: number[]): 'stable' | 'unstable' | 'volatile' {
    if (values.length < 2) return 'stable';

    const mean = this.average(values);
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    const coefficientOfVariation = mean > 0 ? (stdDev / mean) * 100 : 0;

    if (coefficientOfVariation < 10) return 'stable';
    if (coefficientOfVariation < 25) return 'unstable';
    return 'volatile';
  }

  /**
   * 计算百分位数
   */
  private calculatePercentile(values: number[], percentile: number): number {
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[Math.max(0, index)] || 0;
  }

  /**
   * 检测异常峰值
   */
  private detectSpikes(values: number[]): number {
    if (values.length < 3) return 0;

    const mean = this.average(values);
    const stdDev = Math.sqrt(values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length);
    const threshold = mean + 2 * stdDev; // 2个标准差

    return values.filter(val => val > threshold).length;
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<boolean> {
    try {
      // 检查缓存管理器健康状态
      const cacheHealthy = await this.cacheManager.healthCheck();

      // 检查监控状态
      const monitoringHealthy = this.isMonitoring;

      // 检查最近是否有指标收集
      const latestMetrics = this.getLatestMetrics();
      const metricsHealthy = latestMetrics ?
        (Date.now() - latestMetrics.timestamp.getTime()) < 5 * 60 * 1000 : // 5分钟内
        false;

      const isHealthy = cacheHealthy && monitoringHealthy && metricsHealthy;

      Logger.debug('缓存监控健康检查', {
        cacheHealthy,
        monitoringHealthy,
        metricsHealthy,
        isHealthy,
      });

      return isHealthy;

    } catch (error) {
      Logger.error('缓存监控健康检查失败', error);
      return false;
    }
  }
}
