/**
 * 技能管理器
 * 负责管理玩家技能的学习、使用、冷却等核心逻辑
 */

import { _decorator } from 'cc';
import { BaseManager } from './BaseManager';
import { ConfigManager } from './ConfigManager';
import { EventManager } from './EventManager';
import { 
    ISkillData, 
    IPlayerSkill, 
    ISkillResult, 
    ISkillLearnEvent,
    ISkillUseEvent,
    ISkillCooldownEvent,
    ISkillLevelUpEvent,
    SkillDamageType,
    SkillTargetType,
    SkillEffectType
} from '../config/interfaces/ISkillData';

const { ccclass } = _decorator;

/**
 * 技能管理器配置
 */
export interface ISkillManagerConfig {
    /** 最大技能槽数量 */
    maxSkillSlots: number;
    
    /** 冷却时间更新间隔（毫秒） */
    cooldownUpdateInterval: number;
    
    /** 是否启用技能经验系统 */
    enableSkillExperience: boolean;
    
    /** 默认技能经验获得倍率 */
    defaultExpMultiplier: number;
}

@ccclass('SkillManager')
export class SkillManager extends BaseManager {
    private static _instance: SkillManager;
    
    // 玩家技能数据
    private _playerSkills: Map<string, IPlayerSkill> = new Map();
    
    // 技能槽配置（技能栏显示的技能）
    private _skillSlots: (string | null)[] = [];
    
    // 冷却时间管理
    private _cooldowns: Map<string, number> = new Map();
    private _cooldownTimer: number = 0;
    
    // 配置
    private _config: ISkillManagerConfig = {
        maxSkillSlots: 8,
        cooldownUpdateInterval: 100,
        enableSkillExperience: true,
        defaultExpMultiplier: 1.0
    };
    
    /**
     * 获取单例实例
     */
    public static getInstance(): SkillManager {
        if (!SkillManager._instance) {
            SkillManager._instance = new SkillManager();
        }
        return SkillManager._instance;
    }

    /**
     * 初始化技能管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('⚔️ SkillManager: 开始初始化技能管理器');
        
        try {
            // 等待配置管理器初始化
            const configManager = ConfigManager.getInstance();
            await configManager.waitForInitialization();
            
            // 初始化技能槽
            this.initializeSkillSlots();
            
            // 启动冷却时间更新
            this.startCooldownTimer();
            
            // 注册事件监听
            this.registerEventListeners();
            
            console.log('✅ SkillManager: 初始化完成');
            
        } catch (error) {
            console.error('❌ SkillManager: 初始化失败', error);
            throw error;
        }
    }

    /**
     * 销毁技能管理器
     */
    public destroyManager(): void {
        // 停止冷却时间更新
        this.stopCooldownTimer();
        
        // 清理数据
        this._playerSkills.clear();
        this._cooldowns.clear();
        this._skillSlots = [];
        
        console.log('🗑️ SkillManager: 已销毁');
    }

    /**
     * 初始化技能槽
     */
    private initializeSkillSlots(): void {
        this._skillSlots = new Array(this._config.maxSkillSlots).fill(null);
        console.log(`⚔️ 初始化 ${this._config.maxSkillSlots} 个技能槽`);
    }

    /**
     * 启动冷却时间更新定时器
     */
    private startCooldownTimer(): void {
        this._cooldownTimer = setInterval(() => {
            this.updateCooldowns();
        }, this._config.cooldownUpdateInterval);
    }

    /**
     * 停止冷却时间更新定时器
     */
    private stopCooldownTimer(): void {
        if (this._cooldownTimer) {
            clearInterval(this._cooldownTimer);
            this._cooldownTimer = 0;
        }
    }

    /**
     * 注册事件监听
     */
    private registerEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        // 监听玩家数据变化
        eventManager.on('player_data_loaded', this.onPlayerDataLoaded, this);
        eventManager.on('player_level_up', this.onPlayerLevelUp, this);
    }

    /**
     * 玩家数据加载事件处理
     */
    private onPlayerDataLoaded(playerData: any): void {
        console.log('⚔️ 加载玩家技能数据');
        
        // 从玩家数据中恢复技能信息
        if (playerData.skills) {
            this._playerSkills.clear();
            for (const skillData of playerData.skills) {
                this._playerSkills.set(skillData.skillId, skillData);
            }
        }
        
        // 恢复技能槽配置
        if (playerData.skillSlots) {
            this._skillSlots = [...playerData.skillSlots];
        }
    }

    /**
     * 玩家升级事件处理
     */
    private onPlayerLevelUp(levelData: any): void {
        console.log('⚔️ 玩家升级，检查可学习技能');
        
        // 检查是否有新技能可以学习
        const availableSkills = this.getAvailableSkillsForLevel(levelData.newLevel);
        if (availableSkills.length > 0) {
            EventManager.getInstance().emit('skills_available', availableSkills);
        }
    }

    // ==================== 技能学习 ====================

    /**
     * 学习技能
     */
    public async learnSkill(skillId: string): Promise<ISkillResult> {
        console.log(`⚔️ 尝试学习技能: ${skillId}`);
        
        try {
            // 获取技能配置
            const skillData = ConfigManager.getInstance().getSkillData(skillId);
            if (!skillData) {
                return {
                    success: false,
                    error: `技能不存在: ${skillId}`,
                    manaCost: 0,
                    cooldown: 0,
                    effects: []
                };
            }
            
            // 检查是否已学会
            const playerSkill = this._playerSkills.get(skillId);
            if (playerSkill && playerSkill.learned) {
                return {
                    success: false,
                    error: '技能已学会',
                    manaCost: 0,
                    cooldown: 0,
                    effects: []
                };
            }
            
            // 检查学习条件
            const canLearn = this.checkSkillRequirements(skillData);
            if (!canLearn.success) {
                return {
                    success: false,
                    error: canLearn.error,
                    manaCost: 0,
                    cooldown: 0,
                    effects: []
                };
            }
            
            // 学习技能
            const newPlayerSkill: IPlayerSkill = {
                skillId: skillId,
                level: 1,
                experience: 0,
                experienceRequired: this.calculateExpRequired(1),
                learned: true,
                lastUsed: 0,
                cooldownRemaining: 0
            };
            
            this._playerSkills.set(skillId, newPlayerSkill);
            
            // 发送学习事件
            const learnEvent: ISkillLearnEvent = {
                playerId: 'current_player', // TODO: 从玩家管理器获取
                skillId: skillId,
                newLevel: 1,
                timestamp: Date.now()
            };
            
            EventManager.getInstance().emit('skill_learned', learnEvent);
            
            console.log(`✅ 成功学习技能: ${skillData.name}`);
            
            return {
                success: true,
                manaCost: 0,
                cooldown: 0,
                effects: [],
                experienceGained: 0
            };
            
        } catch (error) {
            console.error(`❌ 学习技能失败: ${skillId}`, error);
            return {
                success: false,
                error: error.message,
                manaCost: 0,
                cooldown: 0,
                effects: []
            };
        }
    }

    /**
     * 检查技能学习条件
     */
    private checkSkillRequirements(skillData: ISkillData): { success: boolean; error?: string } {
        // TODO: 实现具体的条件检查逻辑
        // 1. 检查玩家等级
        // 2. 检查前置技能
        // 3. 检查所需物品
        
        return { success: true };
    }

    /**
     * 计算升级所需经验
     */
    private calculateExpRequired(level: number): number {
        // 简单的经验计算公式
        return level * 100 + (level - 1) * 50;
    }

    // ==================== 技能使用 ====================

    /**
     * 使用技能
     */
    public async useSkill(skillId: string, targetId?: string): Promise<ISkillResult> {
        console.log(`⚔️ 尝试使用技能: ${skillId}`);
        
        try {
            // 获取技能数据
            const skillData = ConfigManager.getInstance().getSkillData(skillId);
            const playerSkill = this._playerSkills.get(skillId);
            
            if (!skillData || !playerSkill || !playerSkill.learned) {
                return {
                    success: false,
                    error: '技能未学会或不存在',
                    manaCost: 0,
                    cooldown: 0,
                    effects: []
                };
            }
            
            // 检查冷却时间
            if (this.getSkillCooldown(skillId) > 0) {
                return {
                    success: false,
                    error: '技能正在冷却中',
                    manaCost: skillData.manaCost,
                    cooldown: skillData.cooldown,
                    effects: []
                };
            }
            
            // TODO: 检查法力值
            // TODO: 检查目标有效性
            
            // 计算技能效果
            const result = this.calculateSkillEffect(skillData, playerSkill, targetId);
            
            // 设置冷却时间
            this.setCooldown(skillId, skillData.cooldown);
            
            // 更新最后使用时间
            playerSkill.lastUsed = Date.now();
            
            // 增加技能经验
            if (this._config.enableSkillExperience) {
                this.addSkillExperience(skillId, 10); // 基础经验值
            }
            
            // 发送使用事件
            const useEvent: ISkillUseEvent = {
                playerId: 'current_player',
                skillId: skillId,
                targetId: targetId,
                result: result,
                timestamp: Date.now()
            };
            
            EventManager.getInstance().emit('skill_used', useEvent);
            
            console.log(`✅ 成功使用技能: ${skillData.name}`);
            return result;
            
        } catch (error) {
            console.error(`❌ 使用技能失败: ${skillId}`, error);
            return {
                success: false,
                error: error.message,
                manaCost: 0,
                cooldown: 0,
                effects: []
            };
        }
    }

    /**
     * 计算技能效果
     */
    private calculateSkillEffect(skillData: ISkillData, playerSkill: IPlayerSkill, targetId?: string): ISkillResult {
        // TODO: 实现具体的技能效果计算
        // 这里是简化版本

        const baseDamage = skillData.baseDamageMultiplier * 100; // 简化计算
        const levelBonus = (playerSkill.level - 1) * 10;
        const totalDamage = baseDamage + levelBonus;

        return {
            success: true,
            damage: skillData.damageType !== SkillDamageType.Healing ? totalDamage : 0,
            healing: skillData.damageType === SkillDamageType.Healing ? totalDamage : 0,
            manaCost: skillData.manaCost,
            cooldown: skillData.cooldown,
            effects: [], // TODO: 处理技能效果
            isCritical: Math.random() < 0.1, // 10%暴击率
            experienceGained: 10
        };
    }

    // ==================== 冷却时间管理 ====================

    /**
     * 设置技能冷却时间
     */
    public setCooldown(skillId: string, cooldownTime: number): void {
        const endTime = Date.now() + (cooldownTime * 1000);
        this._cooldowns.set(skillId, endTime);

        // 发送冷却事件
        const cooldownEvent: ISkillCooldownEvent = {
            playerId: 'current_player',
            skillId: skillId,
            startTime: Date.now(),
            duration: cooldownTime * 1000,
            remaining: cooldownTime * 1000
        };

        EventManager.getInstance().emit('skill_cooldown_start', cooldownEvent);
    }

    /**
     * 获取技能剩余冷却时间（秒）
     */
    public getSkillCooldown(skillId: string): number {
        const endTime = this._cooldowns.get(skillId);
        if (!endTime) {
            return 0;
        }

        const remaining = Math.max(0, endTime - Date.now());
        return remaining / 1000;
    }

    /**
     * 更新所有技能冷却时间
     */
    private updateCooldowns(): void {
        const currentTime = Date.now();
        const expiredSkills: string[] = [];

        for (const [skillId, endTime] of this._cooldowns) {
            if (currentTime >= endTime) {
                expiredSkills.push(skillId);
            } else {
                // 更新玩家技能的冷却时间
                const playerSkill = this._playerSkills.get(skillId);
                if (playerSkill) {
                    playerSkill.cooldownRemaining = (endTime - currentTime) / 1000;
                }
            }
        }

        // 移除已过期的冷却时间
        for (const skillId of expiredSkills) {
            this._cooldowns.delete(skillId);

            const playerSkill = this._playerSkills.get(skillId);
            if (playerSkill) {
                playerSkill.cooldownRemaining = 0;
            }

            // 发送冷却结束事件
            EventManager.getInstance().emit('skill_cooldown_end', { skillId });
        }

        // 发送冷却更新事件
        if (this._cooldowns.size > 0) {
            EventManager.getInstance().emit('skill_cooldowns_update', this.getAllCooldowns());
        }
    }

    /**
     * 获取所有技能冷却时间
     */
    public getAllCooldowns(): { [skillId: string]: number } {
        const cooldowns: { [skillId: string]: number } = {};

        for (const [skillId] of this._cooldowns) {
            cooldowns[skillId] = this.getSkillCooldown(skillId);
        }

        return cooldowns;
    }

    // ==================== 技能槽管理 ====================

    /**
     * 设置技能槽
     */
    public setSkillSlot(slotIndex: number, skillId: string | null): boolean {
        if (slotIndex < 0 || slotIndex >= this._config.maxSkillSlots) {
            console.error(`❌ 技能槽索引超出范围: ${slotIndex}`);
            return false;
        }

        // 检查技能是否已学会
        if (skillId && (!this._playerSkills.has(skillId) || !this._playerSkills.get(skillId)!.learned)) {
            console.error(`❌ 技能未学会，无法设置到技能槽: ${skillId}`);
            return false;
        }

        this._skillSlots[slotIndex] = skillId;

        // 发送技能槽变化事件
        EventManager.getInstance().emit('skill_slot_changed', {
            slotIndex,
            skillId,
            skillSlots: [...this._skillSlots]
        });

        console.log(`⚔️ 设置技能槽 ${slotIndex}: ${skillId || '空'}`);
        return true;
    }

    /**
     * 获取技能槽配置
     */
    public getSkillSlots(): (string | null)[] {
        return [...this._skillSlots];
    }

    /**
     * 获取技能槽中的技能ID
     */
    public getSkillInSlot(slotIndex: number): string | null {
        if (slotIndex < 0 || slotIndex >= this._skillSlots.length) {
            return null;
        }
        return this._skillSlots[slotIndex];
    }

    // ==================== 技能经验系统 ====================

    /**
     * 增加技能经验
     */
    public addSkillExperience(skillId: string, experience: number): boolean {
        const playerSkill = this._playerSkills.get(skillId);
        if (!playerSkill || !playerSkill.learned) {
            return false;
        }

        const oldLevel = playerSkill.level;
        playerSkill.experience += experience * this._config.defaultExpMultiplier;

        // 检查是否升级
        while (playerSkill.experience >= playerSkill.experienceRequired && playerSkill.level < 10) { // 假设最大等级为10
            playerSkill.experience -= playerSkill.experienceRequired;
            playerSkill.level++;
            playerSkill.experienceRequired = this.calculateExpRequired(playerSkill.level);

            console.log(`⚔️ 技能升级: ${skillId} Lv.${oldLevel} → Lv.${playerSkill.level}`);

            // 发送升级事件
            const levelUpEvent: ISkillLevelUpEvent = {
                playerId: 'current_player',
                skillId: skillId,
                oldLevel: oldLevel,
                newLevel: playerSkill.level,
                timestamp: Date.now()
            };

            EventManager.getInstance().emit('skill_level_up', levelUpEvent);
        }

        return true;
    }

    // ==================== 查询方法 ====================

    /**
     * 获取玩家技能
     */
    public getPlayerSkill(skillId: string): IPlayerSkill | null {
        return this._playerSkills.get(skillId) || null;
    }

    /**
     * 获取所有已学会的技能
     */
    public getLearnedSkills(): IPlayerSkill[] {
        return Array.from(this._playerSkills.values()).filter(skill => skill.learned);
    }

    /**
     * 获取指定等级可学习的技能
     */
    public getAvailableSkillsForLevel(level: number): ISkillData[] {
        const configManager = ConfigManager.getInstance();
        const allSkills = configManager.getAllSkillData();

        return allSkills.filter((skill: ISkillData) => {
            // 检查等级要求
            if (skill.requirements.level > level) {
                return false;
            }

            // 检查是否已学会
            const playerSkill = this._playerSkills.get(skill.id);
            if (playerSkill && playerSkill.learned) {
                return false;
            }

            // TODO: 检查其他前置条件

            return true;
        });
    }

    /**
     * 检查技能是否可用
     */
    public isSkillAvailable(skillId: string): boolean {
        const playerSkill = this._playerSkills.get(skillId);
        if (!playerSkill || !playerSkill.learned) {
            return false;
        }

        return this.getSkillCooldown(skillId) <= 0;
    }

    /**
     * 获取技能管理器状态
     */
    public getSkillManagerStats(): any {
        return {
            learnedSkills: this.getLearnedSkills().length,
            activeCooldowns: this._cooldowns.size,
            skillSlots: this._skillSlots.length,
            config: this._config
        };
    }
}
