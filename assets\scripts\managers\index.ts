/**
 * 管理器模块统一导出
 * 提供所有管理器类和相关类型的导入入口
 */

// 导入管理器类用于内部使用
import { BaseManager } from './BaseManager';
import { GameManager } from './GameManager';
import { SceneManager } from './SceneManager';
import { EventManager } from './EventManager';
import { ResourceManager } from './ResourceManager';
import { AudioManager } from './AudioManager';
import { InputManager } from './InputManager';
import { NetworkManager } from '../network/NetworkManager';
import { ConfigManager } from './ConfigManager';
import { UIManager } from './UIManager';

// 重新导出管理器类
export { BaseManager } from './BaseManager';
export { GameManager } from './GameManager';
export { SceneManager } from './SceneManager';
export { EventManager } from './EventManager';
export { ResourceManager } from './ResourceManager';
export { AudioManager } from './AudioManager';
export { InputManager } from './InputManager';
export { NetworkManager } from '../network/NetworkManager';
export { ConfigManager } from './ConfigManager';
export { UIManager } from './UIManager';

// 类型定义
export * from './types/ManagerTypes';

/**
 * 管理器初始化器
 * 提供统一的管理器初始化和销毁功能
 */
export class ManagerInitializer {
    
    /**
     * 已初始化的管理器列表
     */
    private static _initializedManagers: Set<string> = new Set();
    
    /**
     * 初始化所有核心管理器
     */
    public static async initializeAllManagers(): Promise<void> {
        console.log('🎯 开始初始化所有管理器...');
        
        try {
            // 按依赖顺序初始化管理器
            await this.initializeManager('EventManager');
            await this.initializeManager('ResourceManager');
            await this.initializeManager('ConfigManager');
            await this.initializeManager('UIManager');
            await this.initializeManager('AudioManager');
            await this.initializeManager('InputManager');
            await this.initializeManager('NetworkManager');
            await this.initializeManager('SceneManager');
            await this.initializeManager('GameManager');
            
            console.log('✅ 所有管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 管理器初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 初始化单个管理器
     */
    public static async initializeManager(managerName: string): Promise<void> {
        if (this._initializedManagers.has(managerName)) {
            console.log(`⚠️ 管理器已初始化: ${managerName}`);
            return;
        }
        
        console.log(`🎯 初始化管理器: ${managerName}`);
        
        try {
            let manager: any;
            
            switch (managerName) {
                case 'GameManager':
                    manager = GameManager.getInstance();
                    break;
                case 'SceneManager':
                    manager = SceneManager.getInstance();
                    break;
                case 'EventManager':
                    manager = EventManager.getInstance();
                    break;
                case 'ResourceManager':
                    manager = ResourceManager.getInstance();
                    break;
                case 'ConfigManager':
                    manager = ConfigManager.getInstance();
                    break;
                case 'UIManager':
                    manager = UIManager.getInstance();
                    break;
                case 'AudioManager':
                    manager = AudioManager.getInstance();
                    break;
                case 'InputManager':
                    manager = InputManager.getInstance();
                    break;
                case 'NetworkManager':
                    manager = NetworkManager.getInstance();
                    break;
                default:
                    throw new Error(`未知的管理器: ${managerName}`);
            }
            
            // 等待管理器初始化完成
            await manager.waitForInitialization();
            
            this._initializedManagers.add(managerName);
            console.log(`✅ 管理器初始化完成: ${managerName}`);
            
        } catch (error) {
            console.error(`❌ 管理器初始化失败: ${managerName}`, error);
            throw error;
        }
    }
    
    /**
     * 销毁所有管理器
     */
    public static destroyAllManagers(): void {
        console.log('🗑️ 开始销毁所有管理器...');
        
        // 按相反顺序销毁管理器
        const managersToDestroy = [
            'GameManager',
            'SceneManager',
            'ResourceManager',
            'EventManager'
        ];
        
        for (const managerName of managersToDestroy) {
            this.destroyManager(managerName);
        }
        
        this._initializedManagers.clear();
        console.log('✅ 所有管理器销毁完成');
    }
    
    /**
     * 销毁单个管理器
     */
    public static destroyManager(managerName: string): void {
        if (!this._initializedManagers.has(managerName)) {
            console.log(`⚠️ 管理器未初始化: ${managerName}`);
            return;
        }
        
        console.log(`🗑️ 销毁管理器: ${managerName}`);
        
        try {
            switch (managerName) {
                case 'GameManager':
                    GameManager.destroyInstance();
                    break;
                case 'SceneManager':
                    SceneManager.destroyInstance();
                    break;
                case 'EventManager':
                    EventManager.destroyInstance();
                    break;
                case 'ResourceManager':
                    ResourceManager.destroyInstance();
                    break;
                default:
                    console.warn(`未知的管理器: ${managerName}`);
                    return;
            }
            
            this._initializedManagers.delete(managerName);
            console.log(`✅ 管理器销毁完成: ${managerName}`);
            
        } catch (error) {
            console.error(`❌ 管理器销毁失败: ${managerName}`, error);
        }
    }
    
    /**
     * 检查管理器是否已初始化
     */
    public static isManagerInitialized(managerName: string): boolean {
        return this._initializedManagers.has(managerName);
    }
    
    /**
     * 获取已初始化的管理器列表
     */
    public static getInitializedManagers(): string[] {
        return Array.from(this._initializedManagers);
    }
    
    /**
     * 重新初始化所有管理器
     */
    public static async reinitializeAllManagers(): Promise<void> {
        console.log('🔄 重新初始化所有管理器...');
        
        this.destroyAllManagers();
        await this.initializeAllManagers();
        
        console.log('✅ 所有管理器重新初始化完成');
    }
    
    /**
     * 获取管理器状态信息
     */
    public static getManagersStatus(): {
        initialized: string[];
        total: number;
        status: Record<string, any>;
    } {
        const allManagers = ['GameManager', 'SceneManager', 'EventManager', 'ResourceManager'];
        const status: Record<string, any> = {};
        
        for (const managerName of allManagers) {
            const isInitialized = this._initializedManagers.has(managerName);
            status[managerName] = {
                initialized: isInitialized,
                instance: isInitialized ? this.getManagerInstance(managerName) : null
            };
        }
        
        return {
            initialized: Array.from(this._initializedManagers),
            total: allManagers.length,
            status
        };
    }
    
    /**
     * 获取管理器实例
     */
    private static getManagerInstance(managerName: string): any {
        switch (managerName) {
            case 'GameManager':
                return GameManager.hasInstance() ? GameManager.getInstance() : null;
            case 'SceneManager':
                return SceneManager.hasInstance() ? SceneManager.getInstance() : null;
            case 'EventManager':
                return EventManager.hasInstance() ? EventManager.getInstance() : null;
            case 'ResourceManager':
                return ResourceManager.hasInstance() ? ResourceManager.getInstance() : null;
            case 'AudioManager':
                return AudioManager.getInstance();
            case 'InputManager':
                return InputManager.getInstance();
            case 'NetworkManager':
                return NetworkManager.getInstance();
            default:
                return null;
        }
    }
}

/**
 * 管理器快捷访问器
 * 提供便捷的管理器访问方法
 */
export class Managers {
    
    /**
     * 获取游戏管理器
     */
    public static get Game(): GameManager {
        return GameManager.getInstance();
    }
    
    /**
     * 获取场景管理器
     */
    public static get Scene(): SceneManager {
        return SceneManager.getInstance();
    }
    
    /**
     * 获取事件管理器
     */
    public static get Event(): EventManager {
        return EventManager.getInstance();
    }
    
    /**
     * 获取资源管理器
     */
    public static get Resource(): ResourceManager {
        return ResourceManager.getInstance();
    }

    /**
     * 获取配置管理器
     */
    public static get Config(): ConfigManager {
        return ConfigManager.getInstance();
    }

    /**
     * 获取UI管理器
     */
    public static get UI(): UIManager {
        return UIManager.getInstance();
    }

    /**
     * 获取音频管理器
     */
    public static get Audio(): AudioManager {
        return AudioManager.getInstance();
    }

    /**
     * 获取输入管理器
     */
    public static get Input(): InputManager {
        return InputManager.getInstance();
    }

    /**
     * 获取网络管理器
     */
    public static get Network(): NetworkManager {
        return NetworkManager.getInstance();
    }
}
