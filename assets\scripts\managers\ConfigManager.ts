/**
 * 配置管理器
 * 负责加载和管理游戏配置数据
 */

import { _decorator } from 'cc';
import { BaseManager } from './BaseManager';
import { ISkillData, ISkillConfig } from '../config/interfaces/ISkillData';
import { IEntityData, IEntityConfig } from '../config/interfaces/IEntityData';
import { IItemData, IItemConfig } from '../config/interfaces/IItemData';
import { IQuestData, IQuestConfig } from '../config/interfaces/IQuestData';
import { IRewardTableData, IRewardConfig } from '../config/interfaces/IRewardData';
import { resources } from 'cc';

const { ccclass } = _decorator;

/**
 * 配置文件路径常量
 */
export const CONFIG_PATHS = {
    SKILLS: 'config/skills',
    ENTITIES: 'config/entities',
    ITEMS: 'config/items',
    QUESTS: 'config/quests',
    LEVELS: 'config/levels',
    REWARDS: 'config/rewards'
} as const;

/**
 * 配置加载选项
 */
export interface IConfigLoadOptions {
    /** 是否强制重新加载 */
    forceReload?: boolean;
    
    /** 是否启用缓存 */
    enableCache?: boolean;
    
    /** 加载超时时间（毫秒） */
    timeout?: number;
    
    /** 是否验证数据完整性 */
    validateData?: boolean;
}

/**
 * 配置加载结果
 */
export interface IConfigLoadResult<T> {
    /** 是否成功 */
    success: boolean;
    
    /** 配置数据 */
    data?: T;
    
    /** 错误信息 */
    error?: string;
    
    /** 加载时间 */
    loadTime: number;
    
    /** 数据版本 */
    version?: string;
}

@ccclass('ConfigManager')
export class ConfigManager extends BaseManager {
    private static _instance: ConfigManager;
    
    // 配置数据缓存
    private _skillData: Map<string, ISkillData> = new Map();
    private _entityData: Map<string, IEntityData> = new Map();
    private _itemData: Map<string, IItemData> = new Map();
    private _questData: Map<string, IQuestData> = new Map();
    private _rewardData: Map<string, IRewardTableData> = new Map();
    
    // 配置文件缓存
    private _skillConfig: ISkillConfig | null = null;
    private _entityConfig: IEntityConfig | null = null;
    private _itemConfig: IItemConfig | null = null;
    private _questConfig: IQuestConfig | null = null;
    private _rewardConfig: IRewardConfig | null = null;
    
    // 加载状态
    private _loadingPromises: Map<string, Promise<any>> = new Map();
    private _loadedConfigs: Set<string> = new Set();
    
    /**
     * 获取单例实例
     */
    public static getInstance(): ConfigManager {
        if (!ConfigManager._instance) {
            ConfigManager._instance = new ConfigManager();
        }
        return ConfigManager._instance;
    }

    /**
     * 公共初始化方法
     * 提供给外部调用的初始化接口
     */
    public async initialize(): Promise<void> {
        if (this._isInitialized) {
            console.log('⚠️ ConfigManager已经初始化，跳过重复初始化');
            return;
        }

        await this.initializeManager();
        this._isInitialized = true;
    }

    /**
     * 初始化配置管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('🔧 ConfigManager: 开始初始化配置管理器');
        
        try {
            // 并行加载所有核心配置
            const loadPromises = [
                this.loadSkillConfig(),
                this.loadEntityConfig(),
                this.loadItemConfig(),
                this.loadQuestConfig(),
                this.loadRewardConfig()
            ];
            
            const results = await Promise.allSettled(loadPromises);
            
            // 检查加载结果
            let successCount = 0;
            results.forEach((result, index) => {
                const configNames = ['技能', '实体', '物品', '任务', '奖励'];
                if (result.status === 'fulfilled') {
                    console.log(`✅ ${configNames[index]}配置加载成功`);
                    successCount++;
                } else {
                    console.error(`❌ ${configNames[index]}配置加载失败:`, result.reason);
                }
            });
            
            if (successCount === 0) {
                throw new Error('所有配置文件加载失败');
            }
            
            console.log(`✅ ConfigManager: 初始化完成 (${successCount}/${results.length} 配置加载成功)`);
            
        } catch (error) {
            console.error('❌ ConfigManager: 初始化失败', error);
            throw error;
        }
    }

    /**
     * 销毁配置管理器
     * 实现BaseManager的抽象方法
     */
    public destroyManager(): void {
        this._skillData.clear();
        this._entityData.clear();
        this._itemData.clear();
        this._questData.clear();
        this._rewardData.clear();
        
        this._skillConfig = null;
        this._entityConfig = null;
        this._itemConfig = null;
        this._questConfig = null;
        this._rewardConfig = null;
        
        this._loadingPromises.clear();
        this._loadedConfigs.clear();
        
        console.log('🗑️ ConfigManager: 已销毁');
    }

    // ==================== 技能配置 ====================

    /**
     * 加载技能配置
     */
    public async loadSkillConfig(options: IConfigLoadOptions = {}): Promise<IConfigLoadResult<ISkillConfig>> {
        return this.loadConfig<ISkillConfig>(
            CONFIG_PATHS.SKILLS,
            'skills',
            options,
            (config) => {
                this._skillConfig = config;
                this._skillData.clear();
                config.skills.forEach(skill => {
                    this._skillData.set(skill.id, skill);
                });
            }
        );
    }

    /**
     * 获取技能数据
     */
    public getSkillData(skillId: string): ISkillData | null {
        return this._skillData.get(skillId) || null;
    }

    /**
     * 获取所有技能数据
     */
    public getAllSkillData(): ISkillData[] {
        return Array.from(this._skillData.values());
    }

    /**
     * 根据条件筛选技能
     */
    public getSkillsByCondition(predicate: (skill: ISkillData) => boolean): ISkillData[] {
        return this.getAllSkillData().filter(predicate);
    }

    // ==================== 实体配置 ====================

    /**
     * 加载实体配置
     */
    public async loadEntityConfig(options: IConfigLoadOptions = {}): Promise<IConfigLoadResult<IEntityConfig>> {
        return this.loadConfig<IEntityConfig>(
            CONFIG_PATHS.ENTITIES,
            'entities',
            options,
            (config) => {
                this._entityConfig = config;
                this._entityData.clear();
                config.entities.forEach(entity => {
                    this._entityData.set(entity.id, entity);
                });
            }
        );
    }

    /**
     * 获取实体数据
     */
    public getEntityData(entityId: string): IEntityData | null {
        return this._entityData.get(entityId) || null;
    }

    /**
     * 获取所有实体数据
     */
    public getAllEntityData(): IEntityData[] {
        return Array.from(this._entityData.values());
    }

    /**
     * 根据类型获取实体
     */
    public getEntitiesByType(type: string): IEntityData[] {
        return this.getAllEntityData().filter(entity => entity.type === type);
    }

    // ==================== 物品配置 ====================

    /**
     * 加载物品配置
     */
    public async loadItemConfig(options: IConfigLoadOptions = {}): Promise<IConfigLoadResult<IItemConfig>> {
        return this.loadConfig<IItemConfig>(
            CONFIG_PATHS.ITEMS,
            'items',
            options,
            (config) => {
                this._itemConfig = config;
                this._itemData.clear();
                config.items.forEach(item => {
                    this._itemData.set(item.id, item);
                });
            }
        );
    }

    /**
     * 获取物品数据
     */
    public getItemData(itemId: string): IItemData | null {
        return this._itemData.get(itemId) || null;
    }

    /**
     * 获取所有物品数据
     */
    public getAllItemData(): IItemData[] {
        return Array.from(this._itemData.values());
    }

    /**
     * 根据类型获取物品
     */
    public getItemsByType(type: string): IItemData[] {
        return this.getAllItemData().filter(item => item.type === type);
    }

    /**
     * 根据稀有度获取物品
     */
    public getItemsByRarity(rarity: string): IItemData[] {
        return this.getAllItemData().filter(item => item.rarity === rarity);
    }

    // ==================== 任务配置 ====================

    /**
     * 加载任务配置
     */
    public async loadQuestConfig(options: IConfigLoadOptions = {}): Promise<IConfigLoadResult<IQuestConfig>> {
        return this.loadConfig<IQuestConfig>(
            CONFIG_PATHS.QUESTS,
            'quests',
            options,
            (config) => {
                this._questConfig = config;
                this._questData.clear();
                config.quests.forEach(quest => {
                    this._questData.set(quest.id, quest);
                });
            }
        );
    }

    /**
     * 获取任务数据
     */
    public getQuestData(questId: string): IQuestData | null {
        return this._questData.get(questId) || null;
    }

    /**
     * 获取所有任务数据
     */
    public getAllQuestData(): IQuestData[] {
        return Array.from(this._questData.values());
    }

    /**
     * 根据类型获取任务
     */
    public getQuestsByType(type: string): IQuestData[] {
        return this.getAllQuestData().filter(quest => quest.type === type);
    }

    // ==================== 奖励配置 ====================

    /**
     * 加载奖励配置
     */
    public async loadRewardConfig(options: IConfigLoadOptions = {}): Promise<IConfigLoadResult<IRewardConfig>> {
        return this.loadConfig<IRewardConfig>(
            CONFIG_PATHS.REWARDS,
            'rewards',
            options,
            (config) => {
                this._rewardConfig = config;
                this._rewardData.clear();
                config.rewardTables.forEach(rewardTable => {
                    this._rewardData.set(rewardTable.id, rewardTable);
                });
            }
        );
    }

    /**
     * 获取奖励表数据
     */
    public getRewardTableData(rewardTableId: string): IRewardTableData | null {
        return this._rewardData.get(rewardTableId) || null;
    }

    /**
     * 获取所有奖励表数据
     */
    public getAllRewardTableData(): IRewardTableData[] {
        return Array.from(this._rewardData.values());
    }

    /**
     * 根据类型获取奖励表
     */
    public getRewardTablesByType(type: string): IRewardTableData[] {
        return this.getAllRewardTableData().filter(rewardTable => rewardTable.type === type);
    }

    // ==================== 通用配置加载 ====================

    /**
     * 通用配置加载方法
     */
    private async loadConfig<T>(
        path: string,
        configName: string,
        options: IConfigLoadOptions,
        onLoaded: (config: T) => void
    ): Promise<IConfigLoadResult<T>> {
        const startTime = Date.now();
        
        try {
            // 检查是否正在加载
            if (this._loadingPromises.has(configName) && !options.forceReload) {
                await this._loadingPromises.get(configName);
            }
            
            // 检查是否已加载且不强制重新加载
            if (this._loadedConfigs.has(configName) && !options.forceReload) {
                return {
                    success: true,
                    loadTime: Date.now() - startTime
                };
            }
            
            // 创建加载Promise
            const loadPromise = this.loadConfigFromResources<T>(path, options);
            this._loadingPromises.set(configName, loadPromise);
            
            const config = await loadPromise;
            
            // 验证数据
            if (options.validateData !== false) {
                this.validateConfig(config, configName);
            }
            
            // 处理加载的配置
            onLoaded(config);
            
            // 标记为已加载
            this._loadedConfigs.add(configName);
            this._loadingPromises.delete(configName);
            
            return {
                success: true,
                data: config,
                loadTime: Date.now() - startTime,
                version: (config as any).version
            };
            
        } catch (error) {
            this._loadingPromises.delete(configName);
            
            return {
                success: false,
                error: error.message,
                loadTime: Date.now() - startTime
            };
        }
    }

    /**
     * 从资源系统加载配置
     */
    private async loadConfigFromResources<T>(path: string, options: IConfigLoadOptions): Promise<T> {
        return new Promise((resolve, reject) => {
            const timeout = options.timeout || 10000;
            
            const timeoutId = setTimeout(() => {
                reject(new Error(`配置加载超时: ${path}`));
            }, timeout);
            
            resources.load(path, (error, asset) => {
                clearTimeout(timeoutId);
                
                if (error) {
                    reject(new Error(`配置加载失败: ${path} - ${error.message}`));
                    return;
                }
                
                try {
                    let config: T;
                    
                    if (typeof asset === 'string') {
                        config = JSON.parse(asset);
                    } else if (asset.json) {
                        config = asset.json;
                    } else {
                        config = asset;
                    }
                    
                    resolve(config);
                } catch (parseError) {
                    reject(new Error(`配置解析失败: ${path} - ${parseError.message}`));
                }
            });
        });
    }

    /**
     * 验证配置数据
     */
    private validateConfig(config: any, configName: string): void {
        if (!config) {
            throw new Error(`配置数据为空: ${configName}`);
        }
        
        if (!config.version) {
            console.warn(`⚠️ 配置缺少版本信息: ${configName}`);
        }
        
        // 根据配置类型进行特定验证
        switch (configName) {
            case 'skills':
                if (!Array.isArray(config.skills)) {
                    throw new Error('技能配置格式错误: skills字段必须是数组');
                }
                break;
            case 'entities':
                if (!Array.isArray(config.entities)) {
                    throw new Error('实体配置格式错误: entities字段必须是数组');
                }
                break;
            case 'items':
                if (!Array.isArray(config.items)) {
                    throw new Error('物品配置格式错误: items字段必须是数组');
                }
                break;
            case 'quests':
                if (!Array.isArray(config.quests)) {
                    throw new Error('任务配置格式错误: quests字段必须是数组');
                }
                break;
            case 'rewards':
                if (!Array.isArray(config.rewardTables)) {
                    throw new Error('奖励配置格式错误: rewardTables字段必须是数组');
                }
                break;
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 重新加载所有配置
     */
    public async reloadAllConfigs(): Promise<void> {
        console.log('🔄 ConfigManager: 重新加载所有配置');
        
        this._loadedConfigs.clear();
        
        await this.initializeManager();
    }

    /**
     * 获取配置统计信息
     */
    public getConfigStats(): any {
        return {
            skills: this._skillData.size,
            entities: this._entityData.size,
            items: this._itemData.size,
            quests: this._questData.size,
            rewards: this._rewardData.size,
            loadedConfigs: Array.from(this._loadedConfigs),
            isInitialized: this._isInitialized
        };
    }

    /**
     * 检查配置是否已加载
     */
    public isConfigLoaded(configName: string): boolean {
        return this._loadedConfigs.has(configName);
    }
}
