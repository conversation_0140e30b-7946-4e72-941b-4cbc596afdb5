/**
 * UI面板配置器
 * 用于注册和配置所有UI面板
 */

import { _decorator, Component } from 'cc';
import { UIManager } from '../../managers/UIManager';
import { UIPanelType, UILayer, UIAnimationType } from '../types/UITypes';

const { ccclass } = _decorator;

@ccclass('UIPanelConfigurator')
export class UIPanelConfigurator extends Component {

    protected onLoad(): void {
        console.log('📋 UI面板配置器加载');
        
        // 延迟注册面板配置，确保UIManager已初始化
        this.scheduleOnce(() => {
            this.registerAllPanels();
        }, 0.1);
    }

    /**
     * 注册所有面板配置
     */
    private registerAllPanels(): void {
        console.log('📋 开始注册UI面板配置...');
        
        try {
            const uiManager = UIManager.getInstance();
            
            if (!uiManager) {
                console.error('❌ UIManager未初始化，无法注册面板配置');
                return;
            }
            
            // 注册背包面板
            this.registerInventoryPanel(uiManager);
            
            // 注册技能面板
            this.registerSkillPanel(uiManager);
            
            // 注册主菜单
            this.registerMainMenu(uiManager);
            
            // 注册游戏HUD
            this.registerGameHUD(uiManager);
            
            // 注册对话框
            this.registerDialog(uiManager);
            
            console.log('✅ 所有UI面板配置注册完成');
            
            // 显示注册统计
            this.showRegistrationStats(uiManager);
            
        } catch (error) {
            console.error('❌ 注册UI面板配置失败:', error);
        }
    }

    /**
     * 注册背包面板
     */
    private registerInventoryPanel(uiManager: UIManager): void {
        uiManager.registerPanel({
            type: UIPanelType.Inventory,
            prefabPath: 'ui/panels/InventoryPanel', // 预制体路径（暂时不存在）
            layer: UILayer.Normal,
            singleton: true,
            cache: true,
            showAnimation: UIAnimationType.Scale,
            hideAnimation: UIAnimationType.Scale,
            animationDuration: 0.3,
            modal: false,
            escapeToClose: true
        });
        
        console.log('📦 背包面板配置已注册');
    }

    /**
     * 注册技能面板
     */
    private registerSkillPanel(uiManager: UIManager): void {
        uiManager.registerPanel({
            type: UIPanelType.Skills,
            prefabPath: 'ui/panels/SkillPanel', // 预制体路径（暂时不存在）
            layer: UILayer.Normal,
            singleton: true,
            cache: true,
            showAnimation: UIAnimationType.Slide,
            hideAnimation: UIAnimationType.Slide,
            animationDuration: 0.25,
            modal: false,
            escapeToClose: true
        });
        
        console.log('⚔️ 技能面板配置已注册');
    }

    /**
     * 注册主菜单
     */
    private registerMainMenu(uiManager: UIManager): void {
        uiManager.registerPanel({
            type: UIPanelType.MainMenu,
            prefabPath: 'ui/panels/MainMenu', // 预制体路径（暂时不存在）
            layer: UILayer.Popup,
            singleton: true,
            cache: true,
            showAnimation: UIAnimationType.Fade,
            hideAnimation: UIAnimationType.Fade,
            animationDuration: 0.2,
            modal: true,
            escapeToClose: true
        });
        
        console.log('🏠 主菜单配置已注册');
    }

    /**
     * 注册游戏HUD
     */
    private registerGameHUD(uiManager: UIManager): void {
        uiManager.registerPanel({
            type: UIPanelType.GameHUD,
            prefabPath: 'ui/panels/GameHUD', // 预制体路径（暂时不存在）
            layer: UILayer.Background,
            singleton: true,
            cache: true,
            showAnimation: UIAnimationType.None,
            hideAnimation: UIAnimationType.None,
            modal: false,
            escapeToClose: false
        });
        
        console.log('🎮 游戏HUD配置已注册');
    }

    /**
     * 注册对话框
     */
    private registerDialog(uiManager: UIManager): void {
        uiManager.registerPanel({
            type: UIPanelType.Dialog,
            prefabPath: 'ui/panels/Dialog', // 预制体路径（暂时不存在）
            layer: UILayer.Dialog,
            singleton: false, // 对话框可以有多个实例
            cache: true,
            showAnimation: UIAnimationType.Scale,
            hideAnimation: UIAnimationType.Scale,
            animationDuration: 0.2,
            modal: true,
            escapeToClose: false
        });
        
        console.log('💬 对话框配置已注册');
    }

    /**
     * 显示注册统计
     */
    private showRegistrationStats(uiManager: UIManager): void {
        const stats = uiManager.getUIStats();
        
        console.log('\n📊 UI面板注册统计:');
        console.log(`   总注册面板: ${stats.totalPanels}`);
        console.log(`   当前活跃面板: ${stats.activePanels}`);
        console.log(`   可见面板: ${stats.visiblePanels}`);
        console.log(`   缓存预制体: ${stats.cachedPrefabs}`);
        console.log('');
        console.log('💡 注意: 预制体文件尚未创建，面板显示会失败');
        console.log('💡 但是面板配置已正确注册，可以测试配置系统');
        console.log('📋 ================================');
    }

    /**
     * 创建测试用的虚拟面板配置
     */
    public registerTestPanels(): void {
        console.log('🧪 注册测试用虚拟面板...');
        
        const uiManager = UIManager.getInstance();
        if (!uiManager) {
            console.error('❌ UIManager未初始化');
            return;
        }
        
        // 注册一个测试面板，不需要预制体
        uiManager.registerPanel({
            type: UIPanelType.Debug,
            prefabPath: 'ui/panels/TestPanel', // 虚拟路径
            layer: UILayer.Debug,
            singleton: true,
            cache: false,
            showAnimation: UIAnimationType.None,
            hideAnimation: UIAnimationType.None,
            modal: false,
            escapeToClose: true
        });
        
        console.log('✅ 测试面板配置已注册');
    }

    /**
     * 显示所有已注册的面板配置
     */
    public showAllPanelConfigs(): void {
        console.log('\n📋 ========== 已注册的面板配置 ==========');
        
        const uiManager = UIManager.getInstance();
        if (!uiManager) {
            console.error('❌ UIManager未初始化');
            return;
        }
        
        const stats = uiManager.getUIStats();
        console.log(`总配置数量: ${stats.totalPanels}`);
        
        // 这里可以添加更详细的配置信息显示
        // 需要UIManager提供获取配置列表的方法
        
        console.log('📋 =====================================');
    }

    /**
     * 测试面板配置
     */
    public testPanelConfigs(): void {
        console.log('\n🧪 ========== 测试面板配置 ==========');
        
        const uiManager = UIManager.getInstance();
        if (!uiManager) {
            console.error('❌ UIManager未初始化');
            return;
        }
        
        // 测试各种面板类型的配置是否存在
        const panelTypes = [
            UIPanelType.Inventory,
            UIPanelType.Skills,
            UIPanelType.MainMenu,
            UIPanelType.GameHUD,
            UIPanelType.Dialog
        ];
        
        let configuredPanels = 0;
        
        for (const panelType of panelTypes) {
            try {
                // 尝试检查面板是否可见（这会触发配置检查）
                const isVisible = uiManager.isPanelVisible(panelType);
                console.log(`✅ ${panelType}: 配置存在 (当前${isVisible ? '可见' : '隐藏'})`);
                configuredPanels++;
            } catch (error) {
                console.log(`❌ ${panelType}: 配置缺失`);
            }
        }
        
        console.log(`📊 配置完整性: ${configuredPanels}/${panelTypes.length} (${(configuredPanels/panelTypes.length*100).toFixed(1)}%)`);
        console.log('🧪 ===============================');
    }
}
