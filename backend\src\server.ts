import { App } from './app';
import { Logger } from './utils/logger';

/**
 * 服务器启动类
 */
class Server {
  private app: App;
  private port: number;
  private server: any;

  constructor() {
    this.app = new App();
    this.port = parseInt(process.env['PORT'] || '3000', 10);
  }

  /**
   * 启动服务器
   */
  public async start(): Promise<void> {
    try {
      // 连接数据库
      await this.app.connectDatabases();

      // 启动HTTP服务器
      this.server = this.app.getApp().listen(this.port, () => {
        Logger.info(`🚀 服务器启动成功`, {
          port: this.port,
          environment: process.env['NODE_ENV'] || 'development',
          pid: process.pid,
        });
        
        Logger.info(`📖 API文档: http://localhost:${this.port}/api/info`);
        Logger.info(`💚 健康检查: http://localhost:${this.port}/health`);
      });

      // 设置服务器超时
      this.server.timeout = 30000; // 30秒

      // 处理进程信号
      this.setupGracefulShutdown();

    } catch (error) {
      Logger.error('服务器启动失败', error);
      process.exit(1);
    }
  }

  /**
   * 优雅关闭服务器
   */
  private async shutdown(): Promise<void> {
    Logger.info('正在关闭服务器...');

    try {
      // 停止接受新连接
      if (this.server) {
        await new Promise<void>((resolve, reject) => {
          this.server.close((error: Error) => {
            if (error) {
              reject(error);
            } else {
              resolve();
            }
          });
        });
      }

      // 断开数据库连接
      await this.app.disconnectDatabases();

      Logger.info('服务器已优雅关闭');
      process.exit(0);
    } catch (error) {
      Logger.error('服务器关闭过程中出错', error);
      process.exit(1);
    }
  }

  /**
   * 设置优雅关闭处理
   */
  private setupGracefulShutdown(): void {
    // 处理SIGTERM信号（Docker停止容器）
    process.on('SIGTERM', () => {
      Logger.info('收到SIGTERM信号，开始优雅关闭...');
      this.shutdown();
    });

    // 处理SIGINT信号（Ctrl+C）
    process.on('SIGINT', () => {
      Logger.info('收到SIGINT信号，开始优雅关闭...');
      this.shutdown();
    });

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      Logger.error('未捕获的异常', error);
      this.shutdown();
    });

    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
      Logger.error('未处理的Promise拒绝', {
        reason,
        promise,
      });
      this.shutdown();
    });
  }
}

// 启动服务器
const server = new Server();
server.start().catch((error) => {
  Logger.error('服务器启动失败', error);
  process.exit(1);
});
