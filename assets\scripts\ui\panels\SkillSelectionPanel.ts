/**
 * 技能选择面板
 * 技能列表、技能学习界面、技能详情等功能
 */

import { _decorator, Node, ScrollView, Prefab, instantiate, Label, Button } from 'cc';
import { EnhancedBasePanel, PanelState } from '../base/EnhancedBasePanel';
import { UIPanelType } from '../types/UITypes';
import { IPlayerSkill, ISkillData } from './SkillPanel';
import { ConfigManager } from '../../managers/ConfigManager';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 技能选择面板数据接口
 */
export interface ISkillSelectionData {
    /** 可学习的技能列表 */
    availableSkills: ISkillData[];
    
    /** 已学会的技能列表 */
    learnedSkills: IPlayerSkill[];
    
    /** 当前选中的技能 */
    selectedSkill: ISkillData | null;
    
    /** 玩家等级 */
    playerLevel: number;
    
    /** 技能点数 */
    skillPoints: number;
    
    /** 过滤条件 */
    filterConfig: ISkillFilterConfig;
}

/**
 * 技能过滤配置接口
 */
export interface ISkillFilterConfig {
    /** 技能类型过滤 */
    skillType: string | null;
    
    /** 等级要求过滤 */
    levelRequirement: number | null;
    
    /** 是否只显示可学习的 */
    onlyLearnable: boolean;
    
    /** 搜索关键词 */
    searchKeyword: string;
}

/**
 * 技能项数据接口
 */
export interface ISkillItemData {
    /** 技能数据 */
    skillData: ISkillData;
    
    /** 是否已学会 */
    isLearned: boolean;
    
    /** 是否可学习 */
    canLearn: boolean;
    
    /** 学习所需技能点 */
    requiredPoints: number;
    
    /** 前置技能是否满足 */
    prerequisitesMet: boolean;
}

@ccclass('SkillSelectionPanel')
export class SkillSelectionPanel extends EnhancedBasePanel {
    
    @property({ type: ScrollView, tooltip: '技能列表滚动视图' })
    public skillListScrollView: ScrollView | null = null;
    
    @property({ type: Node, tooltip: '技能列表内容节点' })
    public skillListContent: Node | null = null;
    
    @property({ type: Prefab, tooltip: '技能项预制体' })
    public skillItemPrefab: Prefab | null = null;
    
    @property({ type: Node, tooltip: '技能详情面板' })
    public skillDetailPanel: Node | null = null;
    
    @property({ type: Label, tooltip: '技能名称标签' })
    public skillNameLabel: Label | null = null;
    
    @property({ type: Label, tooltip: '技能描述标签' })
    public skillDescriptionLabel: Label | null = null;
    
    @property({ type: Label, tooltip: '技能等级要求标签' })
    public skillLevelRequirementLabel: Label | null = null;
    
    @property({ type: Label, tooltip: '技能点消耗标签' })
    public skillPointCostLabel: Label | null = null;
    
    @property({ type: Button, tooltip: '学习技能按钮' })
    public learnSkillButton: Button | null = null;
    
    @property({ type: Button, tooltip: '装备技能按钮' })
    public equipSkillButton: Button | null = null;
    
    @property({ type: Label, tooltip: '玩家等级标签' })
    public playerLevelLabel: Label | null = null;
    
    @property({ type: Label, tooltip: '技能点数标签' })
    public skillPointsLabel: Label | null = null;
    
    @property({ type: Node, tooltip: '过滤器面板' })
    public filterPanel: Node | null = null;
    
    // 面板数据
    private _panelData: ISkillSelectionData = {
        availableSkills: [],
        learnedSkills: [],
        selectedSkill: null,
        playerLevel: 1,
        skillPoints: 0,
        filterConfig: {
            skillType: null,
            levelRequirement: null,
            onlyLearnable: false,
            searchKeyword: ''
        }
    };
    
    // 技能项节点列表
    private _skillItemNodes: Node[] = [];
    
    // 当前显示的技能项数据
    private _displayedSkillItems: ISkillItemData[] = [];

    protected onPanelLoad(): void {
        console.log('📚 技能选择面板加载');
        
        // 查找UI组件
        this.findPanelComponents();
        
        // 绑定按钮事件
        this.bindButtonEvents();
        
        // 初始化面板
        this.initializePanel();
    }

    protected onPanelEnable(): void {
        console.log('📚 技能选择面板启用');
    }

    protected onPanelDisable(): void {
        console.log('📚 技能选择面板禁用');
    }

    protected onPanelDestroy(): void {
        console.log('📚 技能选择面板销毁');
    }

    protected setupDataBindings(): void {
        // 设置数据绑定配置
        this._dataBindings = [
            {
                key: 'playerLevel',
                nodePath: 'PlayerLevelLabel',
                bindingType: 'text',
                formatter: (value) => `等级: ${value}`
            },
            {
                key: 'skillPoints',
                nodePath: 'SkillPointsLabel',
                bindingType: 'text',
                formatter: (value) => `技能点: ${value}`
            }
        ];
    }

    protected bindPanelEvents(): void {
        const eventManager = EventManager.getInstance();
        
        // 监听技能相关事件
        eventManager.on('skill_learned', this.onSkillLearned, this);
        eventManager.on('skill_equipped', this.onSkillEquipped, this);
        eventManager.on('player_level_up', this.onPlayerLevelUp, this);
        eventManager.on('skill_points_changed', this.onSkillPointsChanged, this);
    }

    protected unbindPanelEvents(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.off('skill_learned', this.onSkillLearned, this);
        eventManager.off('skill_equipped', this.onSkillEquipped, this);
        eventManager.off('player_level_up', this.onPlayerLevelUp, this);
        eventManager.off('skill_points_changed', this.onSkillPointsChanged, this);
    }

    protected async onBeforeShow(data?: any): Promise<void> {
        // 加载技能数据
        await this.loadSkillData();
        
        // 如果有传入数据，更新面板数据
        if (data) {
            this._panelData = { ...this._panelData, ...data };
        }
    }

    protected async onAfterShow(data?: any): Promise<void> {
        // 刷新技能列表
        this.refreshSkillList();
        
        // 更新玩家信息显示
        this.updatePlayerInfo();
    }

    protected async onBeforeHide(): Promise<void> {
        // 清空选中状态
        this._panelData.selectedSkill = null;
        this.updateSkillDetail(null);
    }

    protected async onAfterHide(): Promise<void> {
        // 清理技能列表
        this.clearSkillList();
    }

    protected onDataChanged(data: any): void {
        // 更新面板数据
        this._panelData = { ...this._panelData, ...data };
        
        // 刷新显示
        this.refreshDisplay();
    }

    protected onRefresh(): void {
        this.refreshSkillList();
        this.updatePlayerInfo();
    }

    protected onLanguageChanged(): void {
        // 重新加载技能数据（多语言）
        this.loadSkillData().then(() => {
            this.refreshSkillList();
        });
    }

    protected onThemeChanged(): void {
        // 主题变更处理
        this.refreshDisplay();
    }

    /**
     * 查找面板组件
     */
    private findPanelComponents(): void {
        // 查找技能列表相关组件
        if (!this.skillListScrollView) {
            this.skillListScrollView = this.node.getChildByName('SkillListScrollView')?.getComponent(ScrollView) || null;
        }
        
        if (!this.skillListContent && this.skillListScrollView) {
            this.skillListContent = this.skillListScrollView.content;
        }
        
        // 查找技能详情相关组件
        if (!this.skillDetailPanel) {
            this.skillDetailPanel = this.node.getChildByName('SkillDetailPanel');
        }
        
        if (this.skillDetailPanel) {
            this.skillNameLabel = this.skillDetailPanel.getChildByName('SkillNameLabel')?.getComponent(Label) || null;
            this.skillDescriptionLabel = this.skillDetailPanel.getChildByName('SkillDescriptionLabel')?.getComponent(Label) || null;
            this.skillLevelRequirementLabel = this.skillDetailPanel.getChildByName('LevelRequirementLabel')?.getComponent(Label) || null;
            this.skillPointCostLabel = this.skillDetailPanel.getChildByName('PointCostLabel')?.getComponent(Label) || null;
            this.learnSkillButton = this.skillDetailPanel.getChildByName('LearnButton')?.getComponent(Button) || null;
            this.equipSkillButton = this.skillDetailPanel.getChildByName('EquipButton')?.getComponent(Button) || null;
        }
        
        // 查找玩家信息相关组件
        this.playerLevelLabel = this.node.getChildByName('PlayerLevelLabel')?.getComponent(Label) || null;
        this.skillPointsLabel = this.node.getChildByName('SkillPointsLabel')?.getComponent(Label) || null;
        
        // 查找过滤器面板
        this.filterPanel = this.node.getChildByName('FilterPanel');
    }

    /**
     * 绑定按钮事件
     */
    private bindButtonEvents(): void {
        if (this.learnSkillButton) {
            this.learnSkillButton.node.on(Button.EventType.CLICK, this.onLearnSkillClick, this);
        }
        
        if (this.equipSkillButton) {
            this.equipSkillButton.node.on(Button.EventType.CLICK, this.onEquipSkillClick, this);
        }
    }

    /**
     * 初始化面板
     */
    private initializePanel(): void {
        // 隐藏技能详情面板
        if (this.skillDetailPanel) {
            this.skillDetailPanel.active = false;
        }
        
        // 设置面板类型
        this.panelType = UIPanelType.Skills;
    }

    /**
     * 加载技能数据
     */
    private async loadSkillData(): Promise<void> {
        try {
            const configManager = ConfigManager.getInstance();
            
            // 获取所有技能配置
            const allSkills = configManager.getAllSkills();
            this._panelData.availableSkills = allSkills;
            
            console.log(`📚 加载了 ${allSkills.length} 个技能配置`);
            
        } catch (error) {
            console.error('❌ 加载技能数据失败:', error);
            this._panelData.availableSkills = [];
        }
    }

    /**
     * 刷新技能列表
     */
    private refreshSkillList(): void {
        if (!this.skillListContent || !this.skillItemPrefab) {
            console.warn('缺少技能列表组件或预制体');
            return;
        }
        
        // 清空现有列表
        this.clearSkillList();
        
        // 生成技能项数据
        this._displayedSkillItems = this.generateSkillItems();
        
        // 应用过滤
        const filteredItems = this.applyFilter(this._displayedSkillItems);
        
        // 创建技能项节点
        this.createSkillItemNodes(filteredItems);
        
        console.log(`📚 显示了 ${filteredItems.length} 个技能项`);
    }

    /**
     * 清空技能列表
     */
    private clearSkillList(): void {
        if (this.skillListContent) {
            this.skillListContent.removeAllChildren();
        }
        this._skillItemNodes = [];
    }

    /**
     * 生成技能项数据
     */
    private generateSkillItems(): ISkillItemData[] {
        const items: ISkillItemData[] = [];
        
        for (const skillData of this._panelData.availableSkills) {
            const learnedSkill = this._panelData.learnedSkills.find(s => s.skillData.id === skillData.id);
            const isLearned = !!learnedSkill;
            const canLearn = this.checkCanLearnSkill(skillData);
            const prerequisitesMet = this.checkPrerequisites(skillData);
            
            items.push({
                skillData,
                isLearned,
                canLearn,
                requiredPoints: skillData.learnCost || 1,
                prerequisitesMet
            });
        }
        
        return items;
    }

    /**
     * 应用过滤条件
     */
    private applyFilter(items: ISkillItemData[]): ISkillItemData[] {
        let filteredItems = [...items];
        
        const filter = this._panelData.filterConfig;
        
        // 技能类型过滤
        if (filter.skillType) {
            filteredItems = filteredItems.filter(item => item.skillData.type === filter.skillType);
        }
        
        // 等级要求过滤
        if (filter.levelRequirement !== null) {
            filteredItems = filteredItems.filter(item => 
                (item.skillData.levelRequirement || 1) <= filter.levelRequirement!
            );
        }
        
        // 只显示可学习的
        if (filter.onlyLearnable) {
            filteredItems = filteredItems.filter(item => item.canLearn);
        }
        
        // 搜索关键词过滤
        if (filter.searchKeyword) {
            const keyword = filter.searchKeyword.toLowerCase();
            filteredItems = filteredItems.filter(item => 
                item.skillData.name.toLowerCase().includes(keyword) ||
                item.skillData.description.toLowerCase().includes(keyword)
            );
        }
        
        return filteredItems;
    }

    /**
     * 创建技能项节点
     */
    private createSkillItemNodes(items: ISkillItemData[]): void {
        for (const item of items) {
            const itemNode = instantiate(this.skillItemPrefab!);
            itemNode.setParent(this.skillListContent!);
            
            // 更新技能项显示
            this.updateSkillItemDisplay(itemNode, item);
            
            // 绑定点击事件
            itemNode.on(Node.EventType.TOUCH_END, () => {
                this.onSkillItemClick(item);
            });
            
            this._skillItemNodes.push(itemNode);
        }
    }

    /**
     * 更新技能项显示
     */
    private updateSkillItemDisplay(itemNode: Node, item: ISkillItemData): void {
        // 更新技能名称
        const nameLabel = itemNode.getChildByName('NameLabel')?.getComponent(Label);
        if (nameLabel) {
            nameLabel.string = item.skillData.name;
        }
        
        // 更新技能等级要求
        const levelLabel = itemNode.getChildByName('LevelLabel')?.getComponent(Label);
        if (levelLabel) {
            levelLabel.string = `Lv.${item.skillData.levelRequirement || 1}`;
        }
        
        // 更新学习状态
        const statusLabel = itemNode.getChildByName('StatusLabel')?.getComponent(Label);
        if (statusLabel) {
            if (item.isLearned) {
                statusLabel.string = '已学会';
            } else if (item.canLearn) {
                statusLabel.string = '可学习';
            } else {
                statusLabel.string = '不可学';
            }
        }
        
        // 更新技能图标
        const iconNode = itemNode.getChildByName('Icon');
        if (iconNode) {
            // 这里需要加载技能图标
        }
        
        // 设置节点状态
        const button = itemNode.getComponent(Button);
        if (button) {
            button.interactable = item.canLearn || item.isLearned;
        }
    }

    /**
     * 检查是否可以学习技能
     */
    private checkCanLearnSkill(skillData: ISkillData): boolean {
        // 检查等级要求
        if ((skillData.levelRequirement || 1) > this._panelData.playerLevel) {
            return false;
        }
        
        // 检查技能点
        if ((skillData.learnCost || 1) > this._panelData.skillPoints) {
            return false;
        }
        
        // 检查是否已学会
        const isLearned = this._panelData.learnedSkills.some(s => s.skillData.id === skillData.id);
        if (isLearned) {
            return false;
        }
        
        // 检查前置技能
        return this.checkPrerequisites(skillData);
    }

    /**
     * 检查前置技能
     */
    private checkPrerequisites(skillData: ISkillData): boolean {
        if (!skillData.prerequisites || skillData.prerequisites.length === 0) {
            return true;
        }
        
        for (const prerequisite of skillData.prerequisites) {
            const learnedSkill = this._panelData.learnedSkills.find(s => s.skillData.id === prerequisite.skillId);
            if (!learnedSkill || learnedSkill.level < prerequisite.requiredLevel) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 更新技能详情
     */
    private updateSkillDetail(skillData: ISkillData | null): void {
        if (!this.skillDetailPanel) return;
        
        if (!skillData) {
            this.skillDetailPanel.active = false;
            return;
        }
        
        this.skillDetailPanel.active = true;
        
        // 更新技能信息
        if (this.skillNameLabel) {
            this.skillNameLabel.string = skillData.name;
        }
        
        if (this.skillDescriptionLabel) {
            this.skillDescriptionLabel.string = skillData.description;
        }
        
        if (this.skillLevelRequirementLabel) {
            this.skillLevelRequirementLabel.string = `等级要求: ${skillData.levelRequirement || 1}`;
        }
        
        if (this.skillPointCostLabel) {
            this.skillPointCostLabel.string = `技能点消耗: ${skillData.learnCost || 1}`;
        }
        
        // 更新按钮状态
        this.updateSkillButtons(skillData);
    }

    /**
     * 更新技能按钮状态
     */
    private updateSkillButtons(skillData: ISkillData): void {
        const isLearned = this._panelData.learnedSkills.some(s => s.skillData.id === skillData.id);
        const canLearn = this.checkCanLearnSkill(skillData);
        
        // 学习按钮
        if (this.learnSkillButton) {
            this.learnSkillButton.node.active = !isLearned;
            this.learnSkillButton.interactable = canLearn;
        }
        
        // 装备按钮
        if (this.equipSkillButton) {
            this.equipSkillButton.node.active = isLearned;
            this.equipSkillButton.interactable = isLearned;
        }
    }

    /**
     * 更新玩家信息显示
     */
    private updatePlayerInfo(): void {
        if (this.playerLevelLabel) {
            this.playerLevelLabel.string = `等级: ${this._panelData.playerLevel}`;
        }
        
        if (this.skillPointsLabel) {
            this.skillPointsLabel.string = `技能点: ${this._panelData.skillPoints}`;
        }
    }

    /**
     * 刷新显示
     */
    private refreshDisplay(): void {
        this.refreshSkillList();
        this.updatePlayerInfo();
        
        if (this._panelData.selectedSkill) {
            this.updateSkillDetail(this._panelData.selectedSkill);
        }
    }

    // ==================== 事件处理 ====================

    /**
     * 技能项点击事件
     */
    private onSkillItemClick(item: ISkillItemData): void {
        console.log(`📚 选择技能: ${item.skillData.name}`);
        
        this._panelData.selectedSkill = item.skillData;
        this.updateSkillDetail(item.skillData);
        
        // 发送选择事件
        this.emitPanelEvent('skill_selected', {
            skillData: item.skillData,
            item
        });
    }

    /**
     * 学习技能按钮点击
     */
    private onLearnSkillClick(): void {
        if (!this._panelData.selectedSkill) return;
        
        const skillData = this._panelData.selectedSkill;
        
        console.log(`📚 学习技能: ${skillData.name}`);
        
        // 发送学习技能事件
        this.emitPanelEvent('skill_learn_request', {
            skillData
        });
    }

    /**
     * 装备技能按钮点击
     */
    private onEquipSkillClick(): void {
        if (!this._panelData.selectedSkill) return;
        
        const skillData = this._panelData.selectedSkill;
        const learnedSkill = this._panelData.learnedSkills.find(s => s.skillData.id === skillData.id);
        
        if (!learnedSkill) return;
        
        console.log(`📚 装备技能: ${skillData.name}`);
        
        // 发送装备技能事件
        this.emitPanelEvent('skill_equip_request', {
            skill: learnedSkill
        });
    }

    /**
     * 技能学会事件
     */
    private onSkillLearned(eventData: any): void {
        console.log('📚 技能学会事件:', eventData);
        
        // 刷新面板
        this.refreshDisplay();
    }

    /**
     * 技能装备事件
     */
    private onSkillEquipped(eventData: any): void {
        console.log('📚 技能装备事件:', eventData);
    }

    /**
     * 玩家升级事件
     */
    private onPlayerLevelUp(eventData: any): void {
        console.log('📚 玩家升级事件:', eventData);
        
        if (eventData.newLevel) {
            this._panelData.playerLevel = eventData.newLevel;
            this.refreshDisplay();
        }
    }

    /**
     * 技能点变更事件
     */
    private onSkillPointsChanged(eventData: any): void {
        console.log('📚 技能点变更事件:', eventData);
        
        if (eventData.newPoints !== undefined) {
            this._panelData.skillPoints = eventData.newPoints;
            this.updatePlayerInfo();
            this.refreshSkillList();
        }
    }

    // ==================== 公共API ====================

    /**
     * 设置玩家数据
     */
    public setPlayerData(playerLevel: number, skillPoints: number, learnedSkills: IPlayerSkill[]): void {
        this._panelData.playerLevel = playerLevel;
        this._panelData.skillPoints = skillPoints;
        this._panelData.learnedSkills = learnedSkills;
        
        this.refreshDisplay();
    }

    /**
     * 设置过滤条件
     */
    public setFilter(filterConfig: Partial<ISkillFilterConfig>): void {
        this._panelData.filterConfig = { ...this._panelData.filterConfig, ...filterConfig };
        this.refreshSkillList();
    }

    /**
     * 获取选中的技能
     */
    public getSelectedSkill(): ISkillData | null {
        return this._panelData.selectedSkill;
    }

    /**
     * 获取面板数据
     */
    public getSkillSelectionData(): ISkillSelectionData {
        return { ...this._panelData };
    }
}
