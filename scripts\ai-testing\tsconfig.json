{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["./src/*"], "@core/*": ["./core/*"], "@agents/*": ["./agents/*"], "@templates/*": ["./templates/*"], "@utils/*": ["./utils/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": false, "experimentalSpecifierResolution": "node"}}