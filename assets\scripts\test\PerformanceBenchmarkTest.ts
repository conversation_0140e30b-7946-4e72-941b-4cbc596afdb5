import { _decorator, Component, input, Input, EventKeyboard, KeyCode, profiler, game } from 'cc';
import { TestManager } from './TestManager';

const { ccclass } = _decorator;

/**
 * 性能基准测试组件
 * 测试启动时间、内存使用、帧率稳定性、资源加载性能等
 */
@ccclass('PerformanceBenchmarkTest')
export class PerformanceBenchmarkTest extends Component {
    private _startTime: number = 0;
    private _frameCount: number = 0;
    private _fpsHistory: number[] = [];
    private _memoryHistory: number[] = [];
    private _isMonitoring: boolean = false;
    private _monitoringInterval?: number;
    private _isDestroyed: boolean = false;

    protected onLoad(): void {
        console.log('📊 性能基准测试组件加载');

        // 注册到测试管理器
        TestManager.registerTestComponent('PerformanceBenchmarkTest', this);

        this._startTime = Date.now();
        this._initializeArrays();
        this._initializeKeyboardInput();
        this._initializeProfiler();
    }

    protected start(): void {
        console.log('📊 性能基准测试开始');
        this._showTestInstructions();
        this._testStartupTime();
    }

    /**
     * 初始化数组
     */
    private _initializeArrays(): void {
        if (!this._fpsHistory) {
            this._fpsHistory = [];
        }
        if (!this._memoryHistory) {
            this._memoryHistory = [];
        }
        console.log('📊 性能数据数组已初始化');
    }

    /**
     * 初始化键盘输入
     */
    private _initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this._onKeyDown, this);
        console.log('⌨️ 性能测试键盘输入已初始化');
    }

    /**
     * 初始化性能分析器
     */
    private _initializeProfiler(): void {
        // 启用性能分析器
        profiler.showStats();
        console.log('📈 性能分析器已启用');
    }

    /**
     * 显示测试说明
     */
    private _showTestInstructions(): void {
        console.log('📊 ========== 性能基准测试 ==========');
        console.log('📍 当前组件: PerformanceBenchmarkTest (性能基准测试)');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 测试启动时间');
        console.log('   按 2 键 - 测试内存使用');
        console.log('   按 3 键 - 测试帧率稳定性');
        console.log('   按 4 键 - 开始/停止性能监控');
        console.log('   按 5 键 - 显示性能报告');
        console.log('   按 6 键 - 压力测试');
        console.log('   按 7 键 - 清理性能数据');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('📊 ===================================');
    }

    /**
     * 键盘输入处理
     */
    private _onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this._testStartupTime();
                break;
            case KeyCode.DIGIT_2:
                this._testMemoryUsage();
                break;
            case KeyCode.DIGIT_3:
                this._testFrameRateStability();
                break;
            case KeyCode.DIGIT_4:
                this._togglePerformanceMonitoring();
                break;
            case KeyCode.DIGIT_5:
                this._showPerformanceReport();
                break;
            case KeyCode.DIGIT_6:
                this._runStressTest();
                break;
            case KeyCode.DIGIT_7:
                this._clearPerformanceData();
                break;
            case KeyCode.KEY_H:
                this._showTestInstructions();
                break;
        }
    }

    /**
     * 测试启动时间
     */
    private _testStartupTime(): void {
        const currentTime = Date.now();
        const startupTime = currentTime - this._startTime;
        
        console.log('⏱️ ========== 启动时间测试 ==========');
        console.log(`🚀 应用启动时间: ${startupTime}ms`);
        
        // 评估启动性能
        if (startupTime < 1000) {
            console.log('✅ 启动性能: 优秀 (< 1秒)');
        } else if (startupTime < 3000) {
            console.log('⚠️ 启动性能: 良好 (1-3秒)');
        } else {
            console.log('❌ 启动性能: 需要优化 (> 3秒)');
        }
        
        console.log('⏱️ ===============================');
    }

    /**
     * 测试内存使用
     */
    private _testMemoryUsage(): void {
        console.log('💾 ========== 内存使用测试 ==========');
        
        try {
            // 获取内存信息（如果可用）
            if ((performance as any).memory) {
                const memory = (performance as any).memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                const totalMB = Math.round(memory.totalJSHeapSize / 1024 / 1024);
                const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
                
                console.log(`📊 已使用内存: ${usedMB}MB`);
                console.log(`📊 总分配内存: ${totalMB}MB`);
                console.log(`📊 内存限制: ${limitMB}MB`);
                console.log(`📊 内存使用率: ${Math.round((usedMB / limitMB) * 100)}%`);
                
                // 记录内存历史
                this._memoryHistory.push(usedMB);
                if (this._memoryHistory.length > 100) {
                    this._memoryHistory.shift();
                }
                
                // 评估内存使用
                const usagePercentage = (usedMB / limitMB) * 100;
                if (usagePercentage < 50) {
                    console.log('✅ 内存使用: 正常');
                } else if (usagePercentage < 80) {
                    console.log('⚠️ 内存使用: 偏高');
                } else {
                    console.log('❌ 内存使用: 过高，需要优化');
                }
            } else {
                console.log('⚠️ 浏览器不支持内存监控API');
            }
        } catch (error) {
            console.error('❌ 内存测试失败:', error);
        }
        
        console.log('💾 ===============================');
    }

    /**
     * 测试帧率稳定性
     */
    private _testFrameRateStability(): void {
        console.log('🎯 ========== 帧率稳定性测试 ==========');

        // 检查是否可以开始测试
        if (!TestManager.startTest('frameRateStability')) {
            console.log('⚠️ 帧率稳定性测试已在运行中');
            return;
        }

        // 确保数组已初始化
        this._initializeArrays();

        // 开始帧率监控
        this._frameCount = 0;
        this._fpsHistory = [];

        const testDuration = 5000; // 5秒测试
        const startTime = Date.now();

        const frameTest = () => {
            // 检查组件是否已销毁
            if (this._isDestroyed) {
                console.log('📊 帧率测试已停止（组件已销毁）');
                return;
            }

            this._frameCount++;
            const currentTime = Date.now();
            const elapsed = currentTime - startTime;

            if (elapsed < testDuration) {
                // 计算当前FPS
                const currentFPS = Math.round(this._frameCount / (elapsed / 1000));
                if (this._fpsHistory) {
                    this._fpsHistory.push(currentFPS);
                }

                requestAnimationFrame(frameTest);
            } else {
                // 测试完成，分析结果
                if (!this._isDestroyed) {
                    this._analyzeFrameRateResults();
                }
                // 通知测试管理器测试结束
                TestManager.endTest('frameRateStability');
            }
        };

        requestAnimationFrame(frameTest);
        console.log('🎯 开始5秒帧率稳定性测试...');
    }

    /**
     * 分析帧率测试结果
     */
    private _analyzeFrameRateResults(): void {
        // 检查组件是否已销毁
        if (this._isDestroyed) {
            console.log('📊 帧率分析已停止（组件已销毁）');
            return;
        }

        if (!this._fpsHistory || this._fpsHistory.length === 0) {
            console.log('❌ 没有帧率数据');
            return;
        }
        
        const avgFPS = this._fpsHistory.reduce((a, b) => a + b, 0) / this._fpsHistory.length;
        const minFPS = Math.min(...this._fpsHistory);
        const maxFPS = Math.max(...this._fpsHistory);
        
        // 计算帧率稳定性（标准差）
        const variance = this._fpsHistory.reduce((acc, fps) => acc + Math.pow(fps - avgFPS, 2), 0) / this._fpsHistory.length;
        const stability = Math.sqrt(variance);
        
        console.log(`📊 平均帧率: ${Math.round(avgFPS)} FPS`);
        console.log(`📊 最低帧率: ${minFPS} FPS`);
        console.log(`📊 最高帧率: ${maxFPS} FPS`);
        console.log(`📊 帧率稳定性: ${Math.round(stability)} (越低越稳定)`);
        
        // 评估帧率性能
        if (avgFPS >= 55 && stability < 5) {
            console.log('✅ 帧率性能: 优秀');
        } else if (avgFPS >= 45 && stability < 10) {
            console.log('⚠️ 帧率性能: 良好');
        } else {
            console.log('❌ 帧率性能: 需要优化');
        }
        
        console.log('🎯 ===============================');
    }

    /**
     * 切换性能监控
     */
    private _togglePerformanceMonitoring(): void {
        if (this._isMonitoring) {
            this._stopPerformanceMonitoring();
        } else {
            this._startPerformanceMonitoring();
        }
    }

    /**
     * 开始性能监控
     */
    private _startPerformanceMonitoring(): void {
        if (this._isMonitoring) return;
        
        this._isMonitoring = true;
        console.log('📈 开始性能监控...');
        
        this._monitoringInterval = window.setInterval(() => {
            this._collectPerformanceData();
        }, 1000); // 每秒收集一次数据
    }

    /**
     * 停止性能监控
     */
    private _stopPerformanceMonitoring(): void {
        if (!this._isMonitoring) return;
        
        this._isMonitoring = false;
        
        if (this._monitoringInterval) {
            clearInterval(this._monitoringInterval);
            this._monitoringInterval = undefined;
        }
        
        console.log('📈 性能监控已停止');
    }

    /**
     * 收集性能数据
     */
    private _collectPerformanceData(): void {
        try {
            // 收集内存数据
            if ((performance as any).memory) {
                const memory = (performance as any).memory;
                const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024);
                this._memoryHistory.push(usedMB);
                
                // 限制历史数据长度
                if (this._memoryHistory.length > 300) {
                    this._memoryHistory.shift();
                }
            }
            
            // 收集FPS数据
            const currentFPS = Math.round(game.frameRate);
            this._fpsHistory.push(currentFPS);
            
            if (this._fpsHistory.length > 300) {
                this._fpsHistory.shift();
            }
            
        } catch (error) {
            console.error('❌ 性能数据收集失败:', error);
        }
    }

    /**
     * 显示性能报告
     */
    private _showPerformanceReport(): void {
        console.log('📊 ========== 性能报告 ==========');
        
        // FPS报告
        if (this._fpsHistory.length > 0) {
            const avgFPS = this._fpsHistory.reduce((a, b) => a + b, 0) / this._fpsHistory.length;
            const minFPS = Math.min(...this._fpsHistory);
            const maxFPS = Math.max(...this._fpsHistory);
            
            console.log('🎯 帧率统计:');
            console.log(`   平均: ${Math.round(avgFPS)} FPS`);
            console.log(`   最低: ${minFPS} FPS`);
            console.log(`   最高: ${maxFPS} FPS`);
        }
        
        // 内存报告
        if (this._memoryHistory.length > 0) {
            const avgMemory = this._memoryHistory.reduce((a, b) => a + b, 0) / this._memoryHistory.length;
            const minMemory = Math.min(...this._memoryHistory);
            const maxMemory = Math.max(...this._memoryHistory);
            
            console.log('💾 内存统计:');
            console.log(`   平均: ${Math.round(avgMemory)} MB`);
            console.log(`   最低: ${minMemory} MB`);
            console.log(`   最高: ${maxMemory} MB`);
        }
        
        // 监控状态
        console.log(`📈 监控状态: ${this._isMonitoring ? '运行中' : '已停止'}`);
        console.log(`📊 数据点数: FPS(${this._fpsHistory.length}) 内存(${this._memoryHistory.length})`);
        
        console.log('📊 ===============================');
    }

    /**
     * 运行压力测试
     */
    private _runStressTest(): void {
        console.log('💪 ========== 压力测试 ==========');
        console.log('🔥 开始压力测试...');
        
        // 创建大量对象进行压力测试
        const testObjects: any[] = [];
        const testDuration = 3000; // 3秒压力测试
        const startTime = Date.now();
        
        const stressTest = () => {
            // 创建一些对象
            for (let i = 0; i < 1000; i++) {
                testObjects.push({
                    id: i,
                    data: new Array(100).fill(Math.random()),
                    timestamp: Date.now()
                });
            }
            
            // 删除一些对象
            if (testObjects.length > 10000) {
                testObjects.splice(0, 5000);
            }
            
            const elapsed = Date.now() - startTime;
            if (elapsed < testDuration) {
                requestAnimationFrame(stressTest);
            } else {
                // 清理测试对象
                testObjects.length = 0;
                console.log('✅ 压力测试完成');
                console.log('💪 ===============================');
                
                // 显示测试后的性能数据
                setTimeout(() => {
                    this._testMemoryUsage();
                }, 1000);
            }
        };
        
        requestAnimationFrame(stressTest);
    }

    /**
     * 清理性能数据
     */
    private _clearPerformanceData(): void {
        this._fpsHistory = [];
        this._memoryHistory = [];
        this._frameCount = 0;
        
        console.log('🧹 性能数据已清理');
    }

    protected onDestroy(): void {
        // 设置销毁标志，停止所有运行中的测试
        this._isDestroyed = true;

        // 从测试管理器注销
        TestManager.unregisterTestComponent('PerformanceBenchmarkTest');

        // 停止性能监控
        this._stopPerformanceMonitoring();

        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this._onKeyDown, this);

        // 隐藏性能分析器
        profiler.hideStats();

        console.log('📊 性能基准测试组件销毁');
    }
}
