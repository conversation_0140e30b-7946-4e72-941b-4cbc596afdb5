import { _decorator, Component, Node, input, Input, EventKeyboard, KeyC<PERSON>, director, game } from 'cc';
import { ManagerInitializer, Managers } from '../managers';

const { ccclass, property } = _decorator;

/**
 * 独立测试组件
 * 可以添加到任何节点，完全独立运行
 * 不依赖特定的场景配置
 */
@ccclass('StandaloneTest')
export class StandaloneTest extends Component {

    private isManagersInitialized: boolean = false;
    private testStartTime: number = 0;

    protected onLoad(): void {
        console.log('🔧 ========== 独立测试组件启动 ==========');
        console.log('📍 组件位置: ' + this.node.name);
        console.log('📍 场景名称: ' + director.getScene()?.name);
        console.log('📍 当前时间: ' + new Date().toLocaleTimeString());
        
        this.testStartTime = Date.now();
        this.initializeKeyboardInput();
    }

    protected start(): void {
        console.log('🔧 独立测试组件开始运行');
        this.showInstructions();
        this.initializeManagers();
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        console.log('⌨️ 正在初始化键盘输入系统...');
        
        try {
            // 确保输入系统可用
            if (typeof input === 'undefined') {
                console.error('❌ 输入系统不可用');
                return;
            }

            // 注册键盘事件
            input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
            console.log('✅ 键盘输入系统初始化成功');
            
            // 测试输入系统状态
            console.log('📊 输入系统状态:');
            console.log('  - Input对象:', typeof input);
            console.log('  - EventType:', typeof Input.EventType);
            console.log('  - KeyCode:', typeof KeyCode);
            
        } catch (error) {
            console.error('❌ 键盘输入初始化失败:', error);
        }
    }

    /**
     * 显示测试说明
     */
    private showInstructions(): void {
        console.log('🔧 ========== 独立测试说明 ==========');
        console.log('🎯 这是一个完全独立的测试组件');
        console.log('💡 请确保游戏窗口有焦点，然后按以下按键：');
        console.log('');
        console.log('⌨️ 基础测试:');
        console.log('   T键 - 测试键盘输入功能');
        console.log('   1键 - 数字键测试');
        console.log('   A键 - 字母键测试');
        console.log('   空格键 - 空格键测试');
        console.log('');
        console.log('🎮 系统测试:');
        console.log('   M键 - 显示管理器状态');
        console.log('   G键 - 测试GameManager');
        console.log('   S键 - 测试SceneManager');
        console.log('   E键 - 测试EventManager');
        console.log('   R键 - 测试ResourceManager');
        console.log('');
        console.log('🎬 场景测试:');
        console.log('   2键 - 切换到Main场景');
        console.log('   3键 - 切换到Battle场景');
        console.log('   L键 - 切换到Launch场景');
        console.log('');
        console.log('ℹ️ 其他:');
        console.log('   H键 - 显示帮助信息');
        console.log('   I键 - 显示系统信息');
        console.log('   C键 - 清理控制台');
        console.log('🔧 =====================================');
        console.log('💡 提示: 如果按键没有响应，请点击游戏窗口确保有焦点');
    }

    /**
     * 初始化管理器系统
     */
    private async initializeManagers(): Promise<void> {
        console.log('🎯 开始初始化管理器系统...');
        
        try {
            await ManagerInitializer.initializeAllManagers();
            await Managers.Game.startGame();
            
            this.isManagersInitialized = true;
            console.log('✅ 管理器系统初始化完成');
            console.log('🎉 所有系统就绪，可以开始测试！');
            
        } catch (error) {
            console.error('❌ 管理器系统初始化失败:', error);
            console.log('⚠️ 管理器功能不可用，但基础测试仍可使用');
        }
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        const keyName = KeyCode[event.keyCode] || '未知按键';
        console.log(`⌨️ 检测到按键: ${event.keyCode} (${keyName})`);
        
        switch (event.keyCode) {
            case KeyCode.KEY_T:
                this.testKeyboardInput();
                break;
            case KeyCode.DIGIT_1:
                console.log('🔢 数字键1测试成功！');
                break;
            case KeyCode.KEY_A:
                console.log('🔤 字母键A测试成功！');
                break;
            case KeyCode.SPACE:
                console.log('⭐ 空格键测试成功！');
                break;
            case KeyCode.KEY_M:
                this.showManagerStatus();
                break;
            case KeyCode.KEY_G:
                this.testGameManager();
                break;
            case KeyCode.KEY_S:
                this.testSceneManager();
                break;
            case KeyCode.KEY_E:
                this.testEventManager();
                break;
            case KeyCode.KEY_R:
                this.testResourceManager();
                break;
            case KeyCode.DIGIT_2:
                this.switchToScene('Main');
                break;
            case KeyCode.DIGIT_3:
                this.switchToScene('Battle');
                break;
            case KeyCode.KEY_L:
                this.switchToScene('Launch');
                break;
            case KeyCode.KEY_H:
                this.showInstructions();
                break;
            case KeyCode.KEY_I:
                this.showSystemInfo();
                break;
            case KeyCode.KEY_C:
                this.clearConsole();
                break;
            default:
                console.log(`⌨️ 其他按键: ${event.keyCode} (${keyName})`);
                break;
        }
    }

    /**
     * 测试键盘输入功能
     */
    private testKeyboardInput(): void {
        console.log('🧪 ========== 键盘输入功能测试 ==========');
        console.log('✅ 键盘事件监听器正常工作');
        console.log('✅ 按键事件正确触发');
        console.log('✅ 事件处理函数正常执行');
        console.log('✅ 按键代码正确识别');
        console.log('🎉 键盘输入测试通过！');
        console.log('💡 现在可以测试其他功能了');
        console.log('🧪 ======================================');
    }

    /**
     * 显示管理器状态
     */
    private showManagerStatus(): void {
        console.log('📊 ========== 管理器状态 ==========');
        
        if (!this.isManagersInitialized) {
            console.log('⚠️ 管理器系统未初始化或初始化失败');
            console.log('💡 请等待初始化完成或检查错误日志');
            return;
        }

        try {
            const status = ManagerInitializer.getManagersStatus();
            console.log(`📈 已初始化管理器: ${status.initialized.length}/${status.total}`);
            console.log(`📋 管理器列表: ${status.initialized.join(', ')}`);
            
            for (const [name, info] of Object.entries(status.status)) {
                if (info.initialized && info.instance) {
                    const instanceStatus = info.instance.getStatus();
                    console.log(`📊 ${name}:`, instanceStatus);
                } else {
                    console.log(`📊 ${name}: 未初始化`);
                }
            }
            
        } catch (error) {
            console.error('❌ 获取管理器状态失败:', error);
        }
        
        console.log('📊 ================================');
    }

    /**
     * 测试GameManager
     */
    private testGameManager(): void {
        if (!this.isManagersInitialized) {
            console.log('⚠️ 管理器系统未初始化');
            return;
        }

        console.log('🎮 ========== GameManager测试 ==========');
        
        try {
            const gameManager = Managers.Game;
            const gameInfo = gameManager.getGameInfo();
            
            console.log('🎮 游戏信息:');
            console.log(`  - 当前状态: ${gameInfo.state}`);
            console.log(`  - 运行时间: ${gameInfo.runTime}ms`);
            console.log(`  - 是否运行中: ${gameInfo.isRunning}`);
            console.log(`  - 游戏版本: ${gameInfo.config.version}`);
            console.log(`  - 游戏名称: ${gameInfo.config.name}`);
            
            console.log('✅ GameManager测试完成');
            
        } catch (error) {
            console.error('❌ GameManager测试失败:', error);
        }
        
        console.log('🎮 ==================================');
    }

    /**
     * 测试SceneManager
     */
    private testSceneManager(): void {
        if (!this.isManagersInitialized) {
            console.log('⚠️ 管理器系统未初始化');
            return;
        }

        console.log('🎬 ========== SceneManager测试 ==========');
        
        try {
            const sceneManager = Managers.Scene;
            const currentScene = sceneManager.getCurrentScene();
            const history = sceneManager.getSceneHistory();
            
            console.log('🎬 场景信息:');
            console.log(`  - 当前场景: ${currentScene?.name || '未知'}`);
            console.log(`  - 加载时间: ${currentScene?.loadTime || '未知'}`);
            console.log(`  - 是否正在切换: ${sceneManager.isTransitioning()}`);
            console.log(`  - 历史记录数量: ${history.length}`);
            
            if (history.length > 0) {
                console.log('  - 场景历史:', history.map(s => s.name).join(' → '));
            }
            
            console.log('✅ SceneManager测试完成');
            
        } catch (error) {
            console.error('❌ SceneManager测试失败:', error);
        }
        
        console.log('🎬 ====================================');
    }

    /**
     * 测试EventManager
     */
    private testEventManager(): void {
        if (!this.isManagersInitialized) {
            console.log('⚠️ 管理器系统未初始化');
            return;
        }

        console.log('📡 ========== EventManager测试 ==========');
        
        try {
            const eventManager = Managers.Event;
            const testEventName = 'standalone-test-event';
            
            // 添加测试监听器
            eventManager.on(testEventName, (data) => {
                console.log('📡 收到测试事件:', data);
            });
            
            // 触发测试事件
            eventManager.emit(testEventName, { 
                message: '独立测试事件', 
                timestamp: Date.now(),
                testId: Math.random().toString(36).substr(2, 9)
            });
            
            const status = eventManager.getStatus();
            console.log('📡 事件系统状态:');
            console.log(`  - 事件总数: ${status.totalEvents}`);
            console.log(`  - 监听器总数: ${status.totalListeners}`);
            
            console.log('✅ EventManager测试完成');
            
        } catch (error) {
            console.error('❌ EventManager测试失败:', error);
        }
        
        console.log('📡 ====================================');
    }

    /**
     * 测试ResourceManager
     */
    private testResourceManager(): void {
        if (!this.isManagersInitialized) {
            console.log('⚠️ 管理器系统未初始化');
            return;
        }

        console.log('📦 ========== ResourceManager测试 ==========');
        
        try {
            const resourceManager = Managers.Resource;
            const cacheStats = resourceManager.getCacheStats();
            
            console.log('📦 资源管理器状态:');
            console.log(`  - 缓存资源数: ${cacheStats.totalResources}`);
            console.log(`  - 缓存大小: ${cacheStats.formattedSize}`);
            console.log(`  - 使用率: ${cacheStats.usagePercentage.toFixed(1)}%`);
            console.log(`  - 缓存限制: ${cacheStats.cacheLimit}MB`);
            
            const cachedPaths = resourceManager.getCachedResourcePaths();
            if (cachedPaths.length > 0) {
                console.log('  - 已缓存资源:', cachedPaths);
            } else {
                console.log('  - 暂无缓存资源');
            }
            
            console.log('✅ ResourceManager测试完成');
            
        } catch (error) {
            console.error('❌ ResourceManager测试失败:', error);
        }
        
        console.log('📦 ========================================');
    }

    /**
     * 切换场景
     */
    private async switchToScene(sceneName: string): Promise<void> {
        console.log(`🔄 尝试切换到场景: ${sceneName}`);
        
        try {
            if (this.isManagersInitialized) {
                await Managers.Scene.switchScene(sceneName);
                console.log(`✅ 场景切换成功: ${sceneName}`);
            } else {
                // 降级到直接使用director
                director.loadScene(sceneName, (error) => {
                    if (error) {
                        console.error(`❌ 场景切换失败: ${sceneName}`, error);
                    } else {
                        console.log(`✅ 场景切换成功: ${sceneName}`);
                    }
                });
            }
        } catch (error) {
            console.error(`❌ 场景切换异常: ${sceneName}`, error);
        }
    }

    /**
     * 显示系统信息
     */
    private showSystemInfo(): void {
        console.log('ℹ️ ========== 系统信息 ==========');
        console.log(`📍 当前场景: ${director.getScene()?.name || '未知'}`);
        console.log(`📍 组件节点: ${this.node.name}`);
        console.log(`📍 运行时间: ${Date.now() - this.testStartTime}ms`);
        console.log(`📍 Cocos版本: ${game.config ? 'Cocos Creator' : '未知'}`);
        console.log(`📍 当前时间: ${new Date().toLocaleString()}`);
        console.log(`📍 管理器状态: ${this.isManagersInitialized ? '已初始化' : '未初始化'}`);
        console.log('ℹ️ ==============================');
    }

    /**
     * 清理控制台
     */
    private clearConsole(): void {
        console.clear();
        console.log('🧹 控制台已清理');
        console.log('🔧 独立测试组件运行中...');
        console.log('💡 按H键显示帮助信息');
    }

    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🔧 独立测试组件销毁');
    }
}
