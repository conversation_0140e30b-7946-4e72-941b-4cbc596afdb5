import { _decorator, Component } from 'cc';
import { 
    ConnectionState, 
    IWebSocketMessage, 
    IWebSocketConfig,
    IWebSocketClientOptions,
    NetworkEventType
} from './types/NetworkTypes';
import { EventManager } from '../managers/EventManager';

const { ccclass } = _decorator;

/**
 * WebSocket客户端类
 * 提供WebSocket连接管理、消息收发、自动重连等功能
 */
@ccclass('WebSocketClient')
export class WebSocketClient {
    private _websocket?: WebSocket;
    private _url: string = '';
    private _protocols?: string[];
    private _connectionState: ConnectionState = ConnectionState.DISCONNECTED;
    private _reconnectInterval: number = 5000;
    private _maxReconnectAttempts: number = 5;
    private _reconnectAttempts: number = 0;
    private _heartbeatInterval: number = 30000;
    private _timeout: number = 10000;
    private _heartbeatTimer?: number;
    private _reconnectTimer?: number;
    private _eventListeners: Map<string, Function[]> = new Map();
    private _messageQueue: IWebSocketMessage[] = [];
    private _isDestroyed: boolean = false;

    constructor(options?: IWebSocketClientOptions) {
        if (options) {
            this.configure(options);
        }
    }

    /**
     * 配置WebSocket客户端
     */
    public configure(options: IWebSocketClientOptions): void {
        if (options.reconnectInterval) this._reconnectInterval = options.reconnectInterval;
        if (options.maxReconnectAttempts !== undefined) this._maxReconnectAttempts = options.maxReconnectAttempts;
        if (options.heartbeatInterval) this._heartbeatInterval = options.heartbeatInterval;
        if (options.timeout) this._timeout = options.timeout;
        if (options.protocols) this._protocols = options.protocols;
    }

    /**
     * 连接WebSocket服务器
     */
    public async connect(url: string): Promise<void> {
        if (this._isDestroyed) {
            throw new Error('WebSocketClient has been destroyed');
        }

        this._url = url;
        this._setConnectionState(ConnectionState.CONNECTING);

        return new Promise((resolve, reject) => {
            try {
                this._websocket = new WebSocket(url, this._protocols);
                
                const timeout = setTimeout(() => {
                    this._websocket?.close();
                    reject(new Error('Connection timeout'));
                }, this._timeout);

                this._websocket.onopen = () => {
                    clearTimeout(timeout);
                    this._setConnectionState(ConnectionState.CONNECTED);
                    this._reconnectAttempts = 0;
                    this._startHeartbeat();
                    this._processMessageQueue();
                    
                    EventManager.getInstance().emit(NetworkEventType.WEBSOCKET_CONNECT, { url });
                    resolve();
                };

                this._websocket.onmessage = (event) => {
                    this._handleMessage(event);
                };

                this._websocket.onclose = (event) => {
                    clearTimeout(timeout);
                    this._handleClose(event);
                };

                this._websocket.onerror = (event) => {
                    clearTimeout(timeout);
                    this._handleError(event);
                    reject(new Error('WebSocket connection failed'));
                };

            } catch (error) {
                this._setConnectionState(ConnectionState.ERROR);
                reject(error);
            }
        });
    }

    /**
     * 断开WebSocket连接
     */
    public disconnect(): void {
        this._stopHeartbeat();
        this._stopReconnect();
        
        if (this._websocket) {
            this._websocket.close(1000, 'Client disconnect');
            this._websocket = undefined;
        }
        
        this._setConnectionState(ConnectionState.DISCONNECTED);
        EventManager.getInstance().emit(NetworkEventType.WEBSOCKET_DISCONNECT, { url: this._url });
    }

    /**
     * 手动重连
     */
    public async reconnect(): Promise<void> {
        this.disconnect();
        await this.connect(this._url);
    }

    /**
     * 发送消息
     */
    public send(message: any): void {
        let wsMessage: IWebSocketMessage;

        // 如果是字符串，创建简单的文本消息
        if (typeof message === 'string') {
            wsMessage = {
                type: 'text',
                data: message,
                timestamp: Date.now(),
                id: this._generateMessageId()
            };
        } else {
            // 如果是对象，保持原有逻辑
            wsMessage = {
                type: typeof message === 'object' ? message.type || 'message' : 'message',
                data: message,
                timestamp: Date.now(),
                id: this._generateMessageId()
            };
        }

        if (this.isConnected()) {
            this._sendMessage(wsMessage);
        } else {
            // 如果未连接，将消息加入队列
            this._messageQueue.push(wsMessage);
            console.warn('WebSocket not connected, message queued:', wsMessage);
        }
    }

    /**
     * 监听事件
     */
    public on(event: string, callback: Function): void {
        if (!this._eventListeners.has(event)) {
            this._eventListeners.set(event, []);
        }
        this._eventListeners.get(event)!.push(callback);
    }

    /**
     * 移除事件监听
     */
    public off(event: string, callback?: Function): void {
        if (!this._eventListeners.has(event)) return;

        if (callback) {
            const listeners = this._eventListeners.get(event)!;
            const index = listeners.indexOf(callback);
            if (index > -1) {
                listeners.splice(index, 1);
            }
        } else {
            this._eventListeners.delete(event);
        }
    }

    /**
     * 检查是否已连接
     */
    public isConnected(): boolean {
        return this._connectionState === ConnectionState.CONNECTED && 
               this._websocket?.readyState === WebSocket.OPEN;
    }

    /**
     * 获取连接状态
     */
    public getConnectionState(): ConnectionState {
        return this._connectionState;
    }

    /**
     * 销毁WebSocket客户端
     */
    public destroy(): void {
        this._isDestroyed = true;
        this.disconnect();
        this._eventListeners.clear();
        this._messageQueue = [];
    }

    /**
     * 设置连接状态
     */
    private _setConnectionState(state: ConnectionState): void {
        if (this._connectionState !== state) {
            this._connectionState = state;
            this._emit('stateChange', { state });
        }
    }

    /**
     * 处理接收到的消息
     */
    private _handleMessage(event: MessageEvent): void {
        try {
            let message: IWebSocketMessage;

            // 尝试解析JSON格式的消息
            try {
                const data = JSON.parse(event.data);
                message = {
                    type: data.type || 'message',
                    data: data.data || data,
                    timestamp: Date.now(),
                    id: data.id
                };
            } catch (jsonError) {
                // 如果不是JSON格式，则作为普通文本处理
                message = {
                    type: 'text',
                    data: event.data,
                    timestamp: Date.now(),
                    id: this._generateMessageId()
                };
            }

            this._emit('message', message);
            this._emit(message.type, message);

            EventManager.getInstance().emit(NetworkEventType.WEBSOCKET_MESSAGE, { message });
        } catch (error) {
            console.error('Failed to handle WebSocket message:', error);
            this._emit('error', { error, rawData: event.data });
        }
    }

    /**
     * 处理连接关闭
     */
    private _handleClose(event: CloseEvent): void {
        this._stopHeartbeat();
        this._setConnectionState(ConnectionState.DISCONNECTED);
        
        this._emit('close', { code: event.code, reason: event.reason });
        
        // 如果不是主动关闭且未达到最大重连次数，则自动重连
        if (event.code !== 1000 && this._reconnectAttempts < this._maxReconnectAttempts && !this._isDestroyed) {
            this._scheduleReconnect();
        }
    }

    /**
     * 处理连接错误
     */
    private _handleError(event: Event): void {
        this._setConnectionState(ConnectionState.ERROR);
        this._emit('error', { event });
        
        EventManager.getInstance().emit(NetworkEventType.WEBSOCKET_ERROR, { 
            error: 'WebSocket error', 
            event 
        });
    }

    /**
     * 发送消息到服务器
     */
    private _sendMessage(message: IWebSocketMessage): void {
        if (this._websocket && this.isConnected()) {
            // 如果是简单的文本消息，直接发送
            if (typeof message.data === 'string' && message.type === 'text') {
                this._websocket.send(message.data);
            } else {
                // 否则发送JSON格式
                this._websocket.send(JSON.stringify(message));
            }
        }
    }

    /**
     * 处理消息队列
     */
    private _processMessageQueue(): void {
        while (this._messageQueue.length > 0 && this.isConnected()) {
            const message = this._messageQueue.shift()!;
            this._sendMessage(message);
        }
    }

    /**
     * 开始心跳
     */
    private _startHeartbeat(): void {
        this._stopHeartbeat();
        
        if (this._heartbeatInterval > 0) {
            this._heartbeatTimer = window.setInterval(() => {
                if (this.isConnected()) {
                    this.send({ type: 'ping', timestamp: Date.now() });
                }
            }, this._heartbeatInterval);
        }
    }

    /**
     * 停止心跳
     */
    private _stopHeartbeat(): void {
        if (this._heartbeatTimer) {
            clearInterval(this._heartbeatTimer);
            this._heartbeatTimer = undefined;
        }
    }

    /**
     * 安排重连
     */
    private _scheduleReconnect(): void {
        this._setConnectionState(ConnectionState.RECONNECTING);
        this._reconnectAttempts++;
        
        this._reconnectTimer = window.setTimeout(async () => {
            try {
                await this.connect(this._url);
            } catch (error) {
                console.error(`Reconnect attempt ${this._reconnectAttempts} failed:`, error);
            }
        }, this._reconnectInterval);
    }

    /**
     * 停止重连
     */
    private _stopReconnect(): void {
        if (this._reconnectTimer) {
            clearTimeout(this._reconnectTimer);
            this._reconnectTimer = undefined;
        }
    }

    /**
     * 触发事件
     */
    private _emit(event: string, data?: any): void {
        const listeners = this._eventListeners.get(event);
        if (listeners) {
            listeners.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`Error in WebSocket event listener for ${event}:`, error);
                }
            });
        }
    }

    /**
     * 生成消息ID
     */
    private _generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}
