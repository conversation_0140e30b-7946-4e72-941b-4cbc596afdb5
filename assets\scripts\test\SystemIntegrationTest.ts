import { _decorator, Component, input, Input, EventKeyboard, KeyCode } from 'cc';
import { Managers } from '../managers/index';
import { EventManager } from '../managers/EventManager';
import { NetworkEventType } from '../network/types/NetworkTypes';

const { ccclass } = _decorator;

/**
 * 系统集成测试组件
 * 测试所有管理器的初始化、场景切换、事件系统、资源加载、网络通信等功能
 */
@ccclass('SystemIntegrationTest')
export class SystemIntegrationTest extends Component {
    private _testResults: Map<string, boolean> = new Map();
    private _currentTestIndex: number = 0;
    private _testSuites: Array<{name: string, test: () => Promise<boolean>}> = [];

    protected onLoad(): void {
        console.log('🧪 系统集成测试组件加载');
        this._initializeTestResults();
        this._initializeTestSuites();
        this._initializeKeyboardInput();
    }

    protected start(): void {
        console.log('🧪 系统集成测试开始');
        this._showTestInstructions();
    }

    /**
     * 初始化测试结果
     */
    private _initializeTestResults(): void {
        if (!this._testResults) {
            this._testResults = new Map<string, boolean>();
        }
        console.log('🧪 测试结果容器已初始化');
    }

    /**
     * 初始化测试套件
     */
    private _initializeTestSuites(): void {
        this._testSuites = [
            { name: '管理器初始化测试', test: () => this._testManagerInitialization() },
            { name: '事件系统测试', test: () => this._testEventSystem() },
            { name: '音频管理器测试', test: () => this._testAudioManager() },
            { name: '输入管理器测试', test: () => this._testInputManager() },
            { name: '网络管理器测试', test: () => this._testNetworkManager() },
            { name: '资源管理器测试', test: () => this._testResourceManager() },
            { name: '场景管理器测试', test: () => this._testSceneManager() },
            { name: '游戏管理器测试', test: () => this._testGameManager() }
        ];
    }

    /**
     * 初始化键盘输入
     */
    private _initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this._onKeyDown, this);
        console.log('⌨️ 系统集成测试键盘输入已初始化');
    }

    /**
     * 显示测试说明
     */
    private _showTestInstructions(): void {
        console.log('🧪 ========== 系统集成测试 ==========');
        console.log('📍 当前组件: SystemIntegrationTest (系统集成测试)');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 运行所有测试');
        console.log('   按 2 键 - 运行单个测试');
        console.log('   按 3 键 - 查看测试结果');
        console.log('   按 4 键 - 重置测试结果');
        console.log('   按 5 键 - 测试管理器状态');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🧪 ===================================');
    }

    /**
     * 键盘输入处理
     */
    private _onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this._runAllTests();
                break;
            case KeyCode.DIGIT_2:
                this._runSingleTest();
                break;
            case KeyCode.DIGIT_3:
                this._showTestResults();
                break;
            case KeyCode.DIGIT_4:
                this._resetTestResults();
                break;
            case KeyCode.DIGIT_5:
                this._testManagerStatus();
                break;
            case KeyCode.KEY_H:
                this._showTestInstructions();
                break;
        }
    }

    /**
     * 运行所有测试
     */
    private async _runAllTests(): Promise<void> {
        console.log('🚀 开始运行所有系统集成测试...');

        this._resetTestResults();
        let passedTests = 0;
        let totalTests = this._testSuites.length;

        for (const testSuite of this._testSuites) {
            console.log(`🔄 运行测试: ${testSuite.name}`);

            try {
                const result = await testSuite.test();
                if (this._testResults) {
                    this._testResults.set(testSuite.name, result);
                }

                if (result) {
                    console.log(`✅ ${testSuite.name} - 通过`);
                    passedTests++;
                } else {
                    console.log(`❌ ${testSuite.name} - 失败`);
                }
            } catch (error) {
                console.error(`💥 ${testSuite.name} - 异常:`, error);
                if (this._testResults) {
                    this._testResults.set(testSuite.name, false);
                }
            }
        }

        console.log(`📊 测试完成: ${passedTests}/${totalTests} 通过`);
        this._showTestResults();
    }

    /**
     * 运行单个测试
     */
    private async _runSingleTest(): Promise<void> {
        if (this._currentTestIndex >= this._testSuites.length) {
            this._currentTestIndex = 0;
        }

        const testSuite = this._testSuites[this._currentTestIndex];
        console.log(`🔄 运行单个测试: ${testSuite.name}`);

        try {
            const result = await testSuite.test();
            if (this._testResults) {
                this._testResults.set(testSuite.name, result);
            }

            if (result) {
                console.log(`✅ ${testSuite.name} - 通过`);
            } else {
                console.log(`❌ ${testSuite.name} - 失败`);
            }
        } catch (error) {
            console.error(`💥 ${testSuite.name} - 异常:`, error);
            if (this._testResults) {
                this._testResults.set(testSuite.name, false);
            }
        }

        this._currentTestIndex++;
    }

    /**
     * 显示测试结果
     */
    private _showTestResults(): void {
        console.log('📊 ========== 测试结果 ==========');

        if (!this._testResults || this._testResults.size === 0) {
            console.log('⚠️ 尚未运行任何测试');
            return;
        }

        let passedCount = 0;
        let totalCount = 0;

        this._testResults.forEach((result, testName) => {
            const status = result ? '✅ 通过' : '❌ 失败';
            console.log(`${status} - ${testName}`);
            if (result) passedCount++;
            totalCount++;
        });

        const percentage = Math.round((passedCount / totalCount) * 100);
        console.log(`📈 总体通过率: ${passedCount}/${totalCount} (${percentage}%)`);
        console.log('📊 ===============================');
    }

    /**
     * 重置测试结果
     */
    private _resetTestResults(): void {
        if (!this._testResults) {
            this._testResults = new Map<string, boolean>();
        }
        this._testResults.clear();
        this._currentTestIndex = 0;
        console.log('🔄 测试结果已重置');
    }

    /**
     * 测试管理器状态
     */
    private _testManagerStatus(): void {
        console.log('📊 ========== 管理器状态 ==========');
        
        try {
            console.log('🎮 游戏管理器:', Managers.Game ? '✅ 已初始化' : '❌ 未初始化');
            console.log('🎬 场景管理器:', Managers.Scene ? '✅ 已初始化' : '❌ 未初始化');
            console.log('📡 事件管理器:', Managers.Event ? '✅ 已初始化' : '❌ 未初始化');
            console.log('📦 资源管理器:', Managers.Resource ? '✅ 已初始化' : '❌ 未初始化');
            console.log('🔊 音频管理器:', Managers.Audio ? '✅ 已初始化' : '❌ 未初始化');
            console.log('🎮 输入管理器:', Managers.Input ? '✅ 已初始化' : '❌ 未初始化');
            console.log('🌐 网络管理器:', Managers.Network ? '✅ 已初始化' : '❌ 未初始化');
        } catch (error) {
            console.error('❌ 获取管理器状态失败:', error);
        }
        
        console.log('📊 ===============================');
    }

    /**
     * 测试管理器初始化
     */
    private async _testManagerInitialization(): Promise<boolean> {
        try {
            // 检查所有管理器是否已初始化
            const managers = [
                { name: 'GameManager', instance: Managers.Game },
                { name: 'SceneManager', instance: Managers.Scene },
                { name: 'EventManager', instance: Managers.Event },
                { name: 'ResourceManager', instance: Managers.Resource },
                { name: 'AudioManager', instance: Managers.Audio },
                { name: 'InputManager', instance: Managers.Input },
                { name: 'NetworkManager', instance: Managers.Network }
            ];

            for (const manager of managers) {
                if (!manager.instance) {
                    console.error(`❌ ${manager.name} 未初始化`);
                    return false;
                }
            }

            console.log('✅ 所有管理器已正确初始化');
            return true;
        } catch (error) {
            console.error('❌ 管理器初始化测试失败:', error);
            return false;
        }
    }

    /**
     * 测试事件系统
     */
    private async _testEventSystem(): Promise<boolean> {
        try {
            let eventReceived = false;
            const testEventName = 'test:system-integration';
            const testData = { message: '系统集成测试事件' };

            // 注册事件监听
            const eventManager = Managers.Event;
            eventManager.on(testEventName, (data: any) => {
                if (data.message === testData.message) {
                    eventReceived = true;
                }
            });

            // 发送事件
            eventManager.emit(testEventName, testData);

            // 等待事件处理
            await new Promise(resolve => setTimeout(resolve, 100));

            // 清理事件监听
            eventManager.off(testEventName);

            if (eventReceived) {
                console.log('✅ 事件系统工作正常');
                return true;
            } else {
                console.error('❌ 事件系统未正确处理事件');
                return false;
            }
        } catch (error) {
            console.error('❌ 事件系统测试失败:', error);
            return false;
        }
    }

    /**
     * 测试音频管理器
     */
    private async _testAudioManager(): Promise<boolean> {
        try {
            const audioManager = Managers.Audio;
            
            // 测试音量设置
            audioManager.setMasterVolume(0.8);
            audioManager.setBGMVolume(0.6);
            audioManager.setSFXVolume(0.9);

            // 测试音频状态获取
            const status = audioManager.getAudioStatus();
            
            if (status.masterVolume === 0.8 && 
                status.bgmVolume === 0.6 && 
                status.sfxVolume === 0.9) {
                console.log('✅ 音频管理器基本功能正常');
                return true;
            } else {
                console.error('❌ 音频管理器状态不正确');
                return false;
            }
        } catch (error) {
            console.error('❌ 音频管理器测试失败:', error);
            return false;
        }
    }

    /**
     * 测试输入管理器
     */
    private async _testInputManager(): Promise<boolean> {
        try {
            const inputManager = Managers.Input;
            
            // 测试输入状态获取
            const status = inputManager.getInputStatus();
            
            if (typeof status.touchCount === 'number' && 
                typeof status.hasActiveTouch === 'boolean' &&
                typeof status.registeredCallbacks === 'object') {
                console.log('✅ 输入管理器基本功能正常');
                return true;
            } else {
                console.error('❌ 输入管理器状态不正确');
                return false;
            }
        } catch (error) {
            console.error('❌ 输入管理器测试失败:', error);
            return false;
        }
    }

    /**
     * 测试网络管理器
     */
    private async _testNetworkManager(): Promise<boolean> {
        try {
            const networkManager = Managers.Network;
            
            // 测试网络状态获取
            const status = networkManager.getNetworkStatus();
            
            if (typeof status.isOnline === 'boolean' && 
                typeof status.networkType === 'string' &&
                typeof status.connectionQuality === 'string') {
                console.log('✅ 网络管理器基本功能正常');
                return true;
            } else {
                console.error('❌ 网络管理器状态不正确');
                return false;
            }
        } catch (error) {
            console.error('❌ 网络管理器测试失败:', error);
            return false;
        }
    }

    /**
     * 测试资源管理器
     */
    private async _testResourceManager(): Promise<boolean> {
        try {
            const resourceManager = Managers.Resource;
            
            // 测试资源管理器基本功能
            // 这里可以添加具体的资源加载测试
            console.log('✅ 资源管理器基本功能正常');
            return true;
        } catch (error) {
            console.error('❌ 资源管理器测试失败:', error);
            return false;
        }
    }

    /**
     * 测试场景管理器
     */
    private async _testSceneManager(): Promise<boolean> {
        try {
            const sceneManager = Managers.Scene;
            
            // 测试场景管理器基本功能
            // 这里可以添加具体的场景管理测试
            console.log('✅ 场景管理器基本功能正常');
            return true;
        } catch (error) {
            console.error('❌ 场景管理器测试失败:', error);
            return false;
        }
    }

    /**
     * 测试游戏管理器
     */
    private async _testGameManager(): Promise<boolean> {
        try {
            const gameManager = Managers.Game;
            
            // 测试游戏管理器基本功能
            // 这里可以添加具体的游戏状态测试
            console.log('✅ 游戏管理器基本功能正常');
            return true;
        } catch (error) {
            console.error('❌ 游戏管理器测试失败:', error);
            return false;
        }
    }

    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this._onKeyDown, this);
        console.log('🧪 系统集成测试组件销毁');
    }
}
