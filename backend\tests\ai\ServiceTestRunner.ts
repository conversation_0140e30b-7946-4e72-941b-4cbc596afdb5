import { skillService, ISkillResult } from '../../src/services/SkillService';
import { userService, ILevelResult } from '../../src/services/UserService';
import { battleService, BattleType, ActionType } from '../../src/services/BattleService';
import { User } from '../../src/models/User';
import { Character, CharacterClass } from '../../src/models/Character';
import { Skill } from '../../src/models/Skill';
import { Logger } from '../../src/utils/logger';

/**
 * AI测试结果接口
 */
export interface ITestResult {
  testName: string;
  success: boolean;
  duration: number;
  details: any;
  errors?: string[];
  warnings?: string[];
}

/**
 * AI测试套件结果接口
 */
export interface ITestSuiteResult {
  suiteName: string;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  duration: number;
  results: ITestResult[];
  coverage: number;
}

/**
 * 服务测试运行器
 */
export class ServiceTestRunner {
  private testResults: ITestResult[] = [];
  private startTime: number = 0;

  /**
   * 运行所有测试
   */
  public async runAllTests(): Promise<ITestSuiteResult> {
    this.startTime = Date.now();
    this.testResults = [];

    Logger.info('开始AI服务测试');

    // 运行技能服务测试
    await this.runSkillServiceTests();

    // 运行用户服务测试
    await this.runUserServiceTests();

    // 运行战斗服务测试
    await this.runBattleServiceTests();

    // 运行算法验证测试
    await this.runAlgorithmValidationTests();

    // 运行性能测试
    await this.runPerformanceTests();

    const duration = Date.now() - this.startTime;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = this.testResults.length - passedTests;

    const suiteResult: ITestSuiteResult = {
      suiteName: 'Day10-11 服务测试套件',
      totalTests: this.testResults.length,
      passedTests,
      failedTests,
      duration,
      results: this.testResults,
      coverage: (passedTests / this.testResults.length) * 100,
    };

    Logger.info('AI服务测试完成', {
      totalTests: suiteResult.totalTests,
      passedTests: suiteResult.passedTests,
      failedTests: suiteResult.failedTests,
      coverage: suiteResult.coverage,
      duration: suiteResult.duration,
    });

    return suiteResult;
  }

  /**
   * 技能服务测试
   */
  private async runSkillServiceTests(): Promise<void> {
    // 测试1：技能学习功能
    await this.runTest('技能学习功能测试', async () => {
      // 创建测试用户和角色
      const testUser = await this.createTestUser('skilltest_user');
      const testCharacter = await this.createTestCharacter(testUser._id, 'SkillTestChar', CharacterClass.WARRIOR);
      
      // 创建测试技能
      const testSkill = await this.createTestSkill('test_skill_001', '测试技能', 1, 1);

      // 执行技能学习
      const result = await skillService.learnSkill(testUser._id.toString(), testSkill.id);

      // 验证结果
      return {
        success: result.success,
        message: result.message,
        skillLearned: true,
        skillPointsDeducted: true,
      };
    });

    // 测试2：技能使用功能
    await this.runTest('技能使用功能测试', async () => {
      const testUser = await this.createTestUser('skilluse_user');
      const testCharacter = await this.createTestCharacter(testUser._id, 'SkillUseChar', CharacterClass.MAGE);
      const testSkill = await this.createTestSkill('test_skill_002', '火球术', 1, 1);

      // 先学习技能
      await skillService.learnSkill(testUser._id.toString(), testSkill.id);

      // 使用技能
      const result = await skillService.useSkill(testUser._id.toString(), testSkill.id);

      return {
        success: result.success,
        damage: result.damage || 0,
        cooldownSet: result.cooldownRemaining > 0,
        experienceGained: result.experienceGained || 0,
      };
    });

    // 测试3：技能冷却机制
    await this.runTest('技能冷却机制测试', async () => {
      const testUser = await this.createTestUser('cooldown_user');
      const testCharacter = await this.createTestCharacter(testUser._id, 'CooldownChar', CharacterClass.ARCHER);
      const testSkill = await this.createTestSkill('test_skill_003', '箭雨', 1, 1);

      await skillService.learnSkill(testUser._id.toString(), testSkill.id);
      await skillService.useSkill(testUser._id.toString(), testSkill.id);

      const cooldown = await skillService.getSkillCooldown(testUser._id.toString(), testSkill.id);

      return {
        cooldownExists: cooldown.remainingTime > 0,
        cannotUseImmediately: !cooldown.canUse,
        skillId: cooldown.skillId,
      };
    });
  }

  /**
   * 用户服务测试
   */
  private async runUserServiceTests(): Promise<void> {
    // 测试1：用户创建功能
    await this.runTest('用户创建功能测试', async () => {
      const userData = {
        username: 'testuser_' + Date.now(),
        email: 'test_' + Date.now() + '@example.com',
        password: 'TestPassword123!',
      };

      const result = await userService.createUser(userData);

      return {
        userCreated: !!result.user,
        tokenGenerated: !!result.token,
        username: result.user.username,
        email: result.user.email,
        initialLevel: result.user.profile.level,
        initialExp: result.user.profile.experience,
      };
    });

    // 测试2：用户认证功能
    await this.runTest('用户认证功能测试', async () => {
      const userData = {
        username: 'authtest_' + Date.now(),
        email: 'authtest_' + Date.now() + '@example.com',
        password: 'AuthTest123!',
      };

      const createResult = await userService.createUser(userData);
      const authResult = await userService.authenticateUser(userData.username, userData.password);

      return {
        authenticationSuccess: !!authResult.user,
        tokenGenerated: !!authResult.token,
        userMatches: createResult.user._id.toString() === authResult.user._id.toString(),
        loginCountIncremented: authResult.user.profile.loginCount > createResult.user.profile.loginCount,
      };
    });

    // 测试3：经验等级计算
    await this.runTest('经验等级计算测试', async () => {
      const testUser = await this.createTestUser('explevel_user');
      
      // 测试不同经验值的等级计算
      const testCases = [
        { exp: 0, expectedLevel: 1 },
        { exp: 100, expectedLevel: 2 },
        { exp: 250, expectedLevel: 3 },
        { exp: 475, expectedLevel: 4 },
        { exp: 800, expectedLevel: 5 },
      ];

      const results = [];
      for (const testCase of testCases) {
        const levelResult = userService.calculateLevel(testCase.exp);
        results.push({
          exp: testCase.exp,
          calculatedLevel: levelResult.currentLevel,
          expectedLevel: testCase.expectedLevel,
          correct: levelResult.currentLevel === testCase.expectedLevel,
        });
      }

      return {
        testCases: results,
        allCorrect: results.every(r => r.correct),
      };
    });

    // 测试4：经验值添加功能
    await this.runTest('经验值添加功能测试', async () => {
      const testUser = await this.createTestUser('addexp_user');
      
      const initialLevel = testUser.profile.level;
      const expToAdd = 150;
      
      const result = await userService.addExperience(testUser._id.toString(), expToAdd);

      return {
        experienceAdded: expToAdd,
        leveledUp: result.leveledUp,
        levelsGained: result.levelsGained,
        newLevel: result.currentLevel,
        oldLevel: initialLevel,
      };
    });
  }

  /**
   * 战斗服务测试
   */
  private async runBattleServiceTests(): Promise<void> {
    // 测试1：战斗创建功能
    await this.runTest('战斗创建功能测试', async () => {
      const attacker = await this.createTestUser('battle_attacker');
      const defender = await this.createTestUser('battle_defender');
      const attackerChar = await this.createTestCharacter(attacker._id, 'Attacker', CharacterClass.WARRIOR);
      const defenderChar = await this.createTestCharacter(defender._id, 'Defender', CharacterClass.MAGE);

      const battle = await battleService.createBattle(
        attackerChar._id.toString(),
        defenderChar._id.toString(),
        BattleType.PVE
      );

      return {
        battleCreated: !!battle.id,
        battleType: battle.type,
        participantCount: battle.participants.length,
        battleState: battle.state,
        currentTurn: battle.currentTurn,
      };
    });

    // 测试2：战斗行动执行
    await this.runTest('战斗行动执行测试', async () => {
      const attacker = await this.createTestUser('action_attacker');
      const defender = await this.createTestUser('action_defender');
      const attackerChar = await this.createTestCharacter(attacker._id, 'ActionAttacker', CharacterClass.WARRIOR);
      const defenderChar = await this.createTestCharacter(defender._id, 'ActionDefender', CharacterClass.MAGE);

      const battle = await battleService.createBattle(
        attackerChar._id.toString(),
        defenderChar._id.toString(),
        BattleType.PVE
      );

      const action = {
        participantId: attackerChar._id.toString(),
        actionType: ActionType.ATTACK,
        targetId: defenderChar._id.toString(),
        timestamp: new Date(),
      };

      const logEntry = await battleService.executeBattleAction(
        battle.id,
        attackerChar._id.toString(),
        action
      );

      return {
        actionExecuted: !!logEntry,
        actionType: logEntry.action.actionType,
        damageDealt: (logEntry.result as any).damage || 0,
        turnIncremented: logEntry.turn > 0,
      };
    });
  }

  /**
   * 算法验证测试
   */
  private async runAlgorithmValidationTests(): Promise<void> {
    // 测试1：技能伤害计算验证
    await this.runTest('技能伤害计算算法验证', async () => {
      const testUser = await this.createTestUser('damage_calc_user');
      const testCharacter = await this.createTestCharacter(testUser._id, 'DamageCalcChar', CharacterClass.WARRIOR);
      
      // 设置已知属性值
      testCharacter.attributes.damage = 100;
      testCharacter.attributes.strength = 20;
      testCharacter.attributes.intelligence = 10;
      await testCharacter.save();

      const testSkill = await this.createTestSkill('damage_calc_skill', '伤害计算技能', 1, 1);
      testSkill.baseDamageMultiplier = 1.5;
      testSkill.damageType = 'physical';
      await testSkill.save();

      await skillService.learnSkill(testUser._id.toString(), testSkill.id);
      const result = await skillService.useSkill(testUser._id.toString(), testSkill.id);

      // 验证伤害计算
      const expectedBaseDamage = 100 * 1.5 + 20 * 0.5; // damage * multiplier + strength * 0.5
      const actualDamage = result.damage || 0;
      const damageInRange = actualDamage >= expectedBaseDamage * 0.9 && actualDamage <= expectedBaseDamage * 1.1;

      return {
        expectedBaseDamage,
        actualDamage,
        damageInRange,
        calculationCorrect: damageInRange,
      };
    });

    // 测试2：经验等级计算公式验证
    await this.runTest('经验等级计算公式验证', async () => {
      const testCases = [
        { level: 2, expectedExp: 100 },
        { level: 3, expectedExp: 150 },
        { level: 4, expectedExp: 225 },
        { level: 5, expectedExp: 337 },
      ];

      const results = [];
      for (const testCase of testCases) {
        const actualExp = userService.getExpRequiredForLevel(testCase.level);
        const tolerance = Math.abs(actualExp - testCase.expectedExp) <= 5; // 5点误差容忍
        
        results.push({
          level: testCase.level,
          expectedExp: testCase.expectedExp,
          actualExp,
          tolerance,
        });
      }

      return {
        testCases: results,
        formulaCorrect: results.every(r => r.tolerance),
      };
    });
  }

  /**
   * 性能测试
   */
  private async runPerformanceTests(): Promise<void> {
    // 测试1：技能使用性能
    await this.runTest('技能使用性能测试', async () => {
      const testUser = await this.createTestUser('perf_skill_user');
      const testCharacter = await this.createTestCharacter(testUser._id, 'PerfSkillChar', CharacterClass.MAGE);
      const testSkill = await this.createTestSkill('perf_skill', '性能测试技能', 1, 0);

      await skillService.learnSkill(testUser._id.toString(), testSkill.id);

      const iterations = 10;
      const startTime = Date.now();

      for (let i = 0; i < iterations; i++) {
        await skillService.useSkill(testUser._id.toString(), testSkill.id);
      }

      const endTime = Date.now();
      const avgTime = (endTime - startTime) / iterations;

      return {
        iterations,
        totalTime: endTime - startTime,
        averageTime: avgTime,
        performanceGood: avgTime < 150, // 150ms基准
      };
    });

    // 测试2：用户认证性能
    await this.runTest('用户认证性能测试', async () => {
      const userData = {
        username: 'perfauth_' + Date.now(),
        email: 'perfauth_' + Date.now() + '@example.com',
        password: 'PerfAuth123!',
      };

      await userService.createUser(userData);

      const iterations = 5;
      const startTime = Date.now();

      for (let i = 0; i < iterations; i++) {
        await userService.authenticateUser(userData.username, userData.password);
      }

      const endTime = Date.now();
      const avgTime = (endTime - startTime) / iterations;

      return {
        iterations,
        totalTime: endTime - startTime,
        averageTime: avgTime,
        performanceGood: avgTime < 200, // 200ms基准
      };
    });
  }

  /**
   * 运行单个测试
   */
  private async runTest(testName: string, testFunction: () => Promise<any>): Promise<void> {
    const startTime = Date.now();
    let success = false;
    let details: any = {};
    let errors: string[] = [];

    try {
      details = await testFunction();
      success = true;
    } catch (error) {
      success = false;
      errors.push(error.message || '未知错误');
      Logger.error(`测试失败: ${testName}`, error);
    }

    const duration = Date.now() - startTime;

    this.testResults.push({
      testName,
      success,
      duration,
      details,
      errors: errors.length > 0 ? errors : undefined,
    });

    Logger.info(`测试完成: ${testName}`, {
      success,
      duration,
      details: success ? details : undefined,
      errors: !success ? errors : undefined,
    });
  }

  /**
   * 创建测试用户
   */
  private async createTestUser(username: string): Promise<any> {
    const userData = {
      username: username + '_' + Date.now(),
      email: username + '_' + Date.now() + '@test.com',
      password: 'TestPassword123!',
    };

    const result = await userService.createUser(userData);
    return result.user;
  }

  /**
   * 创建测试角色
   */
  private async createTestCharacter(userId: any, name: string, characterClass: CharacterClass): Promise<any> {
    const character = new Character({
      userId,
      name: name + '_' + Date.now(),
      class: characterClass,
      level: 5,
      experience: 0,
      attributes: {
        strength: 15,
        agility: 12,
        intelligence: 10,
        vitality: 14,
        spirit: 8,
        damage: 50,
        maxHp: 200,
        maxMp: 100,
        currentHp: 200,
        currentMp: 100,
        recoverHp: 0.02,
        recoverMp: 0.02,
        recoveryInterval: 1.0,
        atkInterval: 1.5,
        atkSpeed: 1.0,
        castSpeed: 1.0,
        def: 10,
        spellResistance: 5,
        meleeAccuracy: 0.1,
        rangedAccuracy: 0.05,
        magicAccuracy: 0.0,
        meleeDodge: 0.05,
        rangedDodge: 0.0,
        magicDodge: 0.0,
        penetrate: 0.1,
        criticalHit: 0.1,
        toughness: 0.2,
        enmity: 0.5,
        damageIncreasePhysical: 0.2,
      },
      skills: [],
      skillPoints: 5,
      equipment: {},
      inventory: { items: [], maxSlots: 30 },
      location: { mapId: 'test_area', x: 0, y: 0, z: 0 },
      battleStatus: { isInBattle: false, statusEffects: [] },
      statistics: {
        totalPlayTime: 0,
        monstersKilled: 0,
        itemsCollected: 0,
        questsCompleted: 0,
        deathCount: 0,
        goldEarned: 0,
      },
    });

    return await character.save();
  }

  /**
   * 创建测试技能
   */
  private async createTestSkill(id: string, name: string, level: number, cooldown: number): Promise<any> {
    const skill = new Skill({
      id: id + '_' + Date.now(),
      name,
      description: '测试技能描述',
      manaCost: 20,
      castTime: 1.0,
      cooldown,
      damageType: 'physical',
      targetType: 'enemy',
      baseDamageMultiplier: 1.2,
      level,
      maxLevel: 10,
      requirements: {
        level,
        skillPoints: 1,
        prerequisiteSkills: [],
        attributes: {},
      },
      effects: [],
    });

    return await skill.save();
  }
}

export const serviceTestRunner = new ServiceTestRunner();
