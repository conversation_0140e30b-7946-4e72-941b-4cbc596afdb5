#!/usr/bin/env node

/**
 * 简化版AI测试框架 - 快速验证功能
 */

import * as fs from 'fs';
import * as path from 'path';

interface SimpleTestResult {
    name: string;
    status: 'passed' | 'failed';
    message: string;
}

class SimpleAITester {
    private projectRoot: string;

    constructor(projectRoot: string = process.cwd()) {
        this.projectRoot = projectRoot;
    }

    async runBasicTests(): Promise<SimpleTestResult[]> {
        const results: SimpleTestResult[] = [];

        // 测试1: 检查项目结构
        results.push(await this.testProjectStructure());

        // 测试2: 检查Cocos Creator配置
        results.push(await this.testCocosConfig());

        // 测试3: 检查脚本文件
        results.push(await this.testScriptFiles());

        // 测试4: 检查资源文件
        results.push(await this.testAssetFiles());

        return results;
    }

    private async testProjectStructure(): Promise<SimpleTestResult> {
        try {
            const requiredDirs = ['assets', 'settings', 'project.json'];
            const missing = [];

            for (const dir of requiredDirs) {
                const fullPath = path.join(this.projectRoot, dir);
                if (!fs.existsSync(fullPath)) {
                    missing.push(dir);
                }
            }

            if (missing.length === 0) {
                return {
                    name: 'Project Structure',
                    status: 'passed',
                    message: 'All required directories and files found'
                };
            } else {
                return {
                    name: 'Project Structure',
                    status: 'failed',
                    message: `Missing: ${missing.join(', ')}`
                };
            }
        } catch (error) {
            return {
                name: 'Project Structure',
                status: 'failed',
                message: `Error: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }

    private async testCocosConfig(): Promise<SimpleTestResult> {
        try {
            const projectJsonPath = path.join(this.projectRoot, 'project.json');
            if (!fs.existsSync(projectJsonPath)) {
                return {
                    name: 'Cocos Config',
                    status: 'failed',
                    message: 'project.json not found'
                };
            }

            const config = JSON.parse(fs.readFileSync(projectJsonPath, 'utf8'));
            if (config.version && config.engine) {
                return {
                    name: 'Cocos Config',
                    status: 'passed',
                    message: `Engine: ${config.engine}, Version: ${config.version}`
                };
            } else {
                return {
                    name: 'Cocos Config',
                    status: 'failed',
                    message: 'Invalid project.json format'
                };
            }
        } catch (error) {
            return {
                name: 'Cocos Config',
                status: 'failed',
                message: `Error: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }

    private async testScriptFiles(): Promise<SimpleTestResult> {
        try {
            const scriptsDir = path.join(this.projectRoot, 'assets', 'scripts');
            if (!fs.existsSync(scriptsDir)) {
                return {
                    name: 'Script Files',
                    status: 'failed',
                    message: 'Scripts directory not found'
                };
            }

            const scriptFiles = this.findFiles(scriptsDir, ['.ts', '.js']);
            return {
                name: 'Script Files',
                status: 'passed',
                message: `Found ${scriptFiles.length} script files`
            };
        } catch (error) {
            return {
                name: 'Script Files',
                status: 'failed',
                message: `Error: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }

    private async testAssetFiles(): Promise<SimpleTestResult> {
        try {
            const assetsDir = path.join(this.projectRoot, 'assets');
            const assetFiles = this.findFiles(assetsDir, ['.png', '.jpg', '.prefab', '.scene']);
            
            return {
                name: 'Asset Files',
                status: 'passed',
                message: `Found ${assetFiles.length} asset files`
            };
        } catch (error) {
            return {
                name: 'Asset Files',
                status: 'failed',
                message: `Error: ${error instanceof Error ? error.message : String(error)}`
            };
        }
    }

    private findFiles(dir: string, extensions: string[]): string[] {
        const files: string[] = [];
        
        if (!fs.existsSync(dir)) return files;

        const items = fs.readdirSync(dir);
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                files.push(...this.findFiles(fullPath, extensions));
            } else if (extensions.some(ext => item.endsWith(ext))) {
                files.push(fullPath);
            }
        }
        
        return files;
    }

    generateReport(results: SimpleTestResult[]): string {
        const passed = results.filter(r => r.status === 'passed').length;
        const failed = results.filter(r => r.status === 'failed').length;
        
        let report = `\n🤖 AI测试框架 - 简化版报告\n`;
        report += `==========================================\n`;
        report += `总测试数: ${results.length}\n`;
        report += `通过: ${passed}\n`;
        report += `失败: ${failed}\n`;
        report += `成功率: ${((passed / results.length) * 100).toFixed(1)}%\n\n`;
        
        report += `详细结果:\n`;
        for (const result of results) {
            const icon = result.status === 'passed' ? '✅' : '❌';
            report += `${icon} ${result.name}: ${result.message}\n`;
        }
        
        return report;
    }
}

// 主函数
async function main() {
    console.log('🚀 启动简化版AI测试框架...\n');
    
    const projectRoot = process.argv[2] || '../..';
    const tester = new SimpleAITester(projectRoot);
    
    try {
        const results = await tester.runBasicTests();
        const report = tester.generateReport(results);
        
        console.log(report);
        
        // 保存报告
        const reportPath = path.join(__dirname, 'simple-test-report.txt');
        fs.writeFileSync(reportPath, report);
        console.log(`\n📄 报告已保存到: ${reportPath}`);
        
    } catch (error) {
        console.error('❌ 测试执行失败:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

export { SimpleAITester };
