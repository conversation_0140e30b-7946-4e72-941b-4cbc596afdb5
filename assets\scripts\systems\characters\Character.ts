import { _decorator, Component } from 'cc';
import { SectType, IPlayerStats } from '../../data/GameTypes';

const { ccclass, property } = _decorator;

/**
 * 角色基类
 * 武侠放置游戏的角色系统基础类
 */
@ccclass('Character')
export class Character extends Component {
    @property
    public characterId: string = '';

    @property
    public characterName: string = '';

    @property
    public level: number = 1;

    @property
    public experience: number = 0;

    protected _sect: SectType = SectType.SHAOLIN;
    protected _stats: IPlayerStats = {
        health: 100,
        maxHealth: 100,
        attack: 10,
        defense: 5,
        speed: 10,
        criticalRate: 0.05
    };

    protected onLoad(): void {
        console.log(`👤 角色加载: ${this.characterName}`);
        this.initializeCharacter();
    }

    /**
     * 初始化角色
     */
    protected initializeCharacter(): void {
        console.log(`🎭 初始化角色: ${this.characterName}`);
        this.calculateStats();
    }

    /**
     * 计算角色属性
     */
    protected calculateStats(): void {
        // 基础属性计算
        const baseHealth = 100 + (this.level - 1) * 20;
        const baseAttack = 10 + (this.level - 1) * 3;
        const baseDefense = 5 + (this.level - 1) * 2;
        const baseSpeed = 10 + (this.level - 1) * 1;

        this._stats = {
            health: baseHealth,
            maxHealth: baseHealth,
            attack: baseAttack,
            defense: baseDefense,
            speed: baseSpeed,
            criticalRate: 0.05 + (this.level - 1) * 0.001
        };

        console.log(`📊 角色属性计算完成:`, this._stats);
    }

    /**
     * 获取角色属性
     */
    public getStats(): IPlayerStats {
        return { ...this._stats };
    }

    /**
     * 设置门派
     */
    public setSect(sect: SectType): void {
        this._sect = sect;
        console.log(`🏛️ 角色门派设置为: ${sect}`);
        this.calculateStats(); // 重新计算属性
    }

    /**
     * 获取门派
     */
    public getSect(): SectType {
        return this._sect;
    }

    /**
     * 升级
     */
    public levelUp(): boolean {
        const requiredExp = this.getRequiredExperience();
        if (this.experience >= requiredExp) {
            this.level++;
            this.experience -= requiredExp;
            this.calculateStats();
            console.log(`🆙 角色升级! 当前等级: ${this.level}`);
            return true;
        }
        return false;
    }

    /**
     * 获取升级所需经验
     */
    public getRequiredExperience(): number {
        return this.level * 100;
    }

    /**
     * 增加经验
     */
    public addExperience(exp: number): void {
        this.experience += exp;
        console.log(`✨ 获得经验: ${exp}, 当前经验: ${this.experience}`);
        
        // 检查是否可以升级
        while (this.levelUp()) {
            // 连续升级
        }
    }

    /**
     * 受到伤害
     */
    public takeDamage(damage: number): number {
        const actualDamage = Math.max(1, damage - this._stats.defense);
        this._stats.health = Math.max(0, this._stats.health - actualDamage);
        
        console.log(`💥 角色受到伤害: ${actualDamage}, 剩余生命: ${this._stats.health}`);
        
        if (this._stats.health <= 0) {
            this.onDeath();
        }
        
        return actualDamage;
    }

    /**
     * 治疗
     */
    public heal(amount: number): number {
        const actualHeal = Math.min(amount, this._stats.maxHealth - this._stats.health);
        this._stats.health += actualHeal;
        
        console.log(`💚 角色治疗: ${actualHeal}, 当前生命: ${this._stats.health}`);
        
        return actualHeal;
    }

    /**
     * 角色死亡
     */
    protected onDeath(): void {
        console.log(`💀 角色死亡: ${this.characterName}`);
        // 这里可以处理死亡逻辑
    }

    /**
     * 是否存活
     */
    public isAlive(): boolean {
        return this._stats.health > 0;
    }

    /**
     * 重置生命值
     */
    public resetHealth(): void {
        this._stats.health = this._stats.maxHealth;
        console.log(`🔄 角色生命值重置: ${this._stats.health}`);
    }

    /**
     * 获取角色信息
     */
    public getCharacterInfo(): any {
        return {
            id: this.characterId,
            name: this.characterName,
            level: this.level,
            experience: this.experience,
            sect: this._sect,
            stats: this.getStats(),
            isAlive: this.isAlive()
        };
    }

    protected onDestroy(): void {
        console.log(`👤 角色销毁: ${this.characterName}`);
    }
}
