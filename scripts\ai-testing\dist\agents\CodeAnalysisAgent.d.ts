/**
 * 代码分析代理 - 智能代码分析和理解
 * 分析代码结构、复杂度、依赖关系等
 */
import { CodeAnalysisResult } from '../types';
export interface FunctionAnalysis {
    name: string;
    parameters: Parameter[];
    returnType: string;
    complexity: number;
    isAsync: boolean;
    isExported: boolean;
}
export interface Parameter {
    name: string;
    type: string;
    optional: boolean;
    defaultValue?: any;
}
export declare class CodeAnalysisAgent {
    private analysisCache;
    /**
     * 分析代码文件或目录
     */
    analyzeCode(codePath: string): Promise<CodeAnalysisResult>;
    /**
     * 分析单个文件
     */
    analyzeFile(filePath: string): Promise<CodeAnalysisResult>;
    /**
     * 分析目录
     */
    analyzeDirectory(dirPath: string): Promise<CodeAnalysisResult>;
    /**
     * 提取函数信息
     */
    extractFunctions(content: string, extension: string): FunctionAnalysis[];
    /**
     * 提取代码特征
     */
    private extractCodeFeatures;
    /**
     * 计算代码指标
     */
    private calculateCodeMetrics;
    /**
     * 提取依赖关系
     */
    private extractDependencies;
    /**
     * 确定复杂度等级
     */
    private determineComplexity;
    /**
     * 生成改进建议
     */
    private generateSuggestions;
    /**
     * 计算圈复杂度
     */
    private calculateCyclomaticComplexity;
    /**
     * 计算可维护性指数
     */
    private calculateMaintainabilityIndex;
    /**
     * 计算重复代码百分比
     */
    private calculateDuplicateCode;
    /**
     * 解析函数参数
     */
    private parseParameters;
    /**
     * 计算函数复杂度
     */
    private calculateFunctionComplexity;
    /**
     * 获取目录中的代码文件
     */
    private getCodeFiles;
    /**
     * 聚合多个分析结果
     */
    private aggregateResults;
}
//# sourceMappingURL=CodeAnalysisAgent.d.ts.map