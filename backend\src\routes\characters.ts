import { Router } from 'express';
import { characterController } from '../controllers/CharacterController';
import { validate, ValidationSchemas } from '../middleware/validation';
import { auth } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     CharacterCreate:
 *       type: object
 *       required:
 *         - name
 *         - characterClass
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 20
 *           pattern: '^[\u4e00-\u9fa5a-zA-Z0-9_\s]+$'
 *           description: 角色名称，支持中文、字母、数字、下划线和空格
 *           example: 剑客小李
 *         characterClass:
 *           type: string
 *           enum: [warrior, mage, archer, assassin]
 *           description: 角色职业
 *           example: warrior
 *         gender:
 *           type: string
 *           enum: [male, female]
 *           description: 角色性别
 *           example: male
 * 
 *     CharacterUpdate:
 *       type: object
 *       properties:
 *         name:
 *           type: string
 *           minLength: 2
 *           maxLength: 20
 *           pattern: '^[\u4e00-\u9fa5a-zA-Z0-9_\s]+$'
 *           description: 角色名称
 *           example: 新角色名
 * 
 *     CharacterAttributes:
 *       type: object
 *       properties:
 *         strength:
 *           type: number
 *           description: 力量
 *           example: 15
 *         agility:
 *           type: number
 *           description: 敏捷
 *           example: 12
 *         intelligence:
 *           type: number
 *           description: 智力
 *           example: 8
 *         vitality:
 *           type: number
 *           description: 体力
 *           example: 10
 *         spirit:
 *           type: number
 *           description: 精神
 *           example: 5
 *         damage:
 *           type: number
 *           description: 攻击力
 *           example: 45
 *         maxHp:
 *           type: number
 *           description: 最大生命值
 *           example: 150
 *         maxMp:
 *           type: number
 *           description: 最大法力值
 *           example: 80
 *         currentHp:
 *           type: number
 *           description: 当前生命值
 *           example: 150
 *         currentMp:
 *           type: number
 *           description: 当前法力值
 *           example: 80
 *         def:
 *           type: number
 *           description: 防御力
 *           example: 8
 *         spellResistance:
 *           type: number
 *           description: 法术抗性
 *           example: 8
 * 
 *     CharacterResponse:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: 角色ID
 *           example: 507f1f77bcf86cd799439011
 *         userId:
 *           type: string
 *           description: 用户ID
 *           example: 507f1f77bcf86cd799439012
 *         name:
 *           type: string
 *           description: 角色名称
 *           example: 剑客小李
 *         class:
 *           type: string
 *           enum: [warrior, mage, archer, assassin]
 *           description: 角色职业
 *           example: warrior
 *         type:
 *           type: string
 *           enum: [player, npc, enemy, boss, pet, summon]
 *           description: 实体类型
 *           example: player
 *         level:
 *           type: number
 *           description: 等级
 *           example: 1
 *         experience:
 *           type: number
 *           description: 经验值
 *           example: 0
 *         attributes:
 *           $ref: '#/components/schemas/CharacterAttributes'
 *         skills:
 *           type: array
 *           items:
 *             type: string
 *           description: 技能ID列表
 *           example: []
 *         skillPoints:
 *           type: number
 *           description: 可用技能点
 *           example: 1
 *         equipment:
 *           type: object
 *           description: 装备信息
 *           example: {}
 *         inventory:
 *           type: object
 *           properties:
 *             items:
 *               type: array
 *               description: 背包物品
 *               example: []
 *             maxSlots:
 *               type: number
 *               description: 最大背包槽位
 *               example: 30
 *         location:
 *           type: object
 *           properties:
 *             mapId:
 *               type: string
 *               description: 地图ID
 *               example: starter_area
 *             x:
 *               type: number
 *               description: X坐标
 *               example: 0
 *             y:
 *               type: number
 *               description: Y坐标
 *               example: 0
 *             z:
 *               type: number
 *               description: Z坐标
 *               example: 0
 *         isActive:
 *           type: boolean
 *           description: 是否激活
 *           example: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: 更新时间
 */

/**
 * @swagger
 * /api/v1/characters:
 *   post:
 *     summary: 创建角色
 *     tags: [Characters]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CharacterCreate'
 *     responses:
 *       201:
 *         description: 角色创建成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         character:
 *                           $ref: '#/components/schemas/CharacterResponse'
 *       400:
 *         description: 请求参数错误或角色数量超限
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       409:
 *         description: 角色名已存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/',
  auth.required,
  validate(ValidationSchemas.characterCreate),
  asyncHandler(characterController.createCharacter)
);

/**
 * @swagger
 * /api/v1/characters:
 *   get:
 *     summary: 获取用户角色列表
 *     tags: [Characters]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         items:
 *                           type: array
 *                           items:
 *                             $ref: '#/components/schemas/CharacterResponse'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.get('/',
  auth.required,
  asyncHandler(characterController.getCharacters)
);

/**
 * @swagger
 * /api/v1/characters/{characterId}:
 *   get:
 *     summary: 获取角色详情
 *     tags: [Characters]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         character:
 *                           $ref: '#/components/schemas/CharacterResponse'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get('/:characterId',
  auth.required,
  validate(ValidationSchemas.mongoId),
  asyncHandler(characterController.getCharacter)
);

/**
 * @swagger
 * /api/v1/characters/{characterId}:
 *   put:
 *     summary: 更新角色信息
 *     tags: [Characters]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CharacterUpdate'
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         character:
 *                           $ref: '#/components/schemas/CharacterResponse'
 *       400:
 *         $ref: '#/components/responses/BadRequest'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       409:
 *         description: 角色名已存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.put('/:characterId',
  auth.required,
  validate(ValidationSchemas.mongoId),
  validate(ValidationSchemas.characterUpdate),
  asyncHandler(characterController.updateCharacter)
);

/**
 * @swagger
 * /api/v1/characters/{characterId}:
 *   delete:
 *     summary: 删除角色
 *     tags: [Characters]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *     responses:
 *       200:
 *         description: 删除成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.delete('/:characterId',
  auth.required,
  validate(ValidationSchemas.mongoId),
  asyncHandler(characterController.deleteCharacter)
);

/**
 * @swagger
 * /api/v1/characters/{characterId}/set-current:
 *   post:
 *     summary: 设置当前角色
 *     tags: [Characters]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *     responses:
 *       200:
 *         description: 设置成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         currentCharacterId:
 *                           type: string
 *                           description: 当前角色ID
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色不存在
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post('/:characterId/set-current',
  auth.required,
  validate(ValidationSchemas.mongoId),
  asyncHandler(characterController.setCurrentCharacter)
);

export default router;
