# 代码标准规范

> 📖 **导航**: [返回主页](./README.md) | [项目结构](./project-structure.md) | [组件架构](./component-architecture.md)

## 💻 TypeScript编码规范

### 基础命名约定

#### 类和接口命名
```typescript
// 类名使用PascalCase
class GameManager extends Component { }
class BattleSystem { }
class SectManager { }

// 接口名使用PascalCase，推荐I前缀
interface IPlayerData { }
interface ISkillEffect { }
interface IBattleResult { }

// 类型别名使用PascalCase
type SkillTargetType = 'enemy' | 'ally' | 'self' | 'all_enemies' | 'all_allies';
type DamageType = 'physical' | 'magical' | 'fire' | 'ice' | 'thunder';
type SectType = 'shaolin' | 'wudang' | 'emei' | 'huashan' | 'beggar';

// 枚举使用PascalCase
enum BattleState {
    PREPARING = 'preparing',
    FIGHTING = 'fighting',
    FINISHED = 'finished'
}

```

#### 变量和函数命名
```typescript
// 变量和函数使用camelCase
const playerHealth: number = 100;
const maxManaPoints: number = 50;
const currentSectLevel: number = 1;

function calculateDamage(baseDamage: number, multiplier: number): number { }
function onSkillButtonClick(): void { }
function joinSect(sectId: string, playerId: string): boolean { }

// 常量使用UPPER_SNAKE_CASE
const MAX_SKILL_SLOTS = 5;
const DEFAULT_HEALTH = 100;
const SECT_CONTRIBUTION_LIMIT = 10000;
const OFFLINE_REWARD_MAX_HOURS = 24;

// 私有成员使用下划线前缀
private _playerData: IPlayerData;
private _isInitialized: boolean = false;
private _currentSect: string = '';
private _battleState: BattleState = BattleState.PREPARING;

// 受保护成员使用下划线前缀
protected _componentName: string = '';
protected _debugMode: boolean = false;

// 静态成员使用大写开头
static Instance: GameManager;
static readonly VERSION: string = '1.0.0';
```

## 🏮 武侠游戏特定命名规范

### ID命名规范
```typescript
// 门派ID: sect_门派名
const SECT_SHAOLIN = 'sect_shaolin';
const SECT_WUDANG = 'sect_wudang';
const SECT_EMEI = 'sect_emei';
const SECT_HUASHAN = 'sect_huashan';
const SECT_BEGGAR = 'sect_beggar';
const SECT_DEMON = 'sect_demon';

// 技能ID: skill_门派_技能名
const SKILL_SHAOLIN_LUOHAN_QUAN = 'skill_shaolin_luohan_quan';
const SKILL_WUDANG_TAIJI_JIAN = 'skill_wudang_taiji_jian';
const SKILL_EMEI_JIUYIN_ZHENGONG = 'skill_emei_jiuyin_zhengong';

// 物品ID: item_类型_品质_名称
const ITEM_WEAPON_LEGENDARY_DRAGON_SWORD = 'item_weapon_legendary_dragon_sword';
const ITEM_PILL_RARE_HEALING_DAN = 'item_pill_rare_healing_dan';
const ITEM_MATERIAL_COMMON_IRON_ORE = 'item_material_common_iron_ore';

// 角色ID: character_类型_门派/职业
const CHARACTER_PLAYER_SHAOLIN_MONK = 'character_player_shaolin_monk';
const CHARACTER_NPC_WUDANG_ELDER = 'character_npc_wudang_elder';
const CHARACTER_ENEMY_DEMON_CULTIST = 'character_enemy_demon_cultist';

// 地点ID: location_类型_名称
const LOCATION_SECT_SHAOLIN_TEMPLE = 'location_sect_shaolin_temple';
const LOCATION_CITY_LUOYANG = 'location_city_luoyang';
const LOCATION_DUNGEON_DEMON_CAVE = 'location_dungeon_demon_cave';
```

## 📋 类型定义规范

### 基础数据接口
```typescript
// 玩家数据接口
interface IPlayerData {
    id: string;
    name: string;
    level: number;
    sect: string;
    attributes: IPlayerAttributes;
    skills: IPlayerSkills;
    inventory: IInventoryData;
    cultivation: ICultivationData;
    reputation: IReputationData;
}

// 玩家属性接口
interface IPlayerAttributes {
    health: { current: number; max: number };
    mana: { current: number; max: number };
    attack: number;
    defense: number;
    speed: number;
    luck: number;
    // 武侠特色属性
    internalPower: number;      // 内力
    martialTalent: number;      // 武学天赋
    comprehension: number;      // 悟性
    morality: number;           // 道德值
}

// 技能数据接口
interface ISkillData {
    id: string;
    name: string;
    description: string;
    sect: string;
    level: number;
    maxLevel: number;
    manaCost: number;
    castTime: number;
    cooldown: number;
    damageType: DamageType;
    targetType: SkillTargetType;
    effects: ISkillEffect[];
    requirements: ISkillRequirement[];
}

// 门派数据接口
interface ISectData {
    id: string;
    name: string;
    description: string;
    location: string;
    specialty: string;          // 专长 (如：外功、内功、剑法)
    masterNpc: string;
    entryRequirement: ISectRequirement;
    skills: string[];           // 门派技能列表
    bonuses: ISectBonus;        // 门派加成
    reputation: number;         // 在该门派的声望
    rank: SectRank;            // 在该门派的地位
}
```

### 武侠特色数据结构
```typescript
// 修炼数据接口
interface ICultivationData {
    level: number;              // 修炼等级
    experience: number;         // 修炼经验
    realm: string;              // 境界 (如：练气、筑基、金丹)
    techniques: string[];       // 修炼功法
    breakthroughProgress: number; // 突破进度
}

// 声望数据接口
interface IReputationData {
    jianghu: number;            // 江湖声望
    righteous: number;          // 正道声望
    evil: number;               // 邪道声望
    sects: Record<string, number>; // 各门派声望
}

// 战斗结果接口
interface IBattleResult {
    winner: string;
    loser: string;
    duration: number;
    damageDealt: number;
    experienceGained: number;
    itemsDropped: string[];
    reputationChange: number;
}
```

## 📝 注释和文档规范

### JSDoc注释标准
```typescript
/**
 * 战斗系统管理器
 * 负责处理所有战斗相关的逻辑，包括技能释放、伤害计算、战斗结果等
 * 
 * <AUTHOR>
 * @since 1.0.0
 * @version 1.2.0
 */
@ccclass('BattleSystem')
export class BattleSystem extends Component {
    
    /**
     * 计算技能伤害
     * 
     * @param caster 施法者数据
     * @param target 目标数据
     * @param skill 技能数据
     * @returns 伤害计算结果
     * 
     * @example
     * ```typescript
     * const damage = this.calculateSkillDamage(player, enemy, fireballSkill);
     * console.log(`造成伤害: ${damage.finalDamage}`);
     * ```
     */
    public calculateSkillDamage(
        caster: IPlayerData, 
        target: IPlayerData, 
        skill: ISkillData
    ): IDamageResult {
        // 实现逻辑
    }
    
    /**
     * 加入门派
     * 
     * @param playerId 玩家ID
     * @param sectId 门派ID
     * @returns 是否成功加入
     * 
     * @throws {Error} 当玩家已经加入其他门派时抛出错误
     * 
     * @deprecated 使用 SectSystem.joinSect() 替代
     */
    public joinSect(playerId: string, sectId: string): boolean {
        // 实现逻辑
    }
}
```

### 代码注释规范
```typescript
export class SkillSystem extends Component {
    
    // 单行注释：解释复杂逻辑
    private calculateCriticalHit(baseDamage: number, critRate: number): number {
        // 暴击率计算：基础暴击率 + 装备加成 + 技能加成
        const finalCritRate = Math.min(critRate * 1.2, 0.95); // 最大95%暴击率
        
        // 暴击伤害倍数：1.5倍基础伤害
        return Math.random() < finalCritRate ? baseDamage * 1.5 : baseDamage;
    }
    
    /*
     * 多行注释：解释复杂算法
     * 武功熟练度计算公式：
     * 1. 基础经验 = 使用次数 * 技能等级
     * 2. 天赋加成 = 基础经验 * (天赋值 / 100)
     * 3. 门派加成 = 基础经验 * 门派加成系数
     * 4. 最终经验 = 基础经验 + 天赋加成 + 门派加成
     */
    private calculateSkillExperience(useCount: number, skillLevel: number, talent: number): number {
        const baseExp = useCount * skillLevel;
        const talentBonus = baseExp * (talent / 100);
        const sectBonus = baseExp * this.getSectBonus();
        return baseExp + talentBonus + sectBonus;
    }
}
```

## 🔧 代码格式化规范

### Prettier配置
```json
{
    "printWidth": 100,
    "tabWidth": 4,
    "useTabs": false,
    "semi": true,
    "singleQuote": true,
    "quoteProps": "as-needed",
    "trailingComma": "es5",
    "bracketSpacing": true,
    "bracketSameLine": false,
    "arrowParens": "avoid",
    "endOfLine": "lf"
}
```

### ESLint配置要点
```json
{
    "rules": {
        "@typescript-eslint/naming-convention": [
            "error",
            {
                "selector": "class",
                "format": ["PascalCase"]
            },
            {
                "selector": "interface",
                "format": ["PascalCase"],
                "prefix": ["I"]
            },
            {
                "selector": "variable",
                "format": ["camelCase", "UPPER_CASE"]
            },
            {
                "selector": "function",
                "format": ["camelCase"]
            }
        ],
        "prefer-const": "error",
        "no-var": "error",
        "no-console": "warn"
    }
}
```

---

> 📖 **相关文档**: [组件架构](./component-architecture.md) | [数据管理](./data-management.md) | [开发工作流](./development-workflow.md)
