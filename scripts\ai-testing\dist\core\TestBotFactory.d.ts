/**
 * 测试机器人工厂 - 动态创建和管理测试机器人
 * 根据系统特征自动创建适配的测试机器人
 */
import { GenericTestBot } from './GenericTestBot';
import { SystemType, ComplexityLevel } from '../types';
export declare class TestBotFactory {
    private systemDiscovery;
    private templateRegistry;
    private botCache;
    private systemContextCache;
    constructor();
    /**
     * 动态创建测试机器人
     * 根据系统路径分析系统特征并创建适配的测试机器人
     */
    createTestBotForSystem(systemPath: string): Promise<GenericTestBot>;
    /**
     * 批量创建测试机器人
     * 为整个项目的所有系统创建测试机器人
     */
    createTestBotsForProject(projectPath: string): Promise<Map<string, GenericTestBot>>;
    /**
     * 动态更新测试机器人
     * 根据代码变更更新现有的测试机器人
     */
    updateTestBot(systemPath: string, changes: CodeChange[]): Promise<void>;
    /**
     * 获取系统统计信息
     */
    getSystemStatistics(): SystemStatistics;
    /**
     * 清理缓存
     */
    clearCache(): void;
    /**
     * 分析系统特征
     */
    private analyzeSystem;
    /**
     * 提取系统特征
     */
    private extractSystemFeatures;
    /**
     * 分析系统依赖
     */
    private analyzeDependencies;
    /**
     * 为测试机器人加载测试模板
     */
    private loadTestTemplatesForBot;
    /**
     * 分析代码变更影响
     */
    private analyzeChangeImpact;
    /**
     * 更新测试模板
     */
    private updateTestTemplates;
    private detectAlgorithms;
    private detectAPIs;
    private detectUI;
    private detectDatabase;
    private detectGameLogic;
    private detectRealtime;
}
export interface CodeChange {
    file: string;
    type: 'added' | 'modified' | 'deleted';
    content: string;
}
export interface ChangeImpactAnalysis {
    systemTypeChanged: boolean;
    newSystemType: SystemType;
    requiresNewTemplates: boolean;
    requiresTestRefresh: boolean;
    newRequirements: string[];
}
export interface SystemStatistics {
    totalSystems: number;
    systemTypes: Map<SystemType, number>;
    complexityDistribution: Map<ComplexityLevel, number>;
    averageTestCoverage: number;
}
//# sourceMappingURL=TestBotFactory.d.ts.map