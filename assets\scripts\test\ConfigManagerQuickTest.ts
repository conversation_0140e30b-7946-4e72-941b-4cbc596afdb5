/**
 * ConfigManager快速测试
 * 最简单的配置管理器测试组件
 * 
 * 使用方法：
 * 1. 添加到任意场景节点
 * 2. 运行场景
 * 3. 查看控制台输出
 */

import { _decorator, Component } from 'cc';
import { ConfigManager } from '../managers/ConfigManager';

const { ccclass } = _decorator;

@ccclass('ConfigManagerQuickTest')
export class ConfigManagerQuickTest extends Component {

    protected async onLoad(): Promise<void> {
        console.log('🧪 ========== ConfigManager快速测试 ==========');
        
        try {
            // 获取ConfigManager实例
            console.log('📦 获取ConfigManager实例...');
            const configManager = ConfigManager.getInstance();
            
            // 检查实例状态
            const status = configManager.getStatus();
            console.log('📊 ConfigManager状态:', status);
            
            // 初始化ConfigManager
            console.log('⏳ 开始初始化ConfigManager...');
            await configManager.initialize();
            
            console.log('✅ ConfigManager初始化成功！');
            
            // 测试基本功能
            this.testBasicFunctions(configManager);
            
        } catch (error) {
            console.error('❌ ConfigManager测试失败:', error);
            console.log('💡 可能的原因:');
            console.log('   1. 配置文件不存在');
            console.log('   2. JSON格式错误');
            console.log('   3. 文件路径不正确');
        }
        
        console.log('🧪 ========== 测试完成 ==========');
    }

    /**
     * 测试基本功能
     */
    private testBasicFunctions(configManager: ConfigManager): void {
        console.log('\n🔍 测试基本功能:');
        
        // 获取统计信息
        const stats = configManager.getConfigStats();
        console.log('📊 配置统计:', stats);
        
        // 测试技能查询
        console.log('\n🎯 测试技能查询:');
        const allSkills = configManager.getAllSkillData();
        console.log(`   找到 ${allSkills.length} 个技能`);
        
        if (allSkills.length > 0) {
            const firstSkill = allSkills[0];
            console.log(`   第一个技能: ${firstSkill.name} (${firstSkill.id})`);
            
            // 测试单个技能查询
            const skillById = configManager.getSkillData(firstSkill.id);
            if (skillById) {
                console.log(`   ✅ 单个技能查询成功: ${skillById.name}`);
            } else {
                console.log(`   ❌ 单个技能查询失败`);
            }
        }
        
        // 测试物品查询
        console.log('\n📦 测试物品查询:');
        const allItems = configManager.getAllItemData();
        console.log(`   找到 ${allItems.length} 个物品`);
        
        if (allItems.length > 0) {
            const firstItem = allItems[0];
            console.log(`   第一个物品: ${firstItem.name} (${firstItem.id})`);
        }
        
        // 测试奖励表查询
        console.log('\n🎁 测试奖励表查询:');
        const allRewardTables = configManager.getAllRewardTableData();
        console.log(`   找到 ${allRewardTables.length} 个奖励表`);
        
        if (allRewardTables.length > 0) {
            const firstTable = allRewardTables[0];
            console.log(`   第一个奖励表: ${firstTable.name} (${firstTable.id})`);
        }
        
        // 显示成功信息
        if (stats.skills > 0 || stats.items > 0 || stats.rewards > 0) {
            console.log('\n🎉 ConfigManager基本功能测试通过！');
            console.log('✅ 配置加载正常');
            console.log('✅ 数据查询正常');
            console.log('✅ 统计信息正常');
        } else {
            console.log('\n⚠️ ConfigManager功能有限');
            console.log('💡 可能需要添加配置文件');
        }
    }
}
