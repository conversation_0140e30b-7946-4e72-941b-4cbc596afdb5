import { _decorator, Component, Node } from 'cc';
import { BaseManager } from './BaseManager';
import { EventCallback, AsyncEventCallback, EventListener, EventManagerConfig } from './types/ManagerTypes';

const { ccclass, property } = _decorator;

/**
 * 事件管理器
 * 提供全局事件系统，支持事件注册、移除和触发
 */
@ccclass('EventManager')
export class EventManager extends BaseManager {
    
    /**
     * 事件监听器映射表
     */
    private _eventListeners: Map<string, EventListener[]> = new Map();
    
    /**
     * 事件管理器配置
     */
    private _config: EventManagerConfig = {
        maxListeners: 100,
        enableEventLog: true,
        debug: true,
        autoInit: true
    };
    
    /**
     * 事件触发统计
     */
    private _eventStats: Map<string, { count: number; lastTime: number }> = new Map();

    /**
     * 获取EventManager单例实例
     */
    public static getInstance(): EventManager {
        return super.getInstance.call(this) as EventManager;
    }

    /**
     * 初始化事件管理器
     */
    protected async initializeManager(): Promise<void> {
        console.log('📡 初始化事件管理器...');
        
        try {
            // 清理现有监听器
            this._eventListeners.clear();
            this._eventStats.clear();
            
            // 注册内置事件
            this.registerBuiltinEvents();
            
            console.log('✅ 事件管理器初始化完成');
            
        } catch (error) {
            console.error('❌ 事件管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 销毁事件管理器
     */
    public destroyManager(): void {
        console.log('🗑️ 销毁事件管理器...');
        
        // 移除所有事件监听器
        this.removeAllListeners();
        
        // 清理数据
        this._eventListeners.clear();
        this._eventStats.clear();
        
        console.log('✅ 事件管理器销毁完成');
    }

    /**
     * 注册内置事件
     */
    private registerBuiltinEvents(): void {
        console.log('📡 注册内置事件...');
        
        // 这里可以注册一些内置的系统事件
        // 例如：游戏状态变更、场景切换等
    }

    /**
     * 添加事件监听器
     */
    public on(eventName: string, callback: EventCallback, target?: any): void {
        this.addEventListener(eventName, callback, target, false);
    }

    /**
     * 添加一次性事件监听器
     */
    public once(eventName: string, callback: EventCallback, target?: any): void {
        this.addEventListener(eventName, callback, target, true);
    }

    /**
     * 移除事件监听器
     */
    public off(eventName: string, callback?: EventCallback, target?: any): void {
        this.removeEventListener(eventName, callback, target);
    }

    /**
     * 触发事件
     */
    public emit(eventName: string, ...args: any[]): void {
        this.dispatchEvent(eventName, args);
    }

    /**
     * 异步触发事件
     */
    public async emitAsync(eventName: string, ...args: any[]): Promise<void> {
        await this.dispatchEventAsync(eventName, args);
    }

    /**
     * 添加事件监听器的内部实现
     */
    private addEventListener(eventName: string, callback: EventCallback, target?: any, once: boolean = false): void {
        if (!eventName || typeof callback !== 'function') {
            console.error('❌ 无效的事件名称或回调函数');
            return;
        }

        // 检查监听器数量限制
        const listeners = this._eventListeners.get(eventName) || [];
        if (listeners.length >= (this._config.maxListeners || 100)) {
            console.warn(`⚠️ 事件 ${eventName} 的监听器数量已达到上限`);
            return;
        }

        // 创建监听器对象
        const listener: EventListener = {
            callback,
            target,
            once
        };

        // 添加到监听器列表
        if (!this._eventListeners.has(eventName)) {
            this._eventListeners.set(eventName, []);
        }
        this._eventListeners.get(eventName)!.push(listener);

        if (this._config.enableEventLog) {
            console.log(`📡 添加事件监听器: ${eventName} (${once ? '一次性' : '持续'})`);
        }
    }

    /**
     * 移除事件监听器的内部实现
     */
    private removeEventListener(eventName: string, callback?: EventCallback, target?: any): void {
        const listeners = this._eventListeners.get(eventName);
        if (!listeners) {
            return;
        }

        if (!callback && !target) {
            // 移除所有监听器
            this._eventListeners.delete(eventName);
            if (this._config.enableEventLog) {
                console.log(`📡 移除所有事件监听器: ${eventName}`);
            }
            return;
        }

        // 过滤掉匹配的监听器
        const filteredListeners = listeners.filter(listener => {
            const callbackMatch = !callback || listener.callback === callback;
            const targetMatch = !target || listener.target === target;
            return !(callbackMatch && targetMatch);
        });

        if (filteredListeners.length === 0) {
            this._eventListeners.delete(eventName);
        } else {
            this._eventListeners.set(eventName, filteredListeners);
        }

        if (this._config.enableEventLog) {
            const removedCount = listeners.length - filteredListeners.length;
            console.log(`📡 移除事件监听器: ${eventName} (移除${removedCount}个)`);
        }
    }

    /**
     * 触发事件的内部实现
     */
    private dispatchEvent(eventName: string, args: any[]): void {
        const listeners = this._eventListeners.get(eventName);
        if (!listeners || listeners.length === 0) {
            return;
        }

        // 更新事件统计
        this.updateEventStats(eventName);

        if (this._config.enableEventLog) {
            console.log(`📡 触发事件: ${eventName} (${listeners.length}个监听器)`);
        }

        // 复制监听器列表，避免在执行过程中被修改
        const listenersToExecute = [...listeners];
        const listenersToKeep: EventListener[] = [];

        // 执行监听器回调
        for (const listener of listenersToExecute) {
            try {
                listener.callback.apply(listener.target, args);
                
                // 如果不是一次性监听器，保留它
                if (!listener.once) {
                    listenersToKeep.push(listener);
                }
            } catch (error) {
                console.error(`❌ 事件监听器执行失败: ${eventName}`, error);
                
                // 即使出错，非一次性监听器也要保留
                if (!listener.once) {
                    listenersToKeep.push(listener);
                }
            }
        }

        // 更新监听器列表（移除一次性监听器）
        if (listenersToKeep.length === 0) {
            this._eventListeners.delete(eventName);
        } else {
            this._eventListeners.set(eventName, listenersToKeep);
        }
    }

    /**
     * 异步触发事件的内部实现
     */
    private async dispatchEventAsync(eventName: string, args: any[]): Promise<void> {
        const listeners = this._eventListeners.get(eventName);
        if (!listeners || listeners.length === 0) {
            return;
        }

        // 更新事件统计
        this.updateEventStats(eventName);

        if (this._config.enableEventLog) {
            console.log(`📡 异步触发事件: ${eventName} (${listeners.length}个监听器)`);
        }

        // 复制监听器列表
        const listenersToExecute = [...listeners];
        const listenersToKeep: EventListener[] = [];

        // 并行执行所有监听器
        const promises = listenersToExecute.map(async (listener) => {
            try {
                const result = listener.callback.apply(listener.target, args);
                
                // 如果返回Promise，等待完成
                if (result instanceof Promise) {
                    await result;
                }
                
                // 如果不是一次性监听器，保留它
                if (!listener.once) {
                    listenersToKeep.push(listener);
                }
            } catch (error) {
                console.error(`❌ 异步事件监听器执行失败: ${eventName}`, error);
                
                // 即使出错，非一次性监听器也要保留
                if (!listener.once) {
                    listenersToKeep.push(listener);
                }
            }
        });

        // 等待所有监听器执行完成
        await Promise.all(promises);

        // 更新监听器列表
        if (listenersToKeep.length === 0) {
            this._eventListeners.delete(eventName);
        } else {
            this._eventListeners.set(eventName, listenersToKeep);
        }
    }

    /**
     * 更新事件统计
     */
    private updateEventStats(eventName: string): void {
        const stats = this._eventStats.get(eventName) || { count: 0, lastTime: 0 };
        stats.count++;
        stats.lastTime = Date.now();
        this._eventStats.set(eventName, stats);
    }

    /**
     * 移除所有事件监听器
     */
    public removeAllListeners(): void {
        console.log('🗑️ 移除所有事件监听器');
        this._eventListeners.clear();
    }

    /**
     * 移除指定目标的所有事件监听器
     */
    public removeAllListenersForTarget(target: any): void {
        console.log('🗑️ 移除指定目标的所有事件监听器');
        
        for (const [eventName, listeners] of this._eventListeners) {
            const filteredListeners = listeners.filter(listener => listener.target !== target);
            
            if (filteredListeners.length === 0) {
                this._eventListeners.delete(eventName);
            } else {
                this._eventListeners.set(eventName, filteredListeners);
            }
        }
    }

    /**
     * 检查是否有指定事件的监听器
     */
    public hasListeners(eventName: string): boolean {
        const listeners = this._eventListeners.get(eventName);
        return listeners ? listeners.length > 0 : false;
    }

    /**
     * 获取指定事件的监听器数量
     */
    public getListenerCount(eventName: string): number {
        const listeners = this._eventListeners.get(eventName);
        return listeners ? listeners.length : 0;
    }

    /**
     * 获取所有事件名称
     */
    public getEventNames(): string[] {
        return Array.from(this._eventListeners.keys());
    }

    /**
     * 获取事件统计信息
     */
    public getEventStats(): Map<string, { count: number; lastTime: number }> {
        return new Map(this._eventStats);
    }

    /**
     * 清理事件统计
     */
    public clearEventStats(): void {
        this._eventStats.clear();
        console.log('🗑️ 事件统计已清理');
    }

    /**
     * 获取事件管理器配置
     */
    public getConfig(): EventManagerConfig {
        return { ...this._config };
    }

    /**
     * 更新事件管理器配置
     */
    public updateConfig(config: Partial<EventManagerConfig>): void {
        this._config = { ...this._config, ...config };
        console.log('⚙️ 事件管理器配置已更新:', config);
    }

    /**
     * 获取事件管理器状态信息
     */
    public getStatus(): {
        totalEvents: number;
        totalListeners: number;
        eventStats: Map<string, { count: number; lastTime: number }>;
    } {
        let totalListeners = 0;
        for (const listeners of this._eventListeners.values()) {
            totalListeners += listeners.length;
        }

        return {
            totalEvents: this._eventListeners.size,
            totalListeners,
            eventStats: this.getEventStats()
        };
    }
}
