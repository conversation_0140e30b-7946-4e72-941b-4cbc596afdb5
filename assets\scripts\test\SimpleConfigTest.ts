/**
 * 简单配置测试组件
 * 可以直接添加到任意场景节点上进行配置系统测试
 * 
 * 使用方法：
 * 1. 在Cocos Creator中打开任意场景（推荐Battle场景）
 * 2. 选择Canvas节点
 * 3. 在属性检查器中点击"添加组件" -> "自定义脚本" -> "SimpleConfigTest"
 * 4. 运行场景，查看控制台输出
 * 5. 使用键盘快捷键进行交互测试
 */

import { _decorator, Component, input, Input, EventKeyboard, KeyCode } from 'cc';
import { ConfigManager } from '../managers/ConfigManager';

const { ccclass } = _decorator;

@ccclass('SimpleConfigTest')
export class SimpleConfigTest extends Component {
    private _configManager: ConfigManager | null = null;
    private _isInitialized: boolean = false;

    protected onLoad(): void {
        console.log('🧪 ========== 简单配置测试启动 ==========');
        console.log('📋 组件已加载，开始初始化配置管理器...');
        
        // 注册键盘事件
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        
        // 初始化配置管理器
        this.initializeConfigManager();
    }

    protected onDestroy(): void {
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🧪 简单配置测试组件已销毁');
    }

    /**
     * 初始化配置管理器
     */
    private async initializeConfigManager(): Promise<void> {
        try {
            console.log('🔄 正在初始化ConfigManager...');
            
            this._configManager = ConfigManager.getInstance();
            await this._configManager.initialize();
            
            this._isInitialized = true;
            console.log('✅ ConfigManager初始化成功！');
            
            // 自动运行基础测试
            this.runBasicTests();
            this.showInstructions();
            
        } catch (error) {
            console.error('❌ ConfigManager初始化失败:', error);
            console.log('💡 提示：请确保配置文件存在于 assets/resources/config/ 目录下');
        }
    }

    /**
     * 运行基础测试
     */
    private runBasicTests(): void {
        if (!this._configManager || !this._isInitialized) {
            console.log('⚠️ ConfigManager未初始化，跳过基础测试');
            return;
        }

        console.log('🔍 ========== 基础配置测试 ==========');

        // 测试配置统计
        const stats = this._configManager.getConfigStats();
        console.log('📊 配置统计信息:');
        console.log(`   - 技能数量: ${stats.skills}`);
        console.log(`   - 实体数量: ${stats.entities}`);
        console.log(`   - 物品数量: ${stats.items}`);
        console.log(`   - 任务数量: ${stats.quests}`);
        console.log(`   - 奖励表数量: ${stats.rewards || 0}`);
        console.log(`   - 已加载配置: ${stats.loadedConfigs.join(', ')}`);

        // 测试技能查询
        console.log('\n🎯 技能配置测试:');
        const testSkills = ['basic_attack', 'fireball', 'heal'];
        for (const skillId of testSkills) {
            const skill = this._configManager.getSkillData(skillId);
            if (skill) {
                console.log(`✅ ${skill.name}: 消耗${skill.manaCost}MP, 冷却${skill.cooldown}s`);
            } else {
                console.log(`❌ 未找到技能: ${skillId}`);
            }
        }

        // 测试物品查询
        console.log('\n📦 物品配置测试:');
        const testItems = ['hp_potion_small', 'fireball_scroll', 'iron_sword'];
        for (const itemId of testItems) {
            const item = this._configManager.getItemData(itemId);
            if (item) {
                console.log(`✅ ${item.name}: ${item.type}类型, 价值${item.value}`);
                if (item.useEffects && item.useEffects.length > 0) {
                    console.log(`   效果: ${item.useEffects[0].type}`);
                }
            } else {
                console.log(`❌ 未找到物品: ${itemId}`);
            }
        }

        // 测试奖励表查询
        console.log('\n🎁 奖励表配置测试:');
        const testRewardTables = ['bronze_chest_rewards', 'daily_login_rewards'];
        for (const tableId of testRewardTables) {
            const rewardTable = this._configManager.getRewardTableData(tableId);
            if (rewardTable) {
                console.log(`✅ ${rewardTable.name}: ${rewardTable.rewards.length}个奖励项`);
            } else {
                console.log(`❌ 未找到奖励表: ${tableId}`);
            }
        }

        console.log('🔍 ========== 基础测试完成 ==========\n');
    }

    /**
     * 显示操作说明
     */
    private showInstructions(): void {
        console.log('⌨️ ========== 键盘操作说明 ==========');
        console.log('按键功能:');
        console.log('   1 - 显示所有技能');
        console.log('   2 - 显示所有物品');
        console.log('   3 - 显示所有奖励表');
        console.log('   4 - 重新加载配置');
        console.log('   5 - 性能测试');
        console.log('   H - 显示帮助');
        console.log('⌨️ ===================================\n');
    }

    /**
     * 键盘事件处理
     */
    private onKeyDown(event: EventKeyboard): void {
        if (!this._configManager || !this._isInitialized) {
            console.log('⚠️ ConfigManager未初始化');
            return;
        }

        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.showAllSkills();
                break;
            case KeyCode.DIGIT_2:
                this.showAllItems();
                break;
            case KeyCode.DIGIT_3:
                this.showAllRewardTables();
                break;
            case KeyCode.DIGIT_4:
                this.reloadConfigs();
                break;
            case KeyCode.DIGIT_5:
                this.performanceTest();
                break;
            case KeyCode.KEY_H:
                this.showInstructions();
                break;
        }
    }

    /**
     * 显示所有技能
     */
    private showAllSkills(): void {
        console.log('🎯 ========== 所有技能列表 ==========');
        const skills = this._configManager!.getAllSkillData();
        
        if (skills.length === 0) {
            console.log('⚠️ 没有找到任何技能数据');
            return;
        }

        console.log(`📊 总共 ${skills.length} 个技能:`);
        skills.forEach((skill, index) => {
            console.log(`${index + 1}. ${skill.name} (${skill.id})`);
            console.log(`   消耗: ${skill.manaCost}MP | 冷却: ${skill.cooldown}s | 类型: ${skill.damageType}`);
        });
        console.log('🎯 ===================================\n');
    }

    /**
     * 显示所有物品
     */
    private showAllItems(): void {
        console.log('📦 ========== 所有物品列表 ==========');
        const items = this._configManager!.getAllItemData();
        
        if (items.length === 0) {
            console.log('⚠️ 没有找到任何物品数据');
            return;
        }

        console.log(`📊 总共 ${items.length} 个物品:`);
        items.forEach((item, index) => {
            console.log(`${index + 1}. ${item.name} (${item.id})`);
            console.log(`   类型: ${item.type} | 稀有度: ${item.rarity} | 价值: ${item.value}`);
            if (item.useEffects && item.useEffects.length > 0) {
                console.log(`   效果: ${item.useEffects.map(e => e.type).join(', ')}`);
            }
        });
        console.log('📦 ===================================\n');
    }

    /**
     * 显示所有奖励表
     */
    private showAllRewardTables(): void {
        console.log('🎁 ========== 所有奖励表列表 ==========');
        const rewardTables = this._configManager!.getAllRewardTableData();
        
        if (rewardTables.length === 0) {
            console.log('⚠️ 没有找到任何奖励表数据');
            return;
        }

        console.log(`📊 总共 ${rewardTables.length} 个奖励表:`);
        rewardTables.forEach((table, index) => {
            console.log(`${index + 1}. ${table.name} (${table.id})`);
            console.log(`   类型: ${table.type} | 奖励项: ${table.rewards.length}个`);
            console.log(`   奖励范围: ${table.minRewards}-${table.maxRewards}个`);
        });
        console.log('🎁 ===================================\n');
    }

    /**
     * 重新加载配置
     */
    private async reloadConfigs(): Promise<void> {
        console.log('🔄 ========== 重新加载配置 ==========');
        
        try {
            const startTime = Date.now();
            await this._configManager!.reloadAllConfigs();
            const reloadTime = Date.now() - startTime;
            
            console.log('✅ 配置重新加载成功');
            console.log(`⏱️ 重新加载时间: ${reloadTime}ms`);
            
            // 重新运行基础测试
            this.runBasicTests();
            
        } catch (error) {
            console.error('❌ 配置重新加载失败:', error);
        }
        
        console.log('🔄 ===================================\n');
    }

    /**
     * 性能测试
     */
    private performanceTest(): void {
        console.log('⚡ ========== 性能测试 ==========');
        
        const iterations = 1000;
        
        // 测试技能查询性能
        console.log(`🔄 执行 ${iterations} 次技能查询...`);
        const startTime1 = Date.now();
        for (let i = 0; i < iterations; i++) {
            this._configManager!.getSkillData('fireball');
        }
        const queryTime = Date.now() - startTime1;
        console.log(`⏱️ 技能查询性能: ${queryTime}ms (${(queryTime / iterations).toFixed(3)}ms/次)`);

        // 测试物品查询性能
        console.log(`🔄 执行 ${iterations} 次物品查询...`);
        const startTime2 = Date.now();
        for (let i = 0; i < iterations; i++) {
            this._configManager!.getItemData('hp_potion_small');
        }
        const itemQueryTime = Date.now() - startTime2;
        console.log(`⏱️ 物品查询性能: ${itemQueryTime}ms (${(itemQueryTime / iterations).toFixed(3)}ms/次)`);

        // 测试批量查询性能
        console.log(`🔄 执行 ${iterations / 10} 次批量查询...`);
        const startTime3 = Date.now();
        for (let i = 0; i < iterations / 10; i++) {
            this._configManager!.getAllSkillData();
        }
        const batchTime = Date.now() - startTime3;
        console.log(`⏱️ 批量查询性能: ${batchTime}ms (${(batchTime / (iterations / 10)).toFixed(3)}ms/次)`);
        
        console.log('⚡ ===========================\n');
    }
}
