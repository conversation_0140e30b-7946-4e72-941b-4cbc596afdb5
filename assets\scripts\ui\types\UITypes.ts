/**
 * UI系统类型定义
 * 定义UI系统中使用的所有接口、枚举和类型
 */

import { Node, Prefab } from 'cc';

/**
 * UI面板类型枚举
 */
export enum UIPanelType {
    // 主要游戏面板
    MainMenu = 'main_menu',
    GameHUD = 'game_hud',
    Inventory = 'inventory',
    Equipment = 'equipment',
    Skills = 'skills',
    Quests = 'quests',
    Shop = 'shop',
    
    // 对话框和弹窗
    Dialog = 'dialog',
    Confirmation = 'confirmation',
    Settings = 'settings',
    Loading = 'loading',
    
    // 战斗相关
    BattleHUD = 'battle_hud',
    SkillBar = 'skill_bar',
    TargetInfo = 'target_info',
    
    // 系统面板
    SystemMenu = 'system_menu',
    Debug = 'debug'
}

/**
 * UI层级枚举
 */
export enum UILayer {
    Background = 0,     // 背景层
    Normal = 100,       // 普通UI层
    Popup = 200,        // 弹窗层
    Dialog = 300,       // 对话框层
    Loading = 400,      // 加载层
    System = 500,       // 系统层
    Debug = 600         // 调试层
}

/**
 * UI面板状态枚举
 */
export enum UIPanelState {
    Hidden = 'hidden',
    Showing = 'showing',
    Visible = 'visible',
    Hiding = 'hiding',
    Destroyed = 'destroyed'
}

/**
 * UI动画类型枚举
 */
export enum UIAnimationType {
    None = 'none',
    Fade = 'fade',
    Scale = 'scale',
    Slide = 'slide',
    Custom = 'custom'
}

/**
 * UI面板配置接口
 */
export interface IUIPanelConfig {
    /** 面板类型 */
    type: UIPanelType;
    
    /** 预制体路径 */
    prefabPath: string;
    
    /** UI层级 */
    layer: UILayer;
    
    /** 是否为单例 */
    singleton: boolean;
    
    /** 是否缓存 */
    cache: boolean;
    
    /** 显示动画 */
    showAnimation?: UIAnimationType;
    
    /** 隐藏动画 */
    hideAnimation?: UIAnimationType;
    
    /** 动画持续时间 */
    animationDuration?: number;
    
    /** 是否模态 */
    modal?: boolean;
    
    /** 是否可以通过ESC关闭 */
    escapeToClose?: boolean;
    
    /** 初始化数据 */
    initData?: any;
}

/**
 * UI面板实例接口
 */
export interface IUIPanelInstance {
    /** 面板配置 */
    config: IUIPanelConfig;
    
    /** 面板节点 */
    node: Node;
    
    /** 面板组件 */
    component: IUIPanel;
    
    /** 当前状态 */
    state: UIPanelState;
    
    /** 创建时间 */
    createTime: number;
    
    /** 显示时间 */
    showTime?: number;
    
    /** 隐藏时间 */
    hideTime?: number;
}

/**
 * UI面板基础接口
 */
export interface IUIPanel {
    /** 面板类型 */
    readonly panelType: UIPanelType;
    
    /** 面板配置 */
    readonly config: IUIPanelConfig;
    
    /** 当前状态 */
    readonly state: UIPanelState;
    
    /** 初始化面板 */
    initialize(data?: any): Promise<void>;
    
    /** 显示面板 */
    show(data?: any): Promise<void>;
    
    /** 隐藏面板 */
    hide(): Promise<void>;
    
    /** 销毁面板 */
    destroy(): void;
    
    /** 刷新面板 */
    refresh(data?: any): void;
    
    /** 处理返回键 */
    onBackPressed?(): boolean;
    
    /** 处理ESC键 */
    onEscapePressed?(): boolean;
}

/**
 * UI管理器接口
 */
export interface IUIManager {
    /** 初始化UI管理器 */
    initialize(): Promise<void>;
    
    /** 注册面板配置 */
    registerPanel(config: IUIPanelConfig): void;
    
    /** 显示面板 */
    showPanel(type: UIPanelType, data?: any): Promise<IUIPanelInstance>;
    
    /** 隐藏面板 */
    hidePanel(type: UIPanelType): Promise<void>;
    
    /** 切换面板 */
    togglePanel(type: UIPanelType, data?: any): Promise<void>;
    
    /** 获取面板实例 */
    getPanel(type: UIPanelType): IUIPanelInstance | null;
    
    /** 检查面板是否显示 */
    isPanelVisible(type: UIPanelType): boolean;
    
    /** 隐藏所有面板 */
    hideAllPanels(): Promise<void>;
    
    /** 销毁面板 */
    destroyPanel(type: UIPanelType): void;
    
    /** 清理缓存 */
    clearCache(): void;
}

/**
 * UI事件类型枚举
 */
export enum UIEventType {
    PanelShow = 'panel_show',
    PanelHide = 'panel_hide',
    PanelDestroy = 'panel_destroy',
    PanelClick = 'panel_click',
    ButtonClick = 'button_click',
    InputChange = 'input_change',
    SliderChange = 'slider_change',
    ToggleChange = 'toggle_change'
}

/**
 * UI事件数据接口
 */
export interface IUIEventData {
    /** 事件类型 */
    type: UIEventType;
    
    /** 面板类型 */
    panelType?: UIPanelType;
    
    /** 事件源 */
    source?: any;
    
    /** 事件数据 */
    data?: any;
    
    /** 时间戳 */
    timestamp: number;
}

/**
 * UI按钮配置接口
 */
export interface IUIButtonConfig {
    /** 按钮ID */
    id: string;
    
    /** 按钮文本 */
    text?: string;
    
    /** 按钮图标 */
    icon?: string;
    
    /** 点击回调 */
    onClick?: () => void;
    
    /** 是否启用 */
    enabled?: boolean;
    
    /** 是否可见 */
    visible?: boolean;
    
    /** 快捷键 */
    hotkey?: string;
}

/**
 * UI输入框配置接口
 */
export interface IUIInputConfig {
    /** 输入框ID */
    id: string;
    
    /** 占位符文本 */
    placeholder?: string;
    
    /** 默认值 */
    defaultValue?: string;
    
    /** 最大长度 */
    maxLength?: number;
    
    /** 输入类型 */
    inputType?: 'text' | 'password' | 'number' | 'email';
    
    /** 变化回调 */
    onChange?: (value: string) => void;
    
    /** 是否只读 */
    readonly?: boolean;
}

/**
 * UI滑块配置接口
 */
export interface IUISliderConfig {
    /** 滑块ID */
    id: string;
    
    /** 最小值 */
    min: number;
    
    /** 最大值 */
    max: number;
    
    /** 当前值 */
    value: number;
    
    /** 步长 */
    step?: number;
    
    /** 变化回调 */
    onChange?: (value: number) => void;
}

/**
 * UI布局配置接口
 */
export interface IUILayoutConfig {
    /** 布局类型 */
    type: 'horizontal' | 'vertical' | 'grid';
    
    /** 间距 */
    spacing?: number;
    
    /** 内边距 */
    padding?: {
        top: number;
        right: number;
        bottom: number;
        left: number;
    };
    
    /** 对齐方式 */
    alignment?: 'start' | 'center' | 'end';
    
    /** 是否自适应 */
    autoResize?: boolean;
}

/**
 * UI主题配置接口
 */
export interface IUIThemeConfig {
    /** 主题名称 */
    name: string;
    
    /** 主色调 */
    primaryColor: string;
    
    /** 次色调 */
    secondaryColor: string;
    
    /** 背景色 */
    backgroundColor: string;
    
    /** 文本色 */
    textColor: string;
    
    /** 字体大小 */
    fontSize: {
        small: number;
        normal: number;
        large: number;
        title: number;
    };
    
    /** 圆角半径 */
    borderRadius: number;
    
    /** 阴影配置 */
    shadow: {
        color: string;
        blur: number;
        offset: { x: number; y: number };
    };
}
