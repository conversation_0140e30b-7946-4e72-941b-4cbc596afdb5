/**
 * 快速配置验证脚本
 * 用于验证配置文件是否正确加载
 * 
 * 使用方法：
 * 1. 将此组件添加到任意场景节点
 * 2. 运行场景
 * 3. 查看控制台输出验证结果
 */

import { _decorator, Component } from 'cc';
import { ConfigManager } from '../managers/ConfigManager';

const { ccclass } = _decorator;

@ccclass('QuickConfigVerify')
export class QuickConfigVerify extends Component {

    protected async onLoad(): Promise<void> {
        console.log('🔍 ========== 快速配置验证 ==========');
        
        try {
            // 获取ConfigManager实例
            const configManager = ConfigManager.getInstance();
            
            console.log('⏳ 正在初始化ConfigManager...');
            await configManager.initialize();
            
            console.log('✅ ConfigManager初始化成功！');
            
            // 验证配置数据
            this.verifyConfigurations(configManager);
            
        } catch (error) {
            console.error('❌ 配置验证失败:', error);
            this.showTroubleshootingTips();
        }
        
        console.log('🔍 ========== 验证完成 ==========');
    }

    /**
     * 验证配置数据
     */
    private verifyConfigurations(configManager: ConfigManager): void {
        console.log('\n📊 配置数据验证:');
        
        // 获取统计信息
        const stats = configManager.getConfigStats();
        
        console.log(`📈 配置统计:`);
        console.log(`   - 技能数量: ${stats.skills}`);
        console.log(`   - 实体数量: ${stats.entities}`);
        console.log(`   - 物品数量: ${stats.items}`);
        console.log(`   - 任务数量: ${stats.quests}`);
        console.log(`   - 奖励表数量: ${stats.rewards || 0}`);
        console.log(`   - 已加载配置: ${stats.loadedConfigs.join(', ')}`);
        
        // 验证技能数据
        this.verifySkillData(configManager);
        
        // 验证物品数据
        this.verifyItemData(configManager);
        
        // 验证奖励表数据
        this.verifyRewardData(configManager);
        
        // 显示总结
        this.showSummary(stats);
    }

    /**
     * 验证技能数据
     */
    private verifySkillData(configManager: ConfigManager): void {
        console.log('\n🎯 技能数据验证:');
        
        const allSkills = configManager.getAllSkillData();
        
        if (allSkills.length === 0) {
            console.log('⚠️ 没有找到技能数据');
            return;
        }
        
        console.log(`✅ 找到 ${allSkills.length} 个技能`);
        
        // 验证前几个技能
        const sampleSkills = allSkills.slice(0, 3);
        sampleSkills.forEach(skill => {
            console.log(`   - ${skill.name}: ${skill.description}`);
        });
        
        // 测试特定技能查询
        const testSkill = configManager.getSkillData('fireball');
        if (testSkill) {
            console.log(`✅ 技能查询测试成功: ${testSkill.name}`);
        } else {
            console.log('⚠️ 技能查询测试失败: 未找到fireball技能');
        }
    }

    /**
     * 验证物品数据
     */
    private verifyItemData(configManager: ConfigManager): void {
        console.log('\n📦 物品数据验证:');
        
        const allItems = configManager.getAllItemData();
        
        if (allItems.length === 0) {
            console.log('⚠️ 没有找到物品数据');
            return;
        }
        
        console.log(`✅ 找到 ${allItems.length} 个物品`);
        
        // 验证前几个物品
        const sampleItems = allItems.slice(0, 3);
        sampleItems.forEach(item => {
            console.log(`   - ${item.name}: ${item.type}类型, 价值${item.value}`);
        });
        
        // 测试特定物品查询
        const testItem = configManager.getItemData('hp_potion_small');
        if (testItem) {
            console.log(`✅ 物品查询测试成功: ${testItem.name}`);
            
            // 验证使用效果
            if (testItem.useEffects && testItem.useEffects.length > 0) {
                console.log(`   使用效果: ${testItem.useEffects[0].type}`);
            }
        } else {
            console.log('⚠️ 物品查询测试失败: 未找到hp_potion_small物品');
        }
    }

    /**
     * 验证奖励表数据
     */
    private verifyRewardData(configManager: ConfigManager): void {
        console.log('\n🎁 奖励表数据验证:');
        
        const allRewardTables = configManager.getAllRewardTableData();
        
        if (allRewardTables.length === 0) {
            console.log('⚠️ 没有找到奖励表数据');
            return;
        }
        
        console.log(`✅ 找到 ${allRewardTables.length} 个奖励表`);
        
        // 验证前几个奖励表
        const sampleTables = allRewardTables.slice(0, 2);
        sampleTables.forEach(table => {
            console.log(`   - ${table.name}: ${table.rewards.length}个奖励项`);
        });
        
        // 测试特定奖励表查询
        const testTable = configManager.getRewardTableData('bronze_chest_rewards');
        if (testTable) {
            console.log(`✅ 奖励表查询测试成功: ${testTable.name}`);
            console.log(`   奖励类型: ${testTable.type}, 奖励范围: ${testTable.minRewards}-${testTable.maxRewards}`);
        } else {
            console.log('⚠️ 奖励表查询测试失败: 未找到bronze_chest_rewards');
        }
    }

    /**
     * 显示验证总结
     */
    private showSummary(stats: any): void {
        console.log('\n📋 验证总结:');
        
        const totalConfigs = stats.loadedConfigs.length;
        const hasData = stats.skills > 0 || stats.items > 0 || stats.rewards > 0;
        
        if (totalConfigs > 0 && hasData) {
            console.log('🎉 配置系统验证成功！');
            console.log('✅ 所有核心功能正常工作');
            console.log('✅ 数据加载和查询功能正常');
            console.log('✅ 可以开始使用配置系统');
            
            console.log('\n🎮 下一步操作:');
            console.log('1. 添加SimpleConfigTest组件进行交互测试');
            console.log('2. 使用键盘快捷键测试各项功能');
            console.log('3. 查看详细的配置数据');
            
        } else {
            console.log('⚠️ 配置系统存在问题');
            console.log('❌ 部分配置文件可能缺失或格式错误');
            this.showTroubleshootingTips();
        }
    }

    /**
     * 显示故障排除提示
     */
    private showTroubleshootingTips(): void {
        console.log('\n🛠️ 故障排除提示:');
        console.log('1. 检查配置文件是否存在:');
        console.log('   - assets/resources/config/skills.json');
        console.log('   - assets/resources/config/items.json');
        console.log('   - assets/resources/config/rewards.json');
        console.log('2. 检查JSON格式是否正确');
        console.log('3. 确保文件路径正确');
        console.log('4. 重新编译项目 (Ctrl+R)');
        console.log('5. 查看详细错误信息');
    }
}
