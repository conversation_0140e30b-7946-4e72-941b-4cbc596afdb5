/**
 * 技能栏UI组件 - 挂机游戏专用
 * 显示技能槽、技能图标、冷却时间等，支持自动释放技能
 */

import { _decorator, Node, Prefab, instantiate, Layout, Label } from 'cc';
import { BaseUIComponent, ComponentState, IComponentData } from '../base/BaseUIComponent';
import { IPlayerSkill } from '../panels/SkillPanel';
import { EventManager } from '../../managers/EventManager';

const { ccclass, property } = _decorator;

/**
 * 技能栏数据接口
 */
export interface ISkillBarData extends IComponentData {
    /** 装备的技能列表 */
    equippedSkills: IPlayerSkill[];
    
    /** 技能栏槽位数量 */
    slotCount: number;
    
    /** 是否启用自动释放 */
    autoUseEnabled: boolean;
    
    /** 总释放次数 */
    totalUseCount: number;
    
    /** 当前DPS */
    currentDPS: number;
    
    /** 技能栏配置 */
    config: ISkillBarConfig;
}

/**
 * 技能栏配置接口
 */
export interface ISkillBarConfig {
    /** 是否显示技能名称 */
    showSkillNames: boolean;
    
    /** 是否显示冷却时间 */
    showCooldown: boolean;
    
    /** 是否显示使用次数 */
    showUseCount: boolean;
    
    /** 是否显示DPS统计 */
    showDPS: boolean;
    
    /** 技能栏布局方向 */
    horizontal: boolean;
    
    /** 槽位间距 */
    slotSpacing: number;
}

/**
 * 技能栏事件类型
 */
export enum SkillBarEventType {
    SkillAutoUsed = 'skill_auto_used',
    SkillEquipped = 'skill_equipped',
    SkillUnequipped = 'skill_unequipped',
    SkillClicked = 'skill_clicked',
    ConfigChanged = 'config_changed'
}

@ccclass('SkillBarUI')
export class SkillBarUI extends BaseUIComponent {
    
    @property({ type: Prefab, tooltip: '技能槽预制体' })
    public skillSlotPrefab: Prefab | null = null;
    
    @property({ type: Node, tooltip: '技能槽容器' })
    public slotContainer: Node | null = null;
    
    @property({ type: Label, tooltip: 'DPS显示标签' })
    public dpsLabel: Label | null = null;
    
    @property({ type: Label, tooltip: '总使用次数标签' })
    public totalUseLabel: Label | null = null;
    
    @property({ tooltip: '默认槽位数量' })
    public defaultSlotCount: number = 6;
    
    @property({ tooltip: '是否显示技能名称' })
    public showSkillNames: boolean = true;
    
    @property({ tooltip: '是否显示冷却时间' })
    public showCooldown: boolean = true;
    
    @property({ tooltip: '是否显示DPS统计' })
    public showDPS: boolean = true;
    
    @property({ tooltip: '技能栏水平布局' })
    public horizontalLayout: boolean = true;
    
    // 技能槽节点列表
    private _skillSlots: Node[] = [];
    
    // 技能栏数据
    private _skillBarData: ISkillBarData = {
        equippedSkills: [],
        slotCount: 6,
        autoUseEnabled: true,
        totalUseCount: 0,
        currentDPS: 0,
        config: {
            showSkillNames: true,
            showCooldown: true,
            showUseCount: false,
            showDPS: true,
            horizontal: true,
            slotSpacing: 10
        }
    };
    
    // 自动释放定时器
    private _autoUseTimer: number = 0;
    
    // DPS计算相关
    private _damageHistory: { damage: number; timestamp: number }[] = [];
    private _dpsUpdateTimer: number = 0;

    protected onComponentLoad(): void {
        console.log('⚔️ 技能栏UI组件加载');
        
        // 查找组件
        this.findUIComponents();
        
        // 初始化技能栏数据
        this.initializeSkillBarData();
        
        // 创建技能槽
        this.createSkillSlots();
    }

    protected onComponentEnable(): void {
        // 启动自动释放系统
        this.startAutoUseSystem();
        
        // 启动DPS计算
        this.startDPSCalculation();
        
        console.log('⚔️ 技能栏UI组件启用');
    }

    protected onComponentDisable(): void {
        // 停止自动释放系统
        this.stopAutoUseSystem();
        
        // 停止DPS计算
        this.stopDPSCalculation();
        
        console.log('⚔️ 技能栏UI组件禁用');
    }

    protected bindEvents(): void {
        const eventManager = EventManager.getInstance();
        
        // 监听技能相关事件
        eventManager.on('skill_learned', this.onSkillLearned, this);
        eventManager.on('skill_upgraded', this.onSkillUpgraded, this);
        eventManager.on('skill_equipped', this.onSkillEquipped, this);
        eventManager.on('skill_unequipped', this.onSkillUnequipped, this);
        
        // 监听游戏状态事件
        eventManager.on('game_pause', this.onGamePause, this);
        eventManager.on('game_resume', this.onGameResume, this);
    }

    protected unbindEvents(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.off('skill_learned', this.onSkillLearned, this);
        eventManager.off('skill_upgraded', this.onSkillUpgraded, this);
        eventManager.off('skill_equipped', this.onSkillEquipped, this);
        eventManager.off('skill_unequipped', this.onSkillUnequipped, this);
        eventManager.off('game_pause', this.onGamePause, this);
        eventManager.off('game_resume', this.onGameResume, this);
    }

    protected onUpdate(): void {
        // 更新技能冷却显示
        this.updateSkillCooldowns();
        
        // 检查自动释放
        this.checkAutoUseSkills();
    }

    protected onDataChanged(newData: IComponentData, oldData: IComponentData): void {
        const skillBarData = newData as ISkillBarData;
        
        if (skillBarData.equippedSkills) {
            this.updateSkillSlots(skillBarData.equippedSkills);
        }
        
        if (skillBarData.config) {
            this.applyConfig(skillBarData.config);
        }
        
        if (skillBarData.currentDPS !== undefined) {
            this.updateDPSDisplay(skillBarData.currentDPS);
        }
        
        if (skillBarData.totalUseCount !== undefined) {
            this.updateTotalUseDisplay(skillBarData.totalUseCount);
        }
    }

    /**
     * 查找UI组件
     */
    private findUIComponents(): void {
        // 查找技能槽容器
        if (!this.slotContainer) {
            this.slotContainer = this.node.getChildByName('SlotContainer');
            if (!this.slotContainer) {
                console.warn('未找到技能槽容器，使用根节点');
                this.slotContainer = this.node;
            }
        }
        
        // 查找DPS标签
        if (!this.dpsLabel) {
            const dpsNode = this.node.getChildByName('DPSLabel');
            if (dpsNode) {
                this.dpsLabel = dpsNode.getComponent(Label);
            }
        }
        
        // 查找总使用次数标签
        if (!this.totalUseLabel) {
            const totalUseNode = this.node.getChildByName('TotalUseLabel');
            if (totalUseNode) {
                this.totalUseLabel = totalUseNode.getComponent(Label);
            }
        }
    }

    /**
     * 初始化技能栏数据
     */
    private initializeSkillBarData(): void {
        this._skillBarData = {
            equippedSkills: [],
            slotCount: this.defaultSlotCount,
            autoUseEnabled: true,
            totalUseCount: 0,
            currentDPS: 0,
            config: {
                showSkillNames: this.showSkillNames,
                showCooldown: this.showCooldown,
                showUseCount: false,
                showDPS: this.showDPS,
                horizontal: this.horizontalLayout,
                slotSpacing: 10
            }
        };
        
        this.setComponentData(this._skillBarData);
    }

    /**
     * 创建技能槽
     */
    private createSkillSlots(): void {
        if (!this.skillSlotPrefab || !this.slotContainer) {
            console.warn('缺少技能槽预制体或容器');
            return;
        }
        
        // 清除现有槽位
        this.slotContainer.removeAllChildren();
        this._skillSlots = [];
        
        // 设置布局
        this.setupSlotLayout();
        
        // 创建槽位
        for (let i = 0; i < this._skillBarData.slotCount; i++) {
            const slotNode = instantiate(this.skillSlotPrefab);
            slotNode.name = `SkillSlot_${i}`;
            slotNode.setParent(this.slotContainer);
            
            // 绑定槽位事件
            this.bindSlotEvents(slotNode, i);
            
            this._skillSlots.push(slotNode);
        }
        
        console.log(`⚔️ 创建了 ${this._skillBarData.slotCount} 个技能槽`);
    }

    /**
     * 设置槽位布局
     */
    private setupSlotLayout(): void {
        if (!this.slotContainer) return;
        
        let layout = this.slotContainer.getComponent(Layout);
        if (!layout) {
            layout = this.slotContainer.addComponent(Layout);
        }
        
        if (this._skillBarData.config.horizontal) {
            layout.type = Layout.Type.HORIZONTAL;
        } else {
            layout.type = Layout.Type.VERTICAL;
        }
        
        layout.spacingX = this._skillBarData.config.slotSpacing;
        layout.spacingY = this._skillBarData.config.slotSpacing;
    }

    /**
     * 绑定槽位事件
     */
    private bindSlotEvents(slotNode: Node, index: number): void {
        slotNode.on(Node.EventType.TOUCH_END, () => {
            this.onSlotClicked(index);
        });
        
        // 长按事件（查看技能详情）
        let pressTimer: number = 0;
        
        slotNode.on(Node.EventType.TOUCH_START, () => {
            pressTimer = setTimeout(() => {
                this.onSlotLongPress(index);
            }, 500);
        });
        
        slotNode.on(Node.EventType.TOUCH_END, () => {
            if (pressTimer) {
                clearTimeout(pressTimer);
                pressTimer = 0;
            }
        });
        
        slotNode.on(Node.EventType.TOUCH_CANCEL, () => {
            if (pressTimer) {
                clearTimeout(pressTimer);
                pressTimer = 0;
            }
        });
    }

    /**
     * 更新技能槽
     */
    private updateSkillSlots(equippedSkills: IPlayerSkill[]): void {
        for (let i = 0; i < this._skillSlots.length; i++) {
            const slot = this._skillSlots[i];
            const skill = equippedSkills[i] || null;
            
            this.updateSlotDisplay(slot, skill, i);
        }
    }

    /**
     * 更新槽位显示
     */
    private updateSlotDisplay(slotNode: Node, skill: IPlayerSkill | null, slotIndex: number): void {
        // 获取槽位组件（这里需要SkillSlot组件）
        const skillSlot = slotNode.getComponent('SkillSlot');
        if (skillSlot) {
            // 调用SkillSlot的更新方法
            skillSlot.updateSkill(skill);
        } else {
            // 简单的显示更新
            this.updateSlotSimpleDisplay(slotNode, skill, slotIndex);
        }
    }

    /**
     * 简单的槽位显示更新
     */
    private updateSlotSimpleDisplay(slotNode: Node, skill: IPlayerSkill | null, slotIndex: number): void {
        // 更新技能图标
        const iconNode = slotNode.getChildByName('Icon');
        if (iconNode) {
            iconNode.active = skill !== null;
            // 这里需要设置技能图标
        }
        
        // 更新技能名称
        if (this._skillBarData.config.showSkillNames) {
            const nameLabel = slotNode.getChildByName('NameLabel')?.getComponent(Label);
            if (nameLabel) {
                nameLabel.string = skill ? skill.skillData.name : '';
            }
        }
        
        // 更新冷却显示
        if (this._skillBarData.config.showCooldown) {
            const cooldownNode = slotNode.getChildByName('Cooldown');
            if (cooldownNode) {
                cooldownNode.active = skill !== null;
            }
        }
        
        // 设置槽位可用性
        slotNode.getComponent('Button')?.setEnabled(skill !== null);
    }

    /**
     * 更新技能冷却显示
     */
    private updateSkillCooldowns(): void {
        const currentTime = Date.now();
        
        for (let i = 0; i < this._skillSlots.length; i++) {
            const skill = this._skillBarData.equippedSkills[i];
            if (!skill) continue;
            
            const slot = this._skillSlots[i];
            const cooldownNode = slot.getChildByName('Cooldown');
            
            if (cooldownNode && this._skillBarData.config.showCooldown) {
                const remainingTime = Math.max(0, skill.lastUseTime + (skill.skillData.cooldown * 1000) - currentTime);
                
                if (remainingTime > 0) {
                    cooldownNode.active = true;
                    const cooldownLabel = cooldownNode.getComponent(Label);
                    if (cooldownLabel) {
                        cooldownLabel.string = (remainingTime / 1000).toFixed(1);
                    }
                } else {
                    cooldownNode.active = false;
                }
            }
        }
    }

    /**
     * 检查自动释放技能
     */
    private checkAutoUseSkills(): void {
        if (!this._skillBarData.autoUseEnabled) return;
        
        const currentTime = Date.now();
        
        for (const skill of this._skillBarData.equippedSkills) {
            if (this.canAutoUseSkill(skill, currentTime)) {
                this.autoUseSkill(skill, currentTime);
            }
        }
    }

    /**
     * 检查是否可以自动释放技能
     */
    private canAutoUseSkill(skill: IPlayerSkill, currentTime: number): boolean {
        if (!skill.unlocked) return false;
        
        const cooldownTime = skill.lastUseTime + (skill.skillData.cooldown * 1000);
        return currentTime >= cooldownTime;
    }

    /**
     * 自动释放技能
     */
    private autoUseSkill(skill: IPlayerSkill, currentTime: number): void {
        console.log(`⚔️ 自动释放技能: ${skill.skillData.name}`);
        
        // 更新技能使用时间和次数
        skill.lastUseTime = currentTime;
        skill.useCount++;
        
        // 计算伤害
        const damage = this.calculateSkillDamage(skill);
        
        // 记录伤害用于DPS计算
        this.recordDamage(damage, currentTime);
        
        // 更新总使用次数
        this._skillBarData.totalUseCount++;
        
        // 发送技能使用事件
        this.emitComponentEvent(SkillBarEventType.SkillAutoUsed, {
            skill,
            damage,
            timestamp: currentTime
        });
        
        // 更新显示
        this.setDataValue('totalUseCount', this._skillBarData.totalUseCount);
    }

    /**
     * 计算技能伤害
     */
    private calculateSkillDamage(skill: IPlayerSkill): number {
        const baseDamage = skill.skillData.damage || 100;
        const levelMultiplier = 1 + (skill.level - 1) * 0.2;
        return Math.floor(baseDamage * levelMultiplier * (0.8 + Math.random() * 0.4));
    }

    /**
     * 记录伤害用于DPS计算
     */
    private recordDamage(damage: number, timestamp: number): void {
        this._damageHistory.push({ damage, timestamp });
        
        // 清理1秒前的记录
        const oneSecondAgo = timestamp - 1000;
        this._damageHistory = this._damageHistory.filter(record => record.timestamp > oneSecondAgo);
    }

    /**
     * 启动自动释放系统
     */
    private startAutoUseSystem(): void {
        if (this._autoUseTimer) return;
        
        this._autoUseTimer = setInterval(() => {
            this.checkAutoUseSkills();
        }, 100); // 每100ms检查一次
    }

    /**
     * 停止自动释放系统
     */
    private stopAutoUseSystem(): void {
        if (this._autoUseTimer) {
            clearInterval(this._autoUseTimer);
            this._autoUseTimer = 0;
        }
    }

    /**
     * 启动DPS计算
     */
    private startDPSCalculation(): void {
        if (this._dpsUpdateTimer) return;
        
        this._dpsUpdateTimer = setInterval(() => {
            this.updateDPS();
        }, 1000); // 每秒更新DPS
    }

    /**
     * 停止DPS计算
     */
    private stopDPSCalculation(): void {
        if (this._dpsUpdateTimer) {
            clearInterval(this._dpsUpdateTimer);
            this._dpsUpdateTimer = 0;
        }
    }

    /**
     * 更新DPS
     */
    private updateDPS(): void {
        const currentTime = Date.now();
        const oneSecondAgo = currentTime - 1000;
        
        // 计算过去1秒的总伤害
        const recentDamage = this._damageHistory
            .filter(record => record.timestamp > oneSecondAgo)
            .reduce((total, record) => total + record.damage, 0);
        
        this._skillBarData.currentDPS = recentDamage;
        this.setDataValue('currentDPS', recentDamage);
    }

    /**
     * 更新DPS显示
     */
    private updateDPSDisplay(dps: number): void {
        if (this.dpsLabel && this._skillBarData.config.showDPS) {
            this.dpsLabel.string = `DPS: ${dps}`;
        }
    }

    /**
     * 更新总使用次数显示
     */
    private updateTotalUseDisplay(totalUse: number): void {
        if (this.totalUseLabel) {
            this.totalUseLabel.string = `总释放: ${totalUse}`;
        }
    }

    /**
     * 应用配置
     */
    private applyConfig(config: ISkillBarConfig): void {
        this._skillBarData.config = { ...this._skillBarData.config, ...config };
        
        // 重新设置布局
        this.setupSlotLayout();
        
        // 更新所有槽位显示
        this.updateSkillSlots(this._skillBarData.equippedSkills);
        
        // 更新统计显示
        this.updateDPSDisplay(this._skillBarData.currentDPS);
        this.updateTotalUseDisplay(this._skillBarData.totalUseCount);
    }

    /**
     * 槽位点击事件
     */
    private onSlotClicked(slotIndex: number): void {
        const skill = this._skillBarData.equippedSkills[slotIndex];
        
        this.emitComponentEvent(SkillBarEventType.SkillClicked, {
            slotIndex,
            skill
        });
        
        console.log(`⚔️ 点击技能槽 ${slotIndex}:`, skill?.skillData.name || '空');
    }

    /**
     * 槽位长按事件
     */
    private onSlotLongPress(slotIndex: number): void {
        const skill = this._skillBarData.equippedSkills[slotIndex];
        
        if (skill) {
            // 显示技能详情
            this.emitComponentEvent('skill_detail_request', {
                skill,
                slotIndex
            });
            
            console.log(`⚔️ 长按查看技能详情: ${skill.skillData.name}`);
        }
    }

    // ==================== 事件处理 ====================

    private onSkillLearned(eventData: any): void {
        console.log('⚔️ 技能学会事件:', eventData);
    }

    private onSkillUpgraded(eventData: any): void {
        console.log('⚔️ 技能升级事件:', eventData);
        this.refreshComponent();
    }

    private onSkillEquipped(eventData: any): void {
        console.log('⚔️ 技能装备事件:', eventData);
        this.refreshComponent();
    }

    private onSkillUnequipped(eventData: any): void {
        console.log('⚔️ 技能卸下事件:', eventData);
        this.refreshComponent();
    }

    private onGamePause(): void {
        this.stopAutoUseSystem();
        this.stopDPSCalculation();
    }

    private onGameResume(): void {
        this.startAutoUseSystem();
        this.startDPSCalculation();
    }

    // ==================== 公共API ====================

    /**
     * 装备技能到指定槽位
     */
    public equipSkill(skill: IPlayerSkill, slotIndex?: number): boolean {
        if (slotIndex !== undefined) {
            if (slotIndex < 0 || slotIndex >= this._skillBarData.slotCount) {
                return false;
            }
            this._skillBarData.equippedSkills[slotIndex] = skill;
        } else {
            // 查找空闲槽位
            const emptyIndex = this._skillBarData.equippedSkills.findIndex(s => !s);
            if (emptyIndex === -1) {
                return false;
            }
            this._skillBarData.equippedSkills[emptyIndex] = skill;
        }
        
        this.setDataValue('equippedSkills', this._skillBarData.equippedSkills);
        this.emitComponentEvent(SkillBarEventType.SkillEquipped, { skill, slotIndex });
        
        return true;
    }

    /**
     * 卸下指定槽位的技能
     */
    public unequipSkill(slotIndex: number): boolean {
        if (slotIndex < 0 || slotIndex >= this._skillBarData.slotCount) {
            return false;
        }
        
        const skill = this._skillBarData.equippedSkills[slotIndex];
        if (!skill) {
            return false;
        }
        
        this._skillBarData.equippedSkills[slotIndex] = null as any;
        this.setDataValue('equippedSkills', this._skillBarData.equippedSkills);
        this.emitComponentEvent(SkillBarEventType.SkillUnequipped, { skill, slotIndex });
        
        return true;
    }

    /**
     * 设置自动释放状态
     */
    public setAutoUseEnabled(enabled: boolean): void {
        this._skillBarData.autoUseEnabled = enabled;
        this.setDataValue('autoUseEnabled', enabled);
        
        if (enabled) {
            this.startAutoUseSystem();
        } else {
            this.stopAutoUseSystem();
        }
    }

    /**
     * 获取技能栏统计信息
     */
    public getSkillBarStats(): any {
        return {
            slotCount: this._skillBarData.slotCount,
            equippedCount: this._skillBarData.equippedSkills.filter(s => s).length,
            totalUseCount: this._skillBarData.totalUseCount,
            currentDPS: this._skillBarData.currentDPS,
            autoUseEnabled: this._skillBarData.autoUseEnabled
        };
    }
}
