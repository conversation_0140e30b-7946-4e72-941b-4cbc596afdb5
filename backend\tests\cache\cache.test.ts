import { CacheManager } from '../../src/utils/cache';
import { CacheStrategyManager } from '../../src/config/cacheStrategies';
import { CacheMonitor } from '../../src/utils/cacheMonitor';

describe('Redis缓存系统测试', () => {
  let cacheManager: CacheManager;
  let strategyManager: CacheStrategyManager;
  let cacheMonitor: CacheMonitor;

  beforeAll(async () => {
    // 设置测试环境
    process.env['USE_REAL_REDIS'] = 'false'; // 使用内存Redis
    process.env['CACHE_PREFIX'] = 'test:';

    cacheManager = CacheManager.getInstance();
    strategyManager = CacheStrategyManager.getInstance();
    cacheMonitor = CacheMonitor.getInstance();
  });

  afterAll(async () => {
    try {
      // 清理测试数据
      await cacheManager.flushall();
      cacheMonitor.stopMonitoring();
    } catch (error) {
      console.warn('清理缓存测试数据失败');
    }
  });

  beforeEach(async () => {
    // 每个测试前清理缓存
    try {
      await cacheManager.flushall();
      cacheManager.resetStats();
    } catch (error) {
      // 忽略清理错误
    }
  });

  describe('缓存管理器测试', () => {
    it('应该设置和获取缓存值', async () => {
      const key = 'test_key';
      const value = { name: '测试数据', value: 123 };

      await cacheManager.set(key, value, { ttl: 60 });
      const result = await cacheManager.get(key);

      expect(result).toEqual(value);
    });

    it('应该处理缓存过期', async () => {
      const key = 'expire_test';
      const value = 'expire_value';

      await cacheManager.set(key, value, { ttl: 1 });
      
      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      const result = await cacheManager.get(key);
      expect(result).toBeNull();
    });

    it('应该支持哈希操作', async () => {
      const key = 'hash_test';
      const field = 'field1';
      const value = { data: 'hash_value' };

      await cacheManager.hset(key, field, value);
      const result = await cacheManager.hget(key, field);

      expect(result).toEqual(value);
    });

    it('应该支持列表操作', async () => {
      const key = 'list_test';
      const values = ['item1', 'item2', 'item3'];

      await cacheManager.rpush(key, values);
      const result = await cacheManager.lrange(key, 0, -1);

      expect(result).toEqual(values);
    });

    it('应该支持集合操作', async () => {
      const key = 'set_test';
      const members = ['member1', 'member2', 'member3'];

      await cacheManager.sadd(key, members);
      const result = await cacheManager.smembers(key);

      expect(result.sort()).toEqual(members.sort());
    });

    it('应该支持批量操作', async () => {
      const keyValues = {
        'batch1': 'value1',
        'batch2': 'value2',
        'batch3': 'value3',
      };

      await cacheManager.mset(keyValues);
      const results = await cacheManager.mget(Object.keys(keyValues));

      expect(results).toEqual(Object.values(keyValues));
    });

    it('应该支持分布式锁', async () => {
      const lockKey = 'lock_test';
      
      const lockValue1 = await cacheManager.lock(lockKey, 5);
      expect(lockValue1).not.toBeNull();

      const lockValue2 = await cacheManager.lock(lockKey, 5);
      expect(lockValue2).toBeNull(); // 应该获取锁失败

      const unlocked = await cacheManager.unlock(lockKey, lockValue1!);
      expect(unlocked).toBe(true);
    });

    it('应该支持缓存穿透保护', async () => {
      const key = 'null_cache_test';
      let fetchCount = 0;

      const fetcher = async () => {
        fetchCount++;
        return null; // 模拟数据不存在
      };

      // 第一次调用
      const result1 = await cacheManager.getWithNullCache(key, fetcher);
      expect(result1).toBeNull();
      expect(fetchCount).toBe(1);

      // 第二次调用，应该从缓存获取空值
      const result2 = await cacheManager.getWithNullCache(key, fetcher);
      expect(result2).toBeNull();
      expect(fetchCount).toBe(1); // 不应该再次调用fetcher
    });

    it('应该获取缓存统计信息', async () => {
      // 执行一些缓存操作
      await cacheManager.set('stats_test1', 'value1');
      await cacheManager.get('stats_test1'); // 命中
      await cacheManager.get('stats_test2'); // 未命中

      const stats = cacheManager.getStats();
      
      expect(stats.sets).toBeGreaterThan(0);
      expect(stats.hits).toBeGreaterThan(0);
      expect(stats.misses).toBeGreaterThan(0);
      expect(stats.totalOperations).toBeGreaterThan(0);
    });

    it('应该通过健康检查', async () => {
      const isHealthy = await cacheManager.healthCheck();
      expect(isHealthy).toBe(true);
    });
  });

  describe('缓存策略管理器测试', () => {
    it('应该获取预定义的缓存策略', async () => {
      const userStrategy = strategyManager.getStrategy('user');
      expect(userStrategy).not.toBeNull();
      expect(userStrategy?.prefix).toBe('user:');
      expect(userStrategy?.ttl).toBeGreaterThan(0);
    });

    it('应该根据策略设置和获取缓存', async () => {
      const key = 'strategy_test';
      const value = { userId: 123, name: '测试用户' };

      await strategyManager.set('user', key, value);
      const result = await strategyManager.get('user', key);

      expect(result).toEqual(value);
    });

    it('应该支持Cache Aside模式', async () => {
      const key = 'cache_aside_test';
      const value = { data: 'cache_aside_value' };
      let fetchCount = 0;

      const fetcher = async () => {
        fetchCount++;
        return value;
      };

      // 第一次获取，应该调用fetcher
      const result1 = await strategyManager.get('user', key, fetcher);
      expect(result1).toEqual(value);
      expect(fetchCount).toBe(1);

      // 第二次获取，应该从缓存获取
      const result2 = await strategyManager.get('user', key, fetcher);
      expect(result2).toEqual(value);
      expect(fetchCount).toBe(1); // 不应该再次调用fetcher
    });

    it('应该支持批量操作', async () => {
      const keyValues = {
        'batch_user1': { id: 1, name: 'User1' },
        'batch_user2': { id: 2, name: 'User2' },
      };

      await strategyManager.mset('user', keyValues);
      const results = await strategyManager.mget('user', Object.keys(keyValues));

      expect(results[0]).toEqual(keyValues['batch_user1']);
      expect(results[1]).toEqual(keyValues['batch_user2']);
    });

    it('应该删除缓存', async () => {
      const key = 'delete_test';
      const value = 'delete_value';

      await strategyManager.set('user', key, value);
      let result = await strategyManager.get('user', key);
      expect(result).toBe(value);

      await strategyManager.del('user', key);
      result = await strategyManager.get('user', key);
      expect(result).toBeNull();
    });

    it('应该获取所有策略配置', async () => {
      const strategies = strategyManager.getAllStrategies();
      
      expect(strategies).toHaveProperty('user');
      expect(strategies).toHaveProperty('session');
      expect(strategies).toHaveProperty('gameConfig');
      expect(Object.keys(strategies).length).toBeGreaterThan(5);
    });

    it('应该获取缓存统计信息', async () => {
      // 设置一些测试数据
      await strategyManager.set('user', 'stats_user1', { id: 1 });
      await strategyManager.set('session', 'stats_session1', { token: 'abc' });

      const stats = await strategyManager.getStats();
      
      expect(stats).toHaveProperty('overall');
      expect(stats).toHaveProperty('strategies');
      expect(stats.strategies).toHaveProperty('user');
      expect(stats.strategies).toHaveProperty('session');
    });
  });

  describe('缓存监控器测试', () => {
    it('应该启动和停止监控', async () => {
      expect(cacheMonitor.getMonitoringStatus().isMonitoring).toBe(false);

      cacheMonitor.startMonitoring();
      expect(cacheMonitor.getMonitoringStatus().isMonitoring).toBe(true);

      cacheMonitor.stopMonitoring();
      expect(cacheMonitor.getMonitoringStatus().isMonitoring).toBe(false);
    });

    it('应该更新告警配置', async () => {
      const newConfig = {
        hitRateThreshold: 90,
        errorRateThreshold: 2,
        enabled: true,
      };

      cacheMonitor.updateAlertConfig(newConfig);
      const status = cacheMonitor.getMonitoringStatus();
      
      expect(status.alertConfig.hitRateThreshold).toBe(90);
      expect(status.alertConfig.errorRateThreshold).toBe(2);
      expect(status.alertConfig.enabled).toBe(true);
    });

    it('应该获取监控状态', async () => {
      const status = cacheMonitor.getMonitoringStatus();
      
      expect(status).toHaveProperty('isMonitoring');
      expect(status).toHaveProperty('totalMetrics');
      expect(status).toHaveProperty('recentAlerts');
      expect(status).toHaveProperty('alertConfig');
    });

    it('应该通过健康检查', async () => {
      const isHealthy = await cacheMonitor.healthCheck();
      // 由于监控未启动，健康检查可能失败，这是正常的
      expect(typeof isHealthy).toBe('boolean');
    });

    it('应该获取历史指标', async () => {
      const metrics = cacheMonitor.getHistoricalMetrics(1); // 最近1小时
      expect(Array.isArray(metrics)).toBe(true);
    });

    it('应该获取告警历史', async () => {
      const alerts = cacheMonitor.getAlerts(1); // 最近1小时
      expect(Array.isArray(alerts)).toBe(true);
    });
  });

  describe('集成测试', () => {
    it('应该完整的缓存流程工作正常', async () => {
      // 启动监控
      cacheMonitor.startMonitoring();

      // 使用策略管理器设置缓存
      const userData = { id: 1, name: '集成测试用户', email: '<EMAIL>' };
      await strategyManager.set('user', 'integration_test', userData);

      // 获取缓存
      const result = await strategyManager.get('user', 'integration_test');
      expect(result).toEqual(userData);

      // 检查统计信息
      const stats = cacheManager.getStats();
      expect(stats.totalOperations).toBeGreaterThan(0);

      // 停止监控
      cacheMonitor.stopMonitoring();
    });
  });
});
