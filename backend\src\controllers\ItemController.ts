import { Request, Response, NextFunction } from 'express';
import { Item, UserItem, IItem, IUserItem } from '../models/Item';
import { Skill, UserSkill } from '../models/Skill';
import { Character } from '../models/Character';
import { AppError, ErrorCodes } from '../utils/errors';
import { Logger } from '../utils/logger';
import { CacheManager } from '../utils/cache';
import { sendResponse, parsePaginationParams, calculatePagination, sendPaginatedResponse } from '../utils/response';

/**
 * 物品控制器
 */
export class ItemController {
  private cacheManager: CacheManager;

  constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  /**
   * 获取物品配置列表
   */
  public getItems = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page, limit } = parsePaginationParams(req.query);
      const { type, rarity } = req.query;

      // 构建查询条件
      const filter: any = { isActive: true };
      if (type) filter.type = type;
      if (rarity) filter.rarity = rarity;

      // 查询物品配置
      const items = await Item.find(filter)
        .sort({ rarity: 1, type: 1, name: 1 })
        .skip((page - 1) * limit)
        .limit(limit);

      // 获取总数
      const total = await Item.countDocuments(filter);
      const pagination = calculatePagination(page, limit, total);

      sendPaginatedResponse(res, '获取物品列表成功', items, pagination);
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取物品详情
   */
  public getItem = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { itemId } = req.params;

      const item = await Item.findOne({ id: itemId, isActive: true });
      if (!item) {
        throw new AppError('物品不存在', 404, ErrorCodes.NOT_FOUND);
      }

      sendResponse(res, 200, '获取物品详情成功', { item });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取用户背包
   */
  public getInventory = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId } = req.params;

      // 验证角色是否属于用户
      const character = await Character.findOne({
        _id: characterId,
        userId,
        isActive: true,
      });

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 获取背包物品
      const inventoryItems = await UserItem.find({ userId })
        .populate('itemId', 'name description type rarity iconPath')
        .sort({ slot: 1 });

      sendResponse(res, 200, '获取背包成功', {
        inventory: {
          items: inventoryItems,
          maxSlots: character.inventory.maxSlots,
        },
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 添加物品到背包
   */
  public addItemToInventory = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId } = req.params;
      const { itemId, quantity = 1 } = req.body;

      // 验证角色是否属于用户
      const character = await Character.findOne({
        _id: characterId,
        userId,
        isActive: true,
      });

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 验证物品是否存在
      const itemConfig = await Item.findOne({ id: itemId, isActive: true });
      if (!itemConfig) {
        throw new AppError('物品不存在', 404, ErrorCodes.NOT_FOUND);
      }

      // 检查是否可堆叠
      if (itemConfig.stackable) {
        // 查找现有的同类物品
        const existingItem = await UserItem.findOne({
          userId,
          itemId,
          quantity: { $lt: itemConfig.maxStack },
        });

        if (existingItem) {
          // 更新现有物品数量
          const newQuantity = Math.min(
            existingItem.quantity + quantity,
            itemConfig.maxStack
          );
          existingItem.quantity = newQuantity;
          await existingItem.save();

          Logger.info('物品添加到背包（堆叠）', {
            userId,
            characterId,
            itemId,
            quantity: newQuantity - existingItem.quantity,
            totalQuantity: newQuantity,
          });

          return sendResponse(res, 200, '物品添加成功', {
            item: existingItem,
          });
        }
      }

      // 查找空闲背包槽位
      const usedSlots = await UserItem.find({ userId }).distinct('slot');
      let availableSlot = -1;
      
      for (let i = 0; i < character.inventory.maxSlots; i++) {
        if (!usedSlots.includes(i)) {
          availableSlot = i;
          break;
        }
      }

      if (availableSlot === -1) {
        throw new AppError('背包已满', 400, ErrorCodes.INSUFFICIENT_RESOURCES);
      }

      // 创建新的背包物品
      const userItem = new UserItem({
        userId,
        itemId,
        quantity: Math.min(quantity, itemConfig.maxStack),
        slot: availableSlot,
        equipped: false,
      });

      await userItem.save();

      Logger.info('物品添加到背包', {
        userId,
        characterId,
        itemId,
        quantity: userItem.quantity,
        slot: availableSlot,
      });

      sendResponse(res, 201, '物品添加成功', {
        item: userItem,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 使用物品
   */
  public useItem = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId, instanceId } = req.params;

      // 验证角色是否属于用户
      const character = await Character.findOne({
        _id: characterId,
        userId,
        isActive: true,
      });

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 获取用户物品
      const userItem = await UserItem.findOne({
        instanceId,
        userId,
      });

      if (!userItem) {
        throw new AppError('物品不存在', 404, ErrorCodes.NOT_FOUND);
      }

      // 获取物品配置
      const itemConfig = await Item.findOne({ id: userItem.itemId });
      if (!itemConfig || !itemConfig.usable) {
        throw new AppError('物品不可使用', 400, ErrorCodes.INVALID_ACTION);
      }

      // 使用物品
      await userItem.use();

      // 应用物品效果（这里简化处理）
      if (itemConfig.useEffects) {
        for (const effect of itemConfig.useEffects) {
          await this.applyItemEffect(character, effect);
        }
      }

      await character.save();

      Logger.info('物品使用成功', {
        userId,
        characterId,
        itemId: userItem.itemId,
        instanceId,
      });

      sendResponse(res, 200, '物品使用成功', {
        character: character.toObject(),
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 装备物品
   */
  public equipItem = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId, instanceId } = req.params;

      // 验证角色是否属于用户
      const character = await Character.findOne({
        _id: characterId,
        userId,
        isActive: true,
      });

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 获取用户物品
      const userItem = await UserItem.findOne({
        instanceId,
        userId,
      });

      if (!userItem) {
        throw new AppError('物品不存在', 404, ErrorCodes.NOT_FOUND);
      }

      // 获取物品配置
      const itemConfig = await Item.findOne({ id: userItem.itemId });
      if (!itemConfig || !itemConfig.equipable || !itemConfig.equipSlot) {
        throw new AppError('物品不可装备', 400, ErrorCodes.INVALID_ACTION);
      }

      // 检查装备要求
      if (!this.checkEquipRequirements(character, itemConfig)) {
        throw new AppError('不满足装备要求', 400, ErrorCodes.INVALID_ACTION);
      }

      // 卸下当前装备的同类型物品
      const currentEquippedSlot = itemConfig.equipSlot;
      if (character.equipment[currentEquippedSlot]) {
        const currentEquipped = await UserItem.findOne({
          instanceId: character.equipment[currentEquippedSlot],
          userId,
        });
        
        if (currentEquipped) {
          currentEquipped.equipped = false;
          await currentEquipped.save();
        }
      }

      // 装备新物品
      character.equipment[currentEquippedSlot] = userItem._id;
      userItem.equipped = true;

      await Promise.all([character.save(), userItem.save()]);

      Logger.info('物品装备成功', {
        userId,
        characterId,
        itemId: userItem.itemId,
        instanceId,
        equipSlot: currentEquippedSlot,
      });

      sendResponse(res, 200, '装备成功', {
        character: character.toObject(),
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 卸下装备
   */
  public unequipItem = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId, equipSlot } = req.params;

      // 验证角色是否属于用户
      const character = await Character.findOne({
        _id: characterId,
        userId,
        isActive: true,
      });

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 检查装备槽位是否有装备
      if (!character.equipment[equipSlot]) {
        throw new AppError('该槽位没有装备', 400, ErrorCodes.INVALID_ACTION);
      }

      // 获取装备的物品
      const equippedItem = await UserItem.findOne({
        instanceId: character.equipment[equipSlot],
        userId,
      });

      if (!equippedItem) {
        throw new AppError('装备不存在', 404, ErrorCodes.NOT_FOUND);
      }

      // 卸下装备
      character.equipment[equipSlot] = undefined;
      equippedItem.equipped = false;

      await Promise.all([character.save(), equippedItem.save()]);

      Logger.info('装备卸下成功', {
        userId,
        characterId,
        itemId: equippedItem.itemId,
        equipSlot,
      });

      sendResponse(res, 200, '卸下装备成功', {
        character: character.toObject(),
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 应用物品效果
   */
  private async applyItemEffect(character: any, effect: any): Promise<void> {
    switch (effect.type) {
      case 'heal':
        character.heal(effect.value);
        break;
      case 'restore_mana':
        character.restoreMana(effect.value);
        break;
      case 'buff':
        // 添加增益效果
        character.battleStatus.statusEffects.push({
          effectId: effect.type,
          duration: effect.duration || 0,
          startTime: new Date(),
        });
        break;
      default:
        Logger.warn('未知的物品效果类型', { effectType: effect.type });
    }
  }

  /**
   * 检查装备要求
   */
  private checkEquipRequirements(character: any, itemConfig: IItem): boolean {
    const requirements = itemConfig.requirements;
    
    // 检查等级要求
    if (character.level < requirements.level) {
      return false;
    }

    // 检查职业要求
    if (requirements.class && requirements.class.length > 0) {
      if (!requirements.class.includes(character.class)) {
        return false;
      }
    }

    // 检查属性要求
    if (requirements.attributes) {
      const attrs = character.attributes;
      if (requirements.attributes.strength && attrs.strength < requirements.attributes.strength) return false;
      if (requirements.attributes.agility && attrs.agility < requirements.attributes.agility) return false;
      if (requirements.attributes.intelligence && attrs.intelligence < requirements.attributes.intelligence) return false;
      if (requirements.attributes.vitality && attrs.vitality < requirements.attributes.vitality) return false;
      if (requirements.attributes.spirit && attrs.spirit < requirements.attributes.spirit) return false;
    }

    return true;
  }
}

export const itemController = new ItemController();

/**
 * 技能控制器
 */
export class SkillController {
  private cacheManager: CacheManager;

  constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  /**
   * 获取技能配置列表
   */
  public getSkills = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { page, limit } = parsePaginationParams(req.query);
      const { damageType, targetType } = req.query;

      // 构建查询条件
      const filter: any = { isActive: true };
      if (damageType) filter.damageType = damageType;
      if (targetType) filter.targetType = targetType;

      // 查询技能配置
      const skills = await Skill.find(filter)
        .sort({ 'requirements.level': 1, name: 1 })
        .skip((page - 1) * limit)
        .limit(limit);

      // 获取总数
      const total = await Skill.countDocuments(filter);
      const pagination = calculatePagination(page, limit, total);

      sendPaginatedResponse(res, '获取技能列表成功', skills, pagination);
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取技能详情
   */
  public getSkill = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { skillId } = req.params;

      const skill = await Skill.findOne({ id: skillId, isActive: true });
      if (!skill) {
        throw new AppError('技能不存在', 404, ErrorCodes.NOT_FOUND);
      }

      sendResponse(res, 200, '获取技能详情成功', { skill });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 获取用户技能列表
   */
  public getUserSkills = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId } = req.params;

      // 验证角色是否属于用户
      const character = await Character.findOne({
        _id: characterId,
        userId,
        isActive: true,
      });

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 获取用户技能
      const userSkills = await UserSkill.find({ userId })
        .populate('skillId', 'name description damageType targetType manaCost castTime cooldown');

      sendResponse(res, 200, '获取用户技能成功', {
        skills: userSkills,
        skillPoints: character.skillPoints,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 学习技能
   */
  public learnSkill = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = (req as any).user.id;
      const { characterId } = req.params;
      const { skillId } = req.body;

      // 验证角色是否属于用户
      const character = await Character.findOne({
        _id: characterId,
        userId,
        isActive: true,
      });

      if (!character) {
        throw new AppError('角色不存在', 404, ErrorCodes.CHARACTER_NOT_FOUND);
      }

      // 验证技能是否存在
      const skillConfig = await Skill.findOne({ id: skillId, isActive: true });
      if (!skillConfig) {
        throw new AppError('技能不存在', 404, ErrorCodes.NOT_FOUND);
      }

      // 检查是否已经学习过
      const existingUserSkill = await UserSkill.findOne({ userId, skillId });
      if (existingUserSkill) {
        throw new AppError('技能已学习', 400, ErrorCodes.INVALID_ACTION);
      }

      // 检查学习要求
      if (!this.checkSkillRequirements(character, skillConfig)) {
        throw new AppError('不满足学习要求', 400, ErrorCodes.INVALID_ACTION);
      }

      // 检查技能点
      if (character.skillPoints < skillConfig.requirements.skillPoints) {
        throw new AppError('技能点不足', 400, ErrorCodes.INSUFFICIENT_RESOURCES);
      }

      // 创建用户技能
      const userSkill = new UserSkill({
        userId,
        skillId,
        level: 1,
        experience: 0,
        experienceRequired: 100,
        learned: true,
      });

      await userSkill.save();

      // 扣除技能点
      character.skillPoints -= skillConfig.requirements.skillPoints;
      character.skills.push(skillConfig._id);
      await character.save();

      Logger.info('技能学习成功', {
        userId,
        characterId,
        skillId,
        skillPointsUsed: skillConfig.requirements.skillPoints,
      });

      sendResponse(res, 201, '技能学习成功', {
        skill: userSkill,
        remainingSkillPoints: character.skillPoints,
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * 检查技能学习要求
   */
  private checkSkillRequirements(character: any, skillConfig: any): boolean {
    const requirements = skillConfig.requirements;

    // 检查等级要求
    if (character.level < requirements.level) {
      return false;
    }

    // 检查前置技能
    if (requirements.prerequisiteSkills && requirements.prerequisiteSkills.length > 0) {
      for (const prereqSkillId of requirements.prerequisiteSkills) {
        const hasPrereq = character.skills.some((skill: any) => skill.toString() === prereqSkillId);
        if (!hasPrereq) {
          return false;
        }
      }
    }

    // 检查属性要求
    if (requirements.attributes) {
      const attrs = character.attributes;
      if (requirements.attributes.strength && attrs.strength < requirements.attributes.strength) return false;
      if (requirements.attributes.agility && attrs.agility < requirements.attributes.agility) return false;
      if (requirements.attributes.intelligence && attrs.intelligence < requirements.attributes.intelligence) return false;
      if (requirements.attributes.vitality && attrs.vitality < requirements.attributes.vitality) return false;
      if (requirements.attributes.spirit && attrs.spirit < requirements.attributes.spirit) return false;
    }

    return true;
  }
}

export const skillController = new SkillController();
