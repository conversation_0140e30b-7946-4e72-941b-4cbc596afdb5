{"version": 3, "file": "usage-example.js", "sourceRoot": "", "sources": ["../../examples/usage-example.ts"], "names": [], "mappings": ";;AAEA;;;GAGG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+RM,kDAAmB;AAAE,gDAAkB;AAAE,sDAAqB;AA7RvE,yDAAsD;AACtD,2DAAwD;AACxD,yEAAsE;AACtE,qEAAkE;AAClE,2CAA6B;AAE7B,KAAK,UAAU,mBAAmB;IAC9B,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC7B,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,IAAI,CAAC;QACD,iBAAiB;QACjB,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACnC,MAAM,SAAS,GAAG,IAAI,6BAAa,EAAE,CAAC;QACtC,MAAM,cAAc,GAAG,IAAI,+BAAc,EAAE,CAAC;QAC5C,MAAM,cAAc,GAAG,IAAI,2CAAoB,EAAE,CAAC;QAClD,MAAM,eAAe,GAAG,IAAI,yCAAmB,EAAE,CAAC;QAClD,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAE3B,cAAc;QACd,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;QACpD,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;QAElE,OAAO,CAAC,GAAG,CAAC,SAAS,OAAO,CAAC,MAAM,OAAO,CAAC,CAAC;QAC5C,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC;QAC1F,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,kBAAkB;QAClB,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC/B,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;QAE3B,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,eAAe;YACvD,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,IAAI,aAAa,CAAC,CAAC;YAC9C,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YACzE,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YACnC,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,YAAY,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,MAAM,YAAY,GAAG,EAAE,CAAC;QAExB,KAAK,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,QAAQ,EAAE,CAAC;YAC3C,OAAO,CAAC,GAAG,CAAC,QAAQ,UAAU,YAAY,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,sBAAsB,EAAE,CAAC;YACzD,YAAY,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;YAChC,OAAO,CAAC,GAAG,CAAC,KAAK,UAAU,QAAQ,SAAS,CAAC,MAAM,QAAQ,CAAC,CAAC;QACjE,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,WAAW,YAAY,CAAC,MAAM,UAAU,CAAC,CAAC;QAEtD,cAAc;QACd,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC7B,MAAM,WAAW,GAAG,EAAE,CAAC;QAEvB,KAAK,MAAM,QAAQ,IAAI,YAAY,EAAE,CAAC;YAClC,SAAS;YACT,MAAM,MAAM,GAAG;gBACX,EAAE,EAAE,QAAQ,CAAC,EAAE;gBACf,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE,SAAS;gBAC5D,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,EAAE,YAAY;gBAClE,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,oBAAoB,CAAC,CAAC,CAAC,SAAS;gBACpE,UAAU,EAAE;oBACR;wBACI,WAAW,EAAE,0BAA0B;wBACvC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI;wBAC5B,QAAQ,EAAE,SAAS;wBACnB,MAAM,EAAE,SAAS;qBACpB;iBACJ;aACJ,CAAC;YACF,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC7B,CAAC;QAED,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;QAC1E,MAAM,WAAW,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM,CAAC;QAE1E,OAAO,CAAC,GAAG,CAAC,aAAa,WAAW,QAAQ,WAAW,OAAO,CAAC,CAAC;QAEhE,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,MAAM,MAAM,GAAG,MAAM,eAAe,CAAC,cAAc,CAAC,WAAW,EAAE;YAC7D,WAAW,EAAE,QAAQ;YACrB,WAAW,EAAE,WAAW;YACxB,aAAa,EAAE,MAAM;SACxB,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,WAAW,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC;QACpD,OAAO,CAAC,GAAG,CAAC,UAAU,MAAM,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,aAAa,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,oBAAoB,IAAI,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,WAAW,MAAM,CAAC,QAAQ,CAAC,YAAY,MAAM,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;QAC1D,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;QACnE,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,qBAAqB,CAAC,CAAC;QAEnE,MAAM,eAAe,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QACjE,MAAM,eAAe,CAAC,kBAAkB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;QAEjE,OAAO,CAAC,GAAG,CAAC,cAAc,cAAc,EAAE,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,cAAc,cAAc,EAAE,CAAC,CAAC;QAC5C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,eAAe;QACf,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;QAEjC,SAAS;QACT,MAAM,yBAAyB,GAAG,MAAM,SAAS,CAAC,4BAA4B,CAC1E,qFAAqF,EACrF,qFAAqF,EACrF,mBAAmB,CACtB,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,UAAU,yBAAyB,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;QACnF,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,yBAAyB,CAAC,mBAAmB,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;QAC1F,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAC5B,IAAI,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YACxB,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE,EAAE;gBACrD,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,cAAc,EAAE,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QACpC,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,aAAa;QACb,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC/B,MAAM,KAAK,GAAG,cAAc,CAAC,mBAAmB,EAAE,CAAC;QAEnD,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxB,OAAO,CAAC,GAAG,CAAC,WAAW,KAAK,CAAC,YAAY,EAAE,CAAC,CAAC;QAC7C,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,KAAK,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC;QAC3C,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACxB,KAAK,MAAM,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,sBAAsB,EAAE,CAAC;YAC7D,OAAO,CAAC,GAAG,CAAC,SAAS,UAAU,KAAK,KAAK,EAAE,CAAC,CAAC;QACjD,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,KAAK;QACL,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAChB,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;QAC9B,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QAC/B,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;IAEpC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAClC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;AACL,CAAC;AAED,SAAS;AACT,KAAK,UAAU,kBAAkB;IAC7B,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IAC3B,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,IAAI,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,6BAAa,EAAE,CAAC;QAEtC,cAAc;QACd,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC3B,MAAM,WAAW,GAAG;YAChB;gBACI,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,UAAmB;gBACzB,OAAO,EAAE,sCAAsC;aAClD;YACD;gBACI,IAAI,EAAE,iBAAiB;gBACvB,IAAI,EAAE,OAAgB;gBACtB,OAAO,EAAE,+BAA+B;aAC3C;SACJ,CAAC;QAEF,MAAM,YAAY,GAAG,MAAM,SAAS,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACvE,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,YAAY,YAAY,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,WAAW,YAAY,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,MAAM,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,MAAM,SAAS,GAAG,MAAM,SAAS,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,UAAU,SAAS,CAAC,MAAM,QAAQ,CAAC,CAAC;QAChD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACzB,MAAM,WAAW,GAAG,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YACvC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAiB;YAC3D,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG;YAChD,OAAO,EAAE,EAAE,OAAO,EAAE,6BAA6B,EAAE;SACtD,CAAC,CAAC,CAAC;QAEJ,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3D,OAAO,CAAC,GAAG,CAAC,WAAW,OAAO,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAEhB,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAE7B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;IACtC,CAAC;AACL,CAAC;AAED,SAAS;AACT,KAAK,UAAU,qBAAqB;IAChC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAC1B,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAE5B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,cAAc;IACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC;QAC5B,SAAS;QACT,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAC3B,MAAM,aAAa,GAAG,OAAO,GAAG,SAAS,CAAC;IAE1C,OAAO,CAAC,GAAG,CAAC,YAAY,aAAa,IAAI,CAAC,CAAC;IAE3C,IAAI,aAAa,GAAG,GAAG,EAAE,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;IAC5B,CAAC;SAAM,IAAI,aAAa,GAAG,GAAG,EAAE,CAAC;QAC7B,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;IAC3B,CAAC;SAAM,CAAC;QACJ,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AAC/B,CAAC;AAED,MAAM;AACN,KAAK,UAAU,IAAI;IACf,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IAC7B,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;IAEvD,SAAS;IACT,MAAM,mBAAmB,EAAE,CAAC;IAE5B,SAAS;IACT,MAAM,kBAAkB,EAAE,CAAC;IAE3B,SAAS;IACT,MAAM,qBAAqB,EAAE,CAAC;IAE9B,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC9B,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AAC/B,CAAC;AAED,YAAY;AACZ,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC1B,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACjB,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAClC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAAC;AACP,CAAC"}