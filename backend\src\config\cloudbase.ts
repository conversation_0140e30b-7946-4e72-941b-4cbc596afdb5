/**
 * 微信云开发数据库配置
 */
import tcb from '@cloudbase/node-sdk';

export class CloudBaseConfig {
  private static instance: CloudBaseConfig;
  private app: any;
  private db: any;

  private constructor() {
    this.initializeCloudBase();
  }

  public static getInstance(): CloudBaseConfig {
    if (!CloudBaseConfig.instance) {
      CloudBaseConfig.instance = new CloudBaseConfig();
    }
    return CloudBaseConfig.instance;
  }

  /**
   * 初始化云开发
   */
  private initializeCloudBase(): void {
    const config: any = {
      env: process.env.TCB_ENV || 'your-env-id',
    };

    // 如果在云托管环境中，会自动获取身份信息
    if (!process.env.WECHAT_CLOUD) {
      config.secretId = process.env.TCB_SECRET_ID;
      config.secretKey = process.env.TCB_SECRET_KEY;
    }

    this.app = tcb.init(config);
    this.db = this.app.database();
  }

  /**
   * 获取数据库实例
   */
  public getDatabase(): any {
    return this.db;
  }

  /**
   * 获取云开发应用实例
   */
  public getApp(): any {
    return this.app;
  }

  /**
   * 获取集合
   */
  public collection(name: string): any {
    return this.db.collection(name);
  }

  /**
   * 执行事务
   */
  public async runTransaction(updateFunction: (transaction: any) => Promise<any>): Promise<any> {
    return await this.db.runTransaction(updateFunction);
  }
}

/**
 * 云数据库操作适配器
 * 提供类似MongoDB的操作接口
 */
export class CloudDatabaseAdapter {
  private db: any;

  constructor() {
    this.db = CloudBaseConfig.getInstance().getDatabase();
  }

  /**
   * 查找文档
   */
  async find(collectionName: string, query: any = {}, options: any = {}): Promise<any[]> {
    const collection = this.db.collection(collectionName);
    let dbQuery = collection.where(query);

    // 处理排序
    if (options.sort) {
      for (const [field, direction] of Object.entries(options.sort)) {
        dbQuery = dbQuery.orderBy(field, direction === 1 ? 'asc' : 'desc');
      }
    }

    // 处理限制
    if (options.limit) {
      dbQuery = dbQuery.limit(options.limit);
    }

    // 处理跳过
    if (options.skip) {
      dbQuery = dbQuery.skip(options.skip);
    }

    const result = await dbQuery.get();
    return result.data;
  }

  /**
   * 查找单个文档
   */
  async findOne(collectionName: string, query: any = {}): Promise<any> {
    const result = await this.find(collectionName, query, { limit: 1 });
    return result.length > 0 ? result[0] : null;
  }

  /**
   * 根据ID查找文档
   */
  async findById(collectionName: string, id: string): Promise<any> {
    const collection = this.db.collection(collectionName);
    const result = await collection.doc(id).get();
    return result.data.length > 0 ? result.data[0] : null;
  }

  /**
   * 插入文档
   */
  async insertOne(collectionName: string, document: any): Promise<any> {
    const collection = this.db.collection(collectionName);
    const result = await collection.add(document);
    return {
      insertedId: result.id,
      acknowledged: true
    };
  }

  /**
   * 插入多个文档
   */
  async insertMany(collectionName: string, documents: any[]): Promise<any> {
    const collection = this.db.collection(collectionName);
    const result = await collection.add(documents);
    return {
      insertedIds: result.ids,
      acknowledged: true
    };
  }

  /**
   * 更新文档
   */
  async updateOne(collectionName: string, query: any, update: any): Promise<any> {
    const collection = this.db.collection(collectionName);
    const result = await collection.where(query).update(update);
    return {
      matchedCount: result.stats.updated,
      modifiedCount: result.stats.updated,
      acknowledged: true
    };
  }

  /**
   * 更新多个文档
   */
  async updateMany(collectionName: string, query: any, update: any): Promise<any> {
    const collection = this.db.collection(collectionName);
    const result = await collection.where(query).update(update);
    return {
      matchedCount: result.stats.updated,
      modifiedCount: result.stats.updated,
      acknowledged: true
    };
  }

  /**
   * 根据ID更新文档
   */
  async updateById(collectionName: string, id: string, update: any): Promise<any> {
    const collection = this.db.collection(collectionName);
    const result = await collection.doc(id).update(update);
    return {
      matchedCount: result.stats.updated,
      modifiedCount: result.stats.updated,
      acknowledged: true
    };
  }

  /**
   * 删除文档
   */
  async deleteOne(collectionName: string, query: any): Promise<any> {
    const collection = this.db.collection(collectionName);
    const result = await collection.where(query).remove();
    return {
      deletedCount: result.stats.removed,
      acknowledged: true
    };
  }

  /**
   * 删除多个文档
   */
  async deleteMany(collectionName: string, query: any): Promise<any> {
    const collection = this.db.collection(collectionName);
    const result = await collection.where(query).remove();
    return {
      deletedCount: result.stats.removed,
      acknowledged: true
    };
  }

  /**
   * 根据ID删除文档
   */
  async deleteById(collectionName: string, id: string): Promise<any> {
    const collection = this.db.collection(collectionName);
    const result = await collection.doc(id).remove();
    return {
      deletedCount: result.stats.removed,
      acknowledged: true
    };
  }

  /**
   * 计数
   */
  async count(collectionName: string, query: any = {}): Promise<number> {
    const collection = this.db.collection(collectionName);
    const result = await collection.where(query).count();
    return result.total;
  }

  /**
   * 聚合查询
   */
  async aggregate(collectionName: string, pipeline: any[]): Promise<any[]> {
    const collection = this.db.collection(collectionName);
    const result = await collection.aggregate().end();
    return result.data;
  }
}
