import { _decorator, Component } from 'cc';

const { ccclass } = _decorator;

/**
 * 测试管理器
 * 确保只有一个测试组件在运行，避免冲突
 */
@ccclass('TestManager')
export class TestManager {
    private static _instance: TestManager;
    private static _runningTests: Set<string> = new Set();
    private static _activeComponents: Map<string, Component> = new Map();

    /**
     * 获取单例实例
     */
    public static getInstance(): TestManager {
        if (!TestManager._instance) {
            TestManager._instance = new TestManager();
        }
        return TestManager._instance;
    }

    /**
     * 注册测试组件
     */
    public static registerTestComponent(name: string, component: Component): void {
        // 如果已有同类型的组件在运行，先销毁它
        if (TestManager._activeComponents.has(name)) {
            const existingComponent = TestManager._activeComponents.get(name);
            if (existingComponent && existingComponent.isValid) {
                console.log(`🔄 销毁已存在的测试组件: ${name}`);
                existingComponent.destroy();
            }
        }
        
        TestManager._activeComponents.set(name, component);
        console.log(`✅ 注册测试组件: ${name}`);
    }

    /**
     * 注销测试组件
     */
    public static unregisterTestComponent(name: string): void {
        TestManager._activeComponents.delete(name);
        TestManager._runningTests.delete(name);
        console.log(`❌ 注销测试组件: ${name}`);
    }

    /**
     * 开始测试
     */
    public static startTest(testName: string): boolean {
        if (TestManager._runningTests.has(testName)) {
            console.warn(`⚠️ 测试 ${testName} 已在运行中`);
            return false;
        }
        
        TestManager._runningTests.add(testName);
        console.log(`🚀 开始测试: ${testName}`);
        return true;
    }

    /**
     * 结束测试
     */
    public static endTest(testName: string): void {
        TestManager._runningTests.delete(testName);
        console.log(`✅ 结束测试: ${testName}`);
    }

    /**
     * 检查测试是否在运行
     */
    public static isTestRunning(testName: string): boolean {
        return TestManager._runningTests.has(testName);
    }

    /**
     * 停止所有测试
     */
    public static stopAllTests(): void {
        console.log('🛑 停止所有测试...');
        
        // 停止所有运行中的测试
        TestManager._runningTests.clear();
        
        // 销毁所有活跃的测试组件
        TestManager._activeComponents.forEach((component, name) => {
            if (component && component.isValid) {
                console.log(`🗑️ 销毁测试组件: ${name}`);
                component.destroy();
            }
        });
        
        TestManager._activeComponents.clear();
        console.log('✅ 所有测试已停止');
    }

    /**
     * 获取活跃的测试组件列表
     */
    public static getActiveComponents(): string[] {
        return Array.from(TestManager._activeComponents.keys());
    }

    /**
     * 获取运行中的测试列表
     */
    public static getRunningTests(): string[] {
        return Array.from(TestManager._runningTests);
    }
}
