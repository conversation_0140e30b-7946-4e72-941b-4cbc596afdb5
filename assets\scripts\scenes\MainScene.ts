import { _decorator, Component, Node, director, input, Input, EventKeyboard, KeyCode } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 主界面场景控制器
 * 负责武侠放置游戏的主界面逻辑和UI管理
 */
@ccclass('MainScene')
export class MainScene extends Component {

    protected onLoad(): void {
        console.log('🏠 主界面场景加载');
        this.initializeKeyboardInput();
    }

    protected start(): void {
        console.log('🏠 主界面场景开始');
        console.log('✅ 主界面场景运行正常');
        this.showTestInstructions();
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ 键盘输入已初始化');
    }

    /**
     * 显示测试说明
     */
    private showTestInstructions(): void {
        console.log('🎮 ========== 场景切换测试 ==========');
        console.log('📍 当前场景: Main (主界面)');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 切换到 Launch 场景');
        console.log('   按 2 键 - 切换到 Main 场景');
        console.log('   按 3 键 - 切换到 Battle 场景');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🎮 ===================================');
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.switchToLaunch();
                break;
            case KeyCode.DIGIT_2:
                this.switchToMain();
                break;
            case KeyCode.DIGIT_3:
                this.switchToBattle();
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 切换到启动场景
     */
    private switchToLaunch(): void {
        console.log('🚀 切换到启动场景');
        this.switchScene('Launch');
    }

    /**
     * 切换到主界面场景
     */
    private switchToMain(): void {
        console.log('🏠 切换到主界面场景');
        this.switchScene('Main');
    }

    /**
     * 切换到战斗场景
     */
    private switchToBattle(): void {
        console.log('⚔️ 切换到战斗场景');
        this.switchScene('Battle');
    }

    /**
     * 通用场景切换方法
     */
    private switchScene(sceneName: string): void {
        try {
            console.log(`🔄 正在切换到场景: ${sceneName}`);
            director.loadScene(sceneName, (error) => {
                if (error) {
                    console.error(`❌ 场景切换失败: ${sceneName}`, error);
                } else {
                    console.log(`✅ 场景切换成功: ${sceneName}`);
                }
            });
        } catch (error) {
            console.error(`❌ 场景切换异常: ${sceneName}`, error);
        }
    }

    /**
     * 测试方法 - 进入战斗场景
     */
    public testEnterBattle(): void {
        console.log('⚔️ 测试进入战斗场景');
        director.loadScene('Battle');
    }

    /**
     * 测试方法 - 返回启动场景
     */
    public testBackToLaunch(): void {
        console.log('🔙 测试返回启动场景');
        director.loadScene('Launch');
    }



    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🏠 主界面场景销毁');
    }
}
