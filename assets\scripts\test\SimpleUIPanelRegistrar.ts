/**
 * 简单UI面板注册器
 * 用于快速注册UI面板配置，便于测试
 */

import { _decorator, Component } from 'cc';
import { UIManager } from '../managers/UIManager';
import { UIPanelType, UILayer, UIAnimationType } from '../ui/types/UITypes';

const { ccclass, property } = _decorator;

@ccclass('SimpleUIPanelRegistrar')
export class SimpleUIPanelRegistrar extends Component {
    
    @property({ tooltip: '是否自动注册面板' })
    public autoRegister: boolean = true;
    
    @property({ tooltip: '是否显示注册日志' })
    public showLogs: boolean = true;

    protected onLoad(): void {
        if (this.autoRegister) {
            // 延迟注册，确保UIManager已初始化
            this.scheduleOnce(() => {
                this.registerBasicPanels();
            }, 0.1);
        }
    }

    /**
     * 注册基础面板配置
     */
    public registerBasicPanels(): void {
        if (this.showLogs) {
            console.log('📋 简单UI面板注册器: 开始注册基础面板...');
        }
        
        try {
            const uiManager = UIManager.getInstance();
            
            if (!uiManager) {
                console.error('❌ UIManager未初始化，无法注册面板');
                return;
            }
            
            let registeredCount = 0;
            
            // 注册背包面板
            try {
                uiManager.registerPanel({
                    type: UIPanelType.Inventory,
                    prefabPath: 'ui/panels/InventoryPanel',
                    layer: UILayer.Normal,
                    singleton: true,
                    cache: true,
                    showAnimation: UIAnimationType.Scale,
                    hideAnimation: UIAnimationType.Scale,
                    animationDuration: 0.3,
                    modal: false,
                    escapeToClose: true
                });
                registeredCount++;
                if (this.showLogs) console.log('✅ 背包面板配置已注册');
            } catch (error) {
                if (this.showLogs) console.warn('⚠️ 背包面板配置注册失败:', error.message);
            }
            
            // 注册技能面板
            try {
                uiManager.registerPanel({
                    type: UIPanelType.Skills,
                    prefabPath: 'ui/panels/SkillPanel',
                    layer: UILayer.Normal,
                    singleton: true,
                    cache: true,
                    showAnimation: UIAnimationType.Slide,
                    hideAnimation: UIAnimationType.Slide,
                    animationDuration: 0.25,
                    modal: false,
                    escapeToClose: true
                });
                registeredCount++;
                if (this.showLogs) console.log('✅ 技能面板配置已注册');
            } catch (error) {
                if (this.showLogs) console.warn('⚠️ 技能面板配置注册失败:', error.message);
            }
            
            // 注册主菜单
            try {
                uiManager.registerPanel({
                    type: UIPanelType.MainMenu,
                    prefabPath: 'ui/panels/MainMenu',
                    layer: UILayer.Popup,
                    singleton: true,
                    cache: true,
                    showAnimation: UIAnimationType.Fade,
                    hideAnimation: UIAnimationType.Fade,
                    animationDuration: 0.2,
                    modal: true,
                    escapeToClose: true
                });
                registeredCount++;
                if (this.showLogs) console.log('✅ 主菜单配置已注册');
            } catch (error) {
                if (this.showLogs) console.warn('⚠️ 主菜单配置注册失败:', error.message);
            }
            
            // 注册游戏HUD
            try {
                uiManager.registerPanel({
                    type: UIPanelType.GameHUD,
                    prefabPath: 'ui/panels/GameHUD',
                    layer: UILayer.Background,
                    singleton: true,
                    cache: true,
                    showAnimation: UIAnimationType.None,
                    hideAnimation: UIAnimationType.None,
                    modal: false,
                    escapeToClose: false
                });
                registeredCount++;
                if (this.showLogs) console.log('✅ 游戏HUD配置已注册');
            } catch (error) {
                if (this.showLogs) console.warn('⚠️ 游戏HUD配置注册失败:', error.message);
            }
            
            if (this.showLogs) {
                console.log(`📊 面板注册完成: ${registeredCount}/4 个面板配置已注册`);
                console.log('💡 注意: 预制体文件尚未创建，面板显示仍会失败');
                console.log('💡 但现在可以测试面板配置系统了');
            }
            
        } catch (error) {
            console.error('❌ 注册基础面板失败:', error);
        }
    }

    /**
     * 显示当前UI状态
     */
    public showUIStatus(): void {
        console.log('\n📊 ========== UI系统状态 ==========');
        
        const uiManager = UIManager.getInstance();
        if (!uiManager) {
            console.log('❌ UIManager未初始化');
            return;
        }
        
        const stats = uiManager.getUIStats();
        console.log('🎨 UI管理器状态:');
        console.log(`   总注册面板: ${stats.totalPanels}`);
        console.log(`   活跃面板: ${stats.activePanels}`);
        console.log(`   可见面板: ${stats.visiblePanels}`);
        console.log(`   缓存预制体: ${stats.cachedPrefabs}`);
        
        if (stats.modalStack && stats.modalStack.length > 0) {
            console.log(`   模态栈: [${stats.modalStack.join(', ')}]`);
        }
        
        console.log('📊 ==============================');
    }

    /**
     * 测试面板配置
     */
    public testPanelConfigs(): void {
        console.log('\n🧪 ========== 测试面板配置 ==========');
        
        const uiManager = UIManager.getInstance();
        if (!uiManager) {
            console.log('❌ UIManager未初始化');
            return;
        }
        
        const testPanels = [
            { type: UIPanelType.Inventory, name: '背包面板' },
            { type: UIPanelType.Skills, name: '技能面板' },
            { type: UIPanelType.MainMenu, name: '主菜单' },
            { type: UIPanelType.GameHUD, name: '游戏HUD' }
        ];
        
        let configuredCount = 0;
        
        for (const panel of testPanels) {
            try {
                // 测试面板可见性检查（这会验证配置是否存在）
                const isVisible = uiManager.isPanelVisible(panel.type);
                console.log(`✅ ${panel.name}: 配置存在 (${isVisible ? '可见' : '隐藏'})`);
                configuredCount++;
            } catch (error) {
                console.log(`❌ ${panel.name}: 配置缺失`);
            }
        }
        
        const percentage = (configuredCount / testPanels.length * 100).toFixed(1);
        console.log(`📈 配置完整性: ${configuredCount}/${testPanels.length} (${percentage}%)`);
        
        if (configuredCount === testPanels.length) {
            console.log('🎉 所有面板配置都已正确注册！');
            console.log('💡 现在可以测试面板切换功能了');
            console.log('💡 虽然预制体不存在，但配置系统正常工作');
        } else {
            console.log('⚠️ 部分面板配置缺失，请检查注册过程');
        }
        
        console.log('🧪 ===============================');
    }

    /**
     * 手动注册面板（用于调试）
     */
    public manualRegister(): void {
        console.log('🔧 手动注册面板配置...');
        this.registerBasicPanels();
    }

    /**
     * 清除所有面板配置（用于测试）
     */
    public clearAllPanels(): void {
        console.log('🧹 清除所有面板配置...');
        
        const uiManager = UIManager.getInstance();
        if (uiManager) {
            // 这里需要UIManager提供清除配置的方法
            // 目前只能通过隐藏所有面板来模拟
            uiManager.hideAllPanels();
            console.log('✅ 所有面板已隐藏');
        }
    }

    /**
     * 快速测试所有功能
     */
    public quickTest(): void {
        console.log('\n⚡ ========== 快速功能测试 ==========');
        
        // 1. 检查UIManager
        const uiManager = UIManager.getInstance();
        if (uiManager) {
            console.log('✅ UIManager: 正常');
        } else {
            console.log('❌ UIManager: 异常');
            return;
        }
        
        // 2. 注册面板配置
        console.log('🔧 重新注册面板配置...');
        this.registerBasicPanels();
        
        // 3. 测试配置
        setTimeout(() => {
            this.testPanelConfigs();
        }, 100);
        
        // 4. 显示状态
        setTimeout(() => {
            this.showUIStatus();
        }, 200);
        
        console.log('⚡ ============================');
    }
}
