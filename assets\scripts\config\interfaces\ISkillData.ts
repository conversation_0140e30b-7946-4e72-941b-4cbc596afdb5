/**
 * 技能数据接口定义
 * 基于Godot项目中的Skill.xml结构
 */

/**
 * 技能数据接口
 */
export interface ISkillData {
    /** 技能唯一标识符 */
    id: string;
    
    /** 技能名称 */
    name: string;
    
    /** 技能描述 */
    description: string;
    
    /** 法力消耗 */
    manaCost: number;
    
    /** 施法时间（秒） */
    castTime: number;
    
    /** 冷却时间（秒） */
    cooldown: number;
    
    /** 伤害类型 */
    damageType: SkillDamageType;
    
    /** 目标类型 */
    targetType: SkillTargetType;
    
    /** 基础伤害倍数 */
    baseDamageMultiplier: number;
    
    /** 技能等级 */
    level: number;
    
    /** 最大等级 */
    maxLevel: number;
    
    /** 学习要求 */
    requirements: ISkillRequirements;
    
    /** 技能效果列表 */
    effects: ISkillEffect[];
}

/**
 * 技能伤害类型枚举
 */
export enum SkillDamageType {
    Physical = 'physical',
    Magical = 'magical',
    True = 'true',
    Healing = 'healing'
}

/**
 * 技能目标类型枚举
 */
export enum SkillTargetType {
    Self = 'self',
    Enemy = 'enemy',
    Ally = 'ally',
    Area = 'area',
    All = 'all'
}

/**
 * 技能学习要求接口
 */
export interface ISkillRequirements {
    /** 所需等级 */
    level: number;
    
    /** 前置技能要求 */
    skills: ISkillRequirement[];
    
    /** 所需物品 */
    items: IItemRequirement[];
}

/**
 * 单个技能要求
 */
export interface ISkillRequirement {
    /** 技能ID */
    skillId: string;
    
    /** 所需技能等级 */
    level: number;
}

/**
 * 物品要求接口
 */
export interface IItemRequirement {
    /** 物品ID */
    itemId: string;
    
    /** 所需数量 */
    quantity: number;
}

/**
 * 技能效果接口
 */
export interface ISkillEffect {
    /** 效果类型 */
    type: SkillEffectType;
    
    /** 效果数值 */
    value: number;
    
    /** 持续时间（秒，0表示瞬时效果） */
    duration: number;
    
    /** 效果目标（可选，默认使用技能目标） */
    target?: SkillTargetType;
}

/**
 * 技能效果类型枚举
 */
export enum SkillEffectType {
    Damage = 'damage',
    Heal = 'heal',
    Buff = 'buff',
    Debuff = 'debuff',
    Stun = 'stun',
    Slow = 'slow',
    Poison = 'poison',
    Burn = 'burn',
    Shield = 'shield'
}

/**
 * 玩家技能数据接口（包含玩家特定信息）
 */
export interface IPlayerSkill {
    /** 技能ID */
    skillId: string;
    
    /** 当前等级 */
    level: number;
    
    /** 当前经验值 */
    experience: number;
    
    /** 升级所需经验 */
    experienceRequired: number;
    
    /** 是否已学会 */
    learned: boolean;
    
    /** 最后使用时间 */
    lastUsed: number;
    
    /** 当前冷却剩余时间 */
    cooldownRemaining: number;
}

/**
 * 技能使用结果接口
 */
export interface ISkillResult {
    /** 是否成功使用 */
    success: boolean;
    
    /** 错误信息（如果失败） */
    error?: string;
    
    /** 造成的伤害 */
    damage?: number;
    
    /** 治疗量 */
    healing?: number;
    
    /** 法力消耗 */
    manaCost: number;
    
    /** 冷却时间 */
    cooldown: number;
    
    /** 应用的效果 */
    effects: IAppliedEffect[];
    
    /** 暴击标记 */
    isCritical?: boolean;
    
    /** 经验获得 */
    experienceGained?: number;
}

/**
 * 已应用的效果接口
 */
export interface IAppliedEffect {
    /** 效果类型 */
    type: SkillEffectType;
    
    /** 效果数值 */
    value: number;
    
    /** 剩余持续时间 */
    remainingDuration: number;
    
    /** 效果来源技能ID */
    sourceSkillId: string;
    
    /** 效果施加者ID */
    casterId: string;
    
    /** 效果目标ID */
    targetId: string;
}

/**
 * 技能配置文件根接口
 */
export interface ISkillConfig {
    /** 配置版本 */
    version: string;
    
    /** 最后更新时间 */
    lastUpdated: string;
    
    /** 技能列表 */
    skills: ISkillData[];
}

/**
 * 技能学习事件数据
 */
export interface ISkillLearnEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 技能ID */
    skillId: string;
    
    /** 学习后的等级 */
    newLevel: number;
    
    /** 学习时间 */
    timestamp: number;
}

/**
 * 技能使用事件数据
 */
export interface ISkillUseEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 技能ID */
    skillId: string;
    
    /** 目标ID（可选） */
    targetId?: string;
    
    /** 使用结果 */
    result: ISkillResult;
    
    /** 使用时间 */
    timestamp: number;
}

/**
 * 技能冷却事件数据
 */
export interface ISkillCooldownEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 技能ID */
    skillId: string;
    
    /** 冷却开始时间 */
    startTime: number;
    
    /** 冷却持续时间 */
    duration: number;
    
    /** 剩余冷却时间 */
    remaining: number;
}

/**
 * 技能升级事件数据
 */
export interface ISkillLevelUpEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 技能ID */
    skillId: string;
    
    /** 旧等级 */
    oldLevel: number;
    
    /** 新等级 */
    newLevel: number;
    
    /** 升级时间 */
    timestamp: number;
}
