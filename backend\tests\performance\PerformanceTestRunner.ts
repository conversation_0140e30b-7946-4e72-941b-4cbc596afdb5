import { skillService } from '../../src/services/SkillService';
import { userService } from '../../src/services/UserService';
import { battleService } from '../../src/services/BattleService';
import { cacheStrategyService } from '../../src/services/CacheStrategyService';
import { DatabaseManager } from '../../src/config/database';
import { User } from '../../src/models/User';
import { Character, CharacterClass } from '../../src/models/Character';
import { Skill } from '../../src/models/Skill';
import { Logger } from '../../src/utils/logger';

/**
 * 性能测试结果接口
 */
export interface PerformanceTestResult {
  testName: string;
  iterations: number;
  totalTime: number;
  averageTime: number;
  minTime: number;
  maxTime: number;
  throughput: number; // 每秒操作数
  success: boolean;
  errors: string[];
}

/**
 * 性能测试运行器
 */
export class PerformanceTestRunner {
  private testResults: PerformanceTestResult[] = [];

  /**
   * 运行所有性能测试
   */
  public async runAllPerformanceTests(): Promise<PerformanceTestResult[]> {
    try {
      Logger.info('开始性能测试');

      // 连接数据库
      const databaseManager = DatabaseManager.getInstance();
      await databaseManager.connect();

      // 预热缓存
      await cacheStrategyService.warmupCache();

      // 运行各项性能测试
      await this.testUserAuthentication();
      await this.testSkillUsage();
      await this.testBattleCreation();
      await this.testConcurrentOperations();
      await this.testCachePerformance();

      Logger.info('性能测试完成', {
        totalTests: this.testResults.length,
        averageTime: this.getAverageTime(),
      });

      return this.testResults;

    } catch (error) {
      Logger.error('性能测试失败', error);
      throw error;
    } finally {
      await databaseManager.disconnect();
    }
  }

  /**
   * 测试用户认证性能
   */
  private async testUserAuthentication(): Promise<void> {
    const testName = '用户认证性能测试';
    const iterations = 50;
    const times: number[] = [];
    const errors: string[] = [];

    // 创建测试用户
    const testUser = await this.createTestUser('perf_auth_user');

    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = Date.now();
        
        await userService.authenticateUser(testUser.username, 'TestPassword123!');
        
        const endTime = Date.now();
        times.push(endTime - startTime);
      } catch (error) {
        errors.push(error.message);
      }
    }

    this.addTestResult(testName, iterations, times, errors);
  }

  /**
   * 测试技能使用性能
   */
  private async testSkillUsage(): Promise<void> {
    const testName = '技能使用性能测试';
    const iterations = 100;
    const times: number[] = [];
    const errors: string[] = [];

    // 创建测试数据
    const testUser = await this.createTestUser('perf_skill_user');
    const testCharacter = await this.createTestCharacter(testUser._id, 'PerfSkillChar', CharacterClass.MAGE);
    const testSkill = await this.createTestSkill('perf_skill', '性能测试技能', 1, 0);

    // 学习技能
    await skillService.learnSkill(testUser._id.toString(), testSkill.id);

    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = Date.now();
        
        await skillService.useSkill(testUser._id.toString(), testSkill.id);
        
        const endTime = Date.now();
        times.push(endTime - startTime);
      } catch (error) {
        errors.push(error.message);
      }
    }

    this.addTestResult(testName, iterations, times, errors);
  }

  /**
   * 测试战斗创建性能
   */
  private async testBattleCreation(): Promise<void> {
    const testName = '战斗创建性能测试';
    const iterations = 30;
    const times: number[] = [];
    const errors: string[] = [];

    for (let i = 0; i < iterations; i++) {
      try {
        // 创建测试角色
        const attacker = await this.createTestUser(`perf_attacker_${i}`);
        const defender = await this.createTestUser(`perf_defender_${i}`);
        const attackerChar = await this.createTestCharacter(attacker._id, `Attacker${i}`, CharacterClass.WARRIOR);
        const defenderChar = await this.createTestCharacter(defender._id, `Defender${i}`, CharacterClass.MAGE);

        const startTime = Date.now();
        
        await battleService.createBattle(
          attackerChar._id.toString(),
          defenderChar._id.toString()
        );
        
        const endTime = Date.now();
        times.push(endTime - startTime);
      } catch (error) {
        errors.push(error.message);
      }
    }

    this.addTestResult(testName, iterations, times, errors);
  }

  /**
   * 测试并发操作性能
   */
  private async testConcurrentOperations(): Promise<void> {
    const testName = '并发操作性能测试';
    const concurrency = 20;
    const operationsPerWorker = 5;
    const times: number[] = [];
    const errors: string[] = [];

    const startTime = Date.now();

    // 创建并发任务
    const promises = [];
    for (let i = 0; i < concurrency; i++) {
      promises.push(this.runConcurrentWorker(i, operationsPerWorker));
    }

    try {
      const results = await Promise.allSettled(promises);
      
      for (const result of results) {
        if (result.status === 'rejected') {
          errors.push(result.reason.message || '并发操作失败');
        }
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;
      times.push(totalTime);

      this.addTestResult(testName, concurrency * operationsPerWorker, times, errors);
    } catch (error) {
      errors.push(error.message);
      this.addTestResult(testName, 0, [], errors);
    }
  }

  /**
   * 并发工作器
   */
  private async runConcurrentWorker(workerId: number, operations: number): Promise<void> {
    const testUser = await this.createTestUser(`concurrent_user_${workerId}`);
    
    for (let i = 0; i < operations; i++) {
      // 执行用户认证
      await userService.authenticateUser(testUser.username, 'TestPassword123!');
      
      // 小延迟避免过度压力
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  /**
   * 测试缓存性能
   */
  private async testCachePerformance(): Promise<void> {
    const testName = '缓存性能测试';
    const iterations = 200;
    const times: number[] = [];
    const errors: string[] = [];

    // 创建测试数据
    const testUser = await this.createTestUser('cache_perf_user');

    for (let i = 0; i < iterations; i++) {
      try {
        const startTime = Date.now();
        
        // 测试缓存读取
        await cacheStrategyService.getCachedUser(testUser._id.toString());
        
        const endTime = Date.now();
        times.push(endTime - startTime);
      } catch (error) {
        errors.push(error.message);
      }
    }

    this.addTestResult(testName, iterations, times, errors);
  }

  /**
   * 添加测试结果
   */
  private addTestResult(testName: string, iterations: number, times: number[], errors: string[]): void {
    if (times.length === 0) {
      this.testResults.push({
        testName,
        iterations: 0,
        totalTime: 0,
        averageTime: 0,
        minTime: 0,
        maxTime: 0,
        throughput: 0,
        success: false,
        errors,
      });
      return;
    }

    const totalTime = times.reduce((sum, time) => sum + time, 0);
    const averageTime = totalTime / times.length;
    const minTime = Math.min(...times);
    const maxTime = Math.max(...times);
    const throughput = (times.length / totalTime) * 1000; // 每秒操作数

    this.testResults.push({
      testName,
      iterations,
      totalTime,
      averageTime,
      minTime,
      maxTime,
      throughput,
      success: errors.length === 0,
      errors,
    });

    Logger.info(`性能测试完成: ${testName}`, {
      iterations,
      averageTime: `${averageTime.toFixed(2)}ms`,
      throughput: `${throughput.toFixed(2)} ops/s`,
      errors: errors.length,
    });
  }

  /**
   * 获取平均测试时间
   */
  private getAverageTime(): number {
    if (this.testResults.length === 0) return 0;
    
    const totalAverage = this.testResults.reduce((sum, result) => sum + result.averageTime, 0);
    return totalAverage / this.testResults.length;
  }

  /**
   * 创建测试用户
   */
  private async createTestUser(username: string): Promise<any> {
    const userData = {
      username: username + '_' + Date.now(),
      email: username + '_' + Date.now() + '@test.com',
      password: 'TestPassword123!',
    };

    const result = await userService.createUser(userData);
    return result.user;
  }

  /**
   * 创建测试角色
   */
  private async createTestCharacter(userId: any, name: string, characterClass: CharacterClass): Promise<any> {
    const character = new Character({
      userId,
      name: name + '_' + Date.now(),
      class: characterClass,
      level: 5,
      experience: 0,
      attributes: {
        strength: 15,
        agility: 12,
        intelligence: 10,
        vitality: 14,
        spirit: 8,
        damage: 50,
        maxHp: 200,
        maxMp: 100,
        currentHp: 200,
        currentMp: 100,
        recoverHp: 0.02,
        recoverMp: 0.02,
        recoveryInterval: 1.0,
        atkInterval: 1.5,
        atkSpeed: 1.0,
        castSpeed: 1.0,
        def: 10,
        spellResistance: 5,
        meleeAccuracy: 0.1,
        rangedAccuracy: 0.05,
        magicAccuracy: 0.0,
        meleeDodge: 0.05,
        rangedDodge: 0.0,
        magicDodge: 0.0,
        penetrate: 0.1,
        criticalHit: 0.1,
        toughness: 0.2,
        enmity: 0.5,
        damageIncreasePhysical: 0.2,
      },
      skills: [],
      skillPoints: 5,
      equipment: {},
      inventory: { items: [], maxSlots: 30 },
      location: { mapId: 'test_area', x: 0, y: 0, z: 0 },
      battleStatus: { isInBattle: false, statusEffects: [] },
      statistics: {
        totalPlayTime: 0,
        monstersKilled: 0,
        itemsCollected: 0,
        questsCompleted: 0,
        deathCount: 0,
        goldEarned: 0,
      },
    });

    return await character.save();
  }

  /**
   * 创建测试技能
   */
  private async createTestSkill(id: string, name: string, level: number, cooldown: number): Promise<any> {
    const skill = new Skill({
      id: id + '_' + Date.now(),
      name,
      description: '性能测试技能',
      manaCost: 20,
      castTime: 1.0,
      cooldown,
      damageType: 'physical',
      targetType: 'enemy',
      baseDamageMultiplier: 1.2,
      level,
      maxLevel: 10,
      requirements: {
        level,
        skillPoints: 1,
        prerequisiteSkills: [],
        attributes: {},
      },
      effects: [],
    });

    return await skill.save();
  }
}

export const performanceTestRunner = new PerformanceTestRunner();
