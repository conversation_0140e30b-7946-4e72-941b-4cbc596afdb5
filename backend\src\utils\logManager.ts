import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';

/**
 * 日志级别枚举
 */
export enum LogLevel {
    ERROR = 'error',
    WARN = 'warn',
    INFO = 'info',
    HTTP = 'http',
    VERBOSE = 'verbose',
    DEBUG = 'debug',
    SILLY = 'silly'
}

/**
 * 日志类型枚举
 */
export enum LogType {
    APPLICATION = 'application',
    ACCESS = 'access',
    ERROR = 'error',
    SECURITY = 'security',
    PERFORMANCE = 'performance',
    AUDIT = 'audit'
}

/**
 * 日志配置接口
 */
export interface ILogConfig {
    level: LogLevel;
    enableConsole: boolean;
    enableFile: boolean;
    logDir: string;
    maxFiles: string;
    maxSize: string;
    datePattern: string;
    format: winston.Logform.Format;
}

/**
 * 日志管理器
 */
export class LogManager {
    private static instance: LogManager;
    private loggers: Map<LogType, winston.Logger> = new Map();
    private config: ILogConfig;

    private constructor() {
        this.config = this.getDefaultConfig();
        this.initializeLoggers();
    }

    public static getInstance(): LogManager {
        if (!LogManager.instance) {
            LogManager.instance = new LogManager();
        }
        return LogManager.instance;
    }

    /**
     * 获取默认配置
     */
    private getDefaultConfig(): ILogConfig {
        const logDir = process.env.LOG_DIR || path.join(process.cwd(), 'logs');
        
        // 确保日志目录存在
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }

        return {
            level: (process.env.LOG_LEVEL as LogLevel) || LogLevel.INFO,
            enableConsole: process.env.NODE_ENV !== 'production',
            enableFile: true,
            logDir,
            maxFiles: '30d',
            maxSize: '20m',
            datePattern: 'YYYY-MM-DD',
            format: winston.format.combine(
                winston.format.timestamp({
                    format: 'YYYY-MM-DD HH:mm:ss.SSS'
                }),
                winston.format.errors({ stack: true }),
                winston.format.json(),
                winston.format.prettyPrint()
            )
        };
    }

    /**
     * 初始化所有日志器
     */
    private initializeLoggers(): void {
        Object.values(LogType).forEach(logType => {
            this.loggers.set(logType, this.createLogger(logType));
        });
    }

    /**
     * 创建特定类型的日志器
     */
    private createLogger(logType: LogType): winston.Logger {
        const transports: winston.transport[] = [];

        // 控制台输出
        if (this.config.enableConsole) {
            transports.push(new winston.transports.Console({
                format: winston.format.combine(
                    winston.format.colorize(),
                    winston.format.simple(),
                    winston.format.printf(({ timestamp, level, message, ...meta }) => {
                        const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
                        return `${timestamp} [${level}] [${logType.toUpperCase()}]: ${message} ${metaStr}`;
                    })
                )
            }));
        }

        // 文件输出
        if (this.config.enableFile) {
            // 普通日志文件
            transports.push(new DailyRotateFile({
                filename: path.join(this.config.logDir, `${logType}-%DATE%.log`),
                datePattern: this.config.datePattern,
                maxFiles: this.config.maxFiles,
                maxSize: this.config.maxSize,
                format: this.config.format,
                level: this.config.level
            }));

            // 错误日志文件（只记录错误级别）
            if (logType === LogType.ERROR || logType === LogType.APPLICATION) {
                transports.push(new DailyRotateFile({
                    filename: path.join(this.config.logDir, `error-%DATE%.log`),
                    datePattern: this.config.datePattern,
                    maxFiles: this.config.maxFiles,
                    maxSize: this.config.maxSize,
                    format: this.config.format,
                    level: LogLevel.ERROR
                }));
            }
        }

        return winston.createLogger({
            level: this.config.level,
            format: this.config.format,
            transports,
            exitOnError: false
        });
    }

    /**
     * 获取指定类型的日志器
     */
    public getLogger(logType: LogType = LogType.APPLICATION): winston.Logger {
        return this.loggers.get(logType) || this.loggers.get(LogType.APPLICATION)!;
    }

    /**
     * 记录应用日志
     */
    public log(level: LogLevel, message: string, meta?: any, logType: LogType = LogType.APPLICATION): void {
        const logger = this.getLogger(logType);
        logger.log(level, message, meta);
    }

    /**
     * 记录错误日志
     */
    public error(message: string, error?: Error | any, meta?: any): void {
        const logger = this.getLogger(LogType.ERROR);
        const errorMeta = {
            ...meta,
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : error
        };
        logger.error(message, errorMeta);
    }

    /**
     * 记录警告日志
     */
    public warn(message: string, meta?: any): void {
        this.log(LogLevel.WARN, message, meta);
    }

    /**
     * 记录信息日志
     */
    public info(message: string, meta?: any): void {
        this.log(LogLevel.INFO, message, meta);
    }

    /**
     * 记录调试日志
     */
    public debug(message: string, meta?: any): void {
        this.log(LogLevel.DEBUG, message, meta);
    }

    /**
     * 记录访问日志
     */
    public access(message: string, meta?: any): void {
        this.log(LogLevel.INFO, message, meta, LogType.ACCESS);
    }

    /**
     * 记录安全日志
     */
    public security(message: string, meta?: any): void {
        this.log(LogLevel.WARN, message, meta, LogType.SECURITY);
    }

    /**
     * 记录性能日志
     */
    public performance(message: string, meta?: any): void {
        this.log(LogLevel.INFO, message, meta, LogType.PERFORMANCE);
    }

    /**
     * 记录审计日志
     */
    public audit(message: string, meta?: any): void {
        this.log(LogLevel.INFO, message, meta, LogType.AUDIT);
    }

    /**
     * 更新配置
     */
    public updateConfig(newConfig: Partial<ILogConfig>): void {
        this.config = { ...this.config, ...newConfig };
        this.initializeLoggers();
    }

    /**
     * 获取日志统计信息
     */
    public async getLogStats(): Promise<any> {
        const stats = {
            logTypes: Object.values(LogType),
            logLevels: Object.values(LogLevel),
            config: this.config,
            files: []
        };

        try {
            const files = fs.readdirSync(this.config.logDir);
            stats.files = files.map(file => {
                const filePath = path.join(this.config.logDir, file);
                const stat = fs.statSync(filePath);
                return {
                    name: file,
                    size: stat.size,
                    created: stat.birthtime,
                    modified: stat.mtime
                };
            });
        } catch (error) {
            this.error('Failed to get log stats', error);
        }

        return stats;
    }

    /**
     * 清理旧日志文件
     */
    public async cleanupLogs(daysToKeep: number = 30): Promise<void> {
        try {
            const files = fs.readdirSync(this.config.logDir);
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            let deletedCount = 0;
            for (const file of files) {
                const filePath = path.join(this.config.logDir, file);
                const stat = fs.statSync(filePath);
                
                if (stat.mtime < cutoffDate) {
                    fs.unlinkSync(filePath);
                    deletedCount++;
                }
            }

            this.info('Log cleanup completed', {
                daysToKeep,
                deletedFiles: deletedCount
            });
        } catch (error) {
            this.error('Failed to cleanup logs', error);
        }
    }

    /**
     * 压缩日志文件
     */
    public async compressLogs(): Promise<void> {
        // 这里可以实现日志压缩逻辑
        // 例如使用 zlib 压缩旧的日志文件
        this.info('Log compression feature not implemented yet');
    }

    /**
     * 导出日志
     */
    public async exportLogs(startDate: Date, endDate: Date, logType?: LogType): Promise<string[]> {
        const logs: string[] = [];
        
        try {
            const files = fs.readdirSync(this.config.logDir);
            const targetFiles = files.filter(file => {
                if (logType && !file.startsWith(logType)) {
                    return false;
                }
                
                // 简单的日期过滤（可以改进）
                const stat = fs.statSync(path.join(this.config.logDir, file));
                return stat.mtime >= startDate && stat.mtime <= endDate;
            });

            for (const file of targetFiles) {
                const content = fs.readFileSync(path.join(this.config.logDir, file), 'utf8');
                logs.push(content);
            }
        } catch (error) {
            this.error('Failed to export logs', error);
        }

        return logs;
    }
}

// 导出单例实例
export const logManager = LogManager.getInstance();

// 导出便捷方法
export const log = {
    error: (message: string, error?: Error | any, meta?: any) => logManager.error(message, error, meta),
    warn: (message: string, meta?: any) => logManager.warn(message, meta),
    info: (message: string, meta?: any) => logManager.info(message, meta),
    debug: (message: string, meta?: any) => logManager.debug(message, meta),
    access: (message: string, meta?: any) => logManager.access(message, meta),
    security: (message: string, meta?: any) => logManager.security(message, meta),
    performance: (message: string, meta?: any) => logManager.performance(message, meta),
    audit: (message: string, meta?: any) => logManager.audit(message, meta),
};
