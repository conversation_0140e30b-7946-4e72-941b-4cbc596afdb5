/**
 * 系统发现代理 - 自动发现和分析项目中的系统
 * 使用AI技术智能识别系统边界和特征
 */
import { SystemInfo } from '../types';
export interface ProjectStructure {
    rootPath: string;
    directories: DirectoryInfo[];
    files: FileInfo[];
    packageInfo?: PackageInfo;
}
export interface DirectoryInfo {
    name: string;
    path: string;
    files: FileInfo[];
    subdirectories: DirectoryInfo[];
}
export interface FileInfo {
    name: string;
    path: string;
    extension: string;
    size: number;
    content?: string;
}
export interface SystemBoundary {
    name: string;
    path: string;
    files: string[];
    entryPoints: string[];
    exports: string[];
    imports: string[];
}
export declare class SystemDiscoveryAgent {
    private projectStructureCache;
    private systemInfoCache;
    /**
     * 自动发现项目中的所有系统
     */
    discoverSystems(projectPath: string): Promise<SystemInfo[]>;
    /**
     * 分析指定路径的系统
     */
    analyzeSystemAtPath(systemPath: string): Promise<SystemInfo>;
    /**
     * 智能系统分类
     * 使用模式匹配和启发式规则识别系统类型
     */
    classifySystem(systemBoundary: SystemBoundary): Promise<SystemInfo>;
    /**
     * 扫描项目结构
     */
    private scanProjectStructure;
    /**
     * 识别系统边界
     */
    private identifySystemBoundaries;
    /**
     * 基于目录结构识别系统边界
     */
    private identifyByDirectory;
    /**
     * 基于文件模式识别系统边界
     */
    private identifyByPatterns;
    /**
     * 分析系统模式
     */
    private analyzeSystemPatterns;
    /**
     * 推断系统类型
     */
    private inferSystemType;
    /**
     * 评估系统复杂度
     */
    private evaluateComplexity;
    private shouldIgnoreDirectory;
    private scanDirectory;
    private scanFile;
    private readPackageInfo;
    private collectFilePaths;
    private identifyEntryPoints;
    private findFilesByPattern;
    private findCommonPath;
    private identifyEntryPointsFromFiles;
    private mergeBoundaries;
    private extractFeatures;
    private extractDependencies;
    private determineTestingPriority;
    private recommendTestingStrategies;
    private extractNamingPatterns;
    private extractStructuralPatterns;
    private extractContentPatterns;
    private scanSystemDirectory;
    private analyzeSystemFeatures;
    private determineSystemType;
    private assessSystemComplexity;
    private analyzeDependencies;
}
interface PackageInfo {
    name: string;
    version: string;
    dependencies?: Record<string, string>;
    devDependencies?: Record<string, string>;
}
export {};
//# sourceMappingURL=SystemDiscoveryAgent.d.ts.map