import mongoose = require('mongoose');
import { Logger } from './logger';
import { DatabaseConfig } from '../config/database';

/**
 * 事务选项接口
 */
export interface TransactionOptions {
  readPreference?: string;
  readConcern?: { level: string };
  writeConcern?: { w: string | number; j?: boolean; wtimeout?: number };
  maxTimeMS?: number;
}

/**
 * 批量操作结果接口
 */
export interface BulkOperationResult {
  insertedCount: number;
  matchedCount: number;
  modifiedCount: number;
  deletedCount: number;
  upsertedCount: number;
  insertedIds: { [key: number]: any };
  upsertedIds: { [key: number]: any };
}

/**
 * 聚合管道选项接口
 */
export interface AggregationOptions {
  allowDiskUse?: boolean;
  maxTimeMS?: number;
  batchSize?: number;
  readPreference?: string;
  hint?: string | object;
}

/**
 * 分页选项接口
 */
export interface PaginateOptions {
  page?: number;
  limit?: number;
  sort?: string | object;
  select?: string | object;
  populate?: string | object;
  lean?: boolean;
}

/**
 * 分页结果接口
 */
export interface PaginateResult<T> {
  docs: T[];
  totalDocs: number;
  limit: number;
  page: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  nextPage?: number;
  prevPage?: number;
  pagingCounter: number;
}

/**
 * 数据迁移选项接口
 */
export interface MigrationOptions {
  batchSize?: number;
  dryRun?: boolean;
  skipErrors?: boolean;
  progressCallback?: (progress: MigrationProgress) => void;
}

/**
 * 数据迁移进度接口
 */
export interface MigrationProgress {
  processed: number;
  migrated: number;
  errors: number;
  total: number;
  percentage: number;
}

/**
 * 数据一致性检查规则接口
 */
export interface ConsistencyRule {
  name: string;
  description?: string;
  check: (doc: any) => boolean | Promise<boolean>;
  fix?: (doc: any) => any | Promise<any>;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * 数据一致性检查结果接口
 */
export interface ConsistencyCheckResult {
  total: number;
  issues: number;
  fixed: number;
  errors: number;
  ruleResults: Array<{
    rule: string;
    issues: number;
    fixed: number;
    errors: number;
  }>;
}

/**
 * 数据库工具类
 */
export class DatabaseUtils {
  private static instance: DatabaseUtils;
  private dbConfig: DatabaseConfig;

  private constructor() {
    this.dbConfig = DatabaseConfig.getInstance();
  }

  public static getInstance(): DatabaseUtils {
    if (!DatabaseUtils.instance) {
      DatabaseUtils.instance = new DatabaseUtils();
    }
    return DatabaseUtils.instance;
  }

  /**
   * 执行事务
   */
  public async withTransaction<T>(
    operation: (session: mongoose.ClientSession) => Promise<T>,
    options?: TransactionOptions
  ): Promise<T> {
    const session = await mongoose.startSession();
    
    try {
      Logger.info('开始数据库事务');
      const startTime = Date.now();

      // 配置事务选项
      const transactionOptions: mongoose.TransactionOptions = {
        readPreference: options?.readPreference as any,
        readConcern: options?.readConcern,
        writeConcern: options?.writeConcern,
        maxTimeMS: options?.maxTimeMS,
      };

      const result = await session.withTransaction(async () => {
        return await operation(session);
      }, transactionOptions);

      const duration = Date.now() - startTime;
      Logger.info('数据库事务完成', { duration: `${duration}ms` });

      return result;
    } catch (error) {
      Logger.error('数据库事务失败', error);
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * 批量操作
   */
  public async bulkWrite(
    model: mongoose.Model<any>,
    operations: any[],
    options?: mongoose.BulkWriteOptions
  ): Promise<BulkOperationResult> {
    try {
      Logger.info('开始批量操作', { 
        model: model.modelName, 
        operationCount: operations.length 
      });
      const startTime = Date.now();

      const result = await model.bulkWrite(operations, {
        ordered: false, // 默认无序执行，提高性能
        ...options,
      });

      const duration = Date.now() - startTime;
      Logger.info('批量操作完成', {
        model: model.modelName,
        duration: `${duration}ms`,
        insertedCount: result.insertedCount,
        modifiedCount: result.modifiedCount,
        deletedCount: result.deletedCount,
      });

      return {
        insertedCount: result.insertedCount,
        matchedCount: result.matchedCount,
        modifiedCount: result.modifiedCount,
        deletedCount: result.deletedCount,
        upsertedCount: result.upsertedCount,
        insertedIds: result.insertedIds,
        upsertedIds: result.upsertedIds,
      };
    } catch (error) {
      Logger.error('批量操作失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 聚合查询
   */
  public async aggregate<T>(
    model: mongoose.Model<any>,
    pipeline: any[],
    options?: AggregationOptions
  ): Promise<T[]> {
    try {
      Logger.info('开始聚合查询', { 
        model: model.modelName, 
        pipelineStages: pipeline.length 
      });
      const startTime = Date.now();

      const aggregation = model.aggregate(pipeline);

      // 应用选项
      if (options?.allowDiskUse) {
        aggregation.allowDiskUse(options.allowDiskUse);
      }
      if (options?.maxTimeMS) {
        aggregation.maxTimeMS(options.maxTimeMS);
      }
      if (options?.batchSize) {
        aggregation.option({ batchSize: options.batchSize });
      }
      if (options?.readPreference) {
        aggregation.read(options.readPreference as any);
      }
      if (options?.hint) {
        aggregation.hint(options.hint);
      }

      const results = await aggregation.exec();

      const duration = Date.now() - startTime;
      Logger.info('聚合查询完成', {
        model: model.modelName,
        duration: `${duration}ms`,
        resultCount: results.length,
      });

      return results;
    } catch (error) {
      Logger.error('聚合查询失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 创建索引
   */
  public async createIndex(
    model: mongoose.Model<any>,
    indexSpec: any,
    options?: mongoose.CreateIndexesOptions
  ): Promise<string> {
    try {
      Logger.info('开始创建索引', { 
        model: model.modelName, 
        indexSpec 
      });
      const startTime = Date.now();

      const indexName = await model.createIndexes([{ key: indexSpec, ...options }]);

      const duration = Date.now() - startTime;
      Logger.info('索引创建完成', {
        model: model.modelName,
        duration: `${duration}ms`,
        indexName,
      });

      return indexName[0];
    } catch (error) {
      Logger.error('索引创建失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 删除索引
   */
  public async dropIndex(
    model: mongoose.Model<any>,
    indexName: string
  ): Promise<void> {
    try {
      Logger.info('开始删除索引', { 
        model: model.modelName, 
        indexName 
      });

      await model.collection.dropIndex(indexName);

      Logger.info('索引删除完成', {
        model: model.modelName,
        indexName,
      });
    } catch (error) {
      Logger.error('索引删除失败', { model: model.modelName, indexName, error });
      throw error;
    }
  }

  /**
   * 获取集合统计信息
   */
  public async getCollectionStats(model: mongoose.Model<any>): Promise<any> {
    try {
      const stats = await model.collection.stats();
      
      Logger.info('获取集合统计信息', {
        model: model.modelName,
        count: stats.count,
        size: stats.size,
        avgObjSize: stats.avgObjSize,
      });

      return {
        collection: model.collection.collectionName,
        count: stats.count,
        size: stats.size,
        avgObjSize: stats.avgObjSize,
        storageSize: stats.storageSize,
        indexes: stats.nindexes,
        indexSize: stats.totalIndexSize,
      };
    } catch (error) {
      Logger.error('获取集合统计信息失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 数据库备份（导出集合数据）
   */
  public async exportCollection(
    model: mongoose.Model<any>,
    query: any = {},
    options?: { limit?: number; sort?: any }
  ): Promise<any[]> {
    try {
      Logger.info('开始导出集合数据', { 
        model: model.modelName,
        query,
        options 
      });
      const startTime = Date.now();

      let queryBuilder = model.find(query).lean();

      if (options?.sort) {
        queryBuilder = queryBuilder.sort(options.sort);
      }
      if (options?.limit) {
        queryBuilder = queryBuilder.limit(options.limit);
      }

      const data = await queryBuilder.exec();

      const duration = Date.now() - startTime;
      Logger.info('集合数据导出完成', {
        model: model.modelName,
        duration: `${duration}ms`,
        recordCount: data.length,
      });

      return data;
    } catch (error) {
      Logger.error('集合数据导出失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 数据库恢复（导入集合数据）
   */
  public async importCollection(
    model: mongoose.Model<any>,
    data: any[],
    options?: { upsert?: boolean; batchSize?: number }
  ): Promise<BulkOperationResult> {
    try {
      Logger.info('开始导入集合数据', { 
        model: model.modelName,
        recordCount: data.length,
        options 
      });
      const startTime = Date.now();

      const batchSize = options?.batchSize || 1000;
      const operations = [];

      for (const item of data) {
        if (options?.upsert && item._id) {
          operations.push({
            replaceOne: {
              filter: { _id: item._id },
              replacement: item,
              upsert: true,
            },
          });
        } else {
          operations.push({
            insertOne: {
              document: item,
            },
          });
        }

        // 批量执行
        if (operations.length >= batchSize) {
          await this.bulkWrite(model, operations);
          operations.length = 0; // 清空数组
        }
      }

      // 执行剩余操作
      let result: BulkOperationResult = {
        insertedCount: 0,
        matchedCount: 0,
        modifiedCount: 0,
        deletedCount: 0,
        upsertedCount: 0,
        insertedIds: {},
        upsertedIds: {},
      };

      if (operations.length > 0) {
        result = await this.bulkWrite(model, operations);
      }

      const duration = Date.now() - startTime;
      Logger.info('集合数据导入完成', {
        model: model.modelName,
        duration: `${duration}ms`,
        insertedCount: result.insertedCount,
        modifiedCount: result.modifiedCount,
      });

      return result;
    } catch (error) {
      Logger.error('集合数据导入失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 清理过期数据
   */
  public async cleanupExpiredData(
    model: mongoose.Model<any>,
    dateField: string,
    expirationDays: number
  ): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - expirationDays);

      Logger.info('开始清理过期数据', {
        model: model.modelName,
        dateField,
        expirationDays,
        cutoffDate,
      });

      const result = await model.deleteMany({
        [dateField]: { $lt: cutoffDate },
      });

      Logger.info('过期数据清理完成', {
        model: model.modelName,
        deletedCount: result.deletedCount,
      });

      return result.deletedCount || 0;
    } catch (error) {
      Logger.error('清理过期数据失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 分页查询
   */
  public async paginate<T>(
    model: mongoose.Model<any>,
    filter: any = {},
    options: PaginateOptions = {}
  ): Promise<PaginateResult<T>> {
    try {
      const page = Math.max(1, options.page || 1);
      const limit = Math.min(100, Math.max(1, options.limit || 20));
      const skip = (page - 1) * limit;

      Logger.info('开始分页查询', {
        model: model.modelName,
        filter,
        page,
        limit,
      });
      const startTime = Date.now();

      // 构建查询
      let query = model.find(filter);

      // 应用选项
      if (options.select) {
        query = query.select(options.select);
      }
      if (options.sort) {
        query = query.sort(options.sort);
      }
      if (options.populate) {
        query = query.populate(options.populate);
      }
      if (options.lean) {
        query = query.lean();
      }

      // 执行分页查询
      query = query.skip(skip).limit(limit);

      // 并行执行查询和计数
      const [docs, totalDocs] = await Promise.all([
        query.exec(),
        model.countDocuments(filter),
      ]);

      const totalPages = Math.ceil(totalDocs / limit);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      const result: PaginateResult<T> = {
        docs,
        totalDocs,
        limit,
        page,
        totalPages,
        hasNextPage,
        hasPrevPage,
        nextPage: hasNextPage ? page + 1 : undefined,
        prevPage: hasPrevPage ? page - 1 : undefined,
        pagingCounter: skip + 1,
      };

      const duration = Date.now() - startTime;
      Logger.info('分页查询完成', {
        model: model.modelName,
        duration: `${duration}ms`,
        totalDocs,
        returnedDocs: docs.length,
        page,
        totalPages,
      });

      return result;
    } catch (error) {
      Logger.error('分页查询失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 数据迁移工具
   */
  public async migrateData(
    sourceModel: mongoose.Model<any>,
    targetModel: mongoose.Model<any>,
    transformFn: (doc: any) => any | Promise<any>,
    options: MigrationOptions = {}
  ): Promise<MigrationProgress> {
    try {
      const batchSize = options.batchSize || 100;
      const dryRun = options.dryRun || false;
      const skipErrors = options.skipErrors || false;

      Logger.info('开始数据迁移', {
        sourceModel: sourceModel.modelName,
        targetModel: targetModel.modelName,
        batchSize,
        dryRun,
        skipErrors,
      });

      // 获取总数
      const total = await sourceModel.countDocuments({});
      let processed = 0;
      let migrated = 0;
      let errors = 0;
      let skip = 0;

      while (skip < total) {
        const docs = await sourceModel.find({}).skip(skip).limit(batchSize).lean();

        if (docs.length === 0) {
          break;
        }

        const transformedDocs = [];

        for (const doc of docs) {
          try {
            const transformed = await transformFn(doc);
            if (transformed) {
              transformedDocs.push(transformed);
            }
            processed++;
          } catch (error) {
            Logger.error('数据转换失败', { docId: doc._id, error });
            errors++;
            if (!skipErrors) {
              throw error;
            }
          }
        }

        if (!dryRun && transformedDocs.length > 0) {
          try {
            await targetModel.insertMany(transformedDocs, { ordered: false });
            migrated += transformedDocs.length;
          } catch (error) {
            Logger.error('批量插入失败', error);
            if (!skipErrors) {
              throw error;
            }
            errors += transformedDocs.length;
          }
        } else if (dryRun) {
          migrated += transformedDocs.length;
        }

        skip += batchSize;

        const progress: MigrationProgress = {
          processed,
          migrated,
          errors,
          total,
          percentage: Math.round((processed / total) * 100),
        };

        // 调用进度回调
        if (options.progressCallback) {
          options.progressCallback(progress);
        }

        Logger.info('数据迁移进度', progress);
      }

      const finalProgress: MigrationProgress = {
        processed,
        migrated,
        errors,
        total,
        percentage: 100,
      };

      Logger.info('数据迁移完成', {
        sourceModel: sourceModel.modelName,
        targetModel: targetModel.modelName,
        ...finalProgress,
        dryRun,
      });

      return finalProgress;
    } catch (error) {
      Logger.error('数据迁移失败', error);
      throw error;
    }
  }

  /**
   * 数据一致性检查
   */
  public async checkDataConsistency(
    model: mongoose.Model<any>,
    rules: ConsistencyRule[]
  ): Promise<ConsistencyCheckResult> {
    try {
      Logger.info('开始数据一致性检查', {
        model: model.modelName,
        rulesCount: rules.length,
      });

      const total = await model.countDocuments({});
      let processed = 0;
      let totalIssues = 0;
      let totalFixed = 0;
      let totalErrors = 0;
      const ruleResults: Array<{
        rule: string;
        issues: number;
        fixed: number;
        errors: number;
      }> = [];

      // 初始化规则结果
      for (const rule of rules) {
        ruleResults.push({
          rule: rule.name,
          issues: 0,
          fixed: 0,
          errors: 0,
        });
      }

      const batchSize = 100;
      let skip = 0;

      while (skip < total) {
        const docs = await model.find({}).skip(skip).limit(batchSize);

        if (docs.length === 0) {
          break;
        }

        for (const doc of docs) {
          processed++;

          for (let i = 0; i < rules.length; i++) {
            const rule = rules[i];
            const ruleResult = ruleResults[i];

            try {
              const isValid = await rule.check(doc);

              if (!isValid) {
                ruleResult.issues++;
                totalIssues++;

                Logger.warn('数据一致性问题', {
                  model: model.modelName,
                  docId: doc._id,
                  rule: rule.name,
                  severity: rule.severity || 'medium',
                });

                if (rule.fix) {
                  try {
                    const fixedData = await rule.fix(doc);
                    if (fixedData) {
                      await doc.updateOne(fixedData);
                      ruleResult.fixed++;
                      totalFixed++;

                      Logger.info('数据一致性问题已修复', {
                        model: model.modelName,
                        docId: doc._id,
                        rule: rule.name,
                      });
                    }
                  } catch (fixError) {
                    ruleResult.errors++;
                    totalErrors++;
                    Logger.error('数据一致性修复失败', {
                      model: model.modelName,
                      docId: doc._id,
                      rule: rule.name,
                      error: fixError,
                    });
                  }
                }
              }
            } catch (checkError) {
              ruleResult.errors++;
              totalErrors++;
              Logger.error('数据一致性检查失败', {
                model: model.modelName,
                docId: doc._id,
                rule: rule.name,
                error: checkError,
              });
            }
          }
        }

        skip += batchSize;

        Logger.info('数据一致性检查进度', {
          model: model.modelName,
          processed,
          total,
          percentage: Math.round((processed / total) * 100),
        });
      }

      const result: ConsistencyCheckResult = {
        total: processed,
        issues: totalIssues,
        fixed: totalFixed,
        errors: totalErrors,
        ruleResults,
      };

      Logger.info('数据一致性检查完成', {
        model: model.modelName,
        ...result,
      });

      return result;
    } catch (error) {
      Logger.error('数据一致性检查失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 数据库性能测试
   */
  public async performanceTest(
    model: mongoose.Model<any>,
    operations: {
      insert?: number;
      find?: number;
      update?: number;
      delete?: number;
    }
  ): Promise<any> {
    try {
      Logger.info('开始数据库性能测试', {
        model: model.modelName,
        operations,
      });

      const results: any = {};

      // 插入性能测试
      if (operations.insert) {
        const startTime = Date.now();
        const testDocs = Array.from({ length: operations.insert }, (_, i) => ({
          testField: `perf_test_${i}`,
          testNumber: i,
          testDate: new Date(),
          testBoolean: i % 2 === 0,
          testArray: [`tag${i}`, `category${i % 3}`],
        }));

        await model.insertMany(testDocs);
        results.insert = {
          count: operations.insert,
          duration: Date.now() - startTime,
          avgPerDoc: (Date.now() - startTime) / operations.insert,
          docsPerSecond: Math.round(operations.insert / ((Date.now() - startTime) / 1000)),
        };
      }

      // 查询性能测试
      if (operations.find) {
        const startTime = Date.now();
        const promises = [];

        for (let i = 0; i < operations.find; i++) {
          promises.push(model.findOne({ testField: `perf_test_${i}` }));
        }

        await Promise.all(promises);
        results.find = {
          count: operations.find,
          duration: Date.now() - startTime,
          avgPerQuery: (Date.now() - startTime) / operations.find,
          queriesPerSecond: Math.round(operations.find / ((Date.now() - startTime) / 1000)),
        };
      }

      // 更新性能测试
      if (operations.update) {
        const startTime = Date.now();
        const promises = [];

        for (let i = 0; i < operations.update; i++) {
          promises.push(
            model.updateOne(
              { testField: `perf_test_${i}` },
              { testNumber: i + 1000, updatedAt: new Date() }
            )
          );
        }

        await Promise.all(promises);
        results.update = {
          count: operations.update,
          duration: Date.now() - startTime,
          avgPerUpdate: (Date.now() - startTime) / operations.update,
          updatesPerSecond: Math.round(operations.update / ((Date.now() - startTime) / 1000)),
        };
      }

      // 删除性能测试和清理
      if (operations.delete || operations.insert || operations.find || operations.update) {
        const startTime = Date.now();
        const deleteResult = await model.deleteMany({ testField: /^perf_test_/ });

        if (operations.delete) {
          results.delete = {
            deletedCount: deleteResult.deletedCount,
            duration: Date.now() - startTime,
          };
        }
      }

      Logger.info('数据库性能测试完成', {
        model: model.modelName,
        results,
      });

      return results;
    } catch (error) {
      Logger.error('数据库性能测试失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 获取索引信息
   */
  public async getIndexes(model: mongoose.Model<any>): Promise<any[]> {
    try {
      const indexes = await model.collection.indexes();

      Logger.info('获取索引信息', {
        model: model.modelName,
        indexCount: indexes.length,
      });

      return indexes.map(index => ({
        name: index.name,
        key: index.key,
        unique: index.unique || false,
        sparse: index.sparse || false,
        background: index.background || false,
        size: index.size || 0,
      }));
    } catch (error) {
      Logger.error('获取索引信息失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 优化集合
   */
  public async optimizeCollection(model: mongoose.Model<any>): Promise<any> {
    try {
      Logger.info('开始优化集合', { model: model.modelName });

      // 重建索引
      await model.collection.reIndex();

      // 获取优化后的统计信息
      const stats = await this.getCollectionStats(model);

      Logger.info('集合优化完成', {
        model: model.modelName,
        stats,
      });

      return {
        reindexed: true,
        stats,
      };
    } catch (error) {
      Logger.error('集合优化失败', { model: model.modelName, error });
      throw error;
    }
  }

  /**
   * 获取数据库连接信息
   */
  public getConnectionInfo(): any {
    return this.dbConfig.getStats();
  }
}
