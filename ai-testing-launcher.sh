#!/bin/bash

echo "🤖 启动AI测试框架..."
echo

cd "$(dirname "$0")"

echo "📋 可用命令:"
echo "  1. 完整测试 (npm run test:ai)"
echo "  2. 快速测试 (npm run test:ai:simple)"
echo "  3. 代码质量修复 (npm run fix:quality)"
echo "  4. 启动测试服务器 (npm run server:test)"
echo

read -p "请选择 (1-4): " choice

case $choice in
    1)
        npm run test:ai
        ;;
    2)
        npm run test:ai:simple
        ;;
    3)
        npm run fix:quality
        ;;
    4)
        npm run server:test
        ;;
    *)
        echo "无效选择"
        ;;
esac
