# 网络通信架构

> 📖 **导航**: [返回主页](./README.md) | [数据管理](./data-management.md) | [小程序优化](./miniprogram-optimization.md)

## 🌐 网络架构设计

### 通信架构概览
```typescript
// 网络通信架构
export class NetworkArchitecture {
    // 通信层级
    static readonly CommunicationLayers = {
        // 应用层 - 游戏逻辑
        APPLICATION: {
            description: '游戏业务逻辑层',
            protocols: ['Game Protocol', 'Chat Protocol', 'Battle Protocol'],
            components: ['GameClient', 'ChatClient', 'BattleClient']
        },
        
        // 传输层 - 网络传输
        TRANSPORT: {
            description: '网络传输层',
            protocols: ['WebSocket', 'HTTP/HTTPS', 'UDP (模拟)'],
            components: ['ConnectionManager', 'MessageQueue', 'ReconnectHandler']
        },
        
        // 平台层 - 小程序API
        PLATFORM: {
            description: '小程序平台层',
            protocols: ['WeChat API', 'Douyin API', 'Cloud Functions'],
            components: ['PlatformAdapter', 'CloudService', 'AuthService']
        }
    };
    
    // 消息类型
    static readonly MessageTypes = {
        // 系统消息
        SYSTEM: {
            HEARTBEAT: 'system_heartbeat',
            AUTH: 'system_auth',
            ERROR: 'system_error'
        },
        
        // 游戏消息
        GAME: {
            PLAYER_UPDATE: 'game_player_update',
            SKILL_USE: 'game_skill_use',
            BATTLE_ACTION: 'game_battle_action',
            QUEST_UPDATE: 'game_quest_update'
        },
        
        // 社交消息
        SOCIAL: {
            CHAT_MESSAGE: 'social_chat_message',
            FRIEND_REQUEST: 'social_friend_request',
            GUILD_INVITE: 'social_guild_invite'
        }
    };
}
```

### 网络管理器实现
```typescript
// 网络管理器
@ccclass('NetworkManager')
export class NetworkManager extends BaseManager {
    private static _instance: NetworkManager = null;
    
    // 连接管理
    private _connections: Map<string, IConnection> = new Map();
    private _messageQueue: IMessage[] = [];
    private _isOnline: boolean = true;
    
    // 重连配置
    private _reconnectConfig = {
        maxAttempts: 5,
        baseDelay: 1000,
        maxDelay: 30000,
        backoffMultiplier: 2
    };
    
    public static getInstance(): NetworkManager {
        if (!this._instance) {
            this._instance = new NetworkManager();
        }
        return this._instance;
    }
    
    protected async initializeManager(): Promise<void> {
        this.setupNetworkMonitoring();
        this.initializeConnections();
        this.startMessageProcessor();
    }
    
    // 建立连接
    public async connect(endpoint: string, options: IConnectionOptions = {}): Promise<IConnection> {
        const connectionId = this.generateConnectionId();
        
        try {
            const connection = await this.createConnection(endpoint, options);
            connection.id = connectionId;
            
            this._connections.set(connectionId, connection);
            this.setupConnectionHandlers(connection);
            
            console.log(`Connected to ${endpoint} with ID: ${connectionId}`);
            return connection;
            
        } catch (error) {
            console.error(`Failed to connect to ${endpoint}:`, error);
            throw error;
        }
    }
    
    // 发送消息
    public async sendMessage(message: IMessage, connectionId?: string): Promise<void> {
        // 添加消息头
        message.id = this.generateMessageId();
        message.timestamp = Date.now();
        message.clientId = this.getClientId();
        
        if (!this._isOnline) {
            // 离线时加入队列
            this._messageQueue.push(message);
            return;
        }
        
        try {
            if (connectionId) {
                // 发送到指定连接
                const connection = this._connections.get(connectionId);
                if (connection) {
                    await this.sendToConnection(connection, message);
                } else {
                    throw new Error(`Connection not found: ${connectionId}`);
                }
            } else {
                // 广播到所有连接
                await this.broadcastMessage(message);
            }
        } catch (error) {
            console.error('Failed to send message:', error);
            // 发送失败时加入重试队列
            this._messageQueue.push(message);
        }
    }
    
    // 处理接收到的消息
    private handleIncomingMessage(message: IMessage, connection: IConnection): void {
        try {
            // 验证消息格式
            if (!this.validateMessage(message)) {
                console.warn('Invalid message received:', message);
                return;
            }
            
            // 根据消息类型分发处理
            switch (message.type) {
                case NetworkArchitecture.MessageTypes.SYSTEM.HEARTBEAT:
                    this.handleHeartbeat(message, connection);
                    break;
                    
                case NetworkArchitecture.MessageTypes.GAME.PLAYER_UPDATE:
                    this.handlePlayerUpdate(message);
                    break;
                    
                case NetworkArchitecture.MessageTypes.GAME.SKILL_USE:
                    this.handleSkillUse(message);
                    break;
                    
                case NetworkArchitecture.MessageTypes.SOCIAL.CHAT_MESSAGE:
                    this.handleChatMessage(message);
                    break;
                    
                default:
                    console.warn(`Unknown message type: ${message.type}`);
            }
            
        } catch (error) {
            console.error('Error handling incoming message:', error);
        }
    }
    
    // 重连机制
    private async attemptReconnect(connection: IConnection): Promise<void> {
        let attempts = 0;
        let delay = this._reconnectConfig.baseDelay;
        
        while (attempts < this._reconnectConfig.maxAttempts) {
            try {
                console.log(`Reconnection attempt ${attempts + 1}/${this._reconnectConfig.maxAttempts}`);
                
                await this.wait(delay);
                await this.reconnectConnection(connection);
                
                console.log('Reconnection successful');
                return;
                
            } catch (error) {
                attempts++;
                delay = Math.min(
                    delay * this._reconnectConfig.backoffMultiplier,
                    this._reconnectConfig.maxDelay
                );
                
                console.warn(`Reconnection attempt ${attempts} failed:`, error);
            }
        }
        
        console.error('All reconnection attempts failed');
        this.handleConnectionLost(connection);
    }
    
    // 网络状态监控
    private setupNetworkMonitoring(): void {
        // 监听网络状态变化
        window.addEventListener('online', () => {
            console.log('Network restored');
            this._isOnline = true;
            this.processMessageQueue();
            this.reconnectAllConnections();
        });
        
        window.addEventListener('offline', () => {
            console.log('Network lost');
            this._isOnline = false;
        });
        
        // 定期检查连接状态
        setInterval(() => {
            this.checkConnectionHealth();
        }, 30000); // 每30秒检查一次
    }
    
    // 消息队列处理
    private async processMessageQueue(): Promise<void> {
        if (this._messageQueue.length === 0) {
            return;
        }
        
        console.log(`Processing ${this._messageQueue.length} queued messages`);
        
        const messages = [...this._messageQueue];
        this._messageQueue = [];
        
        for (const message of messages) {
            try {
                await this.sendMessage(message);
            } catch (error) {
                console.error('Failed to send queued message:', error);
                // 重新加入队列
                this._messageQueue.push(message);
            }
        }
    }
    
    // 平台特定实现
    private async createConnection(endpoint: string, options: IConnectionOptions): Promise<IConnection> {
        const platform = this.getCurrentPlatform();
        
        switch (platform) {
            case 'wechat':
                return this.createWeChatConnection(endpoint, options);
            case 'douyin':
                return this.createDouyinConnection(endpoint, options);
            default:
                return this.createWebSocketConnection(endpoint, options);
        }
    }
    
    private async createWeChatConnection(endpoint: string, options: IConnectionOptions): Promise<IConnection> {
        return new Promise((resolve, reject) => {
            const socketTask = wx.connectSocket({
                url: endpoint,
                protocols: options.protocols,
                success: () => {
                    const connection: IConnection = {
                        id: '',
                        endpoint,
                        socket: socketTask,
                        status: 'connected',
                        platform: 'wechat'
                    };
                    resolve(connection);
                },
                fail: reject
            });
        });
    }
    
    private async createDouyinConnection(endpoint: string, options: IConnectionOptions): Promise<IConnection> {
        return new Promise((resolve, reject) => {
            const socketTask = tt.connectSocket({
                url: endpoint,
                protocols: options.protocols,
                success: () => {
                    const connection: IConnection = {
                        id: '',
                        endpoint,
                        socket: socketTask,
                        status: 'connected',
                        platform: 'douyin'
                    };
                    resolve(connection);
                },
                fail: reject
            });
        });
    }
    
    // 消息处理器
    private handlePlayerUpdate(message: IMessage): void {
        const playerData = message.data as IPlayerData;
        DataManager.getInstance().updatePlayerData(playerData);
        
        EventManager.emit(GameEvents.PLAYER_DATA_UPDATED, {
            playerId: playerData.id,
            data: playerData
        });
    }
    
    private handleSkillUse(message: IMessage): void {
        const skillData = message.data as ISkillUseData;
        BattleSystem.getInstance().processSkillUse(skillData);
        
        EventManager.emit(GameEvents.SKILL_USED, skillData);
    }
    
    private handleChatMessage(message: IMessage): void {
        const chatData = message.data as IChatMessage;
        ChatSystem.getInstance().addMessage(chatData);
        
        EventManager.emit(GameEvents.CHAT_MESSAGE_RECEIVED, chatData);
    }
    
    private handleHeartbeat(message: IMessage, connection: IConnection): void {
        // 响应心跳
        this.sendToConnection(connection, {
            type: NetworkArchitecture.MessageTypes.SYSTEM.HEARTBEAT,
            data: { response: true }
        });
    }
    
    // 工具方法
    private generateConnectionId(): string {
        return `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    private generateMessageId(): string {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    private getClientId(): string {
        // 获取客户端唯一标识
        return GameManager.getInstance().getClientId();
    }
    
    private getCurrentPlatform(): 'wechat' | 'douyin' | 'web' {
        if (typeof wx !== 'undefined') return 'wechat';
        if (typeof tt !== 'undefined') return 'douyin';
        return 'web';
    }
    
    private async wait(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 接口定义
interface IConnection {
    id: string;
    endpoint: string;
    socket: any;
    status: 'connecting' | 'connected' | 'disconnected' | 'error';
    platform: 'wechat' | 'douyin' | 'web';
    lastHeartbeat?: number;
}

interface IMessage {
    id?: string;
    type: string;
    data: any;
    timestamp?: number;
    clientId?: string;
}

interface IConnectionOptions {
    protocols?: string[];
    timeout?: number;
    autoReconnect?: boolean;
}
```

---

> 📖 **相关文档**: [数据管理](./data-management.md) | [小程序优化](./miniprogram-optimization.md) | [部署发布](./deployment-process.md)
