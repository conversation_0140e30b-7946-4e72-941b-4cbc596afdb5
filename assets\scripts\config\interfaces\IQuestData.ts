/**
 * 任务数据接口定义
 * 基于Godot项目中的quests.xml结构
 */

/**
 * 任务数据接口
 */
export interface IQuestData {
    /** 任务唯一标识符 */
    id: string;
    
    /** 任务名称 */
    name: string;
    
    /** 任务描述 */
    description: string;
    
    /** 任务类型 */
    type: QuestType;
    
    /** 推荐等级 */
    level: number;
    
    /** 前置条件 */
    prerequisites: IQuestPrerequisites;
    
    /** 任务目标 */
    objectives: IQuestObjective[];
    
    /** 任务奖励 */
    rewards: IQuestRewards;
    
    /** 是否可重复 */
    repeatable: boolean;
    
    /** 重复间隔（小时） */
    repeatInterval?: number;
    
    /** 任务时限（小时，0表示无限制） */
    timeLimit: number;
    
    /** 任务难度 */
    difficulty: QuestDifficulty;
    
    /** 任务标签 */
    tags: string[];
    
    /** NPC对话 */
    dialogues?: IQuestDialogue[];
}

/**
 * 任务类型枚举
 */
export enum QuestType {
    Main = 'main',
    Side = 'side',
    Daily = 'daily',
    Weekly = 'weekly',
    Event = 'event',
    Guild = 'guild',
    PvP = 'pvp',
    Dungeon = 'dungeon'
}

/**
 * 任务难度枚举
 */
export enum QuestDifficulty {
    Easy = 'easy',
    Normal = 'normal',
    Hard = 'hard',
    Expert = 'expert',
    Master = 'master'
}

/**
 * 任务前置条件接口
 */
export interface IQuestPrerequisites {
    /** 所需等级 */
    level: number;
    
    /** 前置任务 */
    quests: string[];
    
    /** 所需物品 */
    items: IQuestItemRequirement[];
    
    /** 所需技能 */
    skills: IQuestSkillRequirement[];
    
    /** 所需声望 */
    reputation?: IReputationRequirement[];
    
    /** 所需职业 */
    classes?: string[];
}

/**
 * 任务物品要求接口
 */
export interface IQuestItemRequirement {
    /** 物品ID */
    itemId: string;
    
    /** 所需数量 */
    quantity: number;
    
    /** 是否消耗 */
    consume: boolean;
}

/**
 * 任务技能要求接口
 */
export interface IQuestSkillRequirement {
    /** 技能ID */
    skillId: string;
    
    /** 所需等级 */
    level: number;
}

/**
 * 声望要求接口
 */
export interface IReputationRequirement {
    /** 阵营ID */
    factionId: string;
    
    /** 所需声望等级 */
    level: number;
}

/**
 * 任务目标接口
 */
export interface IQuestObjective {
    /** 目标ID */
    id: string;
    
    /** 目标类型 */
    type: QuestObjectiveType;
    
    /** 目标描述 */
    description: string;
    
    /** 目标参数 */
    target: string;
    
    /** 所需数量 */
    quantity: number;
    
    /** 当前进度 */
    currentProgress: number;
    
    /** 是否可选 */
    optional: boolean;
    
    /** 是否隐藏 */
    hidden: boolean;
    
    /** 完成条件 */
    conditions?: IObjectiveCondition[];
}

/**
 * 任务目标类型枚举
 */
export enum QuestObjectiveType {
    Kill = 'kill',
    Collect = 'collect',
    Deliver = 'deliver',
    Interact = 'interact',
    Reach = 'reach',
    Escort = 'escort',
    Defend = 'defend',
    Craft = 'craft',
    Use = 'use',
    Learn = 'learn',
    Level = 'level'
}

/**
 * 目标完成条件接口
 */
export interface IObjectiveCondition {
    /** 条件类型 */
    type: string;
    
    /** 条件参数 */
    parameter: string;
    
    /** 条件值 */
    value: any;
}

/**
 * 任务奖励接口
 */
export interface IQuestRewards {
    /** 经验奖励 */
    experience: number;
    
    /** 金币奖励 */
    gold: number;
    
    /** 物品奖励 */
    items: IQuestItemReward[];
    
    /** 技能点奖励 */
    skillPoints: number;
    
    /** 声望奖励 */
    reputation?: IReputationReward[];
    
    /** 称号奖励 */
    titles?: string[];
    
    /** 可选奖励 */
    choiceRewards?: IQuestChoiceReward[];
}

/**
 * 任务物品奖励接口
 */
export interface IQuestItemReward {
    /** 物品ID */
    itemId: string;
    
    /** 奖励数量 */
    quantity: number;
    
    /** 是否绑定 */
    bound: boolean;
}

/**
 * 声望奖励接口
 */
export interface IReputationReward {
    /** 阵营ID */
    factionId: string;
    
    /** 声望值 */
    value: number;
}

/**
 * 可选奖励接口
 */
export interface IQuestChoiceReward {
    /** 选择组ID */
    groupId: string;
    
    /** 可选择的数量 */
    choiceCount: number;
    
    /** 奖励选项 */
    options: IQuestItemReward[];
}

/**
 * 任务对话接口
 */
export interface IQuestDialogue {
    /** 对话ID */
    id: string;
    
    /** NPC ID */
    npcId: string;
    
    /** 对话阶段 */
    stage: QuestDialogueStage;
    
    /** 对话内容 */
    content: string;
    
    /** 对话选项 */
    options?: IDialogueOption[];
}

/**
 * 对话阶段枚举
 */
export enum QuestDialogueStage {
    Accept = 'accept',
    Progress = 'progress',
    Complete = 'complete',
    Decline = 'decline'
}

/**
 * 对话选项接口
 */
export interface IDialogueOption {
    /** 选项ID */
    id: string;
    
    /** 选项文本 */
    text: string;
    
    /** 选择后的动作 */
    action: DialogueAction;
    
    /** 动作参数 */
    actionParams?: any;
}

/**
 * 对话动作枚举
 */
export enum DialogueAction {
    Accept = 'accept',
    Decline = 'decline',
    Continue = 'continue',
    End = 'end',
    Shop = 'shop',
    Trade = 'trade'
}

/**
 * 玩家任务状态接口
 */
export interface IPlayerQuest {
    /** 任务ID */
    questId: string;
    
    /** 任务状态 */
    status: QuestStatus;
    
    /** 接受时间 */
    acceptedTime: number;
    
    /** 完成时间 */
    completedTime?: number;
    
    /** 目标进度 */
    objectiveProgress: Map<string, number>;
    
    /** 是否已提交 */
    submitted: boolean;
    
    /** 重复次数 */
    repeatCount: number;
    
    /** 下次可接受时间 */
    nextAvailableTime?: number;
}

/**
 * 任务状态枚举
 */
export enum QuestStatus {
    Available = 'available',
    Accepted = 'accepted',
    InProgress = 'in_progress',
    Completed = 'completed',
    Failed = 'failed',
    Abandoned = 'abandoned',
    Submitted = 'submitted'
}

/**
 * 任务配置文件根接口
 */
export interface IQuestConfig {
    /** 配置版本 */
    version: string;
    
    /** 最后更新时间 */
    lastUpdated: string;
    
    /** 任务列表 */
    quests: IQuestData[];
}

/**
 * 任务事件接口
 */
export interface IQuestEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 任务ID */
    questId: string;
    
    /** 事件类型 */
    eventType: QuestEventType;
    
    /** 事件数据 */
    eventData?: any;
    
    /** 时间戳 */
    timestamp: number;
}

/**
 * 任务事件类型枚举
 */
export enum QuestEventType {
    Accept = 'accept',
    Progress = 'progress',
    Complete = 'complete',
    Submit = 'submit',
    Abandon = 'abandon',
    Fail = 'fail',
    Reset = 'reset'
}

/**
 * 任务进度更新事件接口
 */
export interface IQuestProgressEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 任务ID */
    questId: string;
    
    /** 目标ID */
    objectiveId: string;
    
    /** 进度增量 */
    progressDelta: number;
    
    /** 当前进度 */
    currentProgress: number;
    
    /** 目标数量 */
    targetQuantity: number;
    
    /** 是否完成 */
    completed: boolean;
    
    /** 时间戳 */
    timestamp: number;
}

/**
 * 任务奖励发放事件接口
 */
export interface IQuestRewardEvent {
    /** 玩家ID */
    playerId: string;
    
    /** 任务ID */
    questId: string;
    
    /** 发放的奖励 */
    rewards: IQuestRewards;
    
    /** 选择的可选奖励 */
    chosenRewards?: IQuestItemReward[];
    
    /** 时间戳 */
    timestamp: number;
}
