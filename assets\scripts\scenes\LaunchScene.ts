import { _decorator, Component, Node, Label, director, input, Input, EventKeyboard, KeyCode } from 'cc';
import { ManagerInitializer, Managers } from '../managers';

const { ccclass, property } = _decorator;

/**
 * 启动场景控制器
 * 负责游戏启动时的初始化和加载流程
 */
@ccclass('LaunchScene')
export class LaunchScene extends Component {
    protected onLoad(): void {
        console.log('🚀 启动场景加载');
        this.initializeKeyboardInput();
    }

    protected start(): void {
        console.log('🚀 启动场景开始运行');
        this.initializeManagers();
        this.showTestInstructions();
    }

    /**
     * 初始化管理器系统
     */
    private async initializeManagers(): Promise<void> {
        try {
            console.log('🎯 启动场景：初始化管理器系统...');
            await ManagerInitializer.initializeAllManagers();

            // 启动游戏
            await Managers.Game.startGame();

            console.log('✅ 启动场景：管理器系统初始化完成');
        } catch (error) {
            console.error('❌ 启动场景：管理器系统初始化失败:', error);
        }
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('⌨️ 键盘输入已初始化');
    }

    /**
     * 显示测试说明
     */
    private showTestInstructions(): void {
        console.log('🎮 ========== 场景切换测试 ==========');
        console.log('📍 当前场景: Launch (启动场景)');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 切换到 Launch 场景');
        console.log('   按 2 键 - 切换到 Main 场景');
        console.log('   按 3 键 - 切换到 Battle 场景');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🎮 ===================================');
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.switchToLaunch();
                break;
            case KeyCode.DIGIT_2:
                this.switchToMain();
                break;
            case KeyCode.DIGIT_3:
                this.switchToBattle();
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
        }
    }

    /**
     * 切换到启动场景
     */
    private switchToLaunch(): void {
        console.log('🚀 切换到启动场景');
        this.switchScene('Launch');
    }

    /**
     * 切换到主界面场景
     */
    private switchToMain(): void {
        console.log('🏠 切换到主界面场景');
        this.switchScene('Main');
    }

    /**
     * 切换到战斗场景
     */
    private switchToBattle(): void {
        console.log('⚔️ 切换到战斗场景');
        this.switchScene('Battle');
    }

    /**
     * 通用场景切换方法
     */
    private async switchScene(sceneName: string): Promise<void> {
        try {
            console.log(`🔄 正在切换到场景: ${sceneName}`);

            // 使用SceneManager进行场景切换
            if (ManagerInitializer.isManagerInitialized('SceneManager')) {
                await Managers.Scene.switchScene(sceneName);
            } else {
                // 降级到直接使用director
                director.loadScene(sceneName, (error) => {
                    if (error) {
                        console.error(`❌ 场景切换失败: ${sceneName}`, error);
                    } else {
                        console.log(`✅ 场景切换成功: ${sceneName}`);
                    }
                });
            }
        } catch (error) {
            console.error(`❌ 场景切换异常: ${sceneName}`, error);
        }
    }



    /**
     * 测试方法 - 切换到主界面
     */
    public testGoToMain(): void {
        console.log('🔄 测试切换到主界面');
        director.loadScene('Main');
    }





    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('🚀 启动场景销毁');
    }
}
