/**
 * 测试生成代理 - 智能测试用例生成
 * 基于代码分析结果自动生成测试用例
 */
import { FunctionAnalysis } from './CodeAnalysisAgent';
import { TestType, CodeAnalysisResult } from '../types';
export interface TestGenerationOptions {
    testFramework: 'jest' | 'mocha' | 'jasmine' | 'custom';
    testTypes: TestType[];
    coverage: 'basic' | 'comprehensive' | 'exhaustive';
    includeEdgeCases: boolean;
    includePerformanceTests: boolean;
    includeIntegrationTests: boolean;
}
export interface GeneratedTest {
    id: string;
    name: string;
    description: string;
    type: TestType;
    priority: number;
    code: string;
    dependencies: string[];
    expectedResult: any;
    metadata: TestMetadata;
}
export interface TestMetadata {
    generatedAt: string;
    targetFunction?: string;
    targetClass?: string;
    complexity: 'simple' | 'medium' | 'complex';
    estimatedExecutionTime: number;
    tags: string[];
}
export declare class TestGenerationAgent {
    private testTemplates;
    private generationStrategies;
    constructor();
    /**
     * 基于代码分析结果生成测试用例
     */
    generateTests(analysisResult: CodeAnalysisResult, options: TestGenerationOptions): Promise<GeneratedTest[]>;
    /**
     * 为特定函数生成测试用例
     */
    generateTestsForFunction(functionAnalysis: FunctionAnalysis, options: TestGenerationOptions): Promise<GeneratedTest[]>;
    /**
     * 生成算法一致性测试
     */
    generateAlgorithmConsistencyTests(godotCode: string, cocosCode: string, algorithmName: string): Promise<GeneratedTest[]>;
    /**
     * 为特定测试类型生成测试用例
     */
    private generateTestsForType;
    /**
     * 生成单元测试
     */
    private generateUnitTests;
    /**
     * 生成集成测试
     */
    private generateIntegrationTests;
    /**
     * 生成性能测试
     */
    private generatePerformanceTests;
    /**
     * 生成基础函数测试
     */
    private generateBasicFunctionTest;
    /**
     * 生成参数测试
     */
    private generateParameterTests;
    private generateConsistencyTestCode;
    private generateClassInstantiationTest;
    private generateAsyncOperationTest;
    private generateNetworkIntegrationTest;
    private generateAlgorithmPerformanceTest;
    private generateBasicFunctionTestCode;
    private generateParameterTestCode;
    private initializeTestTemplates;
    private initializeGenerationStrategies;
    private selectGenerationStrategies;
    private optimizeTestSuite;
    private generateEdgeCaseTests;
    private generateAsyncTests;
    private generatePerformanceTest;
    private generateEdgeCaseTestsFromAnalysis;
}
//# sourceMappingURL=TestGenerationAgent.d.ts.map