# 小程序优化规范

> 📖 **导航**: [返回主页](./README.md) | [UI设计规范](./ui-guidelines.md) | [部署发布](./deployment-process.md)

## 📱 小程序平台限制

### 性能限制
```typescript
// 小程序性能限制常量
export const MiniprogramLimits = {
    // 内存限制
    memory: {
        ios: 256,        // iOS设备内存限制 (MB)
        android: 128,    // Android设备内存限制 (MB)
        warning: 0.8,    // 内存警告阈值 (80%)
        critical: 0.9    // 内存危险阈值 (90%)
    },
    
    // 包体大小限制
    packageSize: {
        main: 4,         // 主包大小限制 (MB)
        subpackage: 2,   // 分包大小限制 (MB)
        total: 20,       // 总包大小限制 (MB)
        singleFile: 500  // 单文件大小限制 (KB)
    },
    
    // 网络限制
    network: {
        concurrent: 10,      // 并发请求数限制
        timeout: 60000,      // 请求超时时间 (ms)
        uploadSize: 10,      // 上传文件大小限制 (MB)
        downloadSize: 50     // 下载文件大小限制 (MB)
    },
    
    // 本地存储限制
    storage: {
        localStorage: 10,    // 本地存储限制 (MB)
        fileSystem: 200,     // 文件系统限制 (MB)
        cache: 50           // 缓存大小限制 (MB)
    }
};
```

### 平台API差异
```typescript
// 平台API适配器
export class PlatformAPIAdapter {
    // 微信小程序API
    static wechatAPIs = {
        login: 'wx.login',
        getUserInfo: 'wx.getUserInfo',
        showToast: 'wx.showToast',
        setStorage: 'wx.setStorageSync',
        getStorage: 'wx.getStorageSync',
        request: 'wx.request',
        uploadFile: 'wx.uploadFile',
        downloadFile: 'wx.downloadFile'
    };
    
    // 抖音小程序API
    static douyinAPIs = {
        login: 'tt.login',
        getUserInfo: 'tt.getUserInfo',
        showToast: 'tt.showToast',
        setStorage: 'tt.setStorageSync',
        getStorage: 'tt.getStorageSync',
        request: 'tt.request',
        uploadFile: 'tt.uploadFile',
        downloadFile: 'tt.downloadFile'
    };
    
    // 统一API调用
    static callAPI(apiName: string, options: any): Promise<any> {
        const platform = this.getCurrentPlatform();
        const apiMap = platform === 'wechat' ? this.wechatAPIs : this.douyinAPIs;
        const apiFunction = this.getAPIFunction(apiMap[apiName]);
        
        return new Promise((resolve, reject) => {
            apiFunction({
                ...options,
                success: resolve,
                fail: reject
            });
        });
    }
    
    private static getCurrentPlatform(): 'wechat' | 'douyin' {
        if (typeof wx !== 'undefined') return 'wechat';
        if (typeof tt !== 'undefined') return 'douyin';
        throw new Error('Unsupported platform');
    }
    
    private static getAPIFunction(apiPath: string): Function {
        const parts = apiPath.split('.');
        let obj = globalThis;
        for (const part of parts) {
            obj = obj[part];
        }
        return obj;
    }
}
```

## 🚀 性能优化策略

### 内存管理
```typescript
// 内存管理器
@ccclass('MemoryManager')
export class MemoryManager extends BaseManager {
    private _memoryUsage: number = 0;
    private _memoryThreshold: number = 0.8;
    private _cleanupCallbacks: (() => void)[] = [];
    
    protected async initializeManager(): Promise<void> {
        this.startMemoryMonitoring();
        this.registerCleanupCallbacks();
    }
    
    private startMemoryMonitoring(): void {
        setInterval(() => {
            this.checkMemoryUsage();
        }, 5000); // 每5秒检查一次内存使用
    }
    
    private checkMemoryUsage(): void {
        const memoryInfo = this.getMemoryInfo();
        this._memoryUsage = memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit;
        
        if (this._memoryUsage > this._memoryThreshold) {
            this.performMemoryCleanup();
        }
        
        if (this._memoryUsage > 0.9) {
            this.performEmergencyCleanup();
        }
    }
    
    private performMemoryCleanup(): void {
        console.log('执行内存清理...');
        
        // 清理资源缓存
        ResourceManager.clearUnusedTextures();
        ResourceManager.clearAudioCache();
        
        // 清理UI缓存
        UIManager.clearInactivePanels();
        
        // 清理对象池
        ObjectPoolManager.cleanupPools();
        
        // 执行注册的清理回调
        this._cleanupCallbacks.forEach(callback => callback());
        
        // 强制垃圾回收
        if (typeof gc !== 'undefined') {
            gc();
        }
    }
    
    private performEmergencyCleanup(): void {
        console.warn('内存使用过高，执行紧急清理...');
        
        // 暂停非关键系统
        GameManager.pauseNonCriticalSystems();
        
        // 清理所有可清理的资源
        this.performMemoryCleanup();
        
        // 降低游戏质量
        this.reduceGameQuality();
        
        // 显示内存警告
        UIManager.showMemoryWarning();
    }
    
    public registerCleanupCallback(callback: () => void): void {
        this._cleanupCallbacks.push(callback);
    }
    
    public getMemoryUsage(): number {
        return this._memoryUsage;
    }
    
    private getMemoryInfo(): any {
        // 获取内存信息的平台特定实现
        if (typeof performance !== 'undefined' && performance.memory) {
            return performance.memory;
        }
        
        // 小程序环境的内存信息获取
        return this.getMiniprogramMemoryInfo();
    }
    
    private getMiniprogramMemoryInfo(): any {
        // 模拟内存信息，实际实现需要根据平台API
        return {
            usedJSHeapSize: 50 * 1024 * 1024,  // 50MB
            jsHeapSizeLimit: 128 * 1024 * 1024  // 128MB
        };
    }
}
```

### 资源优化
```typescript
// 资源优化管理器
@ccclass('ResourceOptimizer')
export class ResourceOptimizer extends BaseManager {
    private _textureCompressionSettings = {
        ui: { format: 'RGBA8888', quality: 'high' },
        character: { format: 'RGB565', quality: 'medium' },
        background: { format: 'RGB565', quality: 'low' },
        effect: { format: 'RGBA4444', quality: 'medium' }
    };
    
    private _audioCompressionSettings = {
        bgm: { format: 'mp3', bitrate: 128 },
        sfx: { format: 'wav', bitrate: 64 },
        voice: { format: 'mp3', bitrate: 96 }
    };
    
    // 纹理优化
    public optimizeTexture(texture: Texture2D, category: string): Texture2D {
        const settings = this._textureCompressionSettings[category];
        if (!settings) return texture;
        
        // 根据设置压缩纹理
        return this.compressTexture(texture, settings);
    }
    
    // 音频优化
    public optimizeAudio(audioClip: AudioClip, category: string): AudioClip {
        const settings = this._audioCompressionSettings[category];
        if (!settings) return audioClip;
        
        // 根据设置压缩音频
        return this.compressAudio(audioClip, settings);
    }
    
    // 批量资源预加载
    public async preloadCriticalResources(): Promise<void> {
        const criticalResources = [
            'textures/ui/common/',
            'audio/sfx/ui_sounds',
            'data/game/core_config.json'
        ];
        
        const loadPromises = criticalResources.map(path => 
            ResourceManager.preloadResource(path)
        );
        
        await Promise.all(loadPromises);
    }
    
    // 动态资源加载
    public async loadResourceOnDemand(resourcePath: string): Promise<any> {
        // 检查内存使用情况
        if (MemoryManager.getMemoryUsage() > 0.8) {
            await MemoryManager.performMemoryCleanup();
        }
        
        // 加载资源
        return ResourceManager.loadResource(resourcePath);
    }
    
    private compressTexture(texture: Texture2D, settings: any): Texture2D {
        // 纹理压缩实现
        // 这里需要根据Cocos Creator的具体API实现
        return texture;
    }
    
    private compressAudio(audioClip: AudioClip, settings: any): AudioClip {
        // 音频压缩实现
        // 这里需要根据Cocos Creator的具体API实现
        return audioClip;
    }
}
```

## 📦 分包策略

### 分包配置
```typescript
// 分包配置
export const SubpackageConfig = {
    // 主包 - 核心功能
    main: {
        maxSize: '4MB',
        priority: 'high',
        preload: true,
        resources: [
            'scenes/launch/',
            'scenes/main/',
            'scripts/core/',
            'scripts/managers/',
            'textures/ui/common/',
            'audio/sfx/ui_sounds',
            'data/game/core_config.json'
        ]
    },
    
    // 战斗包 - 战斗相关功能
    battle: {
        maxSize: '2MB',
        priority: 'medium',
        preload: false,
        resources: [
            'scenes/battle/',
            'scripts/systems/battle/',
            'textures/characters/',
            'textures/skills/',
            'audio/sfx/battle_sounds',
            'data/game/skills.json',
            'data/game/characters.json'
        ]
    },
    
    // 武侠包 - 武侠特色功能
    wuxia: {
        maxSize: '2MB',
        priority: 'medium',
        preload: false,
        resources: [
            'scenes/sect/',
            'scripts/systems/wuxia/',
            'textures/sects/',
            'audio/bgm/sect_themes',
            'data/game/sects.json',
            'data/game/cultivation.json'
        ]
    },
    
    // 社交包 - 社交功能
    social: {
        maxSize: '1.5MB',
        priority: 'low',
        preload: false,
        resources: [
            'scenes/social/',
            'scripts/systems/social/',
            'textures/ui/social/',
            'audio/sfx/social_sounds',
            'data/game/guilds.json'
        ]
    },
    
    // 资源包 - 大型资源文件
    assets: {
        maxSize: '2MB',
        priority: 'low',
        preload: false,
        resources: [
            'textures/world/',
            'audio/bgm/background_music',
            'animations/character_animations'
        ]
    }
};
```

### 动态加载管理
```typescript
// 动态加载管理器
@ccclass('SubpackageManager')
export class SubpackageManager extends BaseManager {
    private _loadedSubpackages: Set<string> = new Set();
    private _loadingPromises: Map<string, Promise<void>> = new Map();
    
    // 加载分包
    public async loadSubpackage(name: string): Promise<void> {
        if (this._loadedSubpackages.has(name)) {
            return; // 已加载
        }
        
        if (this._loadingPromises.has(name)) {
            return this._loadingPromises.get(name); // 正在加载
        }
        
        const loadPromise = this.performSubpackageLoad(name);
        this._loadingPromises.set(name, loadPromise);
        
        try {
            await loadPromise;
            this._loadedSubpackages.add(name);
            this._loadingPromises.delete(name);
        } catch (error) {
            this._loadingPromises.delete(name);
            throw error;
        }
    }
    
    // 预加载分包
    public async preloadSubpackages(names: string[]): Promise<void> {
        const loadPromises = names.map(name => this.loadSubpackage(name));
        await Promise.all(loadPromises);
    }
    
    // 检查分包是否已加载
    public isSubpackageLoaded(name: string): boolean {
        return this._loadedSubpackages.has(name);
    }
    
    // 获取分包加载进度
    public getLoadingProgress(name: string): number {
        // 实现加载进度获取逻辑
        return 0;
    }
    
    private async performSubpackageLoad(name: string): Promise<void> {
        const config = SubpackageConfig[name];
        if (!config) {
            throw new Error(`Unknown subpackage: ${name}`);
        }
        
        // 显示加载进度
        UIManager.showLoadingProgress(`加载${name}包...`);
        
        try {
            // 使用平台API加载分包
            await PlatformAPIAdapter.callAPI('loadSubpackage', { name });
            
            // 预加载分包中的关键资源
            await this.preloadSubpackageResources(config.resources);
            
            UIManager.hideLoadingProgress();
        } catch (error) {
            UIManager.hideLoadingProgress();
            UIManager.showError(`加载${name}包失败: ${error.message}`);
            throw error;
        }
    }
    
    private async preloadSubpackageResources(resources: string[]): Promise<void> {
        const loadPromises = resources.map(resource => 
            ResourceManager.preloadResource(resource)
        );
        
        await Promise.all(loadPromises);
    }
}
```

## 🔧 启动优化

### 启动时间优化
```typescript
// 启动优化管理器
@ccclass('LaunchOptimizer')
export class LaunchOptimizer extends BaseManager {
    private _startTime: number = 0;
    private _launchSteps: string[] = [];
    
    protected async initializeManager(): Promise<void> {
        this._startTime = Date.now();
        this.optimizeLaunchSequence();
    }
    
    private async optimizeLaunchSequence(): Promise<void> {
        // 1. 最小化首屏资源
        await this.loadMinimalResources();
        this.recordStep('minimal_resources_loaded');
        
        // 2. 显示启动画面
        await this.showSplashScreen();
        this.recordStep('splash_screen_shown');
        
        // 3. 并行加载核心系统
        await this.loadCoreSystemsInParallel();
        this.recordStep('core_systems_loaded');
        
        // 4. 初始化游戏状态
        await this.initializeGameState();
        this.recordStep('game_state_initialized');
        
        // 5. 进入主界面
        await this.enterMainScene();
        this.recordStep('main_scene_entered');
        
        // 6. 后台加载其他资源
        this.loadRemainingResourcesInBackground();
        this.recordStep('background_loading_started');
        
        this.reportLaunchPerformance();
    }
    
    private async loadMinimalResources(): Promise<void> {
        const minimalResources = [
            'textures/ui/splash_screen.png',
            'textures/ui/loading_bar.png',
            'audio/sfx/ui_click.wav'
        ];
        
        await ResourceManager.loadResources(minimalResources);
    }
    
    private async loadCoreSystemsInParallel(): Promise<void> {
        const coreSystemPromises = [
            DataManager.initialize(),
            EventManager.initialize(),
            AudioManager.initialize(),
            InputManager.initialize()
        ];
        
        await Promise.all(coreSystemPromises);
    }
    
    private recordStep(stepName: string): void {
        const elapsed = Date.now() - this._startTime;
        this._launchSteps.push(`${stepName}: ${elapsed}ms`);
    }
    
    private reportLaunchPerformance(): void {
        const totalTime = Date.now() - this._startTime;
        console.log('启动性能报告:');
        console.log(`总启动时间: ${totalTime}ms`);
        this._launchSteps.forEach(step => console.log(step));
        
        // 上报性能数据
        AnalyticsManager.reportLaunchPerformance({
            totalTime,
            steps: this._launchSteps
        });
    }
}
```

---

> 📖 **相关文档**: [部署发布](./deployment-process.md) | [资源管理](./resource-management.md) | [UI设计规范](./ui-guidelines.md)
