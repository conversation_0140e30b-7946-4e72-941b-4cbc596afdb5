/**
 * 背包面板
 * 显示和管理玩家的物品背包
 */

import { _decorator, Node, ScrollView, Prefab, instantiate, Layout } from 'cc';
import { UIPanel } from '../components/UIPanel';
import { UIPanelType } from '../types/UITypes';
import { UIButton } from '../components/UIButton';
import { ConfigManager } from '../../managers/ConfigManager';
import { IItemData } from '../../config/interfaces/IItemData';

const { ccclass, property } = _decorator;

/**
 * 背包物品数据接口
 */
export interface IInventoryItem {
    /** 物品配置数据 */
    itemData: IItemData;
    
    /** 物品数量 */
    quantity: number;
    
    /** 物品唯一ID */
    instanceId: string;
    
    /** 获得时间 */
    obtainTime: number;
    
    /** 是否已装备 */
    equipped: boolean;
    
    /** 强化等级 */
    enhanceLevel?: number;
    
    /** 附魔属性 */
    enchantments?: Array<{
        type: string;
        value: number;
    }>;
}

/**
 * 背包过滤类型
 */
export enum InventoryFilterType {
    All = 'all',
    Weapon = 'weapon',
    Armor = 'armor',
    Consumable = 'consumable',
    Material = 'material',
    Quest = 'quest'
}

@ccclass('InventoryPanel')
export class InventoryPanel extends UIPanel {
    
    @property({ type: ScrollView, tooltip: '物品滚动视图' })
    public itemScrollView: ScrollView | null = null;
    
    @property({ type: Node, tooltip: '物品容器' })
    public itemContainer: Node | null = null;
    
    @property({ type: Prefab, tooltip: '物品槽预制体' })
    public itemSlotPrefab: Prefab | null = null;
    
    @property({ type: Node, tooltip: '过滤按钮容器' })
    public filterButtonContainer: Node | null = null;
    
    @property({ type: Node, tooltip: '物品详情面板' })
    public itemDetailPanel: Node | null = null;
    
    @property({ type: UIButton, tooltip: '排序按钮' })
    public sortButton: UIButton | null = null;
    
    @property({ type: UIButton, tooltip: '整理按钮' })
    public organizeButton: UIButton | null = null;
    
    @property({ tooltip: '背包容量' })
    public capacity: number = 50;
    
    // 私有属性
    private _inventoryItems: IInventoryItem[] = [];
    private _itemSlots: Node[] = [];
    private _currentFilter: InventoryFilterType = InventoryFilterType.All;
    private _selectedItem: IInventoryItem | null = null;
    private _filterButtons: Map<InventoryFilterType, UIButton> = new Map();

    protected onPanelLoad(): void {
        super.onPanelLoad();
        
        // 设置面板类型
        this._panelType = UIPanelType.Inventory;
        
        // 设置标题
        this.setTitle('背包');
        
        // 查找组件
        this.findInventoryComponents();
    }

    protected async onPanelInitialize(data?: any): Promise<void> {
        // 创建物品槽
        this.createItemSlots();
        
        // 创建过滤按钮
        this.createFilterButtons();
        
        // 加载背包数据
        await this.loadInventoryData();
    }

    protected bindEvents(): void {
        super.bindEvents();
        
        // 绑定排序按钮
        if (this.sortButton) {
            this.sortButton.setClickCallback(() => {
                this.sortItems();
            });
        }
        
        // 绑定整理按钮
        if (this.organizeButton) {
            this.organizeButton.setClickCallback(() => {
                this.organizeItems();
            });
        }
    }

    protected onPanelRefresh(data?: any): void {
        // 刷新背包显示
        this.refreshInventoryDisplay();
    }

    /**
     * 查找背包组件
     */
    private findInventoryComponents(): void {
        if (!this.itemScrollView) {
            this.itemScrollView = this.node.getComponentInChildren(ScrollView);
        }
        
        if (!this.itemContainer && this.itemScrollView) {
            this.itemContainer = this.itemScrollView.content;
        }
        
        if (!this.filterButtonContainer) {
            this.filterButtonContainer = this.node.getChildByName('FilterButtons');
        }
        
        if (!this.itemDetailPanel) {
            this.itemDetailPanel = this.node.getChildByName('ItemDetail');
        }
        
        if (!this.sortButton) {
            const sortNode = this.node.getChildByName('SortButton');
            if (sortNode) {
                this.sortButton = sortNode.getComponent(UIButton);
            }
        }
        
        if (!this.organizeButton) {
            const organizeNode = this.node.getChildByName('OrganizeButton');
            if (organizeNode) {
                this.organizeButton = organizeNode.getComponent(UIButton);
            }
        }
    }

    /**
     * 创建物品槽
     */
    private createItemSlots(): void {
        if (!this.itemContainer || !this.itemSlotPrefab) {
            console.warn('缺少物品容器或物品槽预制体');
            return;
        }
        
        // 清除现有槽位
        this.itemContainer.removeAllChildren();
        this._itemSlots = [];
        
        // 创建新槽位
        for (let i = 0; i < this.capacity; i++) {
            const slotNode = instantiate(this.itemSlotPrefab);
            slotNode.name = `ItemSlot_${i}`;
            slotNode.setParent(this.itemContainer);
            
            // 绑定槽位事件
            this.bindSlotEvents(slotNode, i);
            
            this._itemSlots.push(slotNode);
        }
        
        console.log(`创建了 ${this.capacity} 个物品槽`);
    }

    /**
     * 绑定槽位事件
     */
    private bindSlotEvents(slotNode: Node, index: number): void {
        slotNode.on(Node.EventType.TOUCH_END, () => {
            this.onSlotClick(index);
        });
    }

    /**
     * 创建过滤按钮
     */
    private createFilterButtons(): void {
        if (!this.filterButtonContainer) {
            return;
        }
        
        const filterTypes = [
            { type: InventoryFilterType.All, text: '全部' },
            { type: InventoryFilterType.Weapon, text: '武器' },
            { type: InventoryFilterType.Armor, text: '防具' },
            { type: InventoryFilterType.Consumable, text: '消耗品' },
            { type: InventoryFilterType.Material, text: '材料' },
            { type: InventoryFilterType.Quest, text: '任务' }
        ];
        
        for (const filter of filterTypes) {
            const buttonNode = new Node(`Filter_${filter.type}`);
            const button = buttonNode.addComponent(UIButton);
            
            button.setText(filter.text);
            button.setClickCallback(() => {
                this.setFilter(filter.type);
            });
            
            buttonNode.setParent(this.filterButtonContainer);
            this._filterButtons.set(filter.type, button);
        }
        
        // 设置默认选中
        this.setFilter(InventoryFilterType.All);
    }

    /**
     * 加载背包数据
     */
    private async loadInventoryData(): Promise<void> {
        try {
            // 这里应该从游戏数据管理器加载实际的背包数据
            // 现在使用模拟数据
            this._inventoryItems = await this.generateMockInventoryData();
            
            console.log(`加载了 ${this._inventoryItems.length} 个背包物品`);
            
            // 刷新显示
            this.refreshInventoryDisplay();
            
        } catch (error) {
            console.error('加载背包数据失败:', error);
        }
    }

    /**
     * 生成模拟背包数据
     */
    private async generateMockInventoryData(): Promise<IInventoryItem[]> {
        try {
            const configManager = ConfigManager.getInstance();
            const allItems = configManager.getAllItemData();
            const inventoryItems: IInventoryItem[] = [];

            // 添加一些示例物品
            for (let i = 0; i < Math.min(10, allItems.length); i++) {
                const itemData = allItems[i];
                const inventoryItem: IInventoryItem = {
                    itemData,
                    quantity: Math.floor(Math.random() * 10) + 1,
                    instanceId: `item_${Date.now()}_${i}`,
                    obtainTime: Date.now() - Math.random() * 86400000, // 随机时间
                    equipped: false,
                    enhanceLevel: Math.floor(Math.random() * 5),
                    enchantments: []
                };

                inventoryItems.push(inventoryItem);
            }

            return inventoryItems;
        } catch (error) {
            console.warn('生成模拟背包数据失败，返回空数组:', error);
            return [];
        }
    }

    /**
     * 刷新背包显示
     */
    private refreshInventoryDisplay(): void {
        // 过滤物品
        const filteredItems = this.getFilteredItems();
        
        // 更新槽位显示
        for (let i = 0; i < this._itemSlots.length; i++) {
            const slot = this._itemSlots[i];
            const item = filteredItems[i] || null;
            
            this.updateSlotDisplay(slot, item);
        }
        
        console.log(`显示了 ${filteredItems.length} 个过滤后的物品`);
    }

    /**
     * 获取过滤后的物品
     */
    private getFilteredItems(): IInventoryItem[] {
        if (this._currentFilter === InventoryFilterType.All) {
            return this._inventoryItems;
        }
        
        return this._inventoryItems.filter(item => {
            switch (this._currentFilter) {
                case InventoryFilterType.Weapon:
                    return item.itemData.type === 'weapon';
                case InventoryFilterType.Armor:
                    return item.itemData.type === 'armor';
                case InventoryFilterType.Consumable:
                    return item.itemData.type === 'consumable';
                case InventoryFilterType.Material:
                    return item.itemData.type === 'material';
                case InventoryFilterType.Quest:
                    return item.itemData.type === 'quest';
                default:
                    return true;
            }
        });
    }

    /**
     * 更新槽位显示
     */
    private updateSlotDisplay(slot: Node, item: IInventoryItem | null): void {
        // 这里需要根据实际的槽位预制体结构来更新显示
        // 包括物品图标、数量、品质等
        
        if (item) {
            slot.active = true;
            // 设置物品图标、数量等
            console.log(`更新槽位显示: ${item.itemData.name} x${item.quantity}`);
        } else {
            slot.active = false;
        }
    }

    /**
     * 槽位点击事件
     */
    private onSlotClick(index: number): void {
        const filteredItems = this.getFilteredItems();
        const item = filteredItems[index];
        
        if (item) {
            this.selectItem(item);
        }
    }

    /**
     * 选择物品
     */
    private selectItem(item: IInventoryItem): void {
        this._selectedItem = item;
        
        // 显示物品详情
        this.showItemDetail(item);
        
        console.log(`选择物品: ${item.itemData.name}`);
    }

    /**
     * 显示物品详情
     */
    private showItemDetail(item: IInventoryItem): void {
        if (!this.itemDetailPanel) {
            return;
        }
        
        this.itemDetailPanel.active = true;
        
        // 更新详情面板内容
        // 这里需要根据实际的详情面板结构来更新
        console.log(`显示物品详情: ${item.itemData.name}`);
    }

    /**
     * 设置过滤器
     */
    private setFilter(filterType: InventoryFilterType): void {
        this._currentFilter = filterType;
        
        // 更新按钮状态
        this._filterButtons.forEach((button, type) => {
            // 这里需要设置按钮的选中状态
            // button.setSelected(type === filterType);
        });
        
        // 刷新显示
        this.refreshInventoryDisplay();
        
        console.log(`设置过滤器: ${filterType}`);
    }

    /**
     * 排序物品
     */
    private sortItems(): void {
        this._inventoryItems.sort((a, b) => {
            // 按品质、类型、名称排序
            if (a.itemData.rarity !== b.itemData.rarity) {
                const rarityOrder = { 'common': 1, 'uncommon': 2, 'rare': 3, 'epic': 4, 'legendary': 5 };
                return (rarityOrder[b.itemData.rarity] || 0) - (rarityOrder[a.itemData.rarity] || 0);
            }
            
            if (a.itemData.type !== b.itemData.type) {
                return a.itemData.type.localeCompare(b.itemData.type);
            }
            
            return a.itemData.name.localeCompare(b.itemData.name);
        });
        
        this.refreshInventoryDisplay();
        console.log('物品已排序');
    }

    /**
     * 整理物品
     */
    private organizeItems(): void {
        // 合并相同物品
        const itemMap = new Map<string, IInventoryItem>();
        
        for (const item of this._inventoryItems) {
            if (item.itemData.stackable) {
                const key = item.itemData.id;
                if (itemMap.has(key)) {
                    const existingItem = itemMap.get(key)!;
                    existingItem.quantity += item.quantity;
                } else {
                    itemMap.set(key, { ...item });
                }
            } else {
                itemMap.set(item.instanceId, item);
            }
        }
        
        this._inventoryItems = Array.from(itemMap.values());
        this.refreshInventoryDisplay();
        console.log('物品已整理');
    }

    // ==================== 公共API ====================

    /**
     * 添加物品
     */
    public addItem(itemData: IItemData, quantity: number = 1): boolean {
        // 检查背包空间
        if (this._inventoryItems.length >= this.capacity) {
            console.warn('背包已满');
            return false;
        }
        
        // 检查是否可堆叠
        if (itemData.stackable) {
            const existingItem = this._inventoryItems.find(item => item.itemData.id === itemData.id);
            if (existingItem) {
                existingItem.quantity += quantity;
                this.refreshInventoryDisplay();
                return true;
            }
        }
        
        // 创建新物品
        const newItem: IInventoryItem = {
            itemData,
            quantity,
            instanceId: `item_${Date.now()}_${Math.random()}`,
            obtainTime: Date.now(),
            equipped: false
        };
        
        this._inventoryItems.push(newItem);
        this.refreshInventoryDisplay();
        
        console.log(`添加物品: ${itemData.name} x${quantity}`);
        return true;
    }

    /**
     * 移除物品
     */
    public removeItem(instanceId: string, quantity: number = 1): boolean {
        const itemIndex = this._inventoryItems.findIndex(item => item.instanceId === instanceId);
        if (itemIndex === -1) {
            return false;
        }
        
        const item = this._inventoryItems[itemIndex];
        
        if (item.quantity <= quantity) {
            // 完全移除
            this._inventoryItems.splice(itemIndex, 1);
        } else {
            // 减少数量
            item.quantity -= quantity;
        }
        
        this.refreshInventoryDisplay();
        console.log(`移除物品: ${item.itemData.name} x${quantity}`);
        return true;
    }

    /**
     * 获取物品数量
     */
    public getItemCount(itemId: string): number {
        return this._inventoryItems
            .filter(item => item.itemData.id === itemId)
            .reduce((total, item) => total + item.quantity, 0);
    }

    /**
     * 检查是否有物品
     */
    public hasItem(itemId: string, quantity: number = 1): boolean {
        return this.getItemCount(itemId) >= quantity;
    }

    /**
     * 获取背包使用率
     */
    public getUsageRate(): number {
        return this._inventoryItems.length / this.capacity;
    }
}
