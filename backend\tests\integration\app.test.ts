import { createTestApp, TestAssertions } from '../utils/testApp';

describe('应用基础功能测试', () => {
  const testApp = createTestApp();
  const request = testApp.getRequest();

  beforeAll(async () => {
    await testApp.start();
  });

  afterAll(async () => {
    await testApp.stop();
  });

  describe('健康检查', () => {
    it('应该返回健康状态', async () => {
      const response = await request
        .get('/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('services');
      expect(response.body.services).toHaveProperty('database');
      expect(response.body.services).toHaveProperty('redis');
    });
  });

  describe('根路由', () => {
    it('应该返回API信息', async () => {
      const response = await request
        .get('/')
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('version');
      expect(response.body).toHaveProperty('docs');
      expect(response.body).toHaveProperty('health');
      expect(response.body).toHaveProperty('api');
    });
  });

  describe('API信息路由', () => {
    it('应该返回API详细信息', async () => {
      const response = await request
        .get('/api/info')
        .expect(200);

      expect(response.body).toHaveProperty('name');
      expect(response.body).toHaveProperty('version');
      expect(response.body).toHaveProperty('description');
      expect(response.body).toHaveProperty('environment');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('API路由信息', () => {
    it('应该返回API路由列表', async () => {
      const response = await request
        .get('/api/v1')
        .expect(200);

      expect(response.body).toHaveProperty('name', 'IdleGame API');
      expect(response.body).toHaveProperty('version', 'v1');
      expect(response.body).toHaveProperty('endpoints');
      expect(response.body.endpoints).toHaveProperty('auth');
      expect(response.body.endpoints).toHaveProperty('users');
      expect(response.body.endpoints).toHaveProperty('game');
      expect(response.body.endpoints).toHaveProperty('character');
      expect(response.body.endpoints).toHaveProperty('battle');
      expect(response.body.endpoints).toHaveProperty('social');
    });
  });

  describe('404错误处理', () => {
    it('应该返回404错误', async () => {
      const response = await request
        .get('/nonexistent-route')
        .expect(404);

      TestAssertions.expectErrorResponse(response, 'NOT_FOUND');
      expect(response.body.message).toContain('/nonexistent-route');
    });
  });

  describe('API文档', () => {
    it('应该提供Swagger文档', async () => {
      const response = await request
        .get('/api/docs.json')
        .expect(200);

      expect(response.body).toHaveProperty('openapi');
      expect(response.body).toHaveProperty('info');
      expect(response.body).toHaveProperty('paths');
      expect(response.body).toHaveProperty('components');
    });

    it('应该提供Swagger UI', async () => {
      const response = await request
        .get('/api/docs')
        .expect(200);

      expect(response.text).toContain('swagger-ui');
    });
  });
});
