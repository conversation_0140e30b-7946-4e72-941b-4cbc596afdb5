/**
 * 网络通信模块类型定义
 * 定义网络请求、响应、错误处理等相关接口和类型
 */

/**
 * HTTP请求方法枚举
 */
export enum HttpMethod {
    GET = 'GET',
    POST = 'POST',
    PUT = 'PUT',
    DELETE = 'DELETE',
    PATCH = 'PATCH'
}

/**
 * 网络连接状态枚举
 */
export enum ConnectionState {
    DISCONNECTED = 'disconnected',
    CONNECTING = 'connecting',
    CONNECTED = 'connected',
    RECONNECTING = 'reconnecting',
    ERROR = 'error'
}

/**
 * 网络类型枚举
 */
export enum NetworkType {
    UNKNOWN = 'unknown',
    WIFI = 'wifi',
    CELLULAR = 'cellular',
    ETHERNET = 'ethernet',
    OFFLINE = 'offline'
}

/**
 * HTTP请求配置接口
 */
export interface IHttpRequestConfig {
    url: string;
    method: HttpMethod;
    headers?: Record<string, string>;
    params?: Record<string, any>;
    data?: any;
    timeout?: number;
    retries?: number;
    retryDelay?: number;
}

/**
 * HTTP响应接口
 */
export interface IHttpResponse<T = any> {
    data: T;
    status: number;
    statusText: string;
    headers: Record<string, string>;
    config: IHttpRequestConfig;
}

/**
 * 网络错误接口
 */
export interface INetworkError {
    code: string;
    message: string;
    status?: number;
    config?: IHttpRequestConfig;
    response?: IHttpResponse;
    timestamp: number;
}

/**
 * 请求拦截器接口
 */
export interface IRequestInterceptor {
    onRequest?: (config: IHttpRequestConfig) => IHttpRequestConfig | Promise<IHttpRequestConfig>;
    onRequestError?: (error: INetworkError) => Promise<INetworkError>;
}

/**
 * 响应拦截器接口
 */
export interface IResponseInterceptor {
    onResponse?: (response: IHttpResponse) => IHttpResponse | Promise<IHttpResponse>;
    onResponseError?: (error: INetworkError) => Promise<INetworkError>;
}

/**
 * WebSocket消息接口
 */
export interface IWebSocketMessage {
    type: string;
    data: any;
    timestamp: number;
    id?: string;
}

/**
 * WebSocket配置接口
 */
export interface IWebSocketConfig {
    url: string;
    protocols?: string[];
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    heartbeatInterval?: number;
    timeout?: number;
}

/**
 * 网络请求接口
 */
export interface INetworkRequest {
    id: string;
    type: 'http' | 'websocket';
    config: IHttpRequestConfig | IWebSocketConfig;
    priority: number;
    timestamp: number;
    retries: number;
    maxRetries: number;
}

/**
 * 网络状态接口
 */
export interface INetworkStatus {
    isOnline: boolean;
    networkType: NetworkType;
    connectionQuality: 'poor' | 'good' | 'excellent';
    latency: number;
    lastCheck: number;
}

/**
 * 网络事件类型
 */
export enum NetworkEventType {
    ONLINE = 'network:online',
    OFFLINE = 'network:offline',
    CONNECTION_CHANGE = 'network:connection-change',
    REQUEST_START = 'network:request-start',
    REQUEST_SUCCESS = 'network:request-success',
    REQUEST_ERROR = 'network:request-error',
    WEBSOCKET_CONNECT = 'websocket:connect',
    WEBSOCKET_DISCONNECT = 'websocket:disconnect',
    WEBSOCKET_MESSAGE = 'websocket:message',
    WEBSOCKET_ERROR = 'websocket:error'
}

/**
 * 网络事件数据接口
 */
export interface INetworkEventData {
    type: NetworkEventType;
    data?: any;
    timestamp: number;
}

/**
 * HTTP客户端选项接口
 */
export interface IHttpClientOptions {
    baseURL?: string;
    timeout?: number;
    headers?: Record<string, string>;
    retries?: number;
    retryDelay?: number;
    requestInterceptor?: IRequestInterceptor;
    responseInterceptor?: IResponseInterceptor;
}

/**
 * WebSocket客户端选项接口
 */
export interface IWebSocketClientOptions {
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    heartbeatInterval?: number;
    timeout?: number;
    protocols?: string[];
}

/**
 * 网络管理器配置接口
 */
export interface INetworkManagerConfig {
    httpOptions?: IHttpClientOptions;
    websocketOptions?: IWebSocketClientOptions;
    enableQueue?: boolean;
    maxQueueSize?: number;
    enableRetry?: boolean;
    enableHeartbeat?: boolean;
}
