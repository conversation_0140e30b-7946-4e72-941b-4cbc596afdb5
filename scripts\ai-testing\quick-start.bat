@echo off
setlocal enabledelayedexpansion

REM AI测试框架快速启动脚本 (Windows版本)
REM 用于快速设置和运行AI测试框架

echo.
echo 🤖 AI测试框架快速启动
echo ==================================
echo.

REM 检查Node.js环境
echo 📋 步骤1: 检查Node.js环境
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装，请先安装Node.js ^(版本 ^>= 16.0.0^)
    pause
    exit /b 1
)

for /f "tokens=1 delims=v" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js版本: %NODE_VERSION%

npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm未安装
    pause
    exit /b 1
)

for /f %%i in ('npm --version') do set NPM_VERSION=%%i
echo ✅ npm版本: %NPM_VERSION%

REM 检查是否在正确的目录
if not exist "package.json" (
    echo ❌ package.json文件不存在，请确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

if not exist "core" (
    echo ❌ 请在AI测试框架目录中运行此脚本 ^(scripts/ai-testing/^)
    pause
    exit /b 1
)

REM 安装依赖
echo.
echo 📋 步骤2: 安装项目依赖
echo 正在安装依赖...
call npm install
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

REM 构建项目
echo.
echo 📋 步骤3: 构建TypeScript项目
if exist "tsconfig.json" (
    echo 正在编译TypeScript...
    call npm run build
    if errorlevel 1 (
        echo ⚠️ TypeScript编译失败，但可以继续使用ts-node运行
    ) else (
        echo ✅ TypeScript编译完成
    )
) else (
    echo ⚠️ 未找到tsconfig.json，跳过编译步骤
)

REM 创建输出目录
echo.
echo 📋 步骤4: 创建输出目录
if not exist "test-results" mkdir test-results
if not exist "validation-reports" mkdir validation-reports
if not exist "examples\output" mkdir examples\output
echo ✅ 输出目录创建完成

REM 运行快速检查
echo.
echo 📋 步骤5: 运行快速项目检查
echo 正在执行快速检查...
call npm run ai-test:quick-check -- --project-path ../../
if errorlevel 1 (
    echo ⚠️ 快速检查发现一些问题，但不影响继续使用
) else (
    echo ✅ 快速检查完成
)

REM 询问是否运行示例
echo.
set /p run_example="是否运行使用示例? (y/n): "
if /i "%run_example%"=="y" (
    echo.
    echo 📋 步骤6: 运行使用示例
    if exist "examples\usage-example.ts" (
        echo 正在运行使用示例...
        call npx ts-node examples/usage-example.ts
        if errorlevel 1 (
            echo ⚠️ 示例运行遇到问题
        ) else (
            echo ✅ 示例运行完成
        )
    ) else (
        echo ⚠️ 未找到使用示例文件
    )
)

REM 显示使用指南
echo.
echo 📋 步骤7: 显示使用指南
echo.
echo 🎉 AI测试框架安装完成！
echo.
echo 📚 常用命令:
echo   npm run ai-test:setup                    # 设置AI测试系统
echo   npm run ai-test:discover                 # 发现系统并生成测试
echo   npm run ai-test:validate-algorithms      # 验证算法一致性
echo   npm run ai-test:quick-check              # 快速项目检查
echo   npm run ai-test:report                   # 生成测试报告
echo.
echo 🔧 CLI工具:
echo   node index.js setup --project ../../    # 设置项目测试
echo   node index.js discover --project ../../ # 发现并测试系统
echo   node index.js validate                  # 验证算法一致性
echo   node index.js stats                     # 显示统计信息
echo   node index.js clean                     # 清理缓存
echo.
echo 📁 输出目录:
echo   .\test-results\          # 测试结果和报告
echo   .\validation-reports\    # 算法验证报告
echo   .\examples\output\       # 示例输出文件
echo.
echo 📖 文档:
echo   .\README.md              # 详细使用文档
echo   .\examples\              # 使用示例
echo   ..\..\plans\ai-testing\  # 设计文档
echo.
echo 🚀 准备就绪！开始使用AI测试框架吧！
echo.

pause
