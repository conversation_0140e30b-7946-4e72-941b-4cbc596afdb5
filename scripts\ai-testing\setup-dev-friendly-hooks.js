#!/usr/bin/env node

/**
 * 设置开发友好的Git Hooks
 * 更宽松的测试标准，适合开发阶段
 */

const fs = require('fs');
const path = require('path');

function setupDevFriendlyHooks() {
    console.log('🔧 设置开发友好的Git Hooks...\n');
    
    const projectRoot = path.resolve(__dirname, '../..');
    const hooksDir = path.join(projectRoot, '.git', 'hooks');
    
    if (!fs.existsSync(hooksDir)) {
        fs.mkdirSync(hooksDir, { recursive: true });
    }

    // 开发友好的 Pre-commit hook
    const preCommitHook = `#!/bin/sh
# 开发友好的AI测试框架 Pre-commit Hook

echo "🤖 运行AI测试框架（开发模式）..."

cd scripts/ai-testing

# 启动测试服务器（如果没有运行）
if ! curl -s http://localhost:3001/health > /dev/null 2>&1; then
    echo "🚀 启动测试服务器..."
    node test-server-simple.js &
    SERVER_PID=$!
    sleep 3
else
    echo "✅ 测试服务器已运行"
fi

# 运行AI测试框架
node ai-test-working.js

TEST_RESULT=$?

# 清理后台服务器进程（如果我们启动的）
if [ ! -z "$SERVER_PID" ]; then
    kill $SERVER_PID 2>/dev/null
fi

# 检查测试结果
if [ $TEST_RESULT -eq 0 ]; then
    echo "✅ AI测试通过，允许提交"
    exit 0
else
    echo "⚠️ AI测试发现问题，但允许提交（开发模式）"
    echo "💡 建议："
    echo "   • 运行 'npm run fix:quality' 改善代码质量"
    echo "   • 查看测试报告了解详细问题"
    echo "   • 使用 'git commit --no-verify' 跳过检查"
    echo ""
    
    # 询问是否继续提交
    echo "是否继续提交？(y/N)"
    read -r response
    case "$response" in
        [yY][eE][sS]|[yY]) 
            echo "✅ 继续提交"
            exit 0
            ;;
        *)
            echo "❌ 取消提交"
            exit 1
            ;;
    esac
fi
`;

    const preCommitPath = path.join(hooksDir, 'pre-commit');
    fs.writeFileSync(preCommitPath, preCommitHook);
    
    // 在Windows上设置可执行权限
    if (process.platform !== 'win32') {
        fs.chmodSync(preCommitPath, '755');
    }

    console.log('✅ 开发友好的Git pre-commit hook已设置');
    
    // 创建备份的严格模式hook
    const strictPreCommitHook = `#!/bin/sh
# 严格模式AI测试框架 Pre-commit Hook

echo "🤖 运行AI测试框架（严格模式）..."

cd scripts/ai-testing
node ai-test-working.js

if [ $? -ne 0 ]; then
    echo "❌ AI测试失败，提交被阻止"
    echo "💡 请运行 'npm run fix:quality' 修复代码质量问题"
    exit 1
fi

echo "✅ AI测试通过，允许提交"
`;

    const strictPreCommitPath = path.join(hooksDir, 'pre-commit-strict');
    fs.writeFileSync(strictPreCommitPath, strictPreCommitHook);
    
    if (process.platform !== 'win32') {
        fs.chmodSync(strictPreCommitPath, '755');
    }

    console.log('✅ 严格模式hook已保存为 pre-commit-strict');
    
    // 创建切换脚本
    const switchScript = `#!/bin/bash

HOOKS_DIR=".git/hooks"

case "$1" in
    "dev"|"development")
        echo "🔧 切换到开发模式Git hooks..."
        if [ -f "$HOOKS_DIR/pre-commit-dev" ]; then
            cp "$HOOKS_DIR/pre-commit-dev" "$HOOKS_DIR/pre-commit"
            chmod +x "$HOOKS_DIR/pre-commit"
            echo "✅ 已切换到开发模式"
        else
            echo "❌ 开发模式hook不存在"
        fi
        ;;
    "strict"|"production")
        echo "🔧 切换到严格模式Git hooks..."
        if [ -f "$HOOKS_DIR/pre-commit-strict" ]; then
            cp "$HOOKS_DIR/pre-commit-strict" "$HOOKS_DIR/pre-commit"
            chmod +x "$HOOKS_DIR/pre-commit"
            echo "✅ 已切换到严格模式"
        else
            echo "❌ 严格模式hook不存在"
        fi
        ;;
    "off"|"disable")
        echo "🔧 禁用Git hooks..."
        if [ -f "$HOOKS_DIR/pre-commit" ]; then
            mv "$HOOKS_DIR/pre-commit" "$HOOKS_DIR/pre-commit.disabled"
            echo "✅ Git hooks已禁用"
        else
            echo "ℹ️ Git hooks已经是禁用状态"
        fi
        ;;
    "on"|"enable")
        echo "🔧 启用Git hooks..."
        if [ -f "$HOOKS_DIR/pre-commit.disabled" ]; then
            mv "$HOOKS_DIR/pre-commit.disabled" "$HOOKS_DIR/pre-commit"
            chmod +x "$HOOKS_DIR/pre-commit"
            echo "✅ Git hooks已启用"
        else
            echo "ℹ️ Git hooks已经是启用状态"
        fi
        ;;
    *)
        echo "用法: $0 {dev|strict|off|on}"
        echo ""
        echo "模式说明:"
        echo "  dev      - 开发模式（宽松检查，允许用户选择）"
        echo "  strict   - 严格模式（严格检查，失败时阻止提交）"
        echo "  off      - 禁用所有Git hooks"
        echo "  on       - 启用Git hooks"
        exit 1
        ;;
esac
`;

    const switchScriptPath = path.join(projectRoot, 'switch-git-hooks.sh');
    fs.writeFileSync(switchScriptPath, switchScript);
    
    if (process.platform !== 'win32') {
        fs.chmodSync(switchScriptPath, '755');
    }

    console.log('✅ Git hooks切换脚本已创建: switch-git-hooks.sh');
    
    console.log('\n📋 使用说明:');
    console.log('  • 当前使用开发友好模式（允许用户选择是否继续提交）');
    console.log('  • 使用 ./switch-git-hooks.sh strict 切换到严格模式');
    console.log('  • 使用 ./switch-git-hooks.sh off 禁用Git hooks');
    console.log('  • 使用 git commit --no-verify 跳过任何检查');
}

if (require.main === module) {
    setupDevFriendlyHooks();
}

module.exports = { setupDevFriendlyHooks };
