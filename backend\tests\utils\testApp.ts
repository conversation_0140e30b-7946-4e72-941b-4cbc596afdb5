import { App } from '../../src/app';
import supertest = require('supertest');

/**
 * 测试应用工具类
 */
export class TestApp {
  private app: App;
  private request: supertest.SuperTest<supertest.Test>;

  constructor() {
    this.app = new App();
    this.request = supertest(this.app.getApp());
  }

  /**
   * 获取测试请求实例
   */
  public getRequest(): supertest.SuperTest<supertest.Test> {
    return this.request;
  }

  /**
   * 获取应用实例
   */
  public getApp(): App {
    return this.app;
  }

  /**
   * 启动测试应用
   */
  public async start(): Promise<void> {
    try {
      await this.app.connectDatabases();
    } catch (error) {
      console.warn('测试环境数据库连接失败，使用模拟数据库');
    }
  }

  /**
   * 停止测试应用
   */
  public async stop(): Promise<void> {
    try {
      await this.app.disconnectDatabases();
    } catch (error) {
      console.warn('测试环境数据库断开失败');
    }
  }

  /**
   * 创建测试用户令牌
   */
  public createTestToken(payload: any = {}): string {
    const defaultPayload = {
      userId: 'test_user_id',
      username: 'test_user',
      email: '<EMAIL>',
      role: 'user',
      ...payload,
    };

    // 简单的测试令牌（实际项目中应该使用真实的JWT）
    return Buffer.from(JSON.stringify(defaultPayload)).toString('base64');
  }

  /**
   * 创建认证头部
   */
  public createAuthHeader(token?: string): { Authorization: string } {
    const testToken = token || this.createTestToken();
    return {
      Authorization: `Bearer ${testToken}`,
    };
  }

  /**
   * 执行认证请求
   */
  public authenticatedRequest(method: 'get' | 'post' | 'put' | 'delete', url: string, token?: string) {
    const authHeader = this.createAuthHeader(token);
    return this.request[method](url).set(authHeader);
  }

  /**
   * 清理测试数据
   */
  public async cleanupTestData(): Promise<void> {
    // TODO: 清理测试数据库中的数据
    console.log('清理测试数据...');
  }
}

/**
 * 创建测试应用实例
 */
export function createTestApp(): TestApp {
  return new TestApp();
}

/**
 * 测试数据工厂
 */
export const TestDataFactory = {
  user: (overrides: any = {}) => ({
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123',
    ...overrides,
  }),

  character: (overrides: any = {}) => ({
    name: '测试角色',
    sect: 'wudang',
    ...overrides,
  }),

  battleAction: (overrides: any = {}) => ({
    battleId: 'test_battle_id',
    action: 'attack',
    ...overrides,
  }),

  chatMessage: (overrides: any = {}) => ({
    content: '测试消息',
    chatType: 'world',
    ...overrides,
  }),
};

/**
 * 测试断言工具
 */
export const TestAssertions = {
  /**
   * 验证API响应结构
   */
  expectApiResponse: (response: any, expectedData?: any) => {
    expect(response.body).toHaveProperty('success');
    expect(response.body).toHaveProperty('message');
    expect(response.body).toHaveProperty('timestamp');
    
    if (response.body.success) {
      expect(response.body).toHaveProperty('data');
      if (expectedData) {
        expect(response.body.data).toMatchObject(expectedData);
      }
    } else {
      expect(response.body).toHaveProperty('error');
      expect(response.body).toHaveProperty('errorCode');
    }
  },

  /**
   * 验证错误响应
   */
  expectErrorResponse: (response: any, expectedErrorCode?: string) => {
    expect(response.body.success).toBe(false);
    expect(response.body).toHaveProperty('error');
    expect(response.body).toHaveProperty('errorCode');
    expect(response.body).toHaveProperty('message');
    expect(response.body).toHaveProperty('timestamp');
    
    if (expectedErrorCode) {
      expect(response.body.errorCode).toBe(expectedErrorCode);
    }
  },

  /**
   * 验证成功响应
   */
  expectSuccessResponse: (response: any, expectedData?: any) => {
    expect(response.body.success).toBe(true);
    expect(response.body).toHaveProperty('message');
    expect(response.body).toHaveProperty('data');
    expect(response.body).toHaveProperty('timestamp');
    
    if (expectedData) {
      expect(response.body.data).toMatchObject(expectedData);
    }
  },

  /**
   * 验证分页响应
   */
  expectPaginatedResponse: (response: any) => {
    TestAssertions.expectSuccessResponse(response);
    expect(response.body.data).toHaveProperty('items');
    expect(response.body.data).toHaveProperty('pagination');
    expect(response.body.data.pagination).toHaveProperty('page');
    expect(response.body.data.pagination).toHaveProperty('limit');
    expect(response.body.data.pagination).toHaveProperty('total');
    expect(Array.isArray(response.body.data.items)).toBe(true);
  },
};
