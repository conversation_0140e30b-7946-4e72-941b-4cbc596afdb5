/**
 * 自动注册脚本
 * 确保所有组件都被正确注册到Cocos Creator
 */

// 导入所有场景控制器，确保它们被注册
import './scenes/LaunchScene';
import './scenes/MainScene';
import './scenes/BattleScene';

// 导入所有管理器
import './managers/BaseManager';
import './managers/GameManager';
import './managers/SceneManager';
import './managers/EventManager';
import './managers/ResourceManager';
import './managers/AudioManager';
import './managers/InputManager';

// 导入网络模块
import './network/NetworkManager';
import './network/HttpClient';
import './network/WebSocketClient';

// 导入所有UI组件
import './ui/components/SceneSwitchButtons';

// 导入所有系统组件
import './systems/characters/Character';

// 导入所有核心工具组件
import './core/utils/TestRunner';

// 导入测试管理器
import './test/TestManager';

// 导入所有测试组件
import './test/BasicTest';
import './test/SimpleTest';
import './test/Day2IntegrationTest';
import './test/ManagerTest';
import './test/KeyboardInputTest';
import './test/SimpleKeyboardTest';
import './test/EmergencyKeyboardTest';
import './test/StandaloneTest';
import './test/SimpleLaunchTest';
import './test/NetworkTest';
// 注释掉独立测试组件，避免键盘事件冲突
// import './test/SystemIntegrationTest';
// import './test/PerformanceBenchmarkTest';

console.log('📋 所有组件已自动注册');
