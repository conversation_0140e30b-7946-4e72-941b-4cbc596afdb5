# 开发工作流规范

> 📖 **导航**: [返回主页](./README.md) | [代码标准](./code-standards.md) | [测试规范](./testing-procedures.md)

## 🔄 版本控制工作流

### Git分支策略
```bash
# 分支命名规范
main                    # 主分支 - 生产环境代码
develop                 # 开发分支 - 集成开发代码
feature/feature-name    # 功能分支 - 新功能开发
hotfix/issue-name      # 热修复分支 - 紧急修复
release/version-number  # 发布分支 - 版本发布准备

# 示例分支名称
feature/wuxia-sect-system      # 武侠门派系统
feature/idle-offline-rewards   # 放置离线奖励
feature/ui-skill-panel        # 技能面板UI
hotfix/battle-damage-bug      # 战斗伤害计算bug修复
release/v1.0.0               # 1.0.0版本发布
```

### 提交信息规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

<body>

<footer>

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整
refactor: 代码重构
test:     测试相关
chore:    构建工具、依赖更新

# 范围说明
core:     核心系统
ui:       用户界面
battle:   战斗系统
sect:     门派系统
skill:    技能系统
idle:     放置系统
network:  网络通信
platform: 平台适配

# 提交信息示例
feat(sect): 实现门派加入和退出功能

- 添加门派数据结构定义
- 实现门派成员管理逻辑
- 添加门派声望系统
- 完成门派技能学习限制

Closes #123

fix(battle): 修复技能伤害计算错误

修复了暴击伤害计算公式中的乘数错误，
现在暴击伤害正确为基础伤害的1.5倍。

Fixes #456
```

### 代码审查流程
```typescript
// Pull Request 模板
interface IPullRequestTemplate {
    title: string;           // 简洁描述变更内容
    description: string;     // 详细描述变更原因和实现方式
    type: 'feature' | 'bugfix' | 'refactor' | 'docs';
    scope: string[];         // 影响的模块范围
    breaking: boolean;       // 是否包含破坏性变更
    testing: string;         // 测试说明
    screenshots?: string[];  // UI变更截图
    checklist: {
        codeReview: boolean;     // 代码已自查
        testPassed: boolean;     // 测试通过
        docsUpdated: boolean;    // 文档已更新
        noBreaking: boolean;     // 无破坏性变更
    };
}

// 代码审查检查清单
const CodeReviewChecklist = {
    // 代码质量
    codeQuality: [
        '代码符合项目编码规范',
        '函数和变量命名清晰明确',
        '代码逻辑清晰，易于理解',
        '没有重复代码',
        '错误处理完善'
    ],
    
    // 功能实现
    functionality: [
        '功能实现符合需求',
        '边界条件处理正确',
        '性能表现良好',
        '内存使用合理',
        '没有明显的bug'
    ],
    
    // 武侠游戏特定检查
    wuxiaSpecific: [
        '门派系统逻辑正确',
        '技能效果计算准确',
        '声望系统运作正常',
        '修炼进度合理',
        '游戏平衡性良好'
    ],
    
    // 小程序适配
    miniprogramAdaptation: [
        '小程序API使用正确',
        '性能优化到位',
        '包体大小控制',
        '兼容性测试通过',
        '用户体验良好'
    ]
};
```

## 🚀 持续集成/持续部署

### GitHub Actions配置
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run linting
      run: npm run lint
    
    - name: Run tests
      run: npm run test
    
    - name: Build project
      run: npm run build
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3

  build-wechat:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3
    
    - name: Build WeChat MiniProgram
      run: |
        npm run build:wechat
        
    - name: Deploy to WeChat
      run: |
        npm run deploy:wechat
      env:
        WECHAT_APP_ID: ${{ secrets.WECHAT_APP_ID }}
        WECHAT_APP_SECRET: ${{ secrets.WECHAT_APP_SECRET }}

  build-douyin:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3
    
    - name: Build Douyin MiniProgram
      run: |
        npm run build:douyin
        
    - name: Deploy to Douyin
      run: |
        npm run deploy:douyin
      env:
        DOUYIN_APP_ID: ${{ secrets.DOUYIN_APP_ID }}
        DOUYIN_APP_SECRET: ${{ secrets.DOUYIN_APP_SECRET }}
```

### 构建脚本配置
```json
{
  "scripts": {
    "dev": "cocos-creator --project . --mode=dev",
    "build": "cocos-creator --project . --build",
    "build:wechat": "cocos-creator --project . --build platform=wechatgame",
    "build:douyin": "cocos-creator --project . --build platform=bytedance-mini-game",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint assets/scripts --ext .ts",
    "lint:fix": "eslint assets/scripts --ext .ts --fix",
    "format": "prettier --write \"assets/scripts/**/*.ts\"",
    "deploy:wechat": "node scripts/deploy-wechat.js",
    "deploy:douyin": "node scripts/deploy-douyin.js",
    "docs": "typedoc --out docs assets/scripts",
    "clean": "rimraf build temp library"
  }
}
```

## 📋 任务管理流程

### 需求管理
```typescript
// 需求模板
interface IRequirement {
    id: string;
    title: string;
    description: string;
    type: 'feature' | 'enhancement' | 'bugfix';
    priority: 'critical' | 'high' | 'medium' | 'low';
    complexity: 'simple' | 'medium' | 'complex';
    estimatedHours: number;
    assignee: string;
    reviewer: string;
    status: 'backlog' | 'in_progress' | 'review' | 'testing' | 'done';
    labels: string[];
    acceptanceCriteria: string[];
    testCases: string[];
}

// 武侠游戏特定需求标签
const WuxiaGameLabels = [
    'sect-system',      // 门派系统
    'skill-system',     // 技能系统
    'battle-system',    // 战斗系统
    'idle-system',      // 放置系统
    'social-system',    // 社交系统
    'ui-ux',           // 用户界面
    'performance',     // 性能优化
    'miniprogram',     // 小程序适配
    'wechat',          // 微信平台
    'douyin',          // 抖音平台
    'data-migration',  // 数据迁移
    'testing',         // 测试相关
    'documentation'    // 文档相关
];
```

### 迭代计划
```typescript
// 迭代计划模板
interface ISprintPlan {
    sprintNumber: number;
    startDate: string;
    endDate: string;
    goal: string;
    capacity: number;           // 团队总工时
    commitments: string[];      // 承诺完成的需求
    risks: string[];           // 风险识别
    dependencies: string[];     // 依赖项
    
    // 武侠游戏特定目标
    wuxiaGoals: {
        sectSystemProgress: number;     // 门派系统进度
        skillSystemProgress: number;    // 技能系统进度
        battleSystemProgress: number;   // 战斗系统进度
        uiProgress: number;            // UI进度
        migrationProgress: number;      // 迁移进度
    };
}

// 示例迭代计划
const Sprint1Plan: ISprintPlan = {
    sprintNumber: 1,
    startDate: '2025-07-22',
    endDate: '2025-08-05',
    goal: '完成核心架构搭建和基础门派系统',
    capacity: 80,
    commitments: [
        '搭建项目基础架构',
        '实现门派数据结构',
        '完成门派加入/退出功能',
        '实现基础技能系统'
    ],
    risks: [
        'Cocos Creator学习曲线',
        '数据迁移复杂度',
        '小程序平台限制'
    ],
    dependencies: [
        '设计文档确认',
        '开发环境搭建',
        '第三方库选型'
    ],
    wuxiaGoals: {
        sectSystemProgress: 60,
        skillSystemProgress: 30,
        battleSystemProgress: 0,
        uiProgress: 20,
        migrationProgress: 40
    }
};
```

## 🔍 质量保证流程

### 代码质量检查
```bash
# 代码质量检查脚本
#!/bin/bash

echo "开始代码质量检查..."

# 1. 代码格式检查
echo "检查代码格式..."
npm run lint
if [ $? -ne 0 ]; then
    echo "❌ 代码格式检查失败"
    exit 1
fi

# 2. 类型检查
echo "检查TypeScript类型..."
npx tsc --noEmit
if [ $? -ne 0 ]; then
    echo "❌ TypeScript类型检查失败"
    exit 1
fi

# 3. 单元测试
echo "运行单元测试..."
npm run test
if [ $? -ne 0 ]; then
    echo "❌ 单元测试失败"
    exit 1
fi

# 4. 代码覆盖率检查
echo "检查代码覆盖率..."
npm run test:coverage
COVERAGE=$(cat coverage/coverage-summary.json | jq '.total.lines.pct')
if (( $(echo "$COVERAGE < 80" | bc -l) )); then
    echo "❌ 代码覆盖率低于80%: $COVERAGE%"
    exit 1
fi

# 5. 构建测试
echo "测试构建..."
npm run build
if [ $? -ne 0 ]; then
    echo "❌ 构建失败"
    exit 1
fi

echo "✅ 所有质量检查通过"
```

---

> 📖 **相关文档**: [测试规范](./testing-procedures.md) | [部署发布](./deployment-process.md) | [代码标准](./code-standards.md)
