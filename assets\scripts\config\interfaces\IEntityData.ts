/**
 * 实体数据接口定义
 * 基于Godot项目中的entities.xml结构
 */

/**
 * 实体数据接口
 */
export interface IEntityData {
    /** 实体唯一标识符 */
    id: string;
    
    /** 实体名称 */
    name: string;
    
    /** 实体类型 */
    type: EntityType;
    
    /** 等级 */
    level: number;
    
    /** 生命值 */
    health: number;
    
    /** 法力值 */
    mana: number;
    
    /** 攻击力 */
    attack: number;
    
    /** 防御力 */
    defense: number;
    
    /** 速度 */
    speed: number;
    
    /** 经验值 */
    experience: number;
    
    /** 拥有的技能列表 */
    skills: string[];
    
    /** 掉落物品列表 */
    drops: IDropItem[];
    
    /** 实体属性 */
    attributes?: IEntityAttributes;
    
    /** AI行为配置 */
    aiConfig?: IEntityAIConfig;
}

/**
 * 实体类型枚举
 */
export enum EntityType {
    Player = 'player',
    NPC = 'npc',
    Enemy = 'enemy',
    Boss = 'boss',
    Pet = 'pet',
    Summon = 'summon'
}

/**
 * 掉落物品接口
 */
export interface IDropItem {
    /** 物品ID */
    itemId: string;
    
    /** 掉落概率（0-1） */
    chance: number;
    
    /** 掉落数量 */
    quantity: number;
    
    /** 最小数量 */
    minQuantity?: number;
    
    /** 最大数量 */
    maxQuantity?: number;
}

/**
 * 实体属性接口
 */
export interface IEntityAttributes {
    /** 力量 */
    strength: number;
    
    /** 敏捷 */
    agility: number;
    
    /** 智力 */
    intelligence: number;
    
    /** 体质 */
    vitality: number;
    
    /** 幸运 */
    luck: number;
    
    /** 魅力 */
    charisma: number;
}

/**
 * 实体AI配置接口
 */
export interface IEntityAIConfig {
    /** AI类型 */
    aiType: EntityAIType;
    
    /** 攻击范围 */
    attackRange: number;
    
    /** 视野范围 */
    visionRange: number;
    
    /** 巡逻范围 */
    patrolRange: number;
    
    /** 追击范围 */
    chaseRange: number;
    
    /** 攻击间隔 */
    attackInterval: number;
    
    /** 移动速度 */
    moveSpeed: number;
    
    /** 是否主动攻击 */
    aggressive: boolean;
    
    /** 技能使用策略 */
    skillStrategy: ISkillStrategy[];
}

/**
 * 实体AI类型枚举
 */
export enum EntityAIType {
    Passive = 'passive',
    Aggressive = 'aggressive',
    Defensive = 'defensive',
    Patrol = 'patrol',
    Guard = 'guard',
    Flee = 'flee'
}

/**
 * 技能使用策略接口
 */
export interface ISkillStrategy {
    /** 技能ID */
    skillId: string;
    
    /** 使用优先级 */
    priority: number;
    
    /** 使用条件 */
    conditions: ISkillCondition[];
    
    /** 冷却时间 */
    cooldown: number;
}

/**
 * 技能使用条件接口
 */
export interface ISkillCondition {
    /** 条件类型 */
    type: SkillConditionType;
    
    /** 条件值 */
    value: number;
    
    /** 比较操作符 */
    operator: ComparisonOperator;
}

/**
 * 技能条件类型枚举
 */
export enum SkillConditionType {
    HealthPercent = 'health_percent',
    ManaPercent = 'mana_percent',
    EnemyCount = 'enemy_count',
    Distance = 'distance',
    BuffCount = 'buff_count',
    DebuffCount = 'debuff_count'
}

/**
 * 比较操作符枚举
 */
export enum ComparisonOperator {
    Equal = 'equal',
    NotEqual = 'not_equal',
    Greater = 'greater',
    GreaterEqual = 'greater_equal',
    Less = 'less',
    LessEqual = 'less_equal'
}

/**
 * 实体状态接口
 */
export interface IEntityState {
    /** 实体ID */
    entityId: string;
    
    /** 当前生命值 */
    currentHealth: number;
    
    /** 当前法力值 */
    currentMana: number;
    
    /** 位置 */
    position: IVector2;
    
    /** 朝向 */
    direction: number;
    
    /** 当前状态 */
    state: EntityState;
    
    /** 目标实体ID */
    targetId?: string;
    
    /** 活跃的效果 */
    activeEffects: IActiveEffect[];
    
    /** 技能冷却状态 */
    skillCooldowns: Map<string, number>;
}

/**
 * 二维向量接口
 */
export interface IVector2 {
    x: number;
    y: number;
}

/**
 * 实体状态枚举
 */
export enum EntityState {
    Idle = 'idle',
    Moving = 'moving',
    Attacking = 'attacking',
    Casting = 'casting',
    Dead = 'dead',
    Stunned = 'stunned',
    Fleeing = 'fleeing'
}

/**
 * 活跃效果接口
 */
export interface IActiveEffect {
    /** 效果ID */
    id: string;
    
    /** 效果类型 */
    type: string;
    
    /** 效果值 */
    value: number;
    
    /** 剩余持续时间 */
    remainingDuration: number;
    
    /** 效果来源 */
    source: string;
    
    /** 是否可叠加 */
    stackable: boolean;
    
    /** 叠加层数 */
    stacks: number;
}

/**
 * 实体配置文件根接口
 */
export interface IEntityConfig {
    /** 配置版本 */
    version: string;
    
    /** 最后更新时间 */
    lastUpdated: string;
    
    /** 实体列表 */
    entities: IEntityData[];
}

/**
 * 实体创建参数接口
 */
export interface IEntityCreateParams {
    /** 实体数据ID */
    entityDataId: string;
    
    /** 创建位置 */
    position: IVector2;
    
    /** 等级覆盖 */
    levelOverride?: number;
    
    /** 属性修正 */
    attributeModifiers?: Partial<IEntityAttributes>;
    
    /** 额外技能 */
    additionalSkills?: string[];
}

/**
 * 实体伤害事件接口
 */
export interface IEntityDamageEvent {
    /** 攻击者ID */
    attackerId: string;
    
    /** 受害者ID */
    victimId: string;
    
    /** 伤害值 */
    damage: number;
    
    /** 伤害类型 */
    damageType: string;
    
    /** 是否暴击 */
    isCritical: boolean;
    
    /** 技能ID（如果是技能伤害） */
    skillId?: string;
    
    /** 时间戳 */
    timestamp: number;
}

/**
 * 实体死亡事件接口
 */
export interface IEntityDeathEvent {
    /** 死亡实体ID */
    entityId: string;
    
    /** 击杀者ID */
    killerId?: string;
    
    /** 掉落物品 */
    drops: IDropItem[];
    
    /** 经验奖励 */
    experienceReward: number;
    
    /** 时间戳 */
    timestamp: number;
}

/**
 * 实体升级事件接口
 */
export interface IEntityLevelUpEvent {
    /** 实体ID */
    entityId: string;
    
    /** 旧等级 */
    oldLevel: number;
    
    /** 新等级 */
    newLevel: number;
    
    /** 属性提升 */
    attributeGains: Partial<IEntityAttributes>;
    
    /** 时间戳 */
    timestamp: number;
}
