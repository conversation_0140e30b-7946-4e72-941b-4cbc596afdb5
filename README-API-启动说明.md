# IdleGame API 服务器启动说明

## 🚨 问题解决方案

您遇到的"无法连接"问题是因为**Docker没有安装**，而我们创建的是Docker配置文件。

## 🚀 快速解决方案

### 方案1：启动简化版API服务器（推荐）

1. **打开新的命令提示符或PowerShell窗口**
2. **导航到项目目录**：
   ```bash
   cd "D:\COCOS\Projects\IdleGame\COCOS_IdelGame\backend"
   ```

3. **启动API服务器**：
   ```bash
   node api-server-8080.js
   ```

4. **看到成功信息后，访问以下链接**：
   - 🏠 **主页**: http://localhost:8080/api/docs
   - 🔍 **健康检查**: http://localhost:8080/api/health
   - 👥 **用户API**: http://localhost:8080/api/v1/users
   - 🎮 **角色API**: http://localhost:8080/api/v1/characters
   - ⚔️ **技能API**: http://localhost:8080/api/v1/skills
   - 🎒 **物品API**: http://localhost:8080/api/v1/items

### 方案2：安装Docker（完整解决方案）

如果您想使用完整的Docker部署：

1. **下载Docker Desktop**：
   - 访问：https://www.docker.com/products/docker-desktop
   - 下载Windows版本

2. **安装Docker Desktop**：
   - 运行安装程序
   - 重启计算机
   - 启动Docker Desktop

3. **启动完整服务**：
   ```bash
   docker-compose up -d
   ```

## 📋 服务说明

### 简化版API服务器特性
- ✅ **端口**: 8080 (避免与MCP工具的3000端口冲突)
- ✅ **无需数据库**: 使用模拟数据
- ✅ **即时启动**: 无需Docker环境
- ✅ **完整API**: 包含所有主要端点
- ✅ **美观界面**: 包含API文档页面

### 可用的API端点

| 端点 | 方法 | 描述 | 示例URL |
|------|------|------|---------|
| `/api/health` | GET | 健康检查 | http://localhost:8080/api/health |
| `/api/docs` | GET | API文档 | http://localhost:8080/api/docs |
| `/api` | GET | 基本信息 | http://localhost:8080/api |
| `/api/v1/users` | GET | 用户列表 | http://localhost:8080/api/v1/users |
| `/api/v1/characters` | GET | 角色列表 | http://localhost:8080/api/v1/characters |
| `/api/v1/skills` | GET | 技能列表 | http://localhost:8080/api/v1/skills |
| `/api/v1/items` | GET | 物品列表 | http://localhost:8080/api/v1/items |

## 🔧 故障排除

### 如果8080端口也被占用：

1. **修改端口**：
   - 编辑 `backend/api-server-8080.js`
   - 将 `const PORT = 8080;` 改为其他端口，如 `const PORT = 9000;`

2. **查找占用端口的程序**：
   ```bash
   netstat -ano | findstr :8080
   ```

3. **终止占用端口的进程**：
   ```bash
   taskkill /PID <进程ID> /F
   ```

### 如果Node.js未安装：

1. **下载Node.js**：
   - 访问：https://nodejs.org/
   - 下载LTS版本

2. **安装Node.js**：
   - 运行安装程序
   - 重启命令提示符

## 🎯 测试步骤

1. **启动服务器**后，您应该看到类似输出：
   ```
   🚀 IdleGame API 服务器启动成功!
   ========================================
   📍 服务地址:
      http://localhost:8080
   
   🔗 快速测试链接:
      健康检查: http://localhost:8080/api/health
      API文档:  http://localhost:8080/api/docs
   ```

2. **在浏览器中访问**：
   - 首先访问：http://localhost:8080/api/docs
   - 您应该看到一个美观的API文档页面

3. **测试API端点**：
   - 点击文档页面中的测试链接
   - 或直接访问各个API端点

## 📞 需要帮助？

如果仍然遇到问题：

1. **检查Node.js版本**：
   ```bash
   node --version
   ```

2. **检查端口占用**：
   ```bash
   netstat -ano | findstr :8080
   ```

3. **查看错误信息**：
   - 启动服务器时的完整错误信息
   - 浏览器控制台的错误信息

## 🎉 成功标志

当您看到以下情况时，说明服务器启动成功：

- ✅ 命令行显示"服务器启动成功"
- ✅ 浏览器能访问 http://localhost:8080/api/docs
- ✅ API端点返回JSON数据
- ✅ 健康检查显示"healthy"状态

---

**💡 提示**: 这个简化版本足以测试前后端集成功能，无需完整的Docker环境。
