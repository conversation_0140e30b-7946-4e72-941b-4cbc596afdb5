"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
// 简单的编译测试文件
const MasterTestBot_1 = require("./core/MasterTestBot");
const TestBotFactory_1 = require("./core/TestBotFactory");
const SystemDiscoveryAgent_1 = require("./agents/SystemDiscoveryAgent");
console.log('Testing imports...');
try {
    const masterBot = new MasterTestBot_1.MasterTestBot();
    const factory = new TestBotFactory_1.TestBotFactory();
    const discovery = new SystemDiscoveryAgent_1.SystemDiscoveryAgent();
    console.log('✅ All imports successful');
}
catch (error) {
    console.error('❌ Import error:', error);
}
//# sourceMappingURL=compile-test.js.map