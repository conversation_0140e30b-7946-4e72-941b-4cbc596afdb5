import { _decorator, Component, input, Input, EventKeyboard, KeyCode } from 'cc';
import { NetworkManager } from '../network/NetworkManager';
import { HttpClient } from '../network/HttpClient';
import { WebSocketClient } from '../network/WebSocketClient';
import { Network } from '../network/index';
import { NetworkEventType } from '../network/types/NetworkTypes';
import { EventManager } from '../managers/EventManager';

const { ccclass } = _decorator;

/**
 * 网络模块测试组件
 * 测试HTTP客户端、WebSocket客户端和网络管理器的功能
 */
@ccclass('NetworkTest')
export class NetworkTest extends Component {
    private _networkManager: NetworkManager;
    private _httpClient: HttpClient;
    private _websocketClient: WebSocketClient;

    protected onLoad(): void {
        console.log('🌐 网络模块测试组件加载');
        this._initializeNetworkComponents();
        this._initializeKeyboardInput();
        this._setupNetworkEventListeners();
    }

    protected start(): void {
        console.log('🌐 网络模块测试开始');
        this._showTestInstructions();
    }

    /**
     * 初始化网络组件
     */
    private _initializeNetworkComponents(): void {
        this._networkManager = NetworkManager.getInstance();
        this._httpClient = new HttpClient({
            baseURL: 'https://jsonplaceholder.typicode.com',
            timeout: 5000,
            retries: 2
        });
        this._websocketClient = new WebSocketClient({
            reconnectInterval: 3000,
            maxReconnectAttempts: 3,
            heartbeatInterval: 30000
        });
    }

    /**
     * 初始化键盘输入
     */
    private _initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this._onKeyDown, this);
        console.log('⌨️ 网络测试键盘输入已初始化');
    }

    /**
     * 设置网络事件监听
     */
    private _setupNetworkEventListeners(): void {
        const eventManager = EventManager.getInstance();
        
        eventManager.on(NetworkEventType.ONLINE, () => {
            console.log('🟢 网络已连接');
        });
        
        eventManager.on(NetworkEventType.OFFLINE, () => {
            console.log('🔴 网络已断开');
        });
        
        eventManager.on(NetworkEventType.REQUEST_START, (data) => {
            console.log('📤 HTTP请求开始:', data.config.url);
        });
        
        eventManager.on(NetworkEventType.REQUEST_SUCCESS, (data) => {
            console.log('✅ HTTP请求成功:', data.response.status);
        });
        
        eventManager.on(NetworkEventType.REQUEST_ERROR, (data) => {
            console.log('❌ HTTP请求失败:', data.error.message);
        });
        
        eventManager.on(NetworkEventType.WEBSOCKET_CONNECT, () => {
            console.log('🔗 WebSocket连接成功');
        });
        
        eventManager.on(NetworkEventType.WEBSOCKET_DISCONNECT, () => {
            console.log('🔌 WebSocket连接断开');
        });
        
        eventManager.on(NetworkEventType.WEBSOCKET_MESSAGE, (data) => {
            console.log('📨 WebSocket消息:', data.message);
        });
    }

    /**
     * 显示测试说明
     */
    private _showTestInstructions(): void {
        console.log('🌐 ========== 网络模块测试 ==========');
        console.log('📍 当前组件: NetworkTest (网络测试)');
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 测试HTTP GET请求');
        console.log('   按 2 键 - 测试HTTP POST请求');
        console.log('   按 3 键 - 测试WebSocket连接');
        console.log('   按 4 键 - 发送WebSocket消息');
        console.log('   按 5 键 - 测试网络状态');
        console.log('   按 6 键 - 测试便捷API');
        console.log('   按 7 键 - 测试错误处理');
        console.log('   按 8 键 - 断开WebSocket');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🌐 ===================================');
    }

    /**
     * 键盘输入处理
     */
    private _onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this._testHttpGet();
                break;
            case KeyCode.DIGIT_2:
                this._testHttpPost();
                break;
            case KeyCode.DIGIT_3:
                this._testWebSocketConnect();
                break;
            case KeyCode.DIGIT_4:
                this._testWebSocketSend();
                break;
            case KeyCode.DIGIT_5:
                this._testNetworkStatus();
                break;
            case KeyCode.DIGIT_6:
                this._testConvenienceAPI();
                break;
            case KeyCode.DIGIT_7:
                this._testErrorHandling();
                break;
            case KeyCode.DIGIT_8:
                this._testWebSocketDisconnect();
                break;
            case KeyCode.KEY_H:
                this._showTestInstructions();
                break;
        }
    }

    /**
     * 测试HTTP GET请求
     */
    private async _testHttpGet(): Promise<void> {
        console.log('🔄 测试HTTP GET请求...');
        
        try {
            const response = await this._httpClient.get('/posts/1');
            console.log('✅ HTTP GET成功:', response);
        } catch (error) {
            console.error('❌ HTTP GET失败:', error);
        }
    }

    /**
     * 测试HTTP POST请求
     */
    private async _testHttpPost(): Promise<void> {
        console.log('🔄 测试HTTP POST请求...');
        
        try {
            const data = {
                title: '武侠放置游戏',
                body: '这是一个测试POST请求',
                userId: 1
            };
            
            const response = await this._httpClient.post('/posts', data);
            console.log('✅ HTTP POST成功:', response);
        } catch (error) {
            console.error('❌ HTTP POST失败:', error);
        }
    }

    /**
     * 测试WebSocket连接
     */
    private async _testWebSocketConnect(): Promise<void> {
        console.log('🔄 测试WebSocket连接...');
        
        try {
            // 使用公共WebSocket测试服务器
            await this._websocketClient.connect('wss://echo.websocket.org');
            console.log('✅ WebSocket连接成功');
            
            // 设置消息监听
            this._websocketClient.on('message', (message) => {
                console.log('📨 收到WebSocket消息:', message);
            });
            
        } catch (error) {
            console.error('❌ WebSocket连接失败:', error);
        }
    }

    /**
     * 测试WebSocket发送消息
     */
    private _testWebSocketSend(): void {
        console.log('🔄 测试WebSocket发送消息...');
        
        if (this._websocketClient.isConnected()) {
            const message = {
                type: 'test',
                data: '这是一个测试消息',
                timestamp: Date.now()
            };
            
            this._websocketClient.send(message);
            console.log('📤 WebSocket消息已发送:', message);
        } else {
            console.warn('⚠️ WebSocket未连接，请先连接');
        }
    }

    /**
     * 测试网络状态
     */
    private _testNetworkStatus(): void {
        console.log('🔄 测试网络状态...');
        
        const status = this._networkManager.getNetworkStatus();
        console.log('📊 网络状态:', {
            在线状态: status.isOnline ? '在线' : '离线',
            网络类型: status.networkType,
            连接质量: status.connectionQuality,
            延迟: status.latency + 'ms',
            最后检查: new Date(status.lastCheck).toLocaleTimeString()
        });
        
        console.log('🔍 网络类型:', this._networkManager.getNetworkType());
        console.log('🌐 是否在线:', this._networkManager.isOnline());
    }

    /**
     * 测试便捷API
     */
    private async _testConvenienceAPI(): Promise<void> {
        console.log('🔄 测试便捷API...');
        
        try {
            // 使用便捷API发送GET请求
            const response = await Network.get('https://jsonplaceholder.typicode.com/users/1');
            console.log('✅ 便捷API GET成功:', response);
            
            // 测试网络状态便捷API
            console.log('📊 便捷API网络状态:', Network.getNetworkStatus());
            console.log('🌐 便捷API在线状态:', Network.isOnline());
            
        } catch (error) {
            console.error('❌ 便捷API测试失败:', error);
        }
    }

    /**
     * 测试错误处理
     */
    private async _testErrorHandling(): Promise<void> {
        console.log('🔄 测试错误处理...');
        
        try {
            // 故意发送一个会失败的请求
            await this._httpClient.get('/nonexistent-endpoint');
        } catch (error) {
            console.log('✅ 错误处理测试成功，捕获到预期错误:', error);
        }
        
        try {
            // 测试超时
            const timeoutClient = new HttpClient({ timeout: 1 });
            await timeoutClient.get('https://httpbin.org/delay/5');
        } catch (error) {
            console.log('✅ 超时处理测试成功:', error);
        }
    }

    /**
     * 测试WebSocket断开
     */
    private _testWebSocketDisconnect(): void {
        console.log('🔄 测试WebSocket断开...');
        
        if (this._websocketClient.isConnected()) {
            this._websocketClient.disconnect();
            console.log('✅ WebSocket已断开');
        } else {
            console.warn('⚠️ WebSocket未连接');
        }
    }

    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this._onKeyDown, this);
        
        // 断开WebSocket连接
        if (this._websocketClient.isConnected()) {
            this._websocketClient.disconnect();
        }
        
        console.log('🌐 网络模块测试组件销毁');
    }
}
