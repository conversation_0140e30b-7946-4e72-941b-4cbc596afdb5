import { _decorator, Component, Node, input, Input, EventKeyboard, KeyCode } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 键盘输入测试组件
 * 用于测试键盘输入是否正常工作
 */
@ccclass('KeyboardInputTest')
export class KeyboardInputTest extends Component {

    protected onLoad(): void {
        console.log('⌨️ 键盘输入测试组件加载');
        this.initializeKeyboardInput();
    }

    protected start(): void {
        console.log('⌨️ 键盘输入测试组件开始');
        this.showTestInstructions();
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        console.log('⌨️ 正在初始化键盘输入...');
        
        try {
            input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
            console.log('✅ 键盘输入初始化成功');
        } catch (error) {
            console.error('❌ 键盘输入初始化失败:', error);
        }
    }

    /**
     * 显示测试说明
     */
    private showTestInstructions(): void {
        console.log('⌨️ ========== 键盘输入测试 ==========');
        console.log('📍 测试目标: 验证键盘输入功能');
        console.log('⌨️ 测试按键:');
        console.log('   按 T 键 - 测试键盘输入');
        console.log('   按 1 键 - 数字键测试');
        console.log('   按 A 键 - 字母键测试');
        console.log('   按 空格键 - 空格键测试');
        console.log('   按 ESC 键 - ESC键测试');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('⌨️ ===================================');
        console.log('💡 提示: 确保游戏窗口有焦点，然后按任意测试按键');
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        console.log(`⌨️ 检测到按键: ${event.keyCode} (${KeyCode[event.keyCode] || '未知按键'})`);
        
        switch (event.keyCode) {
            case KeyCode.KEY_T:
                this.testKeyboardInput();
                break;
            case KeyCode.DIGIT_1:
                console.log('🔢 数字键1测试成功！');
                break;
            case KeyCode.KEY_A:
                console.log('🔤 字母键A测试成功！');
                break;
            case KeyCode.SPACE:
                console.log('⭐ 空格键测试成功！');
                break;
            case KeyCode.ESCAPE:
                console.log('🚪 ESC键测试成功！');
                break;
            case KeyCode.KEY_H:
                this.showTestInstructions();
                break;
            default:
                console.log(`⌨️ 其他按键测试: ${event.keyCode}`);
                break;
        }
    }

    /**
     * 测试键盘输入功能
     */
    private testKeyboardInput(): void {
        console.log('🧪 ========== 键盘输入功能测试 ==========');
        console.log('✅ 键盘输入监听器正常工作');
        console.log('✅ 按键事件正确触发');
        console.log('✅ 事件处理函数正常执行');
        console.log('🎉 键盘输入测试通过！');
        console.log('🧪 ======================================');
    }

    /**
     * 组件启用时
     */
    protected onEnable(): void {
        console.log('⌨️ 键盘输入测试组件启用');
    }

    /**
     * 组件禁用时
     */
    protected onDisable(): void {
        console.log('⌨️ 键盘输入测试组件禁用');
    }

    /**
     * 组件销毁时
     */
    protected onDestroy(): void {
        console.log('⌨️ 正在清理键盘输入监听器...');
        
        try {
            input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);
            console.log('✅ 键盘输入监听器清理成功');
        } catch (error) {
            console.error('❌ 键盘输入监听器清理失败:', error);
        }
        
        console.log('⌨️ 键盘输入测试组件销毁');
    }
}
