{"version": 3, "file": "discover-and-test.js", "sourceRoot": "", "sources": ["../../cli/discover-and-test.ts"], "names": [], "mappings": ";;AAEA;;;GAGG;;;;;AAEH,yCAAoC;AACpC,kDAA0B;AAE1B,yDAAsD;AACtD,2DAAwD;AACxD,yEAAsE;AAEtE,MAAM,OAAO,GAAG,IAAI,mBAAO,EAAE,CAAC;AAa9B,OAAO;KACF,IAAI,CAAC,2BAA2B,CAAC;KACjC,WAAW,CAAC,gDAAgD,CAAC;KAC7D,OAAO,CAAC,OAAO,CAAC;KAChB,MAAM,CAAC,2BAA2B,EAAE,yBAAyB,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;KAC7E,MAAM,CAAC,wBAAwB,EAAE,sCAAsC,EAAE,YAAY,CAAC;KACtF,MAAM,CAAC,eAAe,EAAE,wBAAwB,EAAE,KAAK,CAAC;KACxD,MAAM,CAAC,eAAe,EAAE,iCAAiC,EAAE,KAAK,CAAC;KACjE,MAAM,CAAC,0BAA0B,EAAE,uBAAuB,CAAC;KAC3D,MAAM,CAAC,4BAA4B,EAAE,sCAAsC,EAAE,IAAI,CAAC;KAClF,MAAM,CAAC,YAAY,EAAE,4BAA4B,EAAE,KAAK,CAAC;KACzD,MAAM,CAAC,mBAAmB,EAAE,mCAAmC,EAAE,IAAI,CAAC;KACtE,MAAM,CAAC,KAAK,EAAE,OAA+B,EAAE,EAAE;IAC9C,MAAM,eAAe,CAAC,OAAO,CAAC,CAAC;AACnC,CAAC,CAAC,CAAC;AAEP,KAAK,UAAU,eAAe,CAAC,OAA+B;IAC1D,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC,CAAC;IACvE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,YAAY,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC;IAC3D,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,WAAW,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;IAExD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;QACjB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,4CAA4C,CAAC,CAAC,CAAC;IAC5E,CAAC;IAED,IAAI,CAAC;QACD,eAAe;QACf,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,gDAAgD,CAAC,CAAC,CAAC;QAC1E,MAAM,SAAS,GAAG,IAAI,6BAAa,EAAE,CAAC;QACtC,MAAM,cAAc,GAAG,IAAI,+BAAc,EAAE,CAAC;QAC5C,MAAM,cAAc,GAAG,IAAI,2CAAoB,EAAE,CAAC;QAElD,cAAc;QACd,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC,CAAC;QAC5D,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,eAAe,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAE1E,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,oCAAoC,CAAC,CAAC,CAAC;YAChE,OAAO;QACX,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,WAAW,OAAO,CAAC,MAAM,WAAW,CAAC,CAAC,CAAC;QAC/D,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC9B,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACtG,CAAC,CAAC,CAAC;QAEH,mBAAmB;QACnB,IAAI,eAAe,GAAG,OAAO,CAAC;QAC9B,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACrB,eAAe,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,UAAU,CAAC,CAAC;YACrE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,oBAAoB,eAAe,CAAC,MAAM,qBAAqB,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QAClH,CAAC;QAED,eAAe;QACf,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC;QACxD,IAAI,eAAe,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;YACtC,eAAe,GAAG,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;YACvD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,MAAM,CAAC,uBAAuB,UAAU,UAAU,CAAC,CAAC,CAAC;QAC3E,CAAC;QAED,uBAAuB;QACvB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,sDAAsD,CAAC,CAAC,CAAC;QAEhF,MAAM,OAAO,GAAG,EAAE,CAAC;QAEnB,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,OAAO;YACP,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC,CAAC;YAC5D,MAAM,QAAQ,GAAG,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;gBAClD,OAAO,MAAM,aAAa,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;YAChE,CAAC,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,MAAM,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YAC3D,eAAe,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACtC,IAAI,MAAM,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;oBAChC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,uBAAuB,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBACrG,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;aAAM,CAAC;YACJ,OAAO;YACP,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC,CAAC;YAC9D,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;gBACnC,IAAI,CAAC;oBACD,MAAM,MAAM,GAAG,MAAM,aAAa,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,CAAC,CAAC;oBACpE,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACzB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACb,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,uBAAuB,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC7E,CAAC;YACL,CAAC;QACL,CAAC;QAED,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC,CAAC;QAClE,MAAM,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE9C,YAAY;QACZ,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,qEAAqE,CAAC,CAAC,CAAC;QAChG,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,eAAe,OAAO,CAAC,MAAM,UAAU,CAAC,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,wBAAwB,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;QAErE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC;YAC5C,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC,CAAC;YAC/E,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC,CAAC;YAC3E,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC,CAAC;QAC7E,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,kCAAkC,CAAC,EAAE,KAAK,CAAC,CAAC;QACpE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;AACL,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,MAAW,EAAE,cAA8B,EAAE,OAA+B;IACrG,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,oBAAoB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IAE3D,IAAI,CAAC;QACD,aAAa;QACb,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAEzE,YAAY;QACZ,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,sBAAsB,EAAE,CAAC;QAEzD,yBAAyB;QACzB,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;YAClB,MAAM,cAAc,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,IAAI,eAAe,SAAS,CAAC,MAAM,aAAa,CAAC,CAAC,CAAC;QAEvF,OAAO;YACH,MAAM;YACN,SAAS;YACT,OAAO,EAAE,IAAI;SAChB,CAAC;IAEN,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACb,OAAO,CAAC,KAAK,CAAC,eAAK,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC;QACvD,OAAO;YACH,MAAM;YACN,SAAS,EAAE,EAAE;YACb,OAAO,EAAE,KAAK;YACd,KAAK,EAAE,KAAK,CAAC,OAAO;SACvB,CAAC;IACN,CAAC;AACL,CAAC;AAED,KAAK,UAAU,cAAc,CAAC,MAAW,EAAE,SAAgB,EAAE,OAA+B;IACxF,aAAa;IACb,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,cAAc,SAAS,CAAC,MAAM,mBAAmB,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AAC5F,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,OAAc,EAAE,OAA+B;IAChF,MAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;IAC3D,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAE/E,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC,CAAC;IACjD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,wBAAwB,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IAClE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,iBAAiB,YAAY,EAAE,CAAC,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,aAAa,OAAO,CAAC,MAAM,GAAG,YAAY,EAAE,CAAC,CAAC,CAAC;IACtE,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,iCAAiC,cAAc,EAAE,CAAC,CAAC,CAAC;IAE3E,IAAI,OAAO,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QAC5C,WAAW;QACX,OAAO,CAAC,GAAG,CAAC,eAAK,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC,CAAC;IAChE,CAAC;AACL,CAAC;AAED,YAAY;AACZ,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC1B,OAAO,CAAC,KAAK,EAAE,CAAC;AACpB,CAAC"}