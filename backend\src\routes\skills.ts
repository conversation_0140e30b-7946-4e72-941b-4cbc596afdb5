import { Router } from 'express';
import { skillController } from '../controllers/ItemController';
import { validate, ValidationSchemas } from '../middleware/validation';
import { auth } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';

const router = Router();

/**
 * @swagger
 * /api/v1/skills:
 *   get:
 *     summary: 获取技能配置列表
 *     tags: [Skills]
 *     parameters:
 *       - $ref: '#/components/parameters/PageParam'
 *       - $ref: '#/components/parameters/LimitParam'
 *       - name: damageType
 *         in: query
 *         description: 伤害类型过滤
 *         schema:
 *           type: string
 *           enum: [physical, magical, true, healing]
 *       - name: targetType
 *         in: query
 *         description: 目标类型过滤
 *         schema:
 *           type: string
 *           enum: [self, ally, enemy, all_allies, all_enemies, all, area]
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PaginatedResponse'
 */
router.get('/',
  asyncHandler(skillController.getSkills)
);

/**
 * @swagger
 * /api/v1/skills/{skillId}:
 *   get:
 *     summary: 获取技能详情
 *     tags: [Skills]
 *     parameters:
 *       - name: skillId
 *         in: path
 *         required: true
 *         description: 技能ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         skill:
 *                           $ref: '#/components/schemas/SkillResponse'
 *       404:
 *         description: 技能不存在
 */
router.get('/:skillId',
  asyncHandler(skillController.getSkill)
);

/**
 * @swagger
 * /api/v1/skills/characters/{characterId}:
 *   get:
 *     summary: 获取角色技能列表
 *     tags: [Skills]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         skills:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               _id:
 *                                 type: string
 *                               userId:
 *                                 type: string
 *                               skillId:
 *                                 type: string
 *                               level:
 *                                 type: number
 *                               experience:
 *                                 type: number
 *                               learned:
 *                                 type: boolean
 *                               lastUsed:
 *                                 type: string
 *                                 format: date-time
 *                               cooldownRemaining:
 *                                 type: number
 *                         skillPoints:
 *                           type: number
 *                           description: 可用技能点
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色不存在
 */
router.get('/characters/:characterId',
  auth.required,
  validate(ValidationSchemas.mongoId),
  asyncHandler(skillController.getUserSkills)
);

/**
 * @swagger
 * /api/v1/skills/characters/{characterId}/learn:
 *   post:
 *     summary: 学习技能
 *     tags: [Skills]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: characterId
 *         in: path
 *         required: true
 *         description: 角色ID
 *         schema:
 *           type: string
 *           pattern: '^[0-9a-fA-F]{24}$'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LearnSkillRequest'
 *     responses:
 *       201:
 *         description: 学习成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         skill:
 *                           type: object
 *                           description: 学习的技能信息
 *                         remainingSkillPoints:
 *                           type: number
 *                           description: 剩余技能点
 *       400:
 *         description: 技能已学习、不满足要求或技能点不足
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       404:
 *         description: 角色或技能不存在
 */
router.post('/characters/:characterId/learn',
  auth.required,
  validate(ValidationSchemas.mongoId),
  validate(ValidationSchemas.learnSkill),
  asyncHandler(skillController.learnSkill)
);

export default router;
