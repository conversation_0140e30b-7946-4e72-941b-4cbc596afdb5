# 数据管理规范

> 📖 **导航**: [返回主页](./README.md) | [组件架构](./component-architecture.md) | [网络通信](./network-architecture.md)

## 📊 数据架构设计

### 数据分层架构
```typescript
// 数据管理架构
export class DataArchitecture {
    // 数据层级
    static readonly DataLayers = {
        // 持久化层 - 云端存储
        PERSISTENT: {
            description: '云端数据库存储',
            storage: ['MongoDB', '微信云数据库', '抖音云数据库'],
            dataTypes: ['用户档案', '游戏进度', '社交关系', '交易记录']
        },
        
        // 缓存层 - 本地缓存
        CACHE: {
            description: '本地数据缓存',
            storage: ['LocalStorage', 'IndexedDB', '内存缓存'],
            dataTypes: ['配置数据', '临时状态', '离线数据', '预加载数据']
        },
        
        // 运行时层 - 内存数据
        RUNTIME: {
            description: '运行时数据管理',
            storage: ['JavaScript对象', 'Map/Set集合', '事件队列'],
            dataTypes: ['游戏状态', '实时数据', '临时计算', 'UI状态']
        }
    };
    
    // 数据同步策略
    static readonly SyncStrategies = {
        IMMEDIATE: '立即同步',      // 关键数据立即同步
        PERIODIC: '定期同步',       // 定期批量同步
        ON_DEMAND: '按需同步',      // 用户操作触发同步
        OFFLINE: '离线缓存',        // 离线时缓存，联网时同步
        CONFLICT_RESOLUTION: '冲突解决' // 数据冲突处理
    };
}
```

### 数据管理器实现
```typescript
// 数据管理器基类
@ccclass('DataManager')
export class DataManager extends BaseManager {
    private static _instance: DataManager = null;
    
    // 数据缓存
    private _playerDataCache: Map<string, IPlayerData> = new Map();
    private _gameConfigCache: Map<string, any> = new Map();
    private _staticDataCache: Map<string, any> = new Map();
    
    // 数据同步队列
    private _syncQueue: ISyncTask[] = [];
    private _syncInProgress: boolean = false;
    
    public static getInstance(): DataManager {
        if (!this._instance) {
            this._instance = new DataManager();
        }
        return this._instance;
    }
    
    protected async initializeManager(): Promise<void> {
        await this.loadStaticData();
        await this.loadGameConfig();
        this.startSyncScheduler();
    }
    
    // 玩家数据管理
    public async getPlayerData(playerId: string): Promise<IPlayerData> {
        // 先从缓存获取
        if (this._playerDataCache.has(playerId)) {
            return this._playerDataCache.get(playerId);
        }
        
        // 从本地存储获取
        const localData = this.loadFromLocalStorage(`player_${playerId}`);
        if (localData) {
            this._playerDataCache.set(playerId, localData);
            return localData;
        }
        
        // 从云端获取
        const cloudData = await this.loadFromCloud(`players/${playerId}`);
        if (cloudData) {
            this._playerDataCache.set(playerId, cloudData);
            this.saveToLocalStorage(`player_${playerId}`, cloudData);
            return cloudData;
        }
        
        // 创建新玩家数据
        return this.createNewPlayerData(playerId);
    }
    
    public async savePlayerData(playerData: IPlayerData): Promise<void> {
        const playerId = playerData.id;
        
        // 更新缓存
        this._playerDataCache.set(playerId, playerData);
        
        // 保存到本地存储
        this.saveToLocalStorage(`player_${playerId}`, playerData);
        
        // 添加到同步队列
        this.addToSyncQueue({
            type: 'player_data',
            id: playerId,
            data: playerData,
            priority: 'high',
            timestamp: Date.now()
        });
    }
    
    // 静态数据管理
    private async loadStaticData(): Promise<void> {
        const staticDataFiles = [
            'skills.json',
            'items.json',
            'sects.json',
            'characters.json',
            'quests.json'
        ];
        
        for (const file of staticDataFiles) {
            try {
                const data = await ResourceManager.loadJSON(`data/game/${file}`);
                const key = file.replace('.json', '');
                this._staticDataCache.set(key, data);
            } catch (error) {
                console.error(`Failed to load static data: ${file}`, error);
            }
        }
    }
    
    public getStaticData<T>(dataType: string): T {
        return this._staticDataCache.get(dataType);
    }
    
    // 数据同步管理
    private addToSyncQueue(task: ISyncTask): void {
        this._syncQueue.push(task);
        
        // 高优先级任务立即同步
        if (task.priority === 'high' && !this._syncInProgress) {
            this.processSyncQueue();
        }
    }
    
    private async processSyncQueue(): Promise<void> {
        if (this._syncInProgress || this._syncQueue.length === 0) {
            return;
        }
        
        this._syncInProgress = true;
        
        try {
            // 按优先级排序
            this._syncQueue.sort((a, b) => {
                const priorityOrder = { 'high': 0, 'medium': 1, 'low': 2 };
                return priorityOrder[a.priority] - priorityOrder[b.priority];
            });
            
            // 批量处理同步任务
            const batchSize = 10;
            while (this._syncQueue.length > 0) {
                const batch = this._syncQueue.splice(0, batchSize);
                await this.syncBatch(batch);
            }
        } catch (error) {
            console.error('Sync queue processing failed:', error);
        } finally {
            this._syncInProgress = false;
        }
    }
    
    private async syncBatch(tasks: ISyncTask[]): Promise<void> {
        const syncPromises = tasks.map(task => this.syncSingleTask(task));
        await Promise.allSettled(syncPromises);
    }
    
    private async syncSingleTask(task: ISyncTask): Promise<void> {
        try {
            switch (task.type) {
                case 'player_data':
                    await this.syncPlayerDataToCloud(task.id, task.data);
                    break;
                case 'game_progress':
                    await this.syncGameProgressToCloud(task.id, task.data);
                    break;
                case 'social_data':
                    await this.syncSocialDataToCloud(task.id, task.data);
                    break;
                default:
                    console.warn(`Unknown sync task type: ${task.type}`);
            }
        } catch (error) {
            console.error(`Sync task failed:`, task, error);
            // 重新加入队列，降低优先级
            if (task.retryCount < 3) {
                task.retryCount = (task.retryCount || 0) + 1;
                task.priority = 'low';
                this._syncQueue.push(task);
            }
        }
    }
    
    // 本地存储操作
    private saveToLocalStorage(key: string, data: any): void {
        try {
            const serializedData = JSON.stringify(data);
            localStorage.setItem(key, serializedData);
        } catch (error) {
            console.error('Failed to save to local storage:', key, error);
        }
    }
    
    private loadFromLocalStorage(key: string): any {
        try {
            const serializedData = localStorage.getItem(key);
            return serializedData ? JSON.parse(serializedData) : null;
        } catch (error) {
            console.error('Failed to load from local storage:', key, error);
            return null;
        }
    }
    
    // 云端存储操作
    private async loadFromCloud(path: string): Promise<any> {
        try {
            return await CloudService.getData(path);
        } catch (error) {
            console.error('Failed to load from cloud:', path, error);
            return null;
        }
    }
    
    private async syncPlayerDataToCloud(playerId: string, data: IPlayerData): Promise<void> {
        await CloudService.saveData(`players/${playerId}`, data);
    }
    
    private async syncGameProgressToCloud(playerId: string, data: any): Promise<void> {
        await CloudService.saveData(`progress/${playerId}`, data);
    }
    
    private async syncSocialDataToCloud(playerId: string, data: any): Promise<void> {
        await CloudService.saveData(`social/${playerId}`, data);
    }
    
    // 数据同步调度器
    private startSyncScheduler(): void {
        // 每30秒处理一次同步队列
        setInterval(() => {
            this.processSyncQueue();
        }, 30000);
        
        // 每5分钟进行一次完整同步
        setInterval(() => {
            this.performFullSync();
        }, 300000);
    }
    
    private async performFullSync(): Promise<void> {
        console.log('Performing full data sync...');
        
        // 同步所有缓存的玩家数据
        for (const [playerId, playerData] of this._playerDataCache) {
            this.addToSyncQueue({
                type: 'player_data',
                id: playerId,
                data: playerData,
                priority: 'medium',
                timestamp: Date.now()
            });
        }
    }
}

// 同步任务接口
interface ISyncTask {
    type: 'player_data' | 'game_progress' | 'social_data';
    id: string;
    data: any;
    priority: 'high' | 'medium' | 'low';
    timestamp: number;
    retryCount?: number;
}
```

## 🔄 数据同步策略

### 离线数据处理
```typescript
// 离线数据管理器
@ccclass('OfflineDataManager')
export class OfflineDataManager extends BaseManager {
    private _offlineActions: IOfflineAction[] = [];
    private _lastSyncTime: number = 0;
    
    protected async initializeManager(): Promise<void> {
        this.loadOfflineActions();
        this.setupNetworkStatusMonitoring();
    }
    
    // 记录离线操作
    public recordOfflineAction(action: IOfflineAction): void {
        action.timestamp = Date.now();
        action.id = this.generateActionId();
        
        this._offlineActions.push(action);
        this.saveOfflineActions();
    }
    
    // 网络恢复时同步离线数据
    public async syncOfflineData(): Promise<void> {
        if (this._offlineActions.length === 0) {
            return;
        }
        
        console.log(`Syncing ${this._offlineActions.length} offline actions...`);
        
        try {
            // 按时间戳排序
            this._offlineActions.sort((a, b) => a.timestamp - b.timestamp);
            
            // 批量同步
            for (const action of this._offlineActions) {
                await this.syncOfflineAction(action);
            }
            
            // 清空离线操作记录
            this._offlineActions = [];
            this.saveOfflineActions();
            
            console.log('Offline data sync completed');
        } catch (error) {
            console.error('Offline data sync failed:', error);
        }
    }
    
    private async syncOfflineAction(action: IOfflineAction): Promise<void> {
        switch (action.type) {
            case 'skill_use':
                await this.syncOfflineSkillUse(action);
                break;
            case 'item_use':
                await this.syncOfflineItemUse(action);
                break;
            case 'cultivation':
                await this.syncOfflineCultivation(action);
                break;
            case 'quest_progress':
                await this.syncOfflineQuestProgress(action);
                break;
            default:
                console.warn(`Unknown offline action type: ${action.type}`);
        }
    }
    
    private setupNetworkStatusMonitoring(): void {
        // 监听网络状态变化
        window.addEventListener('online', () => {
            console.log('Network restored, syncing offline data...');
            this.syncOfflineData();
        });
        
        window.addEventListener('offline', () => {
            console.log('Network lost, entering offline mode...');
            this._lastSyncTime = Date.now();
        });
    }
    
    // 计算离线收益
    public calculateOfflineRewards(playerId: string): IOfflineRewards {
        const player = DataManager.getInstance().getPlayerData(playerId);
        const offlineTime = Date.now() - this._lastSyncTime;
        
        // 基础离线收益
        const baseRewards = this.calculateBaseOfflineRewards(player, offlineTime);
        
        // 门派加成
        const sectBonus = SectSystem.getOfflineBonus(player.sect);
        
        // VIP加成
        const vipBonus = VIPSystem.getOfflineBonus(player.vipLevel);
        
        // 计算最终收益
        const finalRewards = this.applyOfflineBonuses(baseRewards, sectBonus, vipBonus);
        
        return {
            duration: offlineTime,
            baseRewards,
            finalRewards,
            bonuses: {
                sect: sectBonus,
                vip: vipBonus
            }
        };
    }
}

// 离线操作接口
interface IOfflineAction {
    id?: string;
    type: 'skill_use' | 'item_use' | 'cultivation' | 'quest_progress';
    playerId: string;
    data: any;
    timestamp?: number;
}

// 离线奖励接口
interface IOfflineRewards {
    duration: number;
    baseRewards: IReward[];
    finalRewards: IReward[];
    bonuses: {
        sect: number;
        vip: number;
    };
}
```

---

> 📖 **相关文档**: [网络通信](./network-architecture.md) | [资源管理](./resource-management.md) | [组件架构](./component-architecture.md)
