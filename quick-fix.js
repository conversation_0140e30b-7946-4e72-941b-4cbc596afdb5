#!/usr/bin/env node

/**
 * 快速修复AI测试框架编译错误
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复AI测试框架编译错误...\n');

// 修复1: 更新types/index.ts中的CoverageReport接口
function fixCoverageReport() {
    const typesFile = path.join(__dirname, 'types', 'index.ts');
    let content = fs.readFileSync(typesFile, 'utf8');
    
    // 查找CoverageReport接口并添加缺失的属性
    const coverageReportRegex = /export interface CoverageReport \{[\s\S]*?\}/;
    const newCoverageReport = `export interface CoverageReport {
    overallCoverage: number;
    lineCoverage: number;
    branchCoverage: number;
    functionCoverage: number;
    uncoveredLines: string[];
    lines?: number;
    functions?: number;
    branches?: number;
    statements?: number;
}`;
    
    content = content.replace(coverageReportRegex, newCoverageReport);
    fs.writeFileSync(typesFile, content);
    console.log('✅ 修复了CoverageReport接口');
}

// 修复2: 更新TestSummary接口
function fixTestSummary() {
    const typesFile = path.join(__dirname, 'types', 'index.ts');
    let content = fs.readFileSync(typesFile, 'utf8');
    
    // 添加performance属性到TestSummary
    const testSummaryRegex = /export interface TestSummary \{[\s\S]*?coverage\?\: CoverageReport;[\s\S]*?\}/;
    const newTestSummary = `export interface TestSummary {
    totalTests: number;
    passedTests: number;
    failedTests: number;
    skippedTests: number;
    successRate: number;
    totalExecutionTime: number;
    insights: TestInsights;
    recommendations: string[];
    nextActions: string[];
    coverage?: CoverageReport;
    performance?: {
        averageExecutionTime: number;
        slowestTests?: TestResult[];
        memoryUsage?: number;
        cpuUsage?: number;
    };
}`;
    
    content = content.replace(testSummaryRegex, newTestSummary);
    fs.writeFileSync(typesFile, content);
    console.log('✅ 修复了TestSummary接口');
}

// 修复3: 更新TrendAnalysis接口
function fixTrendAnalysis() {
    const typesFile = path.join(__dirname, 'types', 'index.ts');
    let content = fs.readFileSync(typesFile, 'utf8');
    
    // 添加historicalData属性
    const trendAnalysisRegex = /export interface TrendAnalysis \{[\s\S]*?\}/;
    const newTrendAnalysis = `export interface TrendAnalysis {
    performanceTrends: PerformanceTrend[];
    qualityTrends: QualityTrend[];
    coverageTrends: CoverageTrend[];
    historicalData?: any[];
}`;
    
    content = content.replace(trendAnalysisRegex, newTrendAnalysis);
    fs.writeFileSync(typesFile, content);
    console.log('✅ 修复了TrendAnalysis接口');
}

// 修复4: 创建简化版的AI测试框架入口
function createSimplifiedEntry() {
    const entryContent = `#!/usr/bin/env node

/**
 * 简化版AI测试框架入口
 */

const { SimpleAITester } = require('./simple-test.js');

async function main() {
    console.log('🤖 启动简化版AI测试框架...');
    
    const tester = new SimpleAITester();
    const results = await tester.runBasicTests();
    const report = tester.generateReport(results);
    
    console.log(report);
    
    // 检查是否有失败的测试
    const failed = results.filter(r => r.status === 'failed').length;
    if (failed > 0) {
        console.log('\\n⚠️ 发现问题，建议检查上述失败项目');
        process.exit(1);
    } else {
        console.log('\\n🎉 所有测试通过！');
    }
}

if (require.main === module) {
    main().catch(console.error);
}
`;
    
    fs.writeFileSync(path.join(__dirname, 'ai-test-simple.js'), entryContent);
    console.log('✅ 创建了简化版入口文件');
}

// 修复5: 创建package.json脚本
function updatePackageScripts() {
    const packageFile = path.join(__dirname, 'package.json');
    const packageData = JSON.parse(fs.readFileSync(packageFile, 'utf8'));
    
    // 添加简化版脚本
    packageData.scripts['test:simple'] = 'node ai-test-simple.js';
    packageData.scripts['test:quick'] = 'node simple-test.js';
    packageData.scripts['fix:compile'] = 'node quick-fix.js';
    
    fs.writeFileSync(packageFile, JSON.stringify(packageData, null, 2));
    console.log('✅ 更新了package.json脚本');
}

// 执行所有修复
async function runAllFixes() {
    try {
        fixCoverageReport();
        fixTestSummary();
        fixTrendAnalysis();
        createSimplifiedEntry();
        updatePackageScripts();
        
        console.log('\\n🎉 所有修复完成！');
        console.log('\\n📋 可用命令:');
        console.log('  npm run test:simple  - 运行简化版测试');
        console.log('  npm run test:quick   - 运行快速测试');
        console.log('  npm run build        - 尝试完整编译');
        
    } catch (error) {
        console.error('❌ 修复过程中出错:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    runAllFixes();
}
