/**
 * 技能列表项组件
 * 负责显示单个技能的信息和交互
 */

import { _decorator, Node, Sprite, Label, Button, ProgressBar, Color } from 'cc';
import { BaseUIComponent } from '../base/BaseUIComponent';
import { ISkillData, IPlayerSkill, SkillDamageType } from '../../config/interfaces/ISkillData';
import { ResourceManager } from '../../managers/ResourceManager';

const { ccclass, property } = _decorator;

/**
 * 技能列表项配置接口
 */
export interface ISkillListItemConfig {
    /** 技能数据 */
    skillData: ISkillData;
    
    /** 玩家技能数据 */
    playerSkill: IPlayerSkill | null;
    
    /** 是否可选择 */
    selectable: boolean;
    
    /** 是否显示详情 */
    showDetails: boolean;
}

/**
 * 技能列表项状态枚举
 */
export enum SkillListItemState {
    Normal = 'normal',
    Selected = 'selected',
    Learned = 'learned',
    Available = 'available',
    Locked = 'locked'
}

@ccclass('SkillListItem')
export class SkillListItem extends BaseUIComponent {
    
    @property({ type: Node, tooltip: '背景节点' })
    public backgroundNode: Node = null!;
    
    @property({ type: Sprite, tooltip: '技能图标' })
    public skillIcon: Sprite = null!;
    
    @property({ type: Label, tooltip: '技能名称' })
    public skillNameLabel: Label = null!;
    
    @property({ type: Label, tooltip: '技能描述' })
    public skillDescLabel: Label = null!;
    
    @property({ type: Label, tooltip: '技能等级' })
    public skillLevelLabel: Label = null!;
    
    @property({ type: Label, tooltip: '法力消耗' })
    public manaCostLabel: Label = null!;
    
    @property({ type: Label, tooltip: '冷却时间' })
    public cooldownLabel: Label = null!;
    
    @property({ type: Label, tooltip: '伤害类型' })
    public damageTypeLabel: Label = null!;
    
    @property({ type: ProgressBar, tooltip: '经验进度条' })
    public expProgressBar: ProgressBar = null!;
    
    @property({ type: Button, tooltip: '选择按钮' })
    public selectButton: Button = null!;
    
    @property({ type: Button, tooltip: '学习按钮' })
    public learnButton: Button = null!;
    
    @property({ type: Node, tooltip: '已学会标记' })
    public learnedMark: Node = null!;
    
    @property({ type: Node, tooltip: '锁定标记' })
    public lockedMark: Node = null!;
    
    // 配置和数据
    private _config: ISkillListItemConfig = null!;
    private _currentState: SkillListItemState = SkillListItemState.Normal;
    private _isSelected: boolean = false;
    
    // 事件回调
    public onItemSelected: ((skillData: ISkillData) => void) | null = null;
    public onLearnSkill: ((skillId: string) => void) | null = null;

    protected onComponentLoad(): void {
        console.log('⚔️ SkillListItem: 组件加载');
    }

    protected onComponentEnable(): void {
        console.log('⚔️ SkillListItem: 组件启用');
    }

    protected bindEvents(): void {
        // 绑定选择按钮事件
        if (this.selectButton) {
            this.selectButton.node.on(Button.EventType.CLICK, this.onSelectButtonClick, this);
        }
        
        // 绑定学习按钮事件
        if (this.learnButton) {
            this.learnButton.node.on(Button.EventType.CLICK, this.onLearnButtonClick, this);
        }
    }

    protected unbindEvents(): void {
        // 解绑选择按钮事件
        if (this.selectButton) {
            this.selectButton.node.off(Button.EventType.CLICK, this.onSelectButtonClick, this);
        }
        
        // 解绑学习按钮事件
        if (this.learnButton) {
            this.learnButton.node.off(Button.EventType.CLICK, this.onLearnButtonClick, this);
        }
    }

    /**
     * 初始化技能列表项
     */
    public async initialize(config: ISkillListItemConfig): Promise<void> {
        console.log(`⚔️ 初始化技能列表项: ${config.skillData.name}`);
        
        this._config = config;
        
        // 更新技能信息显示
        await this.updateSkillInfo();
        
        // 更新状态
        this.updateItemState();
        
        // 更新按钮状态
        this.updateButtonStates();
        
        console.log(`✅ 技能列表项初始化完成: ${config.skillData.name}`);
    }

    /**
     * 更新技能信息显示
     */
    private async updateSkillInfo(): Promise<void> {
        const skillData = this._config.skillData;
        const playerSkill = this._config.playerSkill;
        
        // 更新技能图标
        await this.updateSkillIcon(skillData);
        
        // 更新技能名称
        if (this.skillNameLabel) {
            this.skillNameLabel.string = skillData.name;
        }
        
        // 更新技能描述
        if (this.skillDescLabel) {
            this.skillDescLabel.string = skillData.description;
        }
        
        // 更新技能等级
        if (this.skillLevelLabel) {
            if (playerSkill) {
                this.skillLevelLabel.string = `Lv.${playerSkill.level}/${skillData.maxLevel}`;
            } else {
                this.skillLevelLabel.string = `Lv.1/${skillData.maxLevel}`;
            }
        }
        
        // 更新法力消耗
        if (this.manaCostLabel) {
            this.manaCostLabel.string = `MP: ${skillData.manaCost}`;
        }
        
        // 更新冷却时间
        if (this.cooldownLabel) {
            this.cooldownLabel.string = `CD: ${skillData.cooldown}s`;
        }
        
        // 更新伤害类型
        if (this.damageTypeLabel) {
            this.damageTypeLabel.string = this.getDamageTypeText(skillData.damageType);
            this.damageTypeLabel.color = this.getDamageTypeColor(skillData.damageType);
        }
        
        // 更新经验进度条
        if (this.expProgressBar && playerSkill) {
            const progress = playerSkill.experienceRequired > 0 ? 
                playerSkill.experience / playerSkill.experienceRequired : 0;
            this.expProgressBar.progress = progress;
            this.expProgressBar.node.active = playerSkill.level < skillData.maxLevel;
        } else if (this.expProgressBar) {
            this.expProgressBar.node.active = false;
        }
    }

    /**
     * 更新技能图标
     */
    private async updateSkillIcon(skillData: ISkillData): Promise<void> {
        if (!this.skillIcon) {
            return;
        }
        
        try {
            // 构建图标路径
            const iconPath = `textures/skills/${skillData.id}`;
            
            // 加载技能图标
            const spriteFrame = await ResourceManager.getInstance().loadSpriteFrame(iconPath);
            this.skillIcon.spriteFrame = spriteFrame;
            
        } catch (error) {
            console.warn(`⚠️ 加载技能图标失败: ${skillData.id}`, error);
            
            // 使用默认图标
            this.skillIcon.spriteFrame = null;
        }
    }

    /**
     * 获取伤害类型文本
     */
    private getDamageTypeText(damageType: SkillDamageType): string {
        switch (damageType) {
            case SkillDamageType.Physical:
                return '物理';
            case SkillDamageType.Magical:
                return '魔法';
            case SkillDamageType.True:
                return '真实';
            case SkillDamageType.Healing:
                return '治疗';
            default:
                return '未知';
        }
    }

    /**
     * 获取伤害类型颜色
     */
    private getDamageTypeColor(damageType: SkillDamageType): Color {
        switch (damageType) {
            case SkillDamageType.Physical:
                return new Color(255, 100, 100, 255); // 红色
            case SkillDamageType.Magical:
                return new Color(100, 100, 255, 255); // 蓝色
            case SkillDamageType.True:
                return new Color(255, 255, 100, 255); // 黄色
            case SkillDamageType.Healing:
                return new Color(100, 255, 100, 255); // 绿色
            default:
                return new Color(255, 255, 255, 255); // 白色
        }
    }

    /**
     * 更新列表项状态
     */
    private updateItemState(): void {
        const playerSkill = this._config.playerSkill;
        
        if (playerSkill && playerSkill.learned) {
            this._currentState = SkillListItemState.Learned;
        } else if (this.isSkillAvailable()) {
            this._currentState = SkillListItemState.Available;
        } else {
            this._currentState = SkillListItemState.Locked;
        }
        
        // 更新视觉状态
        this.updateVisualState();
    }

    /**
     * 检查技能是否可学习
     */
    private isSkillAvailable(): boolean {
        // TODO: 实现具体的可学习条件检查
        // 这里简化为总是可学习
        return true;
    }

    /**
     * 更新视觉状态
     */
    private updateVisualState(): void {
        // 更新背景颜色
        if (this.backgroundNode) {
            const sprite = this.backgroundNode.getComponent(Sprite);
            if (sprite) {
                if (this._isSelected) {
                    sprite.color = new Color(100, 150, 255, 255); // 选中状态 - 蓝色
                } else {
                    switch (this._currentState) {
                        case SkillListItemState.Learned:
                            sprite.color = new Color(150, 255, 150, 255); // 已学会 - 绿色
                            break;
                        case SkillListItemState.Available:
                            sprite.color = new Color(255, 255, 150, 255); // 可学习 - 黄色
                            break;
                        case SkillListItemState.Locked:
                            sprite.color = new Color(150, 150, 150, 255); // 锁定 - 灰色
                            break;
                        default:
                            sprite.color = new Color(255, 255, 255, 255); // 默认 - 白色
                            break;
                    }
                }
            }
        }
        
        // 更新标记显示
        if (this.learnedMark) {
            this.learnedMark.active = this._currentState === SkillListItemState.Learned;
        }
        
        if (this.lockedMark) {
            this.lockedMark.active = this._currentState === SkillListItemState.Locked;
        }
    }

    /**
     * 更新按钮状态
     */
    private updateButtonStates(): void {
        // 更新选择按钮
        if (this.selectButton) {
            this.selectButton.interactable = this._config.selectable && 
                                           this._currentState !== SkillListItemState.Locked;
            this.selectButton.node.active = this._config.selectable;
        }
        
        // 更新学习按钮
        if (this.learnButton) {
            this.learnButton.interactable = this._currentState === SkillListItemState.Available;
            this.learnButton.node.active = this._currentState === SkillListItemState.Available;
        }
    }

    // ==================== 事件处理 ====================

    /**
     * 选择按钮点击事件处理
     */
    private onSelectButtonClick(): void {
        console.log(`⚔️ 选择技能: ${this._config.skillData.name}`);
        
        if (this.onItemSelected) {
            this.onItemSelected(this._config.skillData);
        }
        
        // 发送选择事件
        this.emitComponentEvent('item_selected', {
            skillData: this._config.skillData,
            playerSkill: this._config.playerSkill
        });
    }

    /**
     * 学习按钮点击事件处理
     */
    private onLearnButtonClick(): void {
        console.log(`⚔️ 学习技能: ${this._config.skillData.name}`);
        
        if (this.onLearnSkill) {
            this.onLearnSkill(this._config.skillData.id);
        }
        
        // 发送学习事件
        this.emitComponentEvent('learn_skill', {
            skillId: this._config.skillData.id,
            skillData: this._config.skillData
        });
    }

    // ==================== 公共API ====================

    /**
     * 设置选中状态
     */
    public setSelected(selected: boolean): void {
        if (this._isSelected !== selected) {
            this._isSelected = selected;
            this.updateVisualState();
            
            console.log(`⚔️ 技能列表项选中状态: ${this._config.skillData.name} = ${selected}`);
        }
    }

    /**
     * 获取选中状态
     */
    public isSelected(): boolean {
        return this._isSelected;
    }

    /**
     * 获取技能数据
     */
    public getSkillData(): ISkillData {
        return this._config.skillData;
    }

    /**
     * 获取玩家技能数据
     */
    public getPlayerSkill(): IPlayerSkill | null {
        return this._config.playerSkill;
    }

    /**
     * 获取当前状态
     */
    public getCurrentState(): SkillListItemState {
        return this._currentState;
    }

    /**
     * 更新玩家技能数据
     */
    public updatePlayerSkill(playerSkill: IPlayerSkill | null): void {
        this._config.playerSkill = playerSkill;
        
        // 重新更新显示
        this.updateSkillInfo();
        this.updateItemState();
        this.updateButtonStates();
    }

    /**
     * 刷新显示
     */
    public refresh(): void {
        this.updateSkillInfo();
        this.updateItemState();
        this.updateButtonStates();
    }

    /**
     * 设置可选择状态
     */
    public setSelectable(selectable: boolean): void {
        this._config.selectable = selectable;
        this.updateButtonStates();
    }

    /**
     * 检查是否可学习
     */
    public isLearnable(): boolean {
        return this._currentState === SkillListItemState.Available;
    }

    /**
     * 检查是否已学会
     */
    public isLearned(): boolean {
        return this._currentState === SkillListItemState.Learned;
    }

    /**
     * 检查是否被锁定
     */
    public isLocked(): boolean {
        return this._currentState === SkillListItemState.Locked;
    }
}
