import { User, IUser } from '../models/User';
import { Character, ICharacter } from '../models/Character';
import { AppError, ErrorCodes } from '../utils/errors';
import { Logger } from '../utils/logger';
import { CacheManager } from '../utils/cache';
import { cacheStrategyService } from './CacheStrategyService';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

/**
 * 经验等级计算结果接口
 */
export interface ILevelResult {
  currentLevel: number;
  currentExp: number;
  expToNextLevel: number;
  totalExpRequired: number;
  leveledUp: boolean;
  levelsGained: number;
}

/**
 * 用户统计信息接口
 */
export interface IUserStats {
  totalPlayTime: number;
  charactersCreated: number;
  totalGoldEarned: number;
  totalBattlesWon: number;
  totalBattlesLost: number;
  achievements: string[];
  statistics: {
    monstersKilled: number;
    itemsCollected: number;
    skillsLearned: number;
    questsCompleted: number;
  };
  winRate: number;
  averageLevel: number;
}

/**
 * 用户验证结果接口
 */
export interface IUserValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * 用户服务类
 */
export class UserService {
  private static instance: UserService;
  private cacheManager: CacheManager;

  // 经验等级计算常量
  private readonly BASE_EXP = 100;
  private readonly EXP_MULTIPLIER = 1.5;
  private readonly MAX_LEVEL = 100;

  private constructor() {
    this.cacheManager = CacheManager.getInstance();
  }

  public static getInstance(): UserService {
    if (!UserService.instance) {
      UserService.instance = new UserService();
    }
    return UserService.instance;
  }

  /**
   * 创建用户
   */
  public async createUser(userData: {
    username: string;
    email: string;
    password: string;
  }): Promise<{ user: IUser; token: string }> {
    try {
      // 验证用户数据
      const validation = await this.validateUserData(userData);
      if (!validation.isValid) {
        throw new AppError(validation.errors.join(', '), 400, ErrorCodes.VALIDATION_ERROR);
      }

      // 检查用户名和邮箱是否已存在
      const existingUser = await User.findOne({
        $or: [
          { username: userData.username },
          { email: userData.email.toLowerCase() }
        ]
      });

      if (existingUser) {
        if (existingUser.username === userData.username) {
          throw new AppError('用户名已存在', 409, ErrorCodes.USERNAME_TAKEN);
        }
        if (existingUser.email === userData.email.toLowerCase()) {
          throw new AppError('邮箱已被注册', 409, ErrorCodes.EMAIL_TAKEN);
        }
      }

      // 创建用户
      const user = new User({
        username: userData.username,
        email: userData.email.toLowerCase(),
        password: userData.password,
        profile: {
          nickname: userData.username,
          level: 1,
          experience: 0,
          totalPlayTime: 0,
          lastLoginAt: new Date(),
          loginCount: 1,
          preferences: {
            language: 'zh-CN',
            timezone: 'Asia/Shanghai',
            notifications: {
              email: true,
              push: true,
              inGame: true,
            },
            privacy: {
              showOnlineStatus: true,
              allowFriendRequests: true,
              showProfile: true,
            },
          },
        },
        gameData: {
          charactersCreated: 0,
          totalGoldEarned: 0,
          totalBattlesWon: 0,
          totalBattlesLost: 0,
          achievements: [],
          statistics: {
            monstersKilled: 0,
            itemsCollected: 0,
            skillsLearned: 0,
            questsCompleted: 0,
          },
        },
      });

      await user.save();

      // 生成JWT令牌
      const token = this.generateToken(user._id.toString());

      // 缓存用户信息
      await cacheStrategyService.cacheUser(user._id.toString(), user);

      Logger.info('用户创建成功', {
        userId: user._id,
        username: user.username,
        email: user.email,
      });

      return { user, token };
    } catch (error) {
      Logger.error('用户创建失败', { userData: { ...userData, password: '[HIDDEN]' }, error });
      throw error;
    }
  }

  /**
   * 用户认证
   */
  public async authenticateUser(
    identifier: string,
    password: string
  ): Promise<{ user: IUser; token: string }> {
    try {
      // 查找用户（支持用户名或邮箱）
      const user = await User.findOne({
        $or: [
          { username: identifier },
          { email: identifier.toLowerCase() }
        ]
      }).select('+password');

      if (!user) {
        throw new AppError('用户名或密码错误', 401, ErrorCodes.INVALID_CREDENTIALS);
      }

      // 检查账户状态
      if (!user.isActive) {
        throw new AppError('账户已被禁用', 403, ErrorCodes.ACCOUNT_DISABLED);
      }

      if (user.isLocked()) {
        throw new AppError('账户已被锁定，请稍后再试', 423, ErrorCodes.ACCOUNT_LOCKED);
      }

      // 验证密码
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        await user.incrementLoginAttempts();
        throw new AppError('用户名或密码错误', 401, ErrorCodes.INVALID_CREDENTIALS);
      }

      // 重置登录尝试次数
      await user.resetLoginAttempts();

      // 更新登录信息
      user.profile.lastLoginAt = new Date();
      user.profile.loginCount += 1;
      await user.save();

      // 生成JWT令牌
      const token = this.generateToken(user._id.toString());

      // 缓存用户信息
      await cacheStrategyService.cacheUser(user._id.toString(), user);

      Logger.info('用户认证成功', {
        userId: user._id,
        username: user.username,
        loginCount: user.profile.loginCount,
      });

      return { user, token };
    } catch (error) {
      Logger.error('用户认证失败', { identifier, error });
      throw error;
    }
  }

  /**
   * 添加经验值
   */
  public async addExperience(userId: string, expAmount: number): Promise<ILevelResult> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new AppError('用户不存在', 404, ErrorCodes.USER_NOT_FOUND);
      }

      const oldLevel = user.profile.level;
      const oldExp = user.profile.experience;

      // 添加经验值
      user.profile.experience += expAmount;

      // 计算等级提升
      const levelResult = this.calculateLevel(user.profile.experience);
      user.profile.level = levelResult.currentLevel;

      const levelsGained = levelResult.currentLevel - oldLevel;
      const leveledUp = levelsGained > 0;

      await user.save();

      // 更新缓存
      await cacheStrategyService.cacheUser(userId, user);

      // 如果升级了，记录日志
      if (leveledUp) {
        Logger.info('用户升级', {
          userId,
          oldLevel,
          newLevel: levelResult.currentLevel,
          levelsGained,
          expGained: expAmount,
        });
      }

      return {
        ...levelResult,
        leveledUp,
        levelsGained,
      };
    } catch (error) {
      Logger.error('添加经验失败', { userId, expAmount, error });
      throw error;
    }
  }

  /**
   * 计算等级
   */
  public calculateLevel(totalExp: number): ILevelResult {
    let level = 1;
    let expUsed = 0;

    // 计算当前等级
    while (level < this.MAX_LEVEL) {
      const expRequired = this.getExpRequiredForLevel(level + 1);
      if (totalExp < expUsed + expRequired) {
        break;
      }
      expUsed += expRequired;
      level++;
    }

    const currentExp = totalExp - expUsed;
    const expToNextLevel = level < this.MAX_LEVEL ? this.getExpRequiredForLevel(level + 1) - currentExp : 0;
    const totalExpRequired = level < this.MAX_LEVEL ? this.getExpRequiredForLevel(level + 1) : 0;

    return {
      currentLevel: level,
      currentExp,
      expToNextLevel,
      totalExpRequired,
      leveledUp: false,
      levelsGained: 0,
    };
  }

  /**
   * 获取指定等级所需经验值
   */
  public getExpRequiredForLevel(level: number): number {
    if (level <= 1) return 0;
    return Math.floor(this.BASE_EXP * Math.pow(this.EXP_MULTIPLIER, level - 2));
  }

  /**
   * 获取用户统计信息
   */
  public async getUserStats(userId: string): Promise<IUserStats> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new AppError('用户不存在', 404, ErrorCodes.USER_NOT_FOUND);
      }

      // 获取用户角色信息
      const characters = await Character.find({ userId, isActive: true });
      const averageLevel = characters.length > 0 
        ? characters.reduce((sum, char) => sum + char.level, 0) / characters.length 
        : 0;

      // 计算胜率
      const totalBattles = user.gameData.totalBattlesWon + user.gameData.totalBattlesLost;
      const winRate = totalBattles > 0 ? (user.gameData.totalBattlesWon / totalBattles * 100) : 0;

      const stats: IUserStats = {
        totalPlayTime: user.profile.totalPlayTime,
        charactersCreated: user.gameData.charactersCreated,
        totalGoldEarned: user.gameData.totalGoldEarned,
        totalBattlesWon: user.gameData.totalBattlesWon,
        totalBattlesLost: user.gameData.totalBattlesLost,
        achievements: user.gameData.achievements,
        statistics: user.gameData.statistics,
        winRate: Math.round(winRate * 100) / 100,
        averageLevel: Math.round(averageLevel * 100) / 100,
      };

      return stats;
    } catch (error) {
      Logger.error('获取用户统计失败', { userId, error });
      throw error;
    }
  }

  /**
   * 更新用户游戏数据
   */
  public async updateGameData(
    userId: string,
    updates: Partial<IUser['gameData']>
  ): Promise<IUser> {
    try {
      const user = await User.findById(userId);
      if (!user) {
        throw new AppError('用户不存在', 404, ErrorCodes.USER_NOT_FOUND);
      }

      // 更新游戏数据
      Object.keys(updates).forEach(key => {
        if (updates[key] !== undefined) {
          user.gameData[key] = updates[key];
        }
      });

      await user.save();

      // 更新缓存
      await cacheStrategyService.cacheUser(userId, user);

      Logger.info('用户游戏数据更新', {
        userId,
        updates: Object.keys(updates),
      });

      return user;
    } catch (error) {
      Logger.error('更新用户游戏数据失败', { userId, updates, error });
      throw error;
    }
  }

  /**
   * 验证用户数据
   */
  private async validateUserData(userData: {
    username: string;
    email: string;
    password: string;
  }): Promise<IUserValidation> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证用户名
    if (!userData.username || userData.username.length < 3) {
      errors.push('用户名至少3个字符');
    }
    if (userData.username && userData.username.length > 20) {
      errors.push('用户名最多20个字符');
    }
    if (userData.username && !/^[a-zA-Z0-9_]+$/.test(userData.username)) {
      errors.push('用户名只能包含字母、数字和下划线');
    }

    // 验证邮箱
    if (!userData.email) {
      errors.push('邮箱是必填项');
    } else if (!/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(userData.email)) {
      errors.push('请输入有效的邮箱地址');
    }

    // 验证密码
    if (!userData.password || userData.password.length < 6) {
      errors.push('密码至少6个字符');
    }
    if (userData.password && userData.password.length > 50) {
      errors.push('密码最多50个字符');
    }

    // 密码强度检查（警告）
    if (userData.password) {
      if (!/[A-Z]/.test(userData.password)) {
        warnings.push('密码建议包含大写字母');
      }
      if (!/[0-9]/.test(userData.password)) {
        warnings.push('密码建议包含数字');
      }
      if (!/[!@#$%^&*]/.test(userData.password)) {
        warnings.push('密码建议包含特殊字符');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  /**
   * 生成JWT令牌
   */
  private generateToken(userId: string): string {
    return jwt.sign(
      { id: userId },
      process.env['JWT_SECRET'] || 'default-secret',
      { expiresIn: process.env['JWT_EXPIRES_IN'] || '7d' }
    );
  }

  /**
   * 从缓存获取用户数据
   */
  public async getCachedUserData(userId: string): Promise<IUser | null> {
    try {
      return await cacheStrategyService.getCachedUser(userId);
    } catch (error) {
      Logger.error('获取缓存用户数据失败', { userId, error });
      return null;
    }
  }

  /**
   * 清除用户缓存
   */
  public async clearUserCache(userId: string): Promise<void> {
    await cacheStrategyService.clearUserCache(userId);
  }

  /**
   * 验证JWT令牌
   */
  public verifyToken(token: string): { id: string } | null {
    try {
      const decoded = jwt.verify(token, process.env['JWT_SECRET'] || 'default-secret') as any;
      return decoded;
    } catch (error) {
      Logger.error('JWT令牌验证失败', { error });
      return null;
    }
  }
}

export const userService = UserService.getInstance();
