#!/usr/bin/env node

/**
 * AI测试框架部署脚本
 * 集成到开发流程中
 */

const fs = require('fs');
const path = require('path');

class AITestingDeployer {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '../..');
        this.scriptsDir = __dirname;
    }

    async deploy() {
        console.log('🚀 部署AI测试框架到开发流程...\n');

        const tasks = [
            { name: '创建npm脚本', fn: () => this.setupNpmScripts() },
            { name: '配置Git Hooks', fn: () => this.setupGitHooks() },
            { name: '创建VSCode任务', fn: () => this.setupVSCodeTasks() },
            { name: '生成文档', fn: () => this.generateDocumentation() },
            { name: '创建快捷启动脚本', fn: () => this.createLaunchScripts() }
        ];

        for (const task of tasks) {
            console.log(`🔧 ${task.name}...`);
            try {
                await task.fn();
                console.log(`✅ ${task.name} 完成\n`);
            } catch (error) {
                console.log(`❌ ${task.name} 失败: ${error.message}\n`);
            }
        }

        this.showDeploymentSummary();
    }

    setupNpmScripts() {
        const packageJsonPath = path.join(this.projectRoot, 'package.json');
        let packageJson = {};

        if (fs.existsSync(packageJsonPath)) {
            packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
        } else {
            packageJson = {
                name: "idle-game-cocos",
                version: "1.0.0",
                description: "武侠放置游戏 - Cocos Creator版本"
            };
        }

        if (!packageJson.scripts) {
            packageJson.scripts = {};
        }

        // 添加AI测试相关脚本
        packageJson.scripts['test:ai'] = 'cd scripts/ai-testing && node ai-test-working.js';
        packageJson.scripts['test:ai:simple'] = 'cd scripts/ai-testing && node simple-test.js';
        packageJson.scripts['fix:quality'] = 'cd scripts/ai-testing && node code-quality-fixer.js';
        packageJson.scripts['server:test'] = 'cd scripts/ai-testing && node test-server-simple.js';
        packageJson.scripts['dev:full'] = 'npm run server:test & npm run test:ai';

        fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    }

    setupGitHooks() {
        const hooksDir = path.join(this.projectRoot, '.git', 'hooks');
        
        if (!fs.existsSync(hooksDir)) {
            fs.mkdirSync(hooksDir, { recursive: true });
        }

        // Pre-commit hook
        const preCommitHook = `#!/bin/sh
# AI测试框架 Pre-commit Hook

echo "🤖 运行AI测试框架..."

cd scripts/ai-testing
node ai-test-working.js

if [ $? -ne 0 ]; then
    echo "❌ AI测试失败，提交被阻止"
    echo "💡 请运行 'npm run fix:quality' 修复代码质量问题"
    exit 1
fi

echo "✅ AI测试通过，允许提交"
`;

        const preCommitPath = path.join(hooksDir, 'pre-commit');
        fs.writeFileSync(preCommitPath, preCommitHook);
        
        // 在Windows上设置可执行权限
        if (process.platform !== 'win32') {
            fs.chmodSync(preCommitPath, '755');
        }
    }

    setupVSCodeTasks() {
        const vscodeDir = path.join(this.projectRoot, '.vscode');
        if (!fs.existsSync(vscodeDir)) {
            fs.mkdirSync(vscodeDir, { recursive: true });
        }

        const tasksConfig = {
            version: "2.0.0",
            tasks: [
                {
                    label: "AI测试框架 - 完整测试",
                    type: "shell",
                    command: "npm",
                    args: ["run", "test:ai"],
                    group: {
                        kind: "test",
                        isDefault: true
                    },
                    presentation: {
                        echo: true,
                        reveal: "always",
                        focus: false,
                        panel: "shared"
                    },
                    problemMatcher: []
                },
                {
                    label: "AI测试框架 - 快速测试",
                    type: "shell",
                    command: "npm",
                    args: ["run", "test:ai:simple"],
                    group: "test",
                    presentation: {
                        echo: true,
                        reveal: "always",
                        focus: false,
                        panel: "shared"
                    }
                },
                {
                    label: "代码质量修复",
                    type: "shell",
                    command: "npm",
                    args: ["run", "fix:quality"],
                    group: "build",
                    presentation: {
                        echo: true,
                        reveal: "always",
                        focus: false,
                        panel: "shared"
                    }
                },
                {
                    label: "启动测试服务器",
                    type: "shell",
                    command: "npm",
                    args: ["run", "server:test"],
                    group: "build",
                    isBackground: true,
                    presentation: {
                        echo: true,
                        reveal: "always",
                        focus: false,
                        panel: "shared"
                    }
                }
            ]
        };

        const tasksPath = path.join(vscodeDir, 'tasks.json');
        fs.writeFileSync(tasksPath, JSON.stringify(tasksConfig, null, 2));
    }

    generateDocumentation() {
        const docContent = `# 🤖 AI测试框架使用指南

## 概述
AI测试框架是为武侠放置游戏Cocos Creator项目开发的智能测试系统，提供全面的项目健康检查和代码质量分析。

## 快速开始

### 1. 运行完整测试
\`\`\`bash
npm run test:ai
\`\`\`

### 2. 运行简化测试
\`\`\`bash
npm run test:ai:simple
\`\`\`

### 3. 修复代码质量问题
\`\`\`bash
npm run fix:quality
\`\`\`

### 4. 启动测试服务器
\`\`\`bash
npm run server:test
\`\`\`

## 测试项目

### 基础检查
- ✅ 项目结构检查
- ✅ Cocos Creator配置验证
- ✅ 脚本文件分析
- ✅ 资源文件检查

### 高级检查
- ✅ 组件依赖分析
- ✅ 场景完整性验证
- ✅ 资源引用检查
- ✅ 武侠系统测试

### 质量检查
- ✅ 代码质量分析
- ✅ 性能基准测试
- ✅ 后端服务器状态

## CI/CD集成

项目已配置GitHub Actions自动化测试：
- 每次推送到main/develop分支时运行
- 每个Pull Request都会运行测试
- 每天凌晨2点定时运行
- 测试报告自动发布到GitHub Pages

## VSCode集成

使用Ctrl+Shift+P打开命令面板，搜索"Tasks: Run Task"：
- AI测试框架 - 完整测试
- AI测试框架 - 快速测试
- 代码质量修复
- 启动测试服务器

## Git Hooks

Pre-commit hook已配置，每次提交前自动运行AI测试。
如果测试失败，提交将被阻止。

## 报告文件

- \`comprehensive-test-report.json\` - 详细测试报告
- \`code-quality-fix-report.json\` - 代码质量修复报告

## 故障排除

### 测试服务器连接失败
\`\`\`bash
npm run server:test
# 等待几秒钟后重新运行测试
npm run test:ai
\`\`\`

### 代码质量问题过多
\`\`\`bash
npm run fix:quality
npm run test:ai
\`\`\`

### PowerShell执行策略问题
以管理员身份运行PowerShell：
\`\`\`powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
\`\`\`

## 贡献指南

1. 运行测试确保项目健康
2. 修复代码质量问题
3. 提交前确保所有测试通过
4. 定期运行完整测试套件

## 支持

如有问题，请查看测试报告中的详细信息和建议。
`;

        const docPath = path.join(this.projectRoot, 'AI-TESTING-GUIDE.md');
        fs.writeFileSync(docPath, docContent);
    }

    createLaunchScripts() {
        // Windows批处理文件
        const batContent = `@echo off
echo 🤖 启动AI测试框架...
echo.

cd /d "%~dp0"

echo 📋 可用命令:
echo   1. 完整测试 (npm run test:ai)
echo   2. 快速测试 (npm run test:ai:simple)
echo   3. 代码质量修复 (npm run fix:quality)
echo   4. 启动测试服务器 (npm run server:test)
echo.

set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    npm run test:ai
) else if "%choice%"=="2" (
    npm run test:ai:simple
) else if "%choice%"=="3" (
    npm run fix:quality
) else if "%choice%"=="4" (
    npm run server:test
) else (
    echo 无效选择
)

pause
`;

        const batPath = path.join(this.projectRoot, 'ai-testing-launcher.bat');
        fs.writeFileSync(batPath, batContent);

        // Shell脚本
        const shContent = `#!/bin/bash

echo "🤖 启动AI测试框架..."
echo

cd "$(dirname "$0")"

echo "📋 可用命令:"
echo "  1. 完整测试 (npm run test:ai)"
echo "  2. 快速测试 (npm run test:ai:simple)"
echo "  3. 代码质量修复 (npm run fix:quality)"
echo "  4. 启动测试服务器 (npm run server:test)"
echo

read -p "请选择 (1-4): " choice

case $choice in
    1)
        npm run test:ai
        ;;
    2)
        npm run test:ai:simple
        ;;
    3)
        npm run fix:quality
        ;;
    4)
        npm run server:test
        ;;
    *)
        echo "无效选择"
        ;;
esac
`;

        const shPath = path.join(this.projectRoot, 'ai-testing-launcher.sh');
        fs.writeFileSync(shPath, shContent);
        
        if (process.platform !== 'win32') {
            fs.chmodSync(shPath, '755');
        }
    }

    showDeploymentSummary() {
        console.log('\n' + '='.repeat(60));
        console.log('🎉 AI测试框架部署完成！');
        console.log('='.repeat(60));
        
        console.log('\n📋 已创建的文件:');
        console.log('  • package.json - 添加了npm脚本');
        console.log('  • .git/hooks/pre-commit - Git提交前测试');
        console.log('  • .vscode/tasks.json - VSCode任务配置');
        console.log('  • AI-TESTING-GUIDE.md - 使用指南');
        console.log('  • ai-testing-launcher.bat/.sh - 快捷启动脚本');
        console.log('  • .github/workflows/ai-testing.yml - CI/CD配置');
        
        console.log('\n🚀 快速开始:');
        console.log('  npm run test:ai        # 运行完整测试');
        console.log('  npm run fix:quality    # 修复代码质量');
        console.log('  npm run server:test    # 启动测试服务器');
        
        console.log('\n💡 提示:');
        console.log('  • 每次提交前会自动运行测试');
        console.log('  • 在VSCode中按Ctrl+Shift+P搜索"Tasks"运行测试');
        console.log('  • 双击ai-testing-launcher.bat快速启动');
        console.log('  • 查看AI-TESTING-GUIDE.md了解详细用法');
    }
}

// 主函数
async function main() {
    const deployer = new AITestingDeployer();
    
    try {
        await deployer.deploy();
    } catch (error) {
        console.error('❌ 部署失败:', error);
        process.exit(1);
    }
}

if (require.main === module) {
    main();
}

module.exports = { AITestingDeployer };
