@echo off
echo 🤖 启动AI测试框架...
echo.

cd /d "%~dp0"

echo 📋 可用命令:
echo   1. 完整测试 (npm run test:ai)
echo   2. 快速测试 (npm run test:ai:simple)
echo   3. 代码质量修复 (npm run fix:quality)
echo   4. 启动测试服务器 (npm run server:test)
echo.

set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    npm run test:ai
) else if "%choice%"=="2" (
    npm run test:ai:simple
) else if "%choice%"=="3" (
    npm run fix:quality
) else if "%choice%"=="4" (
    npm run server:test
) else (
    echo 无效选择
)

pause
