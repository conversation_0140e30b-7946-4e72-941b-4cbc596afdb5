import { BaseManager } from '../managers/BaseManager';
import { apiClient, IApiResponse } from './ApiClient';
import { EventManager } from '../managers/EventManager';
import { Logger } from '../core/utils/Logger';
import { IEntityData } from '../data/IEntityData';
import { ISkillData } from '../data/ISkillData';
import { IItemData } from '../data/IItemData';

/**
 * 同步状态枚举
 */
export enum SyncStatus {
    IDLE = 'IDLE',
    SYNCING = 'SYNCING',
    SUCCESS = 'SUCCESS',
    FAILED = 'FAILED',
    CONFLICT = 'CONFLICT',
}

/**
 * 数据同步配置
 */
export interface ISyncConfig {
    autoSync: boolean;
    syncInterval: number; // 毫秒
    retryAttempts: number;
    conflictResolution: 'server' | 'client' | 'manual';
}

/**
 * 同步数据项
 */
export interface ISyncItem {
    id: string;
    type: 'user' | 'character' | 'skill' | 'item' | 'inventory';
    data: any;
    lastModified: number;
    version: number;
    isDirty: boolean;
}

/**
 * 同步结果
 */
export interface ISyncResult {
    success: boolean;
    syncedItems: number;
    failedItems: number;
    conflicts: ISyncItem[];
    errors: string[];
}

/**
 * 数据同步管理器
 * 负责本地数据与服务器数据的同步
 */
export class DataSyncManager extends BaseManager {
    private static instance: DataSyncManager;
    private syncStatus: SyncStatus = SyncStatus.IDLE;
    private syncConfig: ISyncConfig;
    private localData: Map<string, ISyncItem> = new Map();
    private syncQueue: ISyncItem[] = [];
    private syncTimer: number | null = null;
    private isOnline: boolean = true;

    private constructor() {
        super();
        this.syncConfig = {
            autoSync: true,
            syncInterval: 30000, // 30秒
            retryAttempts: 3,
            conflictResolution: 'server',
        };
        this.initializeSync();
    }

    public static getInstance(): DataSyncManager {
        if (!DataSyncManager.instance) {
            DataSyncManager.instance = new DataSyncManager();
        }
        return DataSyncManager.instance;
    }

    /**
     * 初始化同步机制
     */
    private initializeSync(): void {
        // 监听网络状态变化
        EventManager.getInstance().on('network:status-changed', (data) => {
            this.isOnline = data.newStatus === 'ONLINE';
            if (this.isOnline && this.syncQueue.length > 0) {
                this.syncData();
            }
        });

        // 监听数据变化事件
        EventManager.getInstance().on('data:changed', (data) => {
            this.markDataDirty(data.type, data.id, data.data);
        });

        // 启动自动同步
        if (this.syncConfig.autoSync) {
            this.startAutoSync();
        }
    }

    /**
     * 配置同步设置
     */
    public configure(config: Partial<ISyncConfig>): void {
        this.syncConfig = { ...this.syncConfig, ...config };
        
        if (this.syncConfig.autoSync) {
            this.startAutoSync();
        } else {
            this.stopAutoSync();
        }
    }

    /**
     * 启动自动同步
     */
    public startAutoSync(): void {
        this.stopAutoSync(); // 先停止现有的定时器
        
        this.syncTimer = window.setInterval(() => {
            if (this.isOnline && this.syncStatus === SyncStatus.IDLE) {
                this.syncData();
            }
        }, this.syncConfig.syncInterval);

        Logger.info('自动同步已启动', { interval: this.syncConfig.syncInterval });
    }

    /**
     * 停止自动同步
     */
    public stopAutoSync(): void {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
        }
        Logger.info('自动同步已停止');
    }

    /**
     * 标记数据为脏数据
     */
    public markDataDirty(type: string, id: string, data: any): void {
        const key = `${type}:${id}`;
        const existingItem = this.localData.get(key);
        
        const syncItem: ISyncItem = {
            id,
            type: type as any,
            data,
            lastModified: Date.now(),
            version: existingItem ? existingItem.version + 1 : 1,
            isDirty: true,
        };

        this.localData.set(key, syncItem);
        
        // 添加到同步队列
        if (!this.syncQueue.find(item => item.id === id && item.type === type)) {
            this.syncQueue.push(syncItem);
        }

        Logger.debug('数据标记为脏数据', { type, id });
    }

    /**
     * 同步数据到服务器
     */
    public async syncData(): Promise<ISyncResult> {
        if (this.syncStatus === SyncStatus.SYNCING) {
            Logger.warn('同步正在进行中，跳过本次同步');
            return {
                success: false,
                syncedItems: 0,
                failedItems: 0,
                conflicts: [],
                errors: ['同步正在进行中'],
            };
        }

        if (!this.isOnline) {
            Logger.warn('网络不可用，跳过同步');
            return {
                success: false,
                syncedItems: 0,
                failedItems: 0,
                conflicts: [],
                errors: ['网络不可用'],
            };
        }

        this.syncStatus = SyncStatus.SYNCING;
        Logger.info('开始数据同步', { queueLength: this.syncQueue.length });

        const result: ISyncResult = {
            success: true,
            syncedItems: 0,
            failedItems: 0,
            conflicts: [],
            errors: [],
        };

        try {
            // 处理同步队列中的每个项目
            for (const item of [...this.syncQueue]) {
                try {
                    await this.syncSingleItem(item);
                    result.syncedItems++;
                    
                    // 从队列中移除已同步的项目
                    const index = this.syncQueue.findIndex(
                        queueItem => queueItem.id === item.id && queueItem.type === item.type
                    );
                    if (index !== -1) {
                        this.syncQueue.splice(index, 1);
                    }
                } catch (error) {
                    result.failedItems++;
                    result.errors.push(`同步失败 ${item.type}:${item.id} - ${error.message}`);
                    Logger.error('单项同步失败', { item, error });
                }
            }

            this.syncStatus = SyncStatus.SUCCESS;
            Logger.info('数据同步完成', result);

        } catch (error) {
            this.syncStatus = SyncStatus.FAILED;
            result.success = false;
            result.errors.push(`同步过程出错: ${error.message}`);
            Logger.error('数据同步失败', error);
        }

        // 发送同步完成事件
        EventManager.getInstance().emit('data:sync-completed', result);

        return result;
    }

    /**
     * 同步单个数据项
     */
    private async syncSingleItem(item: ISyncItem): Promise<void> {
        switch (item.type) {
            case 'user':
                await this.syncUserData(item);
                break;
            case 'character':
                await this.syncCharacterData(item);
                break;
            case 'skill':
                await this.syncSkillData(item);
                break;
            case 'item':
                await this.syncItemData(item);
                break;
            case 'inventory':
                await this.syncInventoryData(item);
                break;
            default:
                throw new Error(`不支持的数据类型: ${item.type}`);
        }

        // 标记为已同步
        item.isDirty = false;
        this.localData.set(`${item.type}:${item.id}`, item);
    }

    /**
     * 同步用户数据
     */
    private async syncUserData(item: ISyncItem): Promise<void> {
        const response = await apiClient.updateUserProfile(item.data);
        if (!response.success) {
            throw new Error(response.message || '用户数据同步失败');
        }
    }

    /**
     * 同步角色数据
     */
    private async syncCharacterData(item: ISyncItem): Promise<void> {
        const response = await apiClient.updateCharacter(item.id, item.data);
        if (!response.success) {
            throw new Error(response.message || '角色数据同步失败');
        }
    }

    /**
     * 同步技能数据
     */
    private async syncSkillData(item: ISyncItem): Promise<void> {
        // 技能数据通常是只读的，这里可能是技能使用记录
        Logger.debug('技能数据同步', { item });
    }

    /**
     * 同步物品数据
     */
    private async syncItemData(item: ISyncItem): Promise<void> {
        // 物品数据通常是只读的，这里可能是物品使用记录
        Logger.debug('物品数据同步', { item });
    }

    /**
     * 同步背包数据
     */
    private async syncInventoryData(item: ISyncItem): Promise<void> {
        // 背包数据同步可能涉及多个API调用
        Logger.debug('背包数据同步', { item });
    }

    /**
     * 从服务器拉取最新数据
     */
    public async pullFromServer(): Promise<void> {
        try {
            Logger.info('开始从服务器拉取数据');

            // 拉取用户资料
            const userResponse = await apiClient.getUserProfile();
            if (userResponse.success && userResponse.data) {
                this.updateLocalData('user', 'current', userResponse.data, false);
            }

            // 拉取角色列表
            const charactersResponse = await apiClient.getCharacters();
            if (charactersResponse.success && charactersResponse.data) {
                for (const character of charactersResponse.data) {
                    this.updateLocalData('character', character.id, character, false);
                }
            }

            Logger.info('服务器数据拉取完成');
            EventManager.getInstance().emit('data:pulled-from-server');

        } catch (error) {
            Logger.error('从服务器拉取数据失败', error);
            throw error;
        }
    }

    /**
     * 更新本地数据
     */
    public updateLocalData(type: string, id: string, data: any, markDirty: boolean = true): void {
        const key = `${type}:${id}`;
        const existingItem = this.localData.get(key);
        
        const syncItem: ISyncItem = {
            id,
            type: type as any,
            data,
            lastModified: Date.now(),
            version: existingItem ? existingItem.version + 1 : 1,
            isDirty: markDirty,
        };

        this.localData.set(key, syncItem);

        if (markDirty && !this.syncQueue.find(item => item.id === id && item.type === type)) {
            this.syncQueue.push(syncItem);
        }

        // 发送数据更新事件
        EventManager.getInstance().emit('data:updated', { type, id, data });
    }

    /**
     * 获取本地数据
     */
    public getLocalData(type: string, id: string): any | null {
        const key = `${type}:${id}`;
        const item = this.localData.get(key);
        return item ? item.data : null;
    }

    /**
     * 清除本地数据
     */
    public clearLocalData(): void {
        this.localData.clear();
        this.syncQueue = [];
        Logger.info('本地数据已清除');
    }

    /**
     * 获取同步状态
     */
    public getSyncStatus(): SyncStatus {
        return this.syncStatus;
    }

    /**
     * 获取待同步项目数量
     */
    public getPendingSyncCount(): number {
        return this.syncQueue.length;
    }

    /**
     * 强制同步
     */
    public async forcSync(): Promise<ISyncResult> {
        return this.syncData();
    }
}

// 导出单例实例
export const dataSyncManager = DataSyncManager.getInstance();
