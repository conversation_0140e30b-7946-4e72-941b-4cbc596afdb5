#!/usr/bin/env ts-node

/**
 * AI测试框架使用示例
 * 演示如何使用AI测试框架进行项目测试
 */

import { MasterTestBot } from '../core/MasterTestBot';
import { TestBotFactory } from '../core/TestBotFactory';
import { SystemDiscoveryAgent } from '../agents/SystemDiscoveryAgent';
import { TestReportGenerator } from '../core/TestReportGenerator';
import * as path from 'path';

async function runAITestingExample(): Promise<void> {
    console.log('🚀 AI测试框架使用示例');
    console.log('=====================================\n');

    try {
        // 1. 初始化AI测试框架组件
        console.log('📋 步骤1: 初始化AI测试框架组件');
        const masterBot = new MasterTestBot();
        const testBotFactory = new TestBotFactory();
        const discoveryAgent = new SystemDiscoveryAgent();
        const reportGenerator = new TestReportGenerator();
        console.log('✅ 组件初始化完成\n');

        // 2. 发现项目中的系统
        console.log('🔍 步骤2: 发现项目系统');
        const projectPath = path.resolve('../../'); // 项目根目录
        const systems = await discoveryAgent.discoverSystems(projectPath);
        
        console.log(`📦 发现 ${systems.length} 个系统:`);
        systems.forEach((system, index) => {
            console.log(`  ${index + 1}. ${system.name} (${system.type}) - ${system.complexity}`);
        });
        console.log('');

        // 3. 为每个系统创建测试机器人
        console.log('🤖 步骤3: 创建测试机器人');
        const testBots = new Map();
        
        for (const system of systems.slice(0, 3)) { // 只处理前3个系统作为示例
            console.log(`🔄 为 ${system.name} 创建测试机器人...`);
            const testBot = await testBotFactory.createTestBotForSystem(system.path);
            testBots.set(system.name, testBot);
            console.log(`✅ ${system.name} 测试机器人创建完成`);
        }
        console.log('');

        // 4. 生成测试用例
        console.log('🧪 步骤4: 生成测试用例');
        const allTestCases = [];
        
        for (const [systemName, testBot] of testBots) {
            console.log(`📝 为 ${systemName} 生成测试用例...`);
            const testCases = await testBot.generateTestsForSystem();
            allTestCases.push(...testCases);
            console.log(`✅ ${systemName}: 生成 ${testCases.length} 个测试用例`);
        }
        console.log(`📊 总计生成 ${allTestCases.length} 个测试用例\n`);

        // 5. 模拟执行测试用例
        console.log('⚡ 步骤5: 执行测试用例');
        const testResults = [];
        
        for (const testCase of allTestCases) {
            // 模拟测试执行
            const result = {
                id: testCase.id,
                name: testCase.name,
                status: Math.random() > 0.1 ? 'passed' : 'failed', // 90%通过率
                executionTime: Math.floor(Math.random() * 1000) + 50, // 50-1050ms
                errorMessage: Math.random() > 0.9 ? 'Mock error message' : undefined,
                assertions: [
                    {
                        description: 'Basic functionality test',
                        passed: Math.random() > 0.05,
                        expected: 'success',
                        actual: 'success'
                    }
                ]
            };
            testResults.push(result);
        }
        
        const passedTests = testResults.filter(r => r.status === 'passed').length;
        const failedTests = testResults.filter(r => r.status === 'failed').length;
        
        console.log(`✅ 测试执行完成: ${passedTests} 通过, ${failedTests} 失败\n`);

        // 6. 生成测试报告
        console.log('📊 步骤6: 生成测试报告');
        const report = await reportGenerator.generateReport(testResults, {
            projectName: '武侠放置游戏',
            projectPath: projectPath,
            testFramework: 'jest'
        });
        
        console.log('📄 测试报告摘要:');
        console.log(`  总测试数: ${report.summary.totalTests}`);
        console.log(`  通过率: ${report.summary.successRate}%`);
        console.log(`  平均执行时间: ${report.summary.performance.averageExecutionTime}ms`);
        console.log(`  质量评分: ${report.analysis.qualityScore}/100`);
        console.log('');

        // 7. 保存报告文件
        console.log('💾 步骤7: 保存报告文件');
        const outputDir = path.join(__dirname, '../test-results');
        const htmlReportPath = path.join(outputDir, 'example-report.html');
        const jsonReportPath = path.join(outputDir, 'example-report.json');
        
        await reportGenerator.generateHTMLReport(report, htmlReportPath);
        await reportGenerator.generateJSONReport(report, jsonReportPath);
        
        console.log(`📄 HTML报告: ${htmlReportPath}`);
        console.log(`📄 JSON报告: ${jsonReportPath}`);
        console.log('');

        // 8. 算法一致性验证示例
        console.log('🔍 步骤8: 算法一致性验证示例');
        
        // 模拟算法验证
        const algorithmValidationResult = await masterBot.validateAlgorithmConsistency(
            'function calculateDamage(attack, defense) { return Math.max(1, attack - defense); }',
            'function calculateDamage(attack, defense) { return Math.max(1, attack - defense); }',
            'damageCalculation'
        );
        
        console.log(`🎯 算法一致性验证结果:`);
        console.log(`  算法: damageCalculation`);
        console.log(`  一致性: ${algorithmValidationResult.isConsistent ? '✅ 一致' : '❌ 不一致'}`);
        console.log(`  偏差: ${(algorithmValidationResult.deviationPercentage * 100).toFixed(3)}%`);
        console.log('');

        // 9. 显示改进建议
        console.log('💡 步骤9: 改进建议');
        if (report.recommendations.length > 0) {
            console.log('📋 系统建议:');
            report.recommendations.forEach((recommendation, index) => {
                console.log(`  ${index + 1}. ${recommendation}`);
            });
        } else {
            console.log('🎉 系统运行良好，暂无改进建议');
        }
        console.log('');

        // 10. 显示统计信息
        console.log('📊 步骤10: 系统统计信息');
        const stats = testBotFactory.getSystemStatistics();
        
        console.log(`📦 系统统计:`);
        console.log(`  总系统数: ${stats.totalSystems}`);
        console.log(`  系统类型分布:`);
        for (const [type, count] of stats.systemTypes) {
            console.log(`    - ${type}: ${count}`);
        }
        console.log(`  复杂度分布:`);
        for (const [complexity, count] of stats.complexityDistribution) {
            console.log(`    - ${complexity}: ${count}`);
        }
        console.log('');

        // 完成
        console.log('🎉 AI测试框架示例运行完成!');
        console.log('=====================================');
        console.log('');
        console.log('🚀 下一步建议:');
        console.log('  1. 查看生成的测试报告');
        console.log('  2. 根据建议优化代码');
        console.log('  3. 集成到CI/CD流程');
        console.log('  4. 定期运行算法一致性验证');

    } catch (error) {
        console.error('❌ 示例运行失败:', error);
        process.exit(1);
    }
}

// 高级使用示例
async function runAdvancedExample(): Promise<void> {
    console.log('\n🔬 高级使用示例');
    console.log('=====================================\n');

    try {
        const masterBot = new MasterTestBot();

        // 1. 代码变更分析示例
        console.log('📝 代码变更分析示例');
        const codeChanges = [
            {
                file: 'BattleSystem.ts',
                type: 'modified' as const,
                content: 'Updated damage calculation algorithm'
            },
            {
                file: 'SkillManager.ts',
                type: 'added' as const,
                content: 'Added new skill effect system'
            }
        ];

        const testStrategy = await masterBot.generateTestStrategy(codeChanges);
        console.log('🎯 生成的测试策略:');
        console.log(`  测试优先级: ${testStrategy.testPriority.length} 项`);
        console.log(`  测试范围: ${testStrategy.testScope.modules.length} 个模块`);
        console.log('');

        // 2. 任务分发示例
        console.log('📋 任务分发示例');
        const testTasks = await masterBot.distributeTestTasks(testStrategy);
        console.log(`📊 分发了 ${testTasks.length} 个测试任务`);
        console.log('');

        // 3. 结果汇总示例
        console.log('📊 结果汇总示例');
        const mockResults = testTasks.map(task => ({
            taskId: task.id,
            status: Math.random() > 0.15 ? 'passed' : 'failed' as const,
            duration: Math.floor(Math.random() * 2000) + 100,
            details: { message: 'Test completed successfully' }
        }));

        const summary = await masterBot.aggregateTestResults(mockResults);
        console.log('📈 测试摘要生成完成');
        console.log(`  洞察数量: ${summary.insights.patterns.length}`);
        console.log(`  建议数量: ${summary.recommendations.length}`);
        console.log('');

        console.log('✅ 高级示例完成!');

    } catch (error) {
        console.error('❌ 高级示例失败:', error);
    }
}

// 性能测试示例
async function runPerformanceExample(): Promise<void> {
    console.log('\n⚡ 性能测试示例');
    console.log('=====================================\n');

    console.log('🔄 模拟性能测试...');
    
    const startTime = Date.now();
    
    // 模拟一些性能密集型操作
    for (let i = 0; i < 1000; i++) {
        // 模拟算法执行
        Math.sqrt(Math.random() * 1000000);
    }
    
    const endTime = Date.now();
    const executionTime = endTime - startTime;
    
    console.log(`⏱️ 执行时间: ${executionTime}ms`);
    
    if (executionTime < 100) {
        console.log('🚀 性能优秀!');
    } else if (executionTime < 500) {
        console.log('👍 性能良好');
    } else {
        console.log('⚠️ 性能需要优化');
    }
    
    console.log('✅ 性能测试示例完成!');
}

// 主函数
async function main(): Promise<void> {
    console.log('🤖 AI测试框架完整示例');
    console.log('=====================================\n');

    // 运行基础示例
    await runAITestingExample();

    // 运行高级示例
    await runAdvancedExample();

    // 运行性能示例
    await runPerformanceExample();

    console.log('\n🎊 所有示例运行完成!');
    console.log('感谢使用AI测试框架!');
}

// 如果直接运行此文件
if (require.main === module) {
    main().catch(error => {
        console.error('❌ 示例运行失败:', error);
        process.exit(1);
    });
}

export { runAITestingExample, runAdvancedExample, runPerformanceExample };
