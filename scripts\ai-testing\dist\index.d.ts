#!/usr/bin/env node
/**
 * AI测试框架主入口
 * 提供命令行接口来使用AI驱动的测试功能
 */
/**
 * 设置AI测试系统
 */
declare function setupAITesting(projectPath?: string): Promise<void>;
/**
 * 发现并测试所有系统
 */
declare function discoverAndTest(projectPath?: string): Promise<void>;
/**
 * 验证算法一致性
 */
declare function validateAlgorithms(systemPath?: string): Promise<void>;
export { setupAITesting, discoverAndTest, validateAlgorithms };
//# sourceMappingURL=index.d.ts.map