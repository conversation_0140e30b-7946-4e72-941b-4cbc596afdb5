import { _decorator, Component, Node, director, input, Input, EventKeyboard, KeyCode } from 'cc';

const { ccclass, property } = _decorator;

/**
 * 场景切换测试组件
 * 提供键盘快捷键切换场景功能
 */
@ccclass('SceneSwitchButtons')
export class SceneSwitchButtons extends Component {

    protected onLoad(): void {
        console.log('🎮 场景切换测试组件加载');
        this.initializeKeyboardInput();
        this.showInstructions();
    }

    protected start(): void {
        console.log('🎮 场景切换测试组件开始');
    }

    /**
     * 初始化键盘输入
     */
    private initializeKeyboardInput(): void {
        input.on(Input.EventType.KEY_DOWN, this.onKeyDown, this);
        console.log('✅ 键盘输入初始化完成');
    }

    /**
     * 显示操作说明
     */
    private showInstructions(): void {
        const currentScene = director.getScene();
        const sceneName = currentScene ? currentScene.name : '未知';

        console.log('🎮 ========== 场景切换测试 ==========');
        console.log(`📍 当前场景: ${sceneName}`);
        console.log('⌨️ 键盘快捷键:');
        console.log('   按 1 键 - 切换到 Launch 场景');
        console.log('   按 2 键 - 切换到 Main 场景');
        console.log('   按 3 键 - 切换到 Battle 场景');
        console.log('   按 H 键 - 显示帮助信息');
        console.log('🎮 ===================================');
    }

    /**
     * 键盘按键处理
     */
    private onKeyDown(event: EventKeyboard): void {
        switch (event.keyCode) {
            case KeyCode.DIGIT_1:
                this.goToLaunch();
                break;
            case KeyCode.DIGIT_2:
                this.goToMain();
                break;
            case KeyCode.DIGIT_3:
                this.goToBattle();
                break;
            case KeyCode.KEY_H:
                this.showInstructions();
                break;
        }
    }

    /**
     * 切换到启动场景
     */
    private goToLaunch(): void {
        console.log('🚀 切换到启动场景');
        this.switchScene('Launch');
    }

    /**
     * 切换到主界面场景
     */
    private goToMain(): void {
        console.log('🏠 切换到主界面场景');
        this.switchScene('Main');
    }

    /**
     * 切换到战斗场景
     */
    private goToBattle(): void {
        console.log('⚔️ 切换到战斗场景');
        this.switchScene('Battle');
    }

    /**
     * 通用场景切换方法
     */
    private switchScene(sceneName: string): void {
        try {
            console.log(`🔄 正在切换到场景: ${sceneName}`);
            director.loadScene(sceneName, (error) => {
                if (error) {
                    console.error(`❌ 场景切换失败: ${sceneName}`, error);
                } else {
                    console.log(`✅ 场景切换成功: ${sceneName}`);
                }
            });
        } catch (error) {
            console.error(`❌ 场景切换异常: ${sceneName}`, error);
        }
    }

    protected onEnable(): void {
        this.showInstructions();
    }

    protected onDestroy(): void {
        // 清理键盘输入事件
        input.off(Input.EventType.KEY_DOWN, this.onKeyDown, this);

        console.log('🎮 场景切换测试组件销毁');
    }
}
